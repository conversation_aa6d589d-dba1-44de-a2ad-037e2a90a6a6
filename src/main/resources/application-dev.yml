
app:
  id: ttfund.web.service.community
apollo:
  cluster: default
  cacheDir: D:/vdb/opt/data/dev1/
  meta: http://**************:8080/
  bootstrap:
    namespaces: application,KF.core.auth
    enabled: true
    eagerLoad:
      enabled: true

swagger:
  enabled: true


# barRedisWrite
spring:
  redis:
    host: **************
    port: 6379
    password: ShdF3yKd84f2s6

#type=1,sAw2daH4j6sR78i@**************:6381
userRedis:
  host: **************
  port: 6381
  password: sAw2daH4j6sR78i



