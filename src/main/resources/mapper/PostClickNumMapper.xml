<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.PostClickNumMapper">
    <insert id="insertOrUpdateBulk" parameterType="ttfund.web.communityservice.bean.jijinBar.post.guba.PostclickNum">
        Insert into TB_POSTCLICKNUM (ID,CLICKNUM,ISENABLED,CREATETIME,UPDATETIME)
        values
        <foreach collection="clicks" item="item" separator=",">
            (#{item.ID},#{item.ClickNum},1,NOW(),NOW())
        </foreach>
        ON DUPLICATE KEY
        Update CLICKNUM=values(CLICKNUM),UPDATETIME=NOW()
    </insert>
</mapper>