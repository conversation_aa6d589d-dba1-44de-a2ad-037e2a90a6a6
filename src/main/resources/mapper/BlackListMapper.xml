<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.BlackListMapper">

    <update id="update" parameterType="ttfund.web.communityservice.bean.jijinBar.post.guba.BlackList">
        Update JIJINBA.TB_BLACKLIST set TIME=#{info.Time},ISENABLED=#{info.IsEnabled},UPDATETIME=NOW() where UID=#{info.UID} and TARGETUID=#{info.TargetUID}
    </update>

    <insert id="insertOrUpdate" parameterType="ttfund.web.communityservice.bean.jijinBar.post.guba.BlackList">
        Insert into JIJINBA.TB_BLACKLIST (UID,TARGETUID,TIME,ISENABLED,CREATETIME,UPDATETIME)
               values (#{info.UID},#{info.TargetUID},#{info.Time},1,NOW(),NOW())
               ON DUPLICATE KEY
               Update ISENABLED=1,TIME=#{info.Time},UPDATETIME=NOW()
    </insert>

    <select id="getBlackListRecordByUids" resultType="ttfund.web.communityservice.bean.jijinBar.user.BlackListEntity">
        select * from tb_blacklist where UID IN
        <foreach collection="uidList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by UID ,UPDATETIME
    </select>

</mapper>