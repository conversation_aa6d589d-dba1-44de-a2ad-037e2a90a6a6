<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.FundTopicMapper">
    <select id="getListByIds" resultType="ttfund.web.communityservice.bean.jijinBar.post.FundTopic">
        SELECT * from tb_fundtopic WHERE DEL=0 and htid in
        <foreach collection="htids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getFundTopicListByNames" resultType="ttfund.web.communityservice.bean.jijinBar.post.FundTopic">
        SELECT * from tb_fundtopic WHERE  DEL=0 and `System` = 1 and name in
        <foreach collection="names" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getByIdList" resultType="ttfund.web.communityservice.bean.jijinBar.post.FundTopic">
        SELECT * from tb_fundtopic WHERE  htid in
        <foreach collection="htids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <insert id="insert" parameterType="map">
        INSERT INTO `tb_fundtopic`
        (`ActionType`, `htid`, `btype`, `stype`, `img`, `img_list`, `name`, `introduction`,
        `collectNumber`, `participantCount`, `clickCount`, `T_ViewPointType`, `T_ViewPointIsShowNum`, `T_ViewPointSShowTime`,
        `T_ViewPointEShowTime`, `T_ViewPointOptions`, `T_AboutCode`, `T_Code`, `T_HotUid`,`createtime`,`updatetime`,`del`,`System`)
        VALUES (
        #{map.ACTIONTYPE}, #{map.HTID}, #{map.BTYPE}, #{map.STYPE}, #{map.IMG}, #{map.IMG_LIST}, #{map.NAME}, #{map.INTRODUCTION},
        #{map.COLLECTNUMBER},#{map.PARTICIPANTCOUNT}, #{map.CLICKCOUNT}, #{map.T_VIEWPOINTTYPE}, #{map.T_VIEWPOINTISSHOWNUM}, #{map.T_VIEWPOINTSSHOWTIME},
        #{map.T_VIEWPOINTESHOWTIME},#{map.T_VIEWPOINTOPTIONS}, #{map.T_ABOUTCODE}, #{map.T_CODE}, #{map.T_HOTUID},NOW(),NOW(),#{map.DEL},#{map.System}
        )
    </insert>

    <update id="update" parameterType="map">
        UPDATE `tb_fundtopic`
        SET `ACTIONTYPE` = #{map.ACTIONTYPE}, `HTID` = #{map.HTID}, `BTYPE` = #{map.BTYPE}, `STYPE` = #{map.STYPE}, `IMG` = #{map.IMG},
        `IMG_LIST` = #{map.IMG_LIST}, `NAME` = #{map.NAME}, `INTRODUCTION` = #{map.INTRODUCTION}, `COLLECTNUMBER` = #{map.COLLECTNUMBER},
        `PARTICIPANTCOUNT` = #{map.PARTICIPANTCOUNT}, `CLICKCOUNT` = #{map.CLICKCOUNT}, `T_VIEWPOINTTYPE` = #{map.T_VIEWPOINTTYPE},
        `T_VIEWPOINTISSHOWNUM` = #{map.T_VIEWPOINTISSHOWNUM}, `T_VIEWPOINTSSHOWTIME` = #{map.T_VIEWPOINTSSHOWTIME},
        `T_VIEWPOINTESHOWTIME` = #{map.T_VIEWPOINTESHOWTIME}, `T_VIEWPOINTOPTIONS` = #{map.T_VIEWPOINTOPTIONS},
        `T_ABOUTCODE` = #{map.T_ABOUTCODE}, `T_CODE` = #{map.T_CODE}, `T_HOTUID` = #{map.T_HOTUID},`UPDATETIME`=NOW(),DEL=#{map.DEL},`SYSTEM`=#{map.System}
         WHERE `HTID` = #{map.HTID}
    </update>

</mapper>