<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.QuestionExtraMapper">
    <update id="setCalculated">
        UPDATE tb_question_extra set IsCalculate=1 ,CalculateTime=NOW() Where QID in
        <foreach collection="qids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <insert id="insertQuestionExtra">
        INSERT INTO tb_question_extra
               (QID,HasBestAnswer,HasAdoptAnswer,IsEnd,IsCalculate,AIDS,CreatTime,CalculateTime,UpdateTime)
        VALUES
               (#{qid},#{hasBestAnswer},#{hasAdoptAnswer},1,0,#{aids},NOW(),NOW(),NOW() )
        ON DUPLICATE KEY
        Update HasBestAnswer=#{hasBestAnswer},HasAdoptAnswer=#{hasAdoptAnswer},AIDS=#{aids}

    </insert>

</mapper>