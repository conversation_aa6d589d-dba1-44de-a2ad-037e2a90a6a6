<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.vertica.UserTbVipAllBasicSynMapper">
    <select id="getVipUsers" resultType="map">
      select b.C_PASSPORTID, a.C_CUSTOMERNO, a.C_VIPFINALLEVEL
      from EMUSER.USER_TB_CUSTOMER_ALL_BASIC_SYN a
      inner join EMUSER.USER_TB_PASSPORT_ALL_BASIC_SYN b
      on a.C_CUSTOMERNO = b.C_CUSTOMERNO
      where a.c_isenabled = 1 and a.C_VIPFINALLEVEL > 0
      <if test="customerNo != null">
          and a.C_CUSTOMERNO > #{customerNo}
      </if>
      order by a.C_CUSTOMERNO asc
      limit #{limit}
    </select>

</mapper>