<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.community.GroupTagPostRelationMapper">

    <select id="getPostVisibilityById" resultType="integer">
        select a.visibility
        from tb_group_scene_knowledge_planet a,
        tb_group_post_relation b
        where a.groupId = b.groupId
        and b.postId = #{postId}
    </select>

    <insert id="insertManyWhenPost"
            parameterType="ttfund.web.communityservice.bean.jijinBar.data.GroupTagPostRelationDto">
        insert into tb_group_tag_post_relation(tagId, postId)
        values
        <foreach collection="modelList" item="item" separator=",">
            (#{item.tagId},#{item.postId})
        </foreach>
    </insert>

    <select id="getLatestPostByTagIds" resultType="ttfund.web.communityservice.bean.jijinBar.data.GroupTagPostRelationDto">
        SELECT *
        FROM (
        select a.tagId,b.*, ROW_NUMBER()  OVER (PARTITION BY a.tagId ORDER BY b.postTimeStamp desc) AS row_num
        from tb_group_tag_post_relation a
        inner join tb_group_post_relation b
        on a.postId = b.postId
        where a.tagId in <foreach collection="tagIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        and a.isDel =0
        and b.postTimeStamp >#{start} and b.postTimeStamp &lt;#{end}
        and b.state=0 and b.postState=0 and b.isDel=0
        ) AS temp
        WHERE row_num = 1;
    </select>

</mapper>