<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.BarPostCountMapper">

    <insert id="upsertPostCount" parameterType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoByDailyCount">
        INSERT INTO `tb_barpost_count`(`Id`, `Code`, `PostCount`, `HisPostCount`, `UpdateTime`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.Id}, #{item.Code}, #{item.PostCount}, #{item.HisPostCount}, #{item.UpdateTime})
        </foreach>
        ON DUPLICATE KEY
        Update PostCount=values(PostCount),UpdateTime=values(UpdateTime)
    </insert>

    <insert id="upsert" parameterType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoByDailyCount">
        INSERT INTO `tb_barpost_count`(`Id`, `Code`, `PostCount`, `HisPostCount`, `UpdateTime`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.Id}, #{item.Code}, #{item.PostCount}, #{item.HisPostCount}, #{item.UpdateTime})
        </foreach>
        ON DUPLICATE KEY
        Update PostCount=values(PostCount),HisPostCount=values(HisPostCount),UpdateTime=values(UpdateTime)
    </insert>

    <update id="mergeByUpdateTimeInterval">
        update tb_barpost_count
        set HisPostCount=(HisPostCount+PostCount),PostCount=0,UpdateTime= #{updateTime}
        where updateTime > #{start} and updateTime &lt;= #{end} and PostCount > 0
        limit #{limit}
    </update>

</mapper>