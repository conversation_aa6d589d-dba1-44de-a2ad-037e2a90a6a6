<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.ProfitMapper">
    <select id="getProfitsIncrementFromMysql" resultType="ttfund.web.communityservice.bean.jijinBar.post.ProfitEntity">
        SELECT * FROM tb_profit where LENGTH(FCODE)=6 and FCode = #{code}
        <if test="uid != null and uid != ''">
            and PID = #{uid}
        </if>
        <if test="updateTime != null">
            and UPDATETIME > #{updateTime}
        </if>
        limit #{batchReadCount}
    </select>

    <insert id="upsertMany" parameterType="ttfund.web.communityservice.bean.jijinBar.user.FundUserProfitkafkaModel">
        insert INTO tb_profit
        (`PID`,`FCODE`,`UPDATETIME`,`HOLDMONTH`,`PROFITSTATE`,`CREATETIME`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.PID},
            #{item.FCODE},
            NOW(),
            #{item.HoldMoth},
            #{item.ProfitState},
            NOW()
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        UPDATETIME = now(),HOLDMONTH=values(HOLDMONTH),PROFITSTATE=values(PROFITSTATE)

    </insert>

    <insert id="updateMany" parameterType="ttfund.web.communityservice.bean.jijinBar.user.FundUserProfitkafkaModel">
        <foreach collection="list" item="item" separator=";">
            update tb_profit
            set UPDATETIME=now(),HOLDMONTH=#{item.HoldMoth},PROFITSTATE=#{item.ProfitState}
            where PID=#{item.PID} and FCODE=#{item.FCODE}
        </foreach>
    </insert>

</mapper>