<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.UserRelationMapper">
    <select id="getUserFansCountByUserIds" resultType="java.util.Map">
        select OBJID,  Count(1) as COUNT from tb_userrelation
        where OBJID in
        <foreach collection="userIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and REFTYPE=1 and ISENABLED=1
        group by OBJID
    </select>

    <select id="getUserFollowRelation" resultType="map">
        select USERID,OBJID from tb_userrelation where USERID in
        (
        <foreach collection="userFollows" item="item" separator=",">
            #{item}
        </foreach>
        )
        and  OBJID=#{userId} and ISENABLED=1  and REFTYPE=1
    </select>

    <select id="getUserByFansCount" resultType="java.util.Map">
        SELECT OBJID ,count(1) COUNT
        FROM tb_userrelation
        where
        <if test=" objId != null">
            OBJID > #{objId} and
        </if>
        REFTYPE = 1 and ISENABLED = 1
        group by OBJID
        having count(*) >= #{fansCount}
        order by OBJID
        limit #{batchReadCount}
    </select>

    <select id="getUserByFansCountAndObjIds" resultType="java.util.Map">
        SELECT OBJID ,count(*) COUNT
        FROM tb_userrelation
        where OBJID in
        <foreach collection="objIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>

        and REFTYPE = 1 and ISENABLED = 1
        group by OBJID
        having count(*) >= #{fansCount}
        order by OBJID
    </select>

    <select id="getTotalFansCountByUids" resultType="map">
        select OBJID, count(0) TotalFansNum
        from TB_USERRELATION
        where OBJID  in
        <foreach collection="uids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>

        and reftype=1 and ISENABLED=1

        group by OBJID
    </select>

    <select id="getPeriodBeFollowedCountByUids" resultType="map">
        select OBJID, count(0) FansAddNum
        from TB_USERRELATION
        where OBJID  in
        <foreach collection="uids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and reftype=1  and ISENABLED=1
        and  UPDATETIME>=#{start} and UPDATETIME &lt; #{end}

        group by OBJID
    </select>

    <select id="getPeriodBeUnFollowedCountByUids" resultType="map">
        select OBJID, count(0) FansOffNum
        from TB_USERRELATION
        where OBJID  in
        <foreach collection="uids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and reftype=2  and ISENABLED=0
        and  UPDATETIME>=#{start} and UPDATETIME &lt; #{end}

        group by OBJID
    </select>

    <select id="getTotalFollowCountByUids" resultType="map">
        select USERID, count(0) TotalFollowNum
        from TB_USERRELATION
        where USERID  in
        <foreach collection="uids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>

        and reftype=1 and ISENABLED=1

        group by USERID
    </select>

    <select id="getPeriodFollowCountByUids" resultType="map">
        select USERID, count(0) FollowAddNum
        from TB_USERRELATION
        where USERID  in
        <foreach collection="uids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and reftype=1  and ISENABLED=1
        and  UPDATETIME>=#{start} and UPDATETIME &lt; #{end}

        group by USERID
    </select>

    <select id="getPeriodUnFollowCountByUids" resultType="map">
        select USERID, count(0) FollowOffNum
        from TB_USERRELATION
        where USERID  in
        <foreach collection="uids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and reftype=2  and ISENABLED=0
        and  UPDATETIME>=#{start} and UPDATETIME &lt; #{end}

        group by USERID
    </select>

    <insert id="insertOrResume" parameterType="ttfund.web.communityservice.bean.messagepush.UserRelationKafkaModel">
    Insert into TB_USERRELATION (BIZTYPE,REFSRC,USERID,USEROTHERID,USERNAME,REFTYPE,OBJTYPE,OBJID,OBJOTHERID,REFTIME,
                                 ISENABLED,CREATETIME,UPDATETIME)
    values (#{relation.BizType},
            #{relation.RefSrc},
            #{relation.UserID},
            #{relation.UserOtherID},
            #{relation.UserName},
            #{relation.RefType},
            #{relation.ObjType},
            #{relation.ObjID},
            #{relation.ObjOtherID},
            #{relation.RefTime},
            1,
            NOW(),
            NOW())
    ON DUPLICATE KEY
    Update BIZTYPE=#{relation.BizType},
           REFSRC=#{relation.RefSrc},
           USEROTHERID=#{relation.UserOtherID},
           USERNAME=#{relation.UserName},
           REFTYPE=#{relation.RefType},
           OBJTYPE=#{relation.ObjType},
           OBJOTHERID=#{relation.ObjOtherID},
           REFTIME=#{relation.RefTime},
           ISENABLED=1,
           UPDATETIME=NOW()
    </insert>

    <update id="update">
    Update TB_USERRELATION
    set BIZTYPE=#{info.BizType},
        REFSRC=#{info.RefSrc},
        USEROTHERID=#{info.UserOtherID},
        USERNAME=#{info.UserName},
        REFTYPE=#{info.RefType},
        OBJTYPE=#{info.ObjType},
        OBJOTHERID=#{info.ObjOtherID},
        REFTIME=#{info.RefTime},
        ISENABLED=#{info.IsEnabled},
        UPDATETIME=NOW()
    where USERID=#{info.UserID} and OBJID=#{info.ObjID}
    </update>

</mapper>