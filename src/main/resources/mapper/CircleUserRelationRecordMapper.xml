<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.community.CircleUserRelationRecordMapper">

    <select id="getListByGte" resultType="ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelationRecord">
        select *
        from tb_circle_userrelation_record
        where updateTime >=#{updateTime}
        <if test="type != null">
            and type = #{type}
        </if>
        <if test="state != null">
            and state = #{state}
        </if>
        <if test="dealResult != null">
            and dealResult = #{dealResult}
        </if>
        and isDel = 0
        order by updateTime asc limit #{batchReadCount}
    </select>

    <select id="getListByGt" resultType="ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelationRecord">
        select *
        from tb_circle_userrelation_record
        where updateTime >#{updateTime}
        <if test="type != null">
            and type = #{type}
        </if>
        <if test="state != null">
            and state = #{state}
        </if>
        <if test="dealResult != null">
            and dealResult = #{dealResult}
        </if>
        and isDel = 0
        order by updateTime asc limit #{batchReadCount}
    </select>

    <select id="getLatest" resultType="ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelationRecord">
        select *
        from tb_circle_userrelation_record
        where circleId = #{circleId} and uid = #{uid}
        order by proposeTime desc
        limit 1
    </select>

    <insert id="insertOne" parameterType="ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelationRecord">
        INSERT INTO tb_circle_userrelation_record
        (id, circleId, uid, state, `type`, proposer, proposeTime, dealTime, dealResult, dealInfo, isDel, createTime, updateTime)
        VALUES(
        #{info.id},
        #{info.circleId},
        #{info.uid},
        #{info.state},
        #{info.type},
        #{info.proposer},
        #{info.proposeTime},
        #{info.dealTime},
        #{info.dealResult},
        #{info.dealInfo},
        #{info.isDel},
        #{info.createTime},
        #{info.updateTime}
        );
    </insert>

    <update id="updateOneWhenDeal"
            parameterType="ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelationRecord">
        update tb_circle_userrelation_record
        set
        state=#{info.state},
        dealTime=#{info.dealTime},
        dealResult=#{info.dealResult},
        dealInfo=#{info.dealInfo},
        updateTime=#{info.updateTime}
        where id = #{info.id}
    </update>

</mapper>