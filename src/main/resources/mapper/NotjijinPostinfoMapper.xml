<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.NotjijinPostinfoMapper">

    <update id="updateNotJijin" parameterType="ttfund.web.communityservice.bean.jijinBar.post.guba.PostInfoKafka">
     Update tb_notjijinpostinfo
     set NEWSID =#{info.NewsID},CODELIST =#{info.CodeList},PIC =#{info.Pic},DISPLAYTIME =#{info.DisplayTime},PDF =#{info.Pdf},
         HUIFUIP =#{info.HuiFuIP},HUIFUNICHENG =#{info.HuiFuNiCheng},HUIFUUID =#{info.HuiFuUID},IP =#{info.IP},TITLE =#{info.Title},
         HUIFUTIME =#{info.HuiFuTime},TIME =#{info.Time},UID=#{info.UID},NICHENG =#{info.Nicheng},CODE =#{info.Code},
         PROJECT =#{info.Project},STATE =#{info.State},PINGLUNNUM =#{info.PingLunNum},ZHUANFANUM =#{info.ZhuanFaNum},YUANID =#{info.YuanID},
         DEL =#{info.Del},PINGLUNQIANXIAN =#{info.PingLunqianxian},TYPE =#{info.Type},POSTFROM =#{info.Postfrom},
         ZHIDING =#{info.ZhiDing},ZBZHIDING =#{info.ZbZhiDing},IMG =#{info.Img},SERVER =#{info.Server},COLOR =#{info.Color},
         NUMXISHU =#{info.NumXiShu},HUIFUTABLE =#{info.HuiFuTable},PUSHTIME =#{info.PushTime},CONTENT =#{info.Content},
         ISENABLED =#{info.IsEnabled},UPDATETIME =NOW(),REPOSTSTATE=#{info.REPOSTSTATE},EXTEND=#{info.Extend}
     WHERE ID=#{info.ID}
    </update>

    <insert id="insertOrUpdateNotJijin" parameterType="ttfund.web.communityservice.bean.jijinBar.post.guba.PostInfoKafka">
     Insert into TB_NOTJIJINPOSTINFO
        (
          ID,NEWSID,CODELIST,PIC,DISPLAYTIME,PDF,HUIFUIP,HUIFUNICHENG,HUIFUUID,IP,TITLE,HUIFUTIME,TIME,UID,NICHENG,CODE,
          PROJECT,STATE,PINGLUNNUM,ZHUANFANUM,YUANID,DEL,PINGLUNQIANXIAN,TYPE,POSTFROM,ZHIDING,ZBZHIDING,IMG,SERVER,
          COLOR,NUMXISHU,HUIFUTABLE,PUSHTIME,CONTENT,ISENABLED,CREATETIME,UPDATETIME,REPOSTSTATE,EXTEND
        )
     values
       (
          #{info.ID},#{info.NewsID},#{info.CodeList},#{info.Pic},#{info.DisplayTime},#{info.Pdf},#{info.HuiFuIP},
          #{info.HuiFuNiCheng},#{info.HuiFuUID},#{info.IP},#{info.Title},#{info.HuiFuTime},#{info.Time},#{info.UID},
          #{info.Nicheng},#{info.Code},#{info.Project},#{info.State},#{info.PingLunNum},#{info.ZhuanFaNum},
          #{info.YuanID},#{info.Del},#{info.PingLunqianxian},#{info.Type},#{info.Postfrom},#{info.ZhiDing},
          #{info.ZbZhiDing},#{info.Img},#{info.Server},#{info.Color},#{info.NumXiShu},#{info.HuiFuTable},#{info.PushTime},
          #{info.Content},1,NOW(),NOW(),#{info.REPOSTSTATE},#{info.Extend})
     ON DUPLICATE KEY
     Update
          NEWSID =#{info.NewsID},CODELIST =#{info.CodeList},PIC =#{info.Pic},DISPLAYTIME =#{info.DisplayTime},
          PDF =#{info.Pdf},HUIFUIP =#{info.HuiFuIP},HUIFUNICHENG =#{info.HuiFuNiCheng},HUIFUUID =#{info.HuiFuUID},
          IP =#{info.IP},TITLE =#{info.Title},HUIFUTIME =#{info.HuiFuTime},TIME =#{info.Time},UID=#{info.UID},
          NICHENG =#{info.Nicheng},CODE =#{info.Code},PROJECT =#{info.Project},STATE =#{info.State},
          PINGLUNNUM =#{info.PingLunNum},ZHUANFANUM =#{info.ZhuanFaNum},YUANID =#{info.YuanID},DEL =#{info.Del},
          PINGLUNQIANXIAN =#{info.PingLunqianxian},TYPE =#{info.Type},POSTFROM =#{info.Postfrom},ZHIDING =#{info.ZhiDing},
          ZBZHIDING =#{info.ZbZhiDing},IMG =#{info.Img},SERVER =#{info.Server},COLOR =#{info.Color},
          NUMXISHU =#{info.NumXiShu},HUIFUTABLE =#{info.HuiFuTable},PUSHTIME =#{info.PushTime},CONTENT =#{info.Content},
          UPDATETIME =NOW(),REPOSTSTATE=#{info.REPOSTSTATE},EXTEND=#{info.Extend}
    </insert>

</mapper>