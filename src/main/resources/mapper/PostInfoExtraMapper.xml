<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.PostInfoExtraMapper">

    <insert id="insertModel" parameterType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel">
        INSERT INTO  `tb_postinfo_extra`
        (`ID`,
         `UID`,
         `CODE`,
         `TIME`,
         `POSTUPDATETIME`,
         `CREATETIME`,
         `UPDATETIME`,
         `CONTENTCOUNT`,
         `DEL`,
         `TTJJDEL`,
         `YUANID`,
         `TIMEPOINT`)
        VALUES
            (
                #{model.ID},  #{model.UID},  #{model.CODE},  #{model.TIME}
            , #{model.UPDATETIME}, now(), now()
            ,#{contentCount},#{model.DEL},#{model.TTJJDEL}
            ,#{model.YUANID},#{model.TIMEPOINT}
            )
            ON DUPLICATE KEY
            UPDATE
            `UID` = #{model.UID},
            `CODE` = #{model.CODE},
            `TIME` = #{model.TIME},
            `POSTUPDATETIME` = #{model.UPDATETIME},
            `UPDATETIME` = now(),
            `CONTENTCOUNT` = #{contentCount},
            `DEL` = #{model.DEL},
            `TTJJDEL` = #{model.TTJJDEL},
            `YUANID` = #{model.YUANID},
            `TIMEPOINT`=#{model.TIMEPOINT}
    </insert>

    <update id="updateModel" parameterType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel">
        UPDATE  `tb_postinfo_extra`
        SET
            `UID` = #{model.UID},
            `CODE` = #{model.CODE},
            `TIME` = #{model.TIME},
            `POSTUPDATETIME` = #{model.UPDATETIME},
            `UPDATETIME` = now(),
            `CONTENTCOUNT` = #{contentCount},
            `DEL` = #{model.DEL},
            `TTJJDEL` = #{model.TTJJDEL},
            `YUANID` = #{model.YUANID},
            `TIMEPOINT`=#{model.TIMEPOINT}
        WHERE `ID` = #{model.ID}
    </update>

    <select id="getPostExtraNew" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT a.ID,a.UID,a.`CODE`,IFNULL(a.PINGLUNNUM,0) as PINGLUNNUM,ifnull(a.LIKECOUNT,0) as LIKECOUNT,ifnull(a.CLICKNUM,0) as CLICKNUM,
        a.POSTUPDATETIME,a.CREATETIME,a.UPDATETIME,a.CONTENTCOUNT,a.DEL,a.TTJJDEL,a.TIMEPOINT,a.YUANID,tq.QID,b.TYPE,b.TIME,
        IFNULL(c.type,0) as YUANTYPE,post.EXTEND
        FROM tb_postinfo_extra a
        INNER JOIN tb_postinfo_new b ON a.ID=b.ID and b.`TIME`>#{updateTime}
        LEFT join tb_question tq on tq.ArticleId=a.ID
        left join tb_postinfo_new c on a.yuanid=c.id
        left join tb_postinfo post on a.ID=post.ID
        where a.UPDATETIME>=#{updateTime} and b.TIME>#{updateTime} and (b.PROJECT=1 or b.`CODE` IN (
        <foreach collection="codes" separator="," item="code">
            #{code}
        </foreach>))
        order by a.UPDATETIME
        limit #{batchReadCount}
    </select>

    <select id="getQuestion" resultType="ttfund.web.communityservice.bean.messagepush.FundQuestionInfo">
        SELECT que.QID,que.ArticleID,que.UserId,que.CreatedTime,que.PayType,que.Amount,que.PayId,que.EndTime,
        que.AnswerType,que.StockBarCode,que.AppType,que.AuditStatusType,que.IsEnd,que.HasBestAnswer,
        que.HasAdoptAnswer,que.UpdateTime,que.NoContent,que.IsEnable,
        (select count(1) from tb_answer where QID=que.QID and AuditStatusType=1 and  IsEnable=1) AnswerCount
        FROM tb_question que
        LEFT JOIN tb_postinfo_new tp on tp.ID=que.ArticleID
        WHERE que.articleid in(
        <foreach collection='allIds' item='id' separator=','>
            #{id}
        </foreach>)
    </select>


    <select id="getAnswerExtra" resultType="ttfund.web.communityservice.bean.jijinBar.post.QA.AnswerExtensionModel">
        select ta.QID,ta.AID,ta.ArticleID,IFNULL(ta.StockBarCode, '') as StockBarCode,ta.CreatorID,ta.UPDATETIME as Modified,ta.Created,
        ta.AuditStatusType,ta.IsAdopted,ta.AdopedType,ta.Adoped,ta.AppType,ta.IsBestAnswer,tq.Amount,
        IFNULL(tp.LIKECOUNT, 0) AS LIKECOUNT,IFNULL(tp.PingLunNum, 0) CommentCount,tp.UpdateTime
        from tb_answer ta
        INNER JOIN tb_postinfo_extra tp on tp.ID=ta.ArticleID
        left join tb_question tq on tq.QID=ta.QID
        where ta.ArticleID in (
        <foreach collection='allIds' item='id' separator=','>
            #{id}
        </foreach>)
        and ta.AuditStatusType=1 and tp.DEL=0
    </select>

    <select id="getPostExtra" resultType="map">
        SELECT a.ID,a.UID,a.`CODE`,IFNULL(a.PINGLUNNUM,0) as PINGLUNNUM,ifnull(a.LIKECOUNT,0) as LIKECOUNT,ifnull(a.CLICKNUM,0) as CLICKNUM,
        a.TIME,a.POSTUPDATETIME,a.CREATETIME,a.UPDATETIME,IFNULL(a.CONTENTCOUNT,0) CONTENTCOUNT,a.DEL,a.TTJJDEL,a.TIMEPOINT,a.YUANID,b.TYPE,IFNULL(c.type,0)  YUANTYPE
        FROM tb_postinfo_extra a
        INNER JOIN tb_postinfo_new b ON a.ID=b.ID
        left join tb_postinfo_new c on a.yuanid=c.id
        where a.UPDATETIME>= #{updateTime}
              and (
                 b.PROJECT=1
                 or b.`CODE` IN
                 <foreach collection="codeTypes" open="(" close=")" separator="," item="item">
                     #{item}
                 </foreach>
              )
        order by a.UPDATETIME limit #{batchReadCount}
    </select>

    <select id="getPostExtraForFind" resultType="map">
        SELECT a.ID,a.UID,a.`CODE`,IFNULL(a.PINGLUNNUM,0) as PINGLUNNUM,ifnull(a.LIKECOUNT,0) as LIKECOUNT,ifnull(a.CLICKNUM,0) as CLICKNUM,
        a.TIME,a.POSTUPDATETIME,a.CREATETIME,a.UPDATETIME,IFNULL(a.CONTENTCOUNT,0) CONTENTLENGTH,a.DEL,a.TTJJDEL,a.TIMEPOINT,a.YUANID,b.TYPE,
        IFNULL(c.type,0)  YUANTYPE,b.RECOMMENDDEL
        FROM tb_postinfo_extra a
        INNER JOIN tb_postinfo_new b ON a.ID=b.ID
        left join tb_postinfo_new c on a.yuanid=c.id
        where a.UPDATETIME>= #{updateTime}
              and b.type not in (49,50)
              and (b.PROJECT=1
                     or b.`CODE` IN <foreach collection="codeTypes" open="(" close=")" separator="," item="item"> #{item} </foreach>
                  )
        order by a.UPDATETIME limit #{batchReadCount}
    </select>

    <insert id="updateLikecount" parameterType="ttfund.web.communityservice.bean.jijinBar.post.PostinfoExtraModel">
      update tb_postinfo_extra
      set LIKECOUNT =#{model.LIKECOUNT},UPDATETIME=now()
      where ID = #{model.ID}
    </insert>

    <update id="updateClicknum" parameterType="ttfund.web.communityservice.bean.jijinBar.post.PostinfoExtraModel">
      <foreach collection="models" item="item" separator=";">
          update tb_postinfo_extra set  CLICKNUM =#{item.CLICKNUM},UPDATETIME=now() where ID = #{item.ID}
      </foreach>
    </update>

    <select id="getPostExtraForQuality" resultType="ttfund.web.communityservice.bean.jijinBar.post.PostinfoExtraModel">
        SELECT
            ID,
            TIME,
            CONTENTCOUNT,
            DEL
        FROM tb_postinfo_extra
        where ID IN <foreach collection="ids" open="(" close=")" separator="," item="item"> #{item} </foreach>
    </select>

    <insert id="updatePinglunNum" parameterType="map">
       update tb_postinfo_extra
       set
       PINGLUNNUM =#{map.PINGLUNNUM},UPDATETIME=now()
       where ID = #{map.ID}
    </insert>

</mapper>