<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.ModelPortfolioMapper">
    <insert id="insertOrUpdate" parameterType="ttfund.web.communityservice.bean.jijinBar.data.ModelPortfolioModel">
     insert into tb_model_portfolio(code,name,uid,state,openState,createTime,updateTime)
     values (#{model.code}, #{model.name}, #{model.uid}, #{model.state}, #{model.openState}, #{model.createTime}, #{model.updateTime})
     on duplicate key update
     code=#{model.code},name=#{model.name},uid=#{model.uid},state=#{model.state},openState=#{model.openState},updateTime=#{model.updateTime}
    </insert>

    <select id="getManagerUids" resultType="String">
        select distinct uid from tb_model_portfolio
        <if test="uid!=null and uid!=''"> where uid > #{uid} </if>
        order by uid asc
        limit #{batchReadCount}
    </select>
</mapper>