<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.PostInfoNewMapper">
    <insert id="insertModel" parameterType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel">
            INSERT INTO  `tb_postinfo_new`(`ID`,`NEWSID`,`CODELIST`,`PIC`,`ALLPIC`,`PICRATIO`,`DISPLAYTIME`,`PDF`
            ,`HUIFUIP`,`HUIFUNICHENG`,`HUIFUUID`,`IP`,`TITLE`,`ISSHOW`,`HUIFUTIME`,`TIME`,`UID`,`NICHENG`,`CODE`
            ,`PROJECT`,`STATE`,`PINGLUNNUM`,`ZHU<PERSON><PERSON>NUM`,`YU<PERSON><PERSON>`,`DEL`,`TTJJDEL`,`PINGLUNQ<PERSON>N<PERSON>IAN`,`T<PERSON>E`,`POSTFROM`
            ,`ZHIDING`,`ZBZHIDING`,`IMG`,`SERVER`,`COLOR`,`NUMXISHU`,`HUIFUTABLE`,`PUSHTIME`,`CONTENT`,`KEYWORDLIST`
            ,`HASEXTEND`,`ISENABLED`,`CREATETIME`,`UPDATETIME`,`CONTENTEND`,`SUMMARY`,`TIMEPOINT`,`REPOSTSTATE`)
            VALUES(#{model.ID},#{model.NEWSID},#{model.CODELIST},#{model.PIC},#{model.ALLPIC},#{model.PICRATIO},#{model.DISPLAYTIME},#{model.PDF}
			,#{model.HUIFUIP},#{model.HUIFUNICHENG},#{model.HUIFUUID},#{model.IP},#{model.TITLE},#{model.ISSHOW},#{model.HUIFUTIME},#{model.TIME},#{model.UID},#{model.NICHENG},#{model.CODE}
			,#{model.PROJECT},#{model.STATE},#{model.PINGLUNNUM},#{model.ZHUANFANUM},#{model.YUANID},#{model.DEL},#{model.TTJJDEL},#{model.PINGLUNQIANXIAN},#{model.TYPE},#{model.POSTFROM}
			,#{model.ZHIDING},#{model.ZBZHIDING},#{model.IMG},#{model.SERVER},#{model.COLOR},#{model.NUMXISHU},#{model.HUIFUTABLE},#{model.PUSHTIME},#{model.CONTENT},#{model.KEYWORDLIST}
			,#{model.HASEXTEND},#{model.ISENABLED},#{model.CREATETIME},#{model.UPDATETIME},#{model.CONTENTEND},#{model.SUMMARY},#{model.TIMEPOINT},#{model.REPOSTSTATE})
    </insert>

    <update id="updateModel" parameterType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel">
        update tb_postinfo_new set NEWSID =#{model.NEWSID}, CODELIST=#{model.CODELIST},PIC=#{model.PIC},ALLPIC=#{model.ALLPIC}
        ,PICRATIO=#{model.PICRATIO},DISPLAYTIME=#{model.DISPLAYTIME},PDF=#{model.PDF},HUIFUIP=#{model.HUIFUIP}
		,HUIFUNICHENG=#{model.HUIFUNICHENG},HUIFUUID=#{model.HUIFUUID},IP=#{model.IP},TITLE=#{model.TITLE}
		,ISSHOW=#{model.ISSHOW},HUIFUTIME=#{model.HUIFUTIME},TIME=#{model.TIME},UID=#{model.UID},NICHENG=#{model.NICHENG}
		,CODE=#{model.CODE},PROJECT = #{model.PROJECT},STATE = #{model.STATE},PINGLUNNUM = #{model.PINGLUNNUM}
		,ZHUANFANUM = #{model.ZHUANFANUM},YUANID = #{model.YUANID},DEL = #{model.DEL},TTJJDEL=#{model.TTJJDEL}
		,PINGLUNQIANXIAN = #{model.PINGLUNQIANXIAN},TYPE = #{model.TYPE},POSTFROM = #{model.POSTFROM},ZHIDING = #{model.ZHIDING}
		,ZBZHIDING = #{model.ZBZHIDING},IMG = #{model.IMG},SERVER = #{model.SERVER},COLOR = #{model.COLOR},NUMXISHU = #{model.NUMXISHU}
		,HUIFUTABLE = #{model.HUIFUTABLE},PUSHTIME = #{model.PUSHTIME},CONTENT = #{model.CONTENT},KEYWORDLIST = #{model.KEYWORDLIST}
		,HASEXTEND = #{model.HASEXTEND},ISENABLED = #{model.ISENABLED},CREATETIME = #{model.CREATETIME}
		,UPDATETIME = #{model.UPDATETIME},CONTENTEND = #{model.CONTENTEND},SUMMARY=#{model.SUMMARY}
		,TIMEPOINT=#{model.TIMEPOINT},REPOSTSTATE=#{model.REPOSTSTATE}
		where ID =#{model.ID}
    </update>

    <select id="getUpdateSimpleListNew"
            resultType="ttfund.web.communityservice.bean.jijinBar.post.userpost.SimplePostModelNew">
        select a.Id,a.UId,a.Time,a.Code,a.YuanId,a.TimePoint,a.UpdateTime,a.Del,a.TTJJDEL,a.Type,IFNULL(b.type,0)
        YUANTYPE ,a.CODELIST, a.CONTENT
        from tb_postinfo_new a
        left join tb_postinfo_new b on a.yuanid=b.id
        where a.UID REGEXP '^[0-9]+$'
        AND (
        a.PROJECT=1 or a.`CODE` IN
        <foreach collection="codeTypes" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        )
        AND a.TIME>date_add(now(), INTERVAL #{keepDays} day )
        and a.UPDATETIME>=#{updateTime} Order by a.UPDATETIME ASC Limit #{pageSize}
    </select>

    <select id="getPotentialHighQualityPost" resultType="map">
         select a.ID, a.UID, a.TIME, c.CONTENTCOUNT, a.ALLPIC
         from tb_postinfo_new a
         left join
         tb_post_fund_relation b
         on a.ID = b.PostId
         left join
         tb_postinfo_extra  c
         on a.ID = c.ID
         where
         a.`TIME`  >= #{startTime} and a.`TIME` &lt;= #{endTime}
         and
         `Type` in <foreach collection="postType" open="(" close=")" item="item" separator=","> #{item} </foreach>
         and
         (
         (a.CODE not in <foreach collection="barConfig" open="(" close=")" item="item" separator=","> #{item} </foreach> )
         or
         (a.CODELIST like 'of%' or a.CODELIST like '%,of%')
         or
         (b.FType = 3)
         )
         and c.CONTENTCOUNT >= #{charCount}
         group by a.ID
         order by a.`TIME` asc
         limit #{batchReadCount}
    </select>

    <select id="getPostListByTime" resultType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel">
        SELECT a.*,IFNULL(b.type,0) YUANTYPE
        FROM tb_postinfo_new a
        left join tb_postinfo_new b on a.yuanid=b.id
        WHERE a.ISENABLED = 1 AND a.DEL=0 AND a.TTJJDEL=0 AND a.UID REGEXP '^[0-9]+$' AND a.NICHENG != '' AND
        a.PROJECT=1
        AND a.TIME > #{time}
        <if test='lastTime != null'>AND a.UPDATETIME >= #{lastTime}</if>
        <if test='fundManagers != null and fundManagers.size() > 0'>
            and a.UID in
            <foreach item="item" collection="fundManagers" open="(" separator="," close=")" index="index">#{item}
            </foreach>
        </if>
        ORDER BY a.TIME ASC LIMIT #{batchReadCount}

    </select>

    <select id="getPostListFromRelation"
            resultType="ttfund.web.communityservice.bean.jijinBar.post.fundManager.FundManagerPostModelTemp">
        SELECT
        b.ID,b.UID,b.CODE,b.TIME,b.TIMEPOINT,b.YUANID,b.TYPE,b.DEL,b.TTJJDEL,
        IFNULL(c.type,0) YUANTYPE,
        a.FType, a.FCode, a.UpDateTime
        from tb_post_fund_relation a
        left join tb_postinfo_new b on a.PostId = b.id
        left join tb_postinfo_new c on b.yuanid= c.id
        where a.UPDATETIME >= #{lastTime}
        and b.UID in
        <foreach item="item" collection="fundManagers" open="(" separator="," close=")" index="index">#{item}</foreach>
        ORDER BY a.UPDATETIME ASC LIMIT #{batchReadCount}
    </select>

    <select id="getPostInfoNewByIds" resultType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel">
        <if test="selectFields != null and selectFields != ''">select ${selectFields}</if>
        <if test="selectFields == null or selectFields == ''">select *</if>
        from tb_postinfo_new
        where  ID in <foreach item="item" collection="ids" open="(" separator="," close=")" index="index">#{item}</foreach>
    </select>

    <select id="getPostCountOfFundBar" resultType="java.util.Map">
        SELECT CODE, count(1) AS COUNT FROM tb_postinfo_new
        where  project = 1
        and `CODE`  NOT  IN
        <foreach item="item" collection="hotFundBarCodes" open="(" separator="," close=")" >
            #{item}
        </foreach>
        AND CODE NOT LIKE '43-%' AND CODE NOT LIKE '48-%'
        and DEL = 0 and ISENABLED = 1
        and  TIME >= #{startTime}
        and TIME &lt;= #{endTime}
        group by code
    </select>
    <select id="getTTJJDELPostInfoNewList" resultType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel">
        SELECT `ID`,`NEWSID`,`CODELIST`,`PIC`,`ALLPIC`,`PICRATIO`,`DISPLAYTIME`,`PDF`,`HUIFUIP`,`HUIFUNICHENG`,`HUIFUUID`,
        `IP`,`TITLE`,`ISSHOW`,`HUIFUTIME`,`TIME`,`NICHENG`,`CODE`,`PROJECT`,`STATE`,`PINGLUNNUM`,`ZHUANFANUM`,`YUANID`,
        `DEL`,`TTJJDEL`,`PINGLUNQIANXIAN`,`TYPE`,`POSTFROM`,`ZHIDING`,`ZBZHIDING`,`IMG`,`SERVER`,`COLOR`,`NUMXISHU`,
        `HUIFUTABLE`,`PUSHTIME`,`CONTENT`,`KEYWORDLIST`,`HASEXTEND`,`ISENABLED`,`CREATETIME`,`UPDATETIME`,`UID`,`SUMMARY`,
        `CONTENTEND`,`TIMEPOINT`
        FROM  `tb_postinfo_new` where (TTJJDEL=1 or DEL=1)
        <if test="isDesc==false">
            and UPDATETIME>= #{updateTime} order by UPDATETIME
        </if>
        <if test="isDesc==true">
            and TIME &lt;= #{updateTime} order by TIME DESC
        </if>
        limit #{batchReadCount}
    </select>

    <select id="getPostNewList" resultType="java.util.Map">
        SELECT t.ID,t.UID,t.YUANID,t.CODE,t.PINGLUNNUM,IFNULL(t1.LIKECOUNT,0) AS LIKECOUNT ,
        IFNULL(t1.CLICKNUM,0) AS CLICKNUM ,
        t.TIME, t.UPDATETIME, t.TIMEPOINT, T.TYPE, IFNULL(t2.type,0) YUANTYPE,
        IFNULL(CHARACTER_LENGTH(t.content),0) CONTENTLENGTH, t.RECOMMENDDEL
        FROM tb_postinfo_new t
        left join tb_postinfo_extra t1 on t.ID = t1.ID
        left join tb_postinfo_new t2 on t.yuanid=t2.id
        where t.DEL=0  and t.TTJJDEL=0 and t.ISENABLED=1 and t.type not in(49,50)
        and t.TIME > #{startTime} and t.TIME &lt; #{endTime}
    </select>

    <select id="getPopularityInfoTable" resultType="java.util.Map">
        SELECT
        t.ID,UID,
        ifnull(t1.LIKECOUNT, 0) AS LIKECOUNT,
        ifnull(pl.PINGLUNNUM, 0) AS PINGLUNNUM,
        ifnull(pl.UserPingLunNum,0) AS UserPingLunNum
        FROM tb_postinfo_new t
        LEFT JOIN tb_likecount t1 ON t.ID = t1.ID
        left join  tb_postpinglunnum pl on t.ID =pl.TopicID
        WHERE
        t.TIME >= #{start}
        AND t.ISENABLED = 1
        AND t.DEL = 0
        AND t.TTJJDEL = 0
        AND NICHENG != ''
        and  (
        t.PROJECT = 1
        OR t.`CODE` IN
         <foreach item="item" collection="codeTypes" open="(" separator="," close=")" >
            #{item}
         </foreach>

        )
        ORDER BY t.UpdateTime DESC
    </select>

    <select id="getPopularityInfo" resultType="map">
        SELECT UID, sum(PINGLUNNUM) as PINGLUNNUM,sum(t1.LIKECOUNT) as LIKECOUNT
        FROM tb_postinfo_new t
        left join tb_likecount t1 on t.ID=t1.ID
        where t.DEL=0 and t.TTJJDEL=0 and (t.PROJECT=1 or t.`CODE` IN
          <foreach collection="codeTypes" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
        ) and t.ISENABLED =1
        group by t.UID
    </select>

    <select id="getZHBPostList" resultType="ttfund.web.communityservice.bean.jijinBar.post.data.ZHBPostInfoModel">
        SELECT
            p.ID ID,
            p.TIME TIME,
            p.UID UID,
            p.`CODE` CODE,
            p.TYPE TYPE,
            p.DEL DEL,
            p.UPDATETIME UPDATETIME
        from tb_postinfo_new p
        where  p.UPDATETIME >= #{lastUpDataTime}
        and p.TIME >= #{time}
        and left(p.`CODE`,3) = '43-'
        and p.del = 0
        ORDER BY p.UPDATETIME ASC
        LIMIT #{batchCount};
    </select>

    <select id="getZHAdminReplys" resultType="ttfund.web.communityservice.bean.jijinBar.post.data.ZHBAdminReplyModel">
        select
            p.*
        from (
            select
                r.`UID`,
                r.`ID` REPLYID,
                p.`CODE`,
                P.`ID` POSTID,
                r.`UPDATETIME` UPDATETIME
            from tb_replyinfo_new r
            inner join tb_postinfo_new p
            on r.`TOPICID` = p.`ID`
            where p.DEL = 0
            and p.`ISENABLED` = 1
            and r.`CODE` = 'jjspzh'
            and r.`DEL` = 0
            and r.`ISENABLED` = 1
            and r.`TTJJDEL` = 0
            and r.`TIME` > #{time}
            and r.`UPDATETIME` > #{updatetime}
            and p.`UPDATETIME` > #{updatetime}
            order by r.`UPDATETIME` desc
            limit #{count}
        ) p
        inner join tb_fund_bar tfb
        on p.UID = tfb .AdminPassportId
        and p.CODE = tfb .BarCode
    </select>

    <select id="getZHAdminReplyCount" resultType="java.lang.Integer">
        select
            count(*) AdminReplyCount
        from tb_replyinfo_new r
        inner join tb_postinfo_new p
        on r.`TOPICID` = p.`ID`
        where p.DEL = 0
        and p.`ISENABLED` = 1
        and r.`CODE` = 'jjspzh'
        and r.`DEL` = 0
        and r.`ISENABLED` = 1
        and r.`TTJJDEL` = 0
        and r.`TIME` >= #{time}
        and p.`TIME` >= #{time}
        and r.`UID` = #{uid}
        and p.`CODE` = #{code}
    </select>


    <select id="getPostWithQuestionInfo" resultType="map">
        SELECT a.ID, a.CODE, a.TYPE, b.QID
        FROM tb_postinfo_new a
        left join tb_question b on a.ID=b.ArticleId
        where a.ID IN
        <foreach collection="postIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getPostThingCountByUids" resultType="map">
        select a.UID, sum(IFNULL( b.clicknum,0)) TotalClickNum,sum(IFNULL(b.likecount,0)) TotalLikeNum,
        sum(IFNULL(b.pinglunnum,0)) TotalReceiveCommentNum, sum(if(a.DEL=0, 1, 0)) TotalPostNum
        from tb_postinfo_new a
        left join tb_postinfo_extra b on b.id=a.id
        where a.uid in
          <foreach collection="uids" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
        group by a.uid
    </select>


    <select id="getPeriodPostCountByUids" resultType="map">
        select UID, count(0) PostNum
        from tb_postinfo_new
        where uid in
        <foreach collection="uids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and TIME>=#{start} and TIME &lt; #{end}
        and DEL = 0

        group by UID
    </select>

    <select id="getPostsByUidsAndTimeInterval" resultType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel">
        select ${selectFields}
        from tb_postinfo_new
        where UID in
        <foreach collection="uids" item="item" open="(" close=")" separator=",">#{item}</foreach>
        <if test="start != null">and TIME >= #{start}</if>
        <if test="end != null">and TIME &lt; #{end}</if>
        and DEL =0 and TTJJDEL =0
    </select>
</mapper>