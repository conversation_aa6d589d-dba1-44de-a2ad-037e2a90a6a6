<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.PostInfoModelMapper">
    <select id="getPostList" resultType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoForMysqlModel">
        <if test="isDesc == false">
            SELECT a.*,IFNULL(b.type,0) YUANTYPE, IFNULL(c.AllowLikesState,0) AllowLikesState , IFNULL(c.SystemCommentAuthority,0) SystemCommentAuthority, c.F1, c.F3
            FROM tb_postinfo a
            left join tb_postinfo b
            on a.yuanid=b.id
            left join tb_postinfo_extend c
            on a.ID = c.id
            where a.UPDATETIME>#{updateTime} and a.uid regexp '^[0-9]+$'
            order by a.UPDATETIME
            limit #{batchReadCount}
        </if>
    </select>

    <select id="getPostListByIds" resultType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoForMysqlModel">
        SELECT a.*,IFNULL(b.type,0) YUANTYPE, IFNULL(c.AllowLikesState,0) AllowLikesState , IFNULL(c.SystemCommentAuthority,0) SystemCommentAuthority, c.F1, c.F3
        FROM tb_postinfo a
        left join tb_postinfo b
        on a.yuanid=b.id
        left join tb_postinfo_extend c
        on a.ID = c.id
        where a.ID in
        <foreach collection="idList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getPostInfoNew" resultType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel">
        SELECT `ID`,`NEWSID`,`CODELIST`,`PIC`,`ALLPIC`,`PICRATIO`,`DISPLAYTIME`,`PDF`,`HUIFUIP`,`HUIFUNICHENG`,`HUIFUUID`,
               `IP`,`TITLE`,`ISSHOW`,`HUIFUTIME`,`TIME`,`NICHENG`,`CODE`,`PROJECT`,`STATE`,`PINGLUNNUM`,`ZHUANFANUM`,`YUANID`,`DEL`,
               `TTJJDEL`,`PINGLUNQIANXIAN`,`TYPE`,`POSTFROM`,`ZHIDING`,`ZBZHIDING`,`IMG`,`SERVER`,`COLOR`,`NUMXISHU`,`HUIFUTABLE`,
               `PUSHTIME`,`CONTENT`,`KEYWORDLIST`,`HASEXTEND`,`ISENABLED`,`CREATETIME`,`UPDATETIME`,`UID`,`SUMMARY`,`CONTENTEND`,`TIMEPOINT`
        FROM `tb_postinfo_new`
        where ISENABLED =1 and UPDATETIME>=#{updateTime}
        order by UPDATETIME
            limit #{limit}
    </select>

    <select id="getPostInfoNewWithTimePoint" resultType="ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel">
        SELECT `ID`,`NEWSID`,`CODELIST`,`PIC`,`ALLPIC`,`PICRATIO`,`DISPLAYTIME`,`PDF`,`HUIFUIP`,`HUIFUNICHENG`,`HUIFUUID`,
               `IP`,`TITLE`,`ISSHOW`,`HUIFUTIME`,`TIME`,`NICHENG`,`CODE`,`PROJECT`,`STATE`,`PINGLUNNUM`,`ZHUANFANUM`,`YUANID`,`DEL`,
               `TTJJDEL`,`PINGLUNQIANXIAN`,`TYPE`,`POSTFROM`,`ZHIDING`,`ZBZHIDING`,`IMG`,`SERVER`,`COLOR`,`NUMXISHU`,`HUIFUTABLE`,
               `PUSHTIME`,`CONTENT`,`KEYWORDLIST`,`HASEXTEND`,`ISENABLED`,`CREATETIME`,`UPDATETIME`,`UID`,`SUMMARY`,`CONTENTEND`,`TIMEPOINT`
        FROM `tb_postinfo_new`
        where ISENABLED =1 and UPDATETIME>=#{updateTime} and TIMEPOINT=#{timePoint}
        order by UPDATETIME
            limit #{limit}
    </select>


    <select id="getYuanDetail" resultType="ttfund.web.communityservice.bean.jijinBar.post.YuanDetailModel">
        SELECT ID,NEWSID,PICRATIO,NICHENG,UID,PIC,SUMMARY,YUANID,TYPE,TITLE,ISSHOW,KEYWORDLIST AS KEYWORDS,CODE
        FROM tb_postinfo_new
        where ID = #{ID}
    </select>

    <select id="getIdsByIds" resultType="integer">
        SELECT ID
        FROM tb_postinfo
        where ID in
        <foreach collection="idList" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getByIds" resultType="ttfund.web.communityservice.bean.jijinBar.post.guba.PostInfoKafka">
        SELECT *
        FROM tb_postinfo
        where ID in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateKafka" parameterType="ttfund.web.communityservice.bean.jijinBar.post.guba.PostInfoKafka">
        Update TB_POSTINFO
        set NEWSID =#{info.NewsID},CODELIST =#{info.CodeList},PIC =#{info.Pic},DISPLAYTIME =#{info.DisplayTime},PDF =#{info.Pdf},
            HUIFUIP =#{info.HuiFuIP},HUIFUNICHENG =#{info.HuiFuNiCheng},HUIFUUID =#{info.HuiFuUID},IP =#{info.IP},
            TITLE =#{info.Title},HUIFUTIME =#{info.HuiFuTime},TIME =#{info.Time},UID=#{info.UID},NICHENG =#{info.Nicheng},
            CODE =#{info.Code},PROJECT =#{info.Project},STATE =#{info.State},PINGLUNNUM =#{info.PingLunNum},
            ZHUANFANUM =#{info.ZhuanFaNum},YUANID =#{info.YuanID},DEL =#{info.Del},PINGLUNQIANXIAN =#{info.PingLunqianxian},
            TYPE =#{info.Type},POSTFROM =#{info.Postfrom},ZHIDING =#{info.ZhiDing},ZBZHIDING =#{info.ZbZhiDing},IMG =#{info.Img},
            SERVER =#{info.Server},COLOR =#{info.Color},NUMXISHU =#{info.NumXiShu},HUIFUTABLE =#{info.HuiFuTable},
            PUSHTIME =#{info.PushTime},CONTENT =#{info.Content},ISENABLED =#{info.IsEnabled},UPDATETIME =NOW(),
            REPOSTSTATE=#{info.REPOSTSTATE},EXTEND=#{info.Extend}
        WHERE ID=#{info.ID}
    </update>

    <insert id="insertOrUpdate" parameterType="ttfund.web.communityservice.bean.jijinBar.post.guba.PostInfoKafka">
        Insert into TB_POSTINFO
               (
               ID,NEWSID,CODELIST,PIC,DISPLAYTIME,PDF,HUIFUIP,HUIFUNICHENG,HUIFUUID,IP,TITLE,HUIFUTIME,TIME,UID,NICHENG,
               CODE,PROJECT,STATE,PINGLUNNUM,ZHUANFANUM,YUANID,DEL,PINGLUNQIANXIAN,TYPE,POSTFROM,ZHIDING,ZBZHIDING,IMG,
               SERVER,COLOR,NUMXISHU,HUIFUTABLE,PUSHTIME,CONTENT,ISENABLED,CREATETIME,UPDATETIME,REPOSTSTATE,EXTEND
               )
        values (
               #{post.ID},#{post.NewsID},#{post.CodeList},#{post.Pic},#{post.DisplayTime},#{post.Pdf},#{post.HuiFuIP},
               #{post.HuiFuNiCheng},#{post.HuiFuUID},#{post.IP},#{post.Title},#{post.HuiFuTime},#{post.Time},#{post.UID},
               #{post.Nicheng},#{post.Code},#{post.Project},#{post.State},#{post.PingLunNum},#{post.ZhuanFaNum},
               #{post.YuanID},#{post.Del},#{post.PingLunqianxian},#{post.Type},#{post.Postfrom},#{post.ZhiDing},
               #{post.ZbZhiDing},#{post.Img},#{post.Server},#{post.Color},#{post.NumXiShu},#{post.HuiFuTable},
               #{post.PushTime},#{post.Content},1,NOW(),NOW(),#{post.REPOSTSTATE},#{post.Extend}
               )
        ON DUPLICATE KEY
        Update
               NEWSID =#{post.NewsID},CODELIST =#{post.CodeList},PIC =#{post.Pic},DISPLAYTIME =#{post.DisplayTime},
               PDF =#{post.Pdf},HUIFUIP =#{post.HuiFuIP},HUIFUNICHENG =#{post.HuiFuNiCheng},HUIFUUID =#{post.HuiFuUID},
               IP =#{post.IP},TITLE =#{post.Title},HUIFUTIME =#{post.HuiFuTime},TIME =#{post.Time},UID=#{post.UID},
               NICHENG =#{post.Nicheng},CODE =#{post.Code},PROJECT =#{post.Project},STATE =#{post.State},
               PINGLUNNUM =#{post.PingLunNum},ZHUANFANUM =#{post.ZhuanFaNum},YUANID =#{post.YuanID},DEL =#{post.Del},
               PINGLUNQIANXIAN =#{post.PingLunqianxian},TYPE =#{post.Type},POSTFROM =#{post.Postfrom},
               ZHIDING =#{post.ZhiDing},ZBZHIDING =#{post.ZbZhiDing},IMG =#{post.Img},SERVER =#{post.Server},
               COLOR =#{post.Color},NUMXISHU =#{post.NumXiShu},HUIFUTABLE =#{post.HuiFuTable},PUSHTIME =#{post.PushTime},
               CONTENT =#{post.Content},UPDATETIME =NOW(),REPOSTSTATE=#{post.REPOSTSTATE},EXTEND=#{post.Extend}
    </insert>

</mapper>