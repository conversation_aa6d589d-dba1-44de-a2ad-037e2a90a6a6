<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.vertica.QuespostAnswerBasicDrctAlMapper">

    <update id="updateOne" parameterType="map">

        UPDATE CONTENT.QUESPOST_ANSWER_BASIC_DRCT_ALL
        SET
        STATUS = #{map.STATUS},

        <if test="map.ANSWERID != null">
            ANSWERID = #{map.ANSWERID},
        </if>

        <if test="map.MESSAGE != null">
            MESSAGE = #{map.MESSAGE},
        </if>

        UPDATETIME = #{map.UPDATETIME}
        WHERE QUESTIONID = #{map.QUESTIONID}

    </update>

</mapper>