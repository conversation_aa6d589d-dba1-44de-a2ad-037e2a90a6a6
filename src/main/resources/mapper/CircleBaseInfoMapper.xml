<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.community.CircleBaseInfoMapper">

    <select id="getByCircleId"
            resultType="ttfund.web.communityservice.bean.jijinBar.data.CircleBaseInfoAndExtendInfo">
        select a.*, b.curMembers, b.curPosts
        from tb_circle_baseinfo a
        left join tb_circle_extendinfo b
        on a.circleId = b.circleId
        where a.circleId = #{circleId}
    </select>

    <select id="getByCircleIds" resultType="ttfund.web.communityservice.bean.jijinBar.data.CircleBaseInfoAndExtendInfo">
        select a.*, b.curM<PERSON><PERSON>, b.curPosts
        from tb_circle_baseinfo a
        left join tb_circle_extendinfo b
        on a.circleId = b.circleId
        where a.circleId in
        (
        <foreach collection="circleIds" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>

</mapper>