<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.UserRelationCountMapper">

    <insert id="upsertMany" parameterType="map">
        insert into tb_userrelation_count(uid, followNum, fansNum, createTime, updateTime)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.uid},#{item.follow_count},#{item.fans_count}, NOW(), NOW())
        </foreach>
        on duplicate key update
        followNum = VALUES(followNum),fansNum = VALUES(fansNum), updateTime = NOW()
    </insert>

    <insert id="upsertOneByFollowNum">
        insert into tb_userrelation_count(uid, followNum, createTime, updateTime)
        values (#{uid},#{followNum}, NOW(), NOW())
        on duplicate key update
        followNum = #{followNum}, updateTime = NOW()
    </insert>

    <insert id="upsertOneByFansNum">
        insert into tb_userrelation_count(uid, fansNum, createTime, updateTime)
        values (#{uid},#{fansNum}, NOW(), NOW())
        on duplicate key update
        fansNum = #{fansNum}, updateTime = NOW()
    </insert>

    <select id="getByUids" resultType="map">
        select uid, ifnull(followNum,0) TotalFollowNum, ifnull(fansNum,0) TotalFansNum
        from tb_userrelation_count
        where uid in
        (
        <foreach collection="uids" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>
</mapper>