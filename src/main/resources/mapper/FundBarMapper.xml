<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.FundBarMapper">

    <select id="getByCodes" resultType="ttfund.web.communityservice.bean.jijinBar.post.guba.FundBarModel">
        select AdminPassportId AdminPassportId,BarCode CODE
        from tb_fund_bar tfb
        where BarCode in
        <foreach collection="codes" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and AdminPassportId !=''
    </select>

    <select id="getListByBarCodes" resultType="ttfund.web.communityservice.bean.jijinBar.post.guba.FundBarModel">
        select *
        from tb_fund_bar
        where BarCode in
        <foreach collection="barCodes" open="(" close=")" item="item" separator=",">#{item}</foreach>
    </select>

</mapper>
