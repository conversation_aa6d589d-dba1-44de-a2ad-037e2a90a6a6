<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.vertica.PostDigestBasicDrctAllMapper">

    <update id="updateOne" parameterType="map">

        UPDATE CONTENT.POST_DIGEST_BASIC_DRCT_ALL
        SET
        STATUS = #{map.STATUS},

        <if test="map.REPLYID != null">
            REPLYID = #{map.REPLYID},
        </if>

        <if test="map.MESSAGE != null">
            MESSAGE = #{map.MESSAGE},
        </if>

        UPDATETIME = #{map.UPDATETIME}
        WHERE POSTID = #{map.POSTID}

    </update>

</mapper>