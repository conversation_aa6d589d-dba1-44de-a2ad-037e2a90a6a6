<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.QuestionMapper">

    <select id="getList" resultType="ttfund.web.communityservice.bean.jijinBar.post.QA.QuestionModel">
        SELECT * FROM tb_question where ArticleId in
        <foreach collection="listArtid" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getFundQuestionList" resultType="ttfund.web.communityservice.bean.messagepush.FundQuestionInfo">
        SELECT que.QID, que.ArticleId, que.UserId, que.CreatedTime, que.PayType, que.Amount, que.PayId, que.EndTime
        , que.AnswerType, que.StockBarCode, que.AppType, que.AuditStatusType, que.IsEnd, que.HasBestAnswer
        , que.HasAdoptAnswer, que.UpdateTime, que.NoContent, que.IsEnable, tp.timepoint
        , (select count(1) from tb_answer where QID = que.QID and AuditStatusType = 1 and IsEnable = 1) AnswerCount
        FROM tb_question que
        LEFT JOIN tb_postinfo_new tp on tp.ID = que.ArticleId
        <where>
            <if test='type == 1'>que.IsEnable = 1 and tp.DEL= 0</if>
            <if test='updateTime != null'>AND que.UpdateTime >= #{updateTime} order by que.UpdateTime ASC limit
                #{batchReadCount}
            </if>
        </where>
    </select>

    <select id="getFundQuestionDelList" resultType="ttfund.web.communityservice.bean.messagepush.FundQuestionInfo">
        SELECT que.QID, que.ArticleId, que.UserId, que.CreatedTime, que.PayType, que.Amount,
        que.PayId,que.EndTime,que.AnswerType,que.StockBarCode,que.AppType,que.AuditStatusType,
        que.IsEnd,que.HasBestAnswer,que.HasAdoptAnswer,que.UpdateTime,que.NoContent,que.IsEnable,tp.timepoint
        FROM tb_question que
        LEFT JOIN tb_postinfo_new tp on tp.ID = que.ArticleId
        <where>
            (que.IsEnable = 0 or tp.DEL = 1)
            <if test="updateTime != null">
                AND que.UpdateTime >= #{updateTime} order by que.UpdateTime ASC limit #{batchReadCount}
            </if>
        </where>
    </select>

    <insert id="insertOrUpdate" parameterType="ttfund.web.communityservice.bean.jijinBar.post.QA.QuestionEntity">
          INSERT INTO tb_question
              (`QID`,`ArticleId`,`UserId`,`CreatedTime`,`PayType`,`Amount`,`PayId`,`EndTime`,`AnswerType`,`StockBarCode`,`AppType`,
               `AuditStatusType`,`IsEnd`,`HasBestAnswer`,`HasAdoptAnswer`,`UpdateTime`,`IsEnable`,`NoContent`)
          VALUES
              ( #{question.QID},#{question.ArticleId},#{question.UserId},#{question.CreatedTime},#{question.PayType},
              #{question.Amount},#{question.PayId},#{question.EndTime},#{question.AnswerType},#{question.StockBarCode},
              #{question.AppType},#{question.AuditStatusType},#{question.IsEnd},#{question.HasBestAnswer},
              #{question.HasAdoptAnswer},NOW(),1,#{question.NoContent} )
          ON DUPLICATE KEY
          Update
               QID=#{question.QID},ArticleId=#{question.ArticleId},UserId=#{question.UserId},CreatedTime=#{question.CreatedTime},
               PayType=#{question.PayType},Amount=#{question.Amount},PayId=#{question.PayId},EndTime=#{question.EndTime},
               AnswerType=#{question.AnswerType},StockBarCode=#{question.StockBarCode},
               AppType=#{question.AppType},AuditStatusType=#{question.AuditStatusType},IsEnd=#{question.IsEnd},
               HasBestAnswer=#{question.HasBestAnswer},HasAdoptAnswer=#{question.HasAdoptAnswer},UpdateTime=NOW(),
               IsEnable=1,NoContent=#{question.NoContent}
    </insert>

    <update id="setPushed" parameterType="string">
        UPDATE tb_question SET IsPush=1 WHERE QID in
        <foreach collection="listQids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="getListOfNotEnd" resultType="ttfund.web.communityservice.bean.messagepush.FundQuestionInfoSpecial">
        select post.ID PostId,post.TIME,que.QID,post.CONTENT,
        que.ArticleId,que.UserId,que.CreatedTime,que.PayType,que.Amount,que.PayId,
        que.EndTime,que.AnswerType,que.StockBarCode,
        post.`CODE` FCode,que.AppType,que.AuditStatusType,
        que.IsEnd,HasBestAnswer,que.HasAdoptAnswer,que.UpdateTime,
        post.IMG PostImg,post.PIC PostPic,post.TITLE PostTitle
        from tb_question que
        inner join  tb_postinfo_new post on que.ArticleId=post.ID
        where que.IsEnable = 1  and que.IsEnd=0 and que.AuditStatusType=1 and   que.UpdateTime>=#{updateTime}
        order by que.updateTime asc
        limit #{limit}
    </select>

</mapper>