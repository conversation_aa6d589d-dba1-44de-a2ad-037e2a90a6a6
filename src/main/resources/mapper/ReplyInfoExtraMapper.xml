<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.ReplyInfoExtraMapper">
    <insert id="insertLikeCount" parameterType="ttfund.web.communityservice.bean.jijinBar.post.ReplyLikeCountModel">
        INSERT INTO  `tb_replyinfo_extra`(
             `ID`,
             `LIKECOUNT`,
             `CREATETIME`,
             `UPDATETIME`)
             VALUES(
             #{model.Id},
             #{model.Likecount},
             now(),
             now())
    </insert>

    <update id="updateLikeCount" parameterType="ttfund.web.communityservice.bean.jijinBar.post.ReplyLikeCountModel">
        UPDATE  `tb_replyinfo_extra`
        SET
        `LIKECOUNT` = #{model.Likecount},
        `UPDATETIME` = now()
        WHERE `ID` = #{model.Id}
    </update>

    <insert id="insertHuiFuNum">
        INSERT INTO  `tb_replyinfo_extra`(
          `ID`,
          `HUIFUNUM`,
          `CREATETIME`,
          `UPDATETIME`)
          VALUES(
          #{replyid},
          #{huifuCount},
          now(),
          now())
    </insert>

    <update id="updateHuiFuNum">
        UPDATE  `tb_replyinfo_extra`
        SET
        `HUIFUNUM` = #{huifuCount},
        `UPDATETIME` = now()
        WHERE `ID` = #{replyid}
    </update>

</mapper>