<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.PostInfoExtendMysqlMapper">

    <insert id="insertOrUpdate" parameterType="ttfund.web.communityservice.bean.jijinBar.post.guba.PostInfoExtendKafka">
        Insert into TB_POSTINFO_EXTEND
          (ID,AllowLikesState,SystemCommentAuthority,F1,F3)
        values
          (#{info.Id},#{info.AllowLikesState},#{info.SystemCommentAuthority},#{info.F1},#{info.F3})
        ON DUPLICATE KEY
        Update
          AllowLikesState=#{info.AllowLikesState},SystemCommentAuthority=#{info.SystemCommentAuthority},F1=#{info.F1},F3=#{info.F3},UpDateTime=NOW()
    </insert>

    <insert id="insertOrUpdateF3" parameterType="ttfund.web.communityservice.bean.jijinBar.post.guba.PostInfoExtendKafka">
        Insert into TB_POSTINFO_EXTEND
          (ID,F3)
        values
          (#{info.Id},#{info.F3})
        ON DUPLICATE KEY
        Update
          F3=#{info.F3},UpDateTime=NOW()
    </insert>

</mapper>
