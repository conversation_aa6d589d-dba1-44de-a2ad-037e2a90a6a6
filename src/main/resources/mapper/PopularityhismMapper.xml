<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.PopularityhismMapper">
    <insert id="insertOrUpdate" parameterType="list">
        INSERT INTO  `tb_popularityhism`
                                (`UID`,
                                `LIKECOUNT`,
                                `PINGLUNNUM`,
                                `CREATETIME`,
                                `UPDATETIME`)
                                VALUES
                                <foreach collection="list" separator="," item="item">
                                    (#{item.UID},
                                    #{item.LIKECOUNT},
                                    #{item.PINGLUNNUM},
                                    #{item.CREATETIME},
                                    #{item.UPDATETIME})
                                </foreach>
        ON DUPLICATE KEY UPDATE LIKECOUNT =VALUES(LIKECOUNT),PINGLUNNUM =VALUES(PINGLUNNUM),  UPDATETIME =VALUES(UPDATETIME)
    </insert>
</mapper>