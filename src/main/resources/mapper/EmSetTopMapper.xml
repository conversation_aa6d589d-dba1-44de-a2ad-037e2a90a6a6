<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.EmSetTopMapper">
    <insert id="insertOrUpdate" parameterType="ttfund.web.communityservice.bean.jijinBar.data.EMSetPostItem">
        INSERT INTO tb_em_settop(
        post_id,
        post_from,
        source_publish_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.post_id},
            #{item.post_from},
            #{item.source_publish_time}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        post_from = VALUES(post_from),
        source_publish_time = VALUES(source_publish_time),
        UpdateTime = now()
    </insert>
</mapper>
