<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.FundManagerLivePostResultMapper">

    <select id="getByIdList" resultType="String">
        select id from tb_fund_manager_live_post_result where id in
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <insert id="insertMany">
        insert into tb_fund_manager_live_post_result(id, state, userid, managerid, passportid, codelist, postid, utime, remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}
            ,#{item.state}
            ,#{item.userid}
            ,#{item.managerid}
            ,#{item.passportid}
            ,#{item.codelist}
            ,#{item.postid}
            ,#{item.utime}
            ,#{item.remark})
        </foreach>
    </insert>

</mapper>