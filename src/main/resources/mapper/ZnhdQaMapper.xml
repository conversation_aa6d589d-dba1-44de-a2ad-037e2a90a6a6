<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.ZnhdQaMapper">
    <select id="getList" resultType="ttfund.web.communityservice.bean.jijinBar.post.QA.ZnhdQaModel">
        select * from tb_znhd_qa FORCE INDEX (index_state)
        where 1=1
        <if test="states != null and states.size() > 0">
            and State in
            <foreach collection="states" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        and ReplyPostState=#{replyPostState}
        order by AskTime asc
        limit #{count}
    </select>

    <update id="updateDel">
        update tb_znhd_qa set Del=#{del}, UpdateTime=now() where PostId in
        <foreach collection="postids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <insert id="insertMany" parameterType="ttfund.web.communityservice.bean.jijinBar.post.QA.ZnhdQaModel">
        INSERT INTO tb_znhd_qa
        (ID,QID,Question,Answer,TopicId,QPassportId,QNickName,Code,GubaCode,BarName,DistributionPassportId,UserType,Score,State,Del,AskTime,PostId,PostTitle,PostPic,PostImg,ReplyPostState,DataType)
        VALUES
        <foreach collection="list" item="item"  separator=",">
            (#{item.ID},#{item.QID},#{item.Question},#{item.Answer},#{item.TopicId},#{item.QPassportId},#{item.QNickName},
            #{item.Code},#{item.GubaCode},#{item.BarName},#{item.DistributionPassportId},#{item.UserType},#{item.Score},
            #{item.State},#{item.Del},#{item.AskTime},#{item.PostId},#{item.PostTitle},#{item.PostPic},#{item.PostImg},
            #{item.ReplyPostState},#{item.DataType})
        </foreach>
        ON DUPLICATE KEY
        Update  Question =values(Question),PostId=values(PostId),PostTitle=values(PostTitle),PostPic=values(PostPic),PostImg=values(PostImg)
    </insert>

</mapper>