<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.PostTopicMapper">
    <insert id="insertOrUpdate" parameterType="ttfund.web.communityservice.bean.jijinBar.post.PostHtRelationItem">
        INSERT INTO TB_POST_HT_RELATION (ID,HTID,VOTEID,POSTID,HTTYPE)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.htid},#{item.voteid},#{item.postid},#{item.HTType})
        </foreach>
        ON DUPLICATE KEY
        update HTID=values(HTID),VOTEID=values(VOTEID),POSTID=values(POSTID),HTTYPE=values(HTTYPE),UPDATETIME=now()
    </insert>

    <insert id="insertOrUpdate2" parameterType="ttfund.web.communityservice.bean.jijinBar.post.PostHtRelationModel">
        INSERT INTO TB_POST_HT_RELATION (ID,HTID,VOTEID,POSTID,Del)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.htid},#{item.voteid},#{item.postid},#{item.Del})
        </foreach>
        ON DUPLICATE KEY
        update HTID=values(HTID),VOTEID=values(VOTEID),POSTID=values(POSTID),Del=values(Del),UPDATETIME=now()
    </insert>
</mapper>