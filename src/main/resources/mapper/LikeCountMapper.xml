<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.LikeCountMapper">

    <select id="getAnswerLikeCounts" resultType="map">
      select b.UID, IFNULL(SUM(a.LIKECOUNT), 0) LIKECOUNT,MAX(a.UPDATETIME) UPDATETIME
      from tb_likecount a
      LEFT JOIN tb_postinfo_new b on a.ID = b.ID
      WHERE b.UID  IN (
       <foreach collection="uidList" separator="," item="item">
           #{item}
       </foreach>
      ) AND b.DEL = 0 AND b.TYPE = 50 group by b.Uid
    </select>

    <insert id="insertOrUpdate" parameterType="ttfund.web.communityservice.bean.jijinBar.post.guba.TbLikeCount">
        Insert into TB_LIKECOUNT (ID,TYPE,TIME,LIKECOUNT,ISENABLED,CREATETIME,UPDATETIME)
        values (#{info.ID},#{info.Type},#{info.Time},#{info.LikeCount},1,NOW(),NOW())
        ON DUPLICATE KEY
        Update TYPE=#{info.Type},TIME=#{info.Time},LIKECOUNT=#{info.LikeCount},UPDATETIME=NOW()
    </insert>

</mapper>