<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.favor.UserFavorMapper">
    <select id="getUserFavorFund" resultType="ttfund.web.communityservice.bean.favor.UserFavorBean">
        select
            uid passportId,
            fcode code
        from ${table}
        <where>
            <if test="userId != null and userId != ''">
                (uid > #{userId}
                or (uid = #{userId} and fcode > #{fundCode}))
            </if>
            <if test="updateTime != null and updateTime != ''">
                and eutime >= #{updateTime}
            </if>
        </where>
        order by uid, fcode
        limit #{size}
    </select>

    <select id="getUserFavorCombine" resultType="ttfund.web.communityservice.bean.favor.UserFavorBean">
        select
            uid passportId,
            SubCustomerNO code
        from f_fundsubcustomer
        <where>
            <if test="userId != null and userId != ''">
                (uid > #{userId}
                or (uid = #{userId} and SubCustomerNO > #{code}))
            </if>
            <if test="updateTime != null and updateTime != ''">
                and eutime >= #{updateTime}
            </if>
        </where>
        order by uid, SubCustomerNO
        limit #{size}
    </select>

    <select id="getUserFavorInvest" resultType="ttfund.web.communityservice.bean.favor.UserFavorBean">
        select
            uid passportId,
            fcode code
        from f_tg
        where status = 0
        <if test="userId != null and userId != ''">
            and (uid > #{userId}
            or (uid = #{userId} and fcode > #{code}))
        </if>
        <if test="updateTime != null and updateTime != ''">
            and eutime >= #{updateTime}
        </if>
        order by uid, fcode
        limit #{size}
    </select>
</mapper>