<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.AnswerMapper">
    <select id="getFundAnswerExtensionList" resultType="ttfund.web.communityservice.bean.jijinBar.post.QA.AnswerExtensionModel">
        select ta.QID, ta.AID, ta.ArticleID, IFNULL(ta.StockBarCode, '') AS StockBarCode,
        ta.CreatorID, ta.Created, ta.AuditStatusType, ta.IsAdopted, ta.AdopedType, ta.Adoped, ta.AppType,
        ta.IsBestAnswer, tq.Amount, IFNULL(tp.LIKECOUNT, 0) AS LIKECOUNT, IFNULL(tp.PINGLUNNUM, 0) CommentCount,
        CASE WHEN ta.UpdateTime > tp.UPDATETIME then ta.UpdateTime ELSE tp.UPDATETIME END UPDATETIME ,tp.timepoint
        from tb_answer ta
        INNER JOIN tb_postinfo_extra tp force index(idx3_postinfo_extra) on tp.ID = ta.ArticleID
        left join tb_question tq on tq.QID = ta.QID
        where ta.AuditStatusType = 1 and tp.DEL = 0
        <if test='updateTime != null'> AND tp.UPDATETIME >= #{updateTime}</if>
        union
        select ta.QID, ta.AID, ta.ArticleID, IFNULL(ta.StockBarCode, '') AS StockBarCode,
        ta.CreatorID, ta.Created, ta.AuditStatusType, ta.IsAdopted, ta.AdopedType, ta.Adoped, ta.AppType,
        ta.IsBestAnswer, tq.Amount, IFNULL(tp.LIKECOUNT, 0) AS LIKECOUNT, IFNULL(tp.PINGLUNNUM, 0) CommentCount,
        CASE WHEN ta.UpdateTime > tp.UPDATETIME then ta.UpdateTime ELSE tp.UPDATETIME END UPDATETIME ,tp.timepoint
        from tb_answer ta force index(tb_answer_UpdateTime_IDX)
        INNER JOIN tb_postinfo_extra tp on tp.ID = ta.ArticleID
        left join tb_question tq on tq.QID = ta.QID
        where ta.AuditStatusType = 1 and tp.DEL = 0
        <if test='updateTime != null'> AND ta.UPDATETIME >= #{updateTime}</if>
        order by UPDATETIME ASC limit #{pageIndex} , #{batchReadCount}
    </select>

    <select id="getFundAnswerList" resultType="ttfund.web.communityservice.bean.jijinBar.post.QA.FundAnswerInfoModel">
        SELECT ta.QID, ta.AID, ta.ArticleId, ta.StockBarCode, ta.CreatorID,
        ta.Created, ta.Modified, ta.AuditStatusType, ta.IsAdopted, ta.AdopedType,
        ta.Adoped, ta.AppType, ta.IsBestAnswer, ta.UpdateTime, ta.IsEnable,
        IFNULL(tq.UserId, '') AS UserId, tq.ArticleId QuestionArticleId
        FROM tb_answer ta
        LEFT JOIN tb_question tq ON ta.QID = tq.QID
        <where>
            <if test='updateTime != null'> ta.UpdateTime > #{updateTime} </if>
        </where>
        order by ta.UpdateTime ASC limit #{batchReadCount}
    </select>

    <select id="getQuestionAnswerCount" resultType="java.util.Map">
        SELECT QID, COUNT(1) AS AnswerCount FROM tb_answer where AuditStatusType = 1 and IsEnable = 1 and QID IN
        <foreach item="item" collection="qIdList" open="(" separator="," close=")" index="index">#{item}</foreach>
        GROUP BY QID
    </select>

    <select id="getAcceptCount" resultType="map">
        select QID,AcceptCount,UpdateTime
        from (
            select
                QID,
                count(*) over(partition by QID) as AcceptCount,
                UpdateTime,
                row_number() over(partition by QID order by UPDATETIME desc) as rn
            from  tb_answer
            where AuditStatusType = 1
              and IsAdopted = 1
              and IsEnable = 1
        ) t
        where t.rn = 1
          and UpdateTime >= #{updateTime}
        order by UpdateTime
            limit #{batchReadCount}
    </select>

    <insert id="insertOrUpdate" parameterType="ttfund.web.communityservice.bean.jijinBar.post.QA.AnswerEntity">
      INSERT INTO tb_answer
        (`QID`,`AID`,`ArticleID`,`CreatorID`,`Created`,`Modified`,`AuditStatusType`,`IsAdopted`,`AdopedType`,`Adoped`,
        `AppType`,`IsBestAnswer`,`UpdateTime`,`IsEnable`,`StockBarCode`)
      VALUES
        (#{answer.QID},#{answer.AID},#{answer.ArticleID},#{answer.CreatorID},#{answer.Created},#{answer.Modified},#{answer.AuditStatusType},
         #{answer.IsAdopted},#{answer.AdopedType},#{answer.Adoped},#{answer.AppType},#{answer.IsBestAnswer},NOW(),1,#{answer.StockBarCode})
      ON DUPLICATE KEY
      Update QID=#{answer.QID},AID=#{answer.AID},ArticleID=#{answer.ArticleID},CreatorID=#{answer.CreatorID},
         Created=#{answer.Created},Modified=#{answer.Modified},AuditStatusType=#{answer.AuditStatusType},IsAdopted=#{answer.IsAdopted},
         AdopedType=#{answer.AdopedType},Adoped=#{answer.Adoped},AppType=#{answer.AppType},IsBestAnswer=#{answer.IsBestAnswer},
         UpdateTime=NOW(),IsEnable=1,StockBarCode=#{answer.StockBarCode}
    </insert>

    <update id="setAdopedType">
        Update tb_answer set IsBestAnswer=1,IsAdopted=1,AdopedType=#{adopedType},UpdateTime=NOW() where AID in
        <foreach collection="aids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="getAnswerCount" resultType="ttfund.web.communityservice.bean.jijinBar.post.QA.QuestionAnswerAcceptCount">
        SELECT QID, SUM(IsAdopted) AcceptCount, COUNT(*) TotalCount
        from tb_answer
        WHERE
        AuditStatusType = 1 and  IsEnable=1 and QID in
        <foreach item="item" collection="listQids" open="(" separator="," close=")" index="index">#{item}</foreach>
        GROUP BY QID
    </select>

    <select id="getListOfAnswerWithQuestionByArticleIds" resultType="map">
        SELECT ${selectFields}
        from tb_answer a
        inner join tb_question b
        on a.QID = b.QID
        WHERE
        a.ArticleID in
        <foreach collection="articleIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <select id="getListByQids" resultType="map">
        SELECT ${selectFields}
        from tb_answer
        WHERE
        QID in
        <foreach collection="qids" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

</mapper>