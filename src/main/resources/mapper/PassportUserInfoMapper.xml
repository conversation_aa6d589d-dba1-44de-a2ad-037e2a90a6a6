<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.PassportUserInfoMapper">

    <select id="queryPassportUserByRegisterTime" resultType="ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew">
        SELECT
            `PassportID`,
            `OpenID`,
            `NickName`,
            `Gender`,
            `Vtype`,
            `VtypeStatus`,
            `Registertime`,
            `RegisterDateTime`,
            `CaifuhaoID`,
            `CreatTime`,
            `UpdateTime`
        FROM `tb_passport_user_info`
        WHERE Registertime > #{userInfo.Registertime}
        OR (Registertime = #{userInfo.Registertime} and PassportID > #{userInfo.PassportID})
        ORDER BY Registertime, PassportID
        LIMIT #{size}
    </select>
</mapper>