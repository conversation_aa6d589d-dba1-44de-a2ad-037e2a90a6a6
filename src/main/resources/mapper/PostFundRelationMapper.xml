<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.PostFundRelationMapper">

    <insert id="insertIgnore" parameterType="ttfund.web.communityservice.bean.jijinBar.post.PostFundRelation">
        insert ignore into tb_post_fund_relation(ID, PostId, FCode, FName, FType, UpDateTime)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.ID}, #{item.PostId}, #{item.FCode}, #{item.FName}, #{item.FType}, #{item.UpDateTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBulk" parameterType="ttfund.web.communityservice.bean.jijinBar.post.PostFundRelation">
    INSERT INTO tb_post_fund_relation (`ID`,`PostId`,`FCode`,`FName`,`FType`,`UpDateTime`)
                            VALUES
                            <foreach collection="list" separator="," item="item">
                                (
                                #{item.ID},
                                #{item.PostId},
                                #{item.FCode},
                                #{item.FName},
                                #{item.FType},
                                NOW())
                            </foreach>
                            ON DUPLICATE KEY
                            Update ID=values(ID),PostId=values(PostId),FCode=values(FCode),FName=values(FName),FType=values(FType),UpDateTime=NOW()
    </insert>

</mapper>