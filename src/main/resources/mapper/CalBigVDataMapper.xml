<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.CalBigVDataMapper">

    <insert id="saveMany" parameterType="ttfund.web.communityservice.bean.jijinBar.post.userpost.BigVCalDataModel">
        INSERT INTO tb_CalBigVData(
        UID,
        CDate,
        TotalClickNum,
        ClickNum,
        TotalLikeNum,
        LikeNum,
        TotalSendCommentNum,
        SendCommentNum,
        TotalReceiveCommentNum,
        ReceiveCommentNum,
        TotalPostNum,
        PostNum,
        TotalFansNum,
        FansAddNum,
        FansOffNum,
        CreateTime,
        UpdateTime,
        IsDel)
        VALUES

        <foreach collection="list" item="item" separator=",">
         (
         #{item.UID},
         #{item.CDate},
         #{item.TotalClickNum},
         #{item.ClickNum},
         #{item.TotalLikeNum},
         #{item.LikeNum},
         #{item.TotalSendCommentNum},
         #{item.SendCommentNum},
         #{item.TotalReceiveCommentNum},
         #{item.ReceiveCommentNum},
         #{item.TotalPostNum},
         #{item.PostNum},
         #{item.TotalFansNum},
         #{item.FansAddNum},
         #{item.FansOffNum},
         #{item.CreateTime},
         #{item.UpdateTime},
         #{item.IsDel}
         )
        </foreach>
        ON DUPLICATE KEY Update
        TotalClickNum=VALUES(TotalClickNum),
        ClickNum=VALUES(ClickNum),
        TotalLikeNum=VALUES(TotalLikeNum),
        LikeNum=VALUES(LikeNum),
        TotalSendCommentNum=VALUES(TotalSendCommentNum),
        SendCommentNum=VALUES(SendCommentNum),
        TotalReceiveCommentNum=VALUES(TotalReceiveCommentNum),
        ReceiveCommentNum=VALUES(ReceiveCommentNum),
        TotalPostNum=VALUES(TotalPostNum),
        PostNum=VALUES(PostNum),
        TotalFansNum=VALUES(TotalFansNum),
        FansAddNum=VALUES(FansAddNum),
        FansOffNum=VALUES(FansOffNum),
        UpdateTime=now(),
        IsDel=VALUES(IsDel)
    </insert>

</mapper>