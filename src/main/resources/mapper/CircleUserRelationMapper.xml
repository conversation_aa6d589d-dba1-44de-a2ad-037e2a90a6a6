<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.community.CircleUserRelationMapper">

    <insert id="insert" parameterType="ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelation">
        insert into tb_circle_userrelation(circleId, uid, role, state, joinType, joinTime, isDel, createTime, updateTime)
        values (#{info.circleId}, #{info.uid}, #{info.role}, #{info.state}, #{info.joinType}, #{info.joinTime}, #{info.isDel}, #{info.createTime}, #{info.updateTime})
    </insert>

    <insert id="upsertWhenJoinCircle" parameterType="ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelation">
        insert into tb_circle_userrelation(circleId, uid, role, state, joinType, joinTime, isDel, createTime, updateTime)
        values (#{info.circleId}, #{info.uid}, #{info.role}, #{info.state}, #{info.joinType}, #{info.joinTime}, #{info.isDel}, #{info.createTime}, #{info.updateTime})
        on duplicate key
        update role=#{info.role}, state=#{info.state}, joinType=#{info.joinType}, joinTime=#{info.joinTime}, isDel=#{info.isDel}, updateTime=#{info.updateTime}

    </insert>

    <update id="updateWhenLeaveCircle" parameterType="ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelation">
        update tb_circle_userrelation
        set state=#{info.state}, leaveType=#{info.leaveType}, leaveTime=#{info.leaveTime}, isDel=#{info.isDel}, updateTime=#{info.updateTime}
        where circleId=#{info.circleId} and uid=#{info.uid}
    </update>

    <select id="get" resultType="ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelation">
        select *
        from tb_circle_userrelation
        where circleId = #{circleId} and uid = #{uid}
    </select>

    <select id="getListIncrementally" resultType="ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelation">
        select *
        from tb_circle_userrelation
        where circleId = #{circleId}
        <if test="uid != null">
            and uid > #{uid}
        </if>
        order by uid asc
        limit #{batchReadCount}
    </select>

</mapper>