<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.community.GroupTagMapper">
    <select id="getListByGroupIdsAndSystemTypes"
            resultType="ttfund.web.communityservice.bean.jijinBar.data.GroupTagDto">
        select * from tb_group_tag
        where groupId in <foreach collection="groupIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        and systemType in <foreach collection="systemTypes" item="item" open="(" close=")" separator=",">#{item}</foreach>
        and isDel =0
    </select>

</mapper>