<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.ReplyRelationMapper">

    <insert id="insertListIgnore" parameterType="ttfund.web.communityservice.bean.jijinBar.post.guba.ReplyRelation">
        Insert IGNORE INTO tb_replyrelation (REPLYID,HUIFUID,TOPICID,ISENABLED,CREATETIME,UPDATETIME)
        values
        <foreach collection="replys" item="item" separator=",">
            (#{item.ReplyID},#{item.HuiFuID},#{item.TopicID},1,NOW(),NOW())
        </foreach>
    </insert>

</mapper>