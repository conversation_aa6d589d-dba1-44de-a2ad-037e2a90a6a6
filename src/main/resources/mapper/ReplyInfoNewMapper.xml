<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.ReplyInfoNewMapper">

    <select id="getUidsByPostidAndTime"
            resultType="ttfund.web.communityservice.bean.jijinBar.post.config.PostActivityParticipateModel">
        SELECT UID,TIME FROM tb_replyinfo
        where TOPICID = #{postId}
        <if test="breakpoint != null">
            and UPDATETIME >= #{breakpoint}
        </if>
        and HUIFUIDLIST is null
        and TIME >= #{start} and TIME <![CDATA[<=]]> #{end}
        and (DEL = 0 or DEL = 1)
    </select>

    <select id="getReplyList" resultType="ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoNewModel">
        SELECT `ID`, `TOPICID`, `LOUCENG`, `DEL`, ifnull(`CODE`, '') as CODE, ifnull(`UID`, '') as UID, ifnull(`NICHENG`, '') as NICHENG, `TIME`,
               ifnull(`TEXT`, '') as TEXT, ifnull(`TEXTEND`, '') as TEXTEND,ifnull(`KEYWORDLIST`, '') as KEYWORDLIST,
               ifnull(`IP`, '') as IP,ifnull(`HUIFUIDLIST`, '') as HUIFUIDLIST,
               `PUSHTIME`,`PIC`,`ISENABLED`,`CREATETIME`,`UPDATETIME`,`TTJJDEL`,`TIMEPOINT`
        FROM tb_replyinfo_new where UPDATETIME>#{updateTime} order by UPDATETIME ASC limit #{limit}
    </select>

    <insert id="insertModel" parameterType="ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoNew">
        INSERT INTO `tb_replyinfo_new` (
           `ID`,`TOPICID`,`LOUCENG`,`DEL`
           ,`CODE`,`UID`,`NICHENG`,`TIME`
           ,`TEXT`,`TEXTEND`,`KEYWORDLIST`
           ,`IP`,`HUIFUIDLIST`,`PUSHTIME`
           ,`ISENABLED`,`CREATETIME`
           ,`UPDATETIME`,`TIMEPOINT`,`PIC`)
        VALUES (
           #{model.ID},#{model.TOPICID},#{model.LOUCENG},#{model.DEL}
           ,#{model.CODE},#{model.UID},#{model.NICHENG},#{model.TIME}
           ,#{model.TEXT},#{model.TEXTEND},#{model.KEYWORDLIST}
           ,#{model.IP},#{model.HUIFUIDLIST},#{model.PUSHTIME}
           ,#{model.ISENABLED},#{model.CREATETIME}
           ,#{model.UPDATETIME},#{model.TIMEPOINT},#{model.PIC})
    </insert>

    <update id="updateModel" parameterType="ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoNew">
        UPDATE `tb_replyinfo_new`
        SET
               `TOPICID` = #{model.TOPICID},
               `LOUCENG` = #{model.LOUCENG},
               `DEL` = #{model.DEL},
               `CODE` = #{model.CODE},
               `UID` = #{model.UID},
               `NICHENG` = #{model.NICHENG},
               `TIME` = #{model.TIME},
               `TEXT` = #{model.TEXT},
               `TEXTEND` = #{model.TEXTEND},
               `KEYWORDLIST` = #{model.KEYWORDLIST},
               `IP` = #{model.IP},
               `HUIFUIDLIST` = #{model.HUIFUIDLIST},
               `PUSHTIME` = #{model.PUSHTIME},
               `PIC`=#{model.PIC},
               `ISENABLED` = #{model.ISENABLED},
               `CREATETIME` = #{model.CREATETIME},
               `UPDATETIME` = #{model.UPDATETIME}
        WHERE `ID` = #{model.ID}
    </update>

    <select id="getReplyTable" resultType="ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoNewModel">
        SELECT `ID`, `TOPICID`, `LOUCENG`, `DEL`, ifnull(`CODE`, '') as CODE, ifnull(`UID`, '') as UID, ifnull(`NICHENG`, '') as NICHENG, `TIME`,
               ifnull(`TEXT`, '') as TEXT, ifnull(`TEXTEND`, '') as TEXTEND,ifnull(`KEYWORDLIST`, '') as KEYWORDLIST,
               ifnull(`IP`, '') as IP,ifnull(`HUIFUIDLIST`, '') as HUIFUIDLIST,
               `PUSHTIME`,`PIC`,`ISENABLED`,`CREATETIME`,`UPDATETIME`,`TTJJDEL`,`TIMEPOINT`
        FROM tb_replyinfo_new where UPDATETIME>=#{updateTime} order by UPDATETIME ASC limit #{batchReadCount}
    </select>

    <select id="getExcellentComment" resultType="map">
        select a.ID, a.TOPICID, a.UID, b.CODE, a.TEXT, a.PIC, a.TIME, c.LIKECOUNT, c.HUIFUNUM, IFNULL(c.LIKECOUNT, 0) + IFNULL(c.HUIFUNUM, 0) AS INTERACTCOUNT
        from tb_replyinfo_new a
        inner join tb_postinfo_new b on a.TOPICID = b.ID
        inner join tb_replyinfo_extra c on a.ID = c.ID
        where a.TIME >= #{time}
        and a.HUIFUIDLIST is null
        and (IFNULL(c.LIKECOUNT, 0) + IFNULL(c.HUIFUNUM, 0)) >= #{interactCountLimit}
        and a.DEL = 0
        and b.DEL = 0
        order by a.TIME asc
        limit #{batchReadCount}
    </select>

    <select id="getTotalReplyCountByUids" resultType="map">
        select UID, count(0) TotalSendCommentNum
        from TB_REPLYINFO_new
        where uid in
        <foreach collection="uids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and DEL = 0

        group by UID
    </select>

    <select id="getPeriodReplyCountByUids" resultType="map">
        select UID, count(0) SendCommentNum
        from TB_REPLYINFO_new
        where uid in
        <foreach collection="uids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and TIME>=#{start} and TIME &lt; #{end}
        and DEL = 0
        group by UID
    </select>

    <select id="getByTime" resultType="map">
        select ${fields}
        from tb_replyinfo_new a
        left join tb_postinfo_new b
        on a.TOPICID = b.ID
        where a.TIME > #{start} and a.TIME &lt; #{end}
        order by a.TIME asc
        limit #{limit}
    </select>
</mapper>