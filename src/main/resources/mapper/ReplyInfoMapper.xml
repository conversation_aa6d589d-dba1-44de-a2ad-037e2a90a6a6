<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.ReplyInfoMapper">

    <select id="getReplyListByIds" resultType="ttfund.web.communityservice.bean.jijinBar.post.ReplyInfo">
        SELECT * FROM tb_replyinfo where id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getReplyCountOfFundBar"  resultType="java.util.Map">
        SELECT r.`CODE`,count(*) AS COUNT FROM tb_replyinfo r
        INNER JOIN tb_postinfo p on r.TOPICID = p.ID
        where p.DEL = 0 and p.PROJECT = 1
        and r.`CODE` regexp '^[0-9]+$' and LENGTH(r.`CODE`)= 6 and r.CREATETIME >= #{startTime} and r.CREATETIME &lt;= #{endTime}
        group by r.`CODE`
    </select>

    <select id="getLatestReply"  resultType="ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoModel">
        SELECT TOPICID, MAX(TIME) AS TIME, ID
        from tb_replyinfo
        where TOPICID in <foreach item="item" collection="postIds" open="(" separator="," close=")" index="index">#{item}</foreach>
        GROUP BY TOPICID
        ORDER BY TIME desc;
    </select>

    <update id="updateKafka" parameterType="ttfund.web.communityservice.bean.jijinBar.post.guba.ReplyInfoKafka">
        Update TB_REPLYINFO
        set TOPICID=#{reply.TopicID},LOUCENG=#{reply.Louceng},DEL=#{reply.Del},CODE=#{reply.Code},UID=#{reply.UID},
            NICHENG=#{reply.Nicheng},TIME=#{reply.Time},TEXT=#{reply.Text},IP=#{reply.IP},HUIFUIDLIST=#{reply.HuiFuIDList},
            PUSHTIME=#{reply.PushTime},ISENABLED=#{reply.IsEnabled},UPDATETIME=NOW(),PIC=#{reply.Pic}
        Where ID=#{reply.ID}
    </update>

    <insert id="insertOrResume" parameterType="ttfund.web.communityservice.bean.jijinBar.post.guba.ReplyInfoKafka">
      Insert into TB_REPLYINFO (ID,TOPICID,LOUCENG,DEL,CODE,UID,NICHENG,TIME,TEXT,IP,HUIFUIDLIST,PUSHTIME,ISENABLED,CREATETIME,UPDATETIME,PIC)
      values (#{reply.ID},#{reply.TopicID},#{reply.Louceng},#{reply.Del},#{reply.Code},#{reply.UID},#{reply.Nicheng},
             #{reply.Time},#{reply.Text},#{reply.IP},#{reply.HuiFuIDList},#{reply.PushTime},1,NOW(),NOW(),#{reply.Pic})
      ON DUPLICATE KEY
      Update TOPICID=#{reply.TopicID},LOUCENG=#{reply.Louceng},DEL=#{reply.Del},CODE=#{reply.Code},UID=#{reply.UID},
             NICHENG=#{reply.Nicheng},TIME=#{reply.Time},TEXT=#{reply.Text},IP=#{reply.IP},HUIFUIDLIST=#{reply.HuiFuIDList},
             PUSHTIME=#{reply.PushTime},ISENABLED=1,UPDATETIME=NOW(),PIC=#{reply.Pic}
    </insert>

</mapper>