<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.communityservice.mapper.barread.PostPingLunNumMapper">
    <insert id="insertOrUpdate" parameterType="map">
           INSERT INTO tb_postpinglunnum
                  (`TopicID`,
                   `PingLunNum`,
                   `UpdateTime`,
                  `UserPingLunNum`)
           VALUES
                   (#{map.TopicID},
                   #{map.PingLunNum},
                   NOW(),
                   #{map.UserPingLunNum})
           ON DUPLICATE KEY
           UPDATE
                   `TopicID` = #{map.TopicID},
                   `PingLunNum` = #{map.PingLunNum},
                   `UpdateTime` = NOW(),
                   `UserPingLunNum` = #{map.UserPingLunNum}
    </insert>
</mapper>