server:
  compression:
    enabled: true
    min-response-size: 1024
    mime-types:
      - image/png
      - text/html
      - application/javascript
      - text/css
      - application/octet-stream
      - application/json
  port: 8182
  tomcat:
    accept-count: 1000
    min-spare-threads: 10
    max-threads: 1024
    max-connections: 65535
  servlet:
    context-path: /community-service
    session:
      timeout: 10s

logging:
  config: classpath:logback-spring.xml
  file:
    path: /vdb/apilog/fund-community-service
  logback:
    rollingpolicy:
      total-size-cap: 100GB
      max-history: 3
      max-file-size: 1GB
      clean-history-on-start: true

spring:
  profiles:
    active: '@spring_profile@'


