package ttfund.web.communityservice.dao.mongo;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.Date;
import java.util.List;

/**
 * post_activity_config表持久层操作实体
 */
@Repository
public class PostActivityConfigDao extends BarMongBaseDao {

    public MongoTemplate getTemplate() {
        return mongoTemplate;
    }

    /**
     * 获取全量帖子活动配置数据
     */
    public <T> List<T> getAll(Class<T> tClass) {
        Query query = new Query();
        query.cursorBatchSize(50000).limit(50000);
        return mongoTemplate.findAll(tClass, BarMongodbConfig.TABLE_POST_ACTIVITY_CONFIG);
    }

    /**
     * 删除全部
     */
    public void delAll() {
        Query query = new Query();
        mongoTemplate.remove(query, BarMongodbConfig.TABLE_POST_ACTIVITY_CONFIG);
    }

    /**
     * 获取配置
     */
    public <T> List<T> getList(Class<T> tClass, String isDel, Date date) {
        Query query = new Query();
        query.addCriteria(Criteria.where("isDel").is(isDel).and("updateTime").lte(date));
        query.cursorBatchSize(50000).limit(50000);
        return mongoTemplate.find(query, tClass, BarMongodbConfig.TABLE_POST_ACTIVITY_CONFIG);
    }


    /**
     * 根据主键删除
     */
    public void del(List<String> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            Query query = new Query();
            query.addCriteria(Criteria.where("_id").in(ids));
            mongoTemplate.remove(query, BarMongodbConfig.TABLE_POST_ACTIVITY_CONFIG);
        }
    }


}
