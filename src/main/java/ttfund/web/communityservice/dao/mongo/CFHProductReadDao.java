package ttfund.web.communityservice.dao.mongo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.caifuhao.CFHProductReadModel;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.enums.EnumFundType;

import java.util.Date;
import java.util.List;

/**
 * 财富号产品解读 持久层操作实体
 */
@Repository
public class CFHProductReadDao extends MongBaseDao {

    @Qualifier(BarMongodbConfig.cfh_mongo_conn_beanname)
    @Autowired()
    private MongoTemplate cfhTemplate;

    /**
     * 根据解读类型和产品类型获取指定数量的解读数据
     */
    public List<CFHProductReadModel> getList(Date updateTime, int count, List<Integer> readTypes, EnumFundType enumFundType) {
        List<CFHProductReadModel> result = null;
        if (updateTime != null && count > 0 && !CollectionUtils.isEmpty(readTypes) && enumFundType != null) {
            Query query = new Query();
            query.addCriteria(Criteria.where("UpdateTime").gte(updateTime).and("ProductType").is(enumFundType.getType())
                    .and("ReadType").in(readTypes));
            query.with(Sort.by(Sort.Order.asc("UpdateTime"))).cursorBatchSize(count).limit(count);
            result = cfhTemplate.find(query, CFHProductReadModel.class, BarMongodbConfig.TABLE_TB_CFHPRODUCTREAD);
        }
        return result;
    }

}
