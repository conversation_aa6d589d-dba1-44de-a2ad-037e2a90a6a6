package ttfund.web.communityservice.dao.mongo;


import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.UpdateResult;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.QA.FundAnswerInfoModel;

import java.util.*;

@Repository
public class FundAnswerInfoDao extends BarMongBaseDao {
    private String collectionName = "FundAnswerInfo";

    /**
     * 回去基金吧回答的相关信息
     *
     * @param isPushAdoped
     * @param isAdopted
     * @param auditStatusType
     * @param pageSize
     * @return
     */
    public List<FundAnswerInfoModel> getList(boolean isPushAdoped, int isAdopted, int auditStatusType, Date updateTime, int pageSize) {

        Query query = new Query();
        query.addCriteria(Criteria.where("IsPushAdoped").is(isPushAdoped)
                .and("IsAdopted").is(isAdopted)
                .and("AuditStatusType").is(auditStatusType)
                .and("UpdateTime").gte(updateTime))
        ;
        query.with(Sort.by(
                Sort.Order.asc("UpdateTime")//支持多个，多个用逗号隔开
        ));
        query.limit(pageSize);
        List<FundAnswerInfoModel> list = mongoTemplate.find(query, FundAnswerInfoModel.class, collectionName);

        return list;

    }

    /**
     * 更新推送采纳状态
     *
     * @param id
     * @param isPushAdoped
     * @return
     */
    public boolean updatePushAdoped(String id, boolean isPushAdoped) {

        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(id));

        Update update = new Update();
        update.set("IsPushAdoped", isPushAdoped);
        UpdateResult updateResult = mongoTemplate.updateFirst(query, update, FundAnswerInfoModel.class, collectionName);

        return this.GetUpdateResult(updateResult);
    }

    /**
     * 更新回答奖励发放信息
     *
     * @param id
     * @param sendPointResult
     * @param sendAmount
     * @return
     */
    public boolean update(String id, int sendPointResult, int sendAmount) {

        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(id));

        Update update = new Update();
        update.set("SendPointResult", sendPointResult);
        update.set("SendAmount", sendAmount);

        UpdateResult updateResult = mongoTemplate.updateFirst(query, update, FundAnswerInfoModel.class, collectionName);

        return this.GetUpdateResult(updateResult);
    }

    public List<Document> getListByids(List<String> ids, List<String> fields) {
        List<Document> result = null;
        if (!CollectionUtils.isEmpty(ids)) {
            Query query = new Query();
            query.addCriteria(Criteria.where("_id").in(ids))
                    .cursorBatchSize(ids.size());
            if (!CollectionUtils.isEmpty(fields)) {
                for (String field : fields) {
                    query.fields().include(field);
                }
            }
            result = mongoTemplate.find(query, Document.class, collectionName);
        }
        return result;
    }

    /**
     * 根据map更新，map里需带_id -批量
     */
    public boolean upsertManyBySetOnInsertWithSetFields(List<Map<String, Object>> mapList, List<String> setFields) {
        boolean result = false;
        List<Pair<Query, Update>> pairList = null;
        if (!CollectionUtils.isEmpty(mapList)) {
            pairList = new ArrayList<>();
            for (Map<String, Object> map : mapList) {
                if (!CollectionUtils.isEmpty(map) && map.containsKey("_id") && StringUtils.hasLength(String.valueOf(map.get("_id")))) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where("_id").is(String.valueOf(map.get("_id"))));
                    Update update = new Update();
                    Set<Map.Entry<String, Object>> entrySet = map.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        if (!"_id".equals(entry.getKey())) {
                            if (!CollectionUtils.isEmpty(setFields) && setFields.contains(entry.getKey())) {
                                update.set(entry.getKey(), entry.getValue());
                            } else {
                                update.setOnInsert(entry.getKey(), entry.getValue());
                            }
                        }
                    }
                    Pair<Query, Update> pair = Pair.of(query, update);
                    pairList.add(pair);
                }
            }
        }

        if (!CollectionUtils.isEmpty(pairList)) {
            BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collectionName);
            bulkOperations.upsert(pairList);
            BulkWriteResult writeResult = bulkOperations.execute();
            result = writeResult.wasAcknowledged();
        }

        return result;
    }
}
