package ttfund.web.communityservice.dao.mongo;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.ReviewScorePost;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.Date;
import java.util.List;

/**
 * ReviewScorePost 表持久层操作实体
 */
@Repository
public class ReviewScorePostDao extends BarMongBaseDao{

    public MongoTemplate getTemplate(){
        return mongoTemplate;
    }

    /**
     * 根据帖子id获取
     */
    public ReviewScorePost getById(String _id) {
        ReviewScorePost result = null;
        if (StringUtils.hasLength(_id)) {
            Query query = new Query();
            query.addCriteria(Criteria.where("_id").is(_id));
            result = mongoTemplate.findOne(query, ReviewScorePost.class, BarMongodbConfig.TABLE_ReviewScorePost);
        }
        return result;
    }

    /**
     * 更新DEL字段
     */
    public void updateDel(long postId, int del, Date eutime) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(postId));
        Update update = new Update();
        update.set("DEL", del);
        update.set("EUTIME", eutime);
        mongoTemplate.updateMulti(query, update, BarMongodbConfig.TABLE_ReviewScorePost);
    }

    /**
     * 更新DEL字段
     */
    public void updateDel(long postId, int del) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(postId));
        Update update = new Update();
        update.set("DEL", del);
        mongoTemplate.updateMulti(query, update, BarMongodbConfig.TABLE_ReviewScorePost);
    }

    /**
     * 根据帖子ID 获取列表
     */
    public List<ReviewScorePost> getList(List<String> postIds) {
        List<ReviewScorePost> result = null;
        if (!CollectionUtils.isEmpty(postIds)) {
            Query query = new Query();
            query.addCriteria(Criteria.where("_id").in(postIds));
            result = mongoTemplate.find(query, ReviewScorePost.class, BarMongodbConfig.TABLE_ReviewScorePost);
        }
        return result;
    }

    public List<ReviewScorePost> findPaygedList(Date date, int count) {
        List<ReviewScorePost> result = null;
        if (date != null) {
            Query query = new Query();
            query.addCriteria(Criteria.where("EUTIME").gte(date));
            query.with(Sort.by(Sort.Order.asc("EUTIME")));
            query.limit(count).cursorBatchSize(count);
            result = mongoTemplate.find(query, ReviewScorePost.class, BarMongodbConfig.TABLE_ReviewScorePost);
        }
        return result;
    }

    public List<ReviewScorePost> getListByCodeList(List<String> codeList) {
        List<ReviewScorePost> result = null;
        if (!CollectionUtils.isEmpty(codeList)) {
            Query query = new Query();
            query.addCriteria(Criteria.where("BARCODE").in(codeList).and("DEL").is(0));
            query.limit(100000).cursorBatchSize(100000);
            result = mongoTemplate.find(query, ReviewScorePost.class, BarMongodbConfig.TABLE_ReviewScorePost);
        }
        return result;
    }

}
