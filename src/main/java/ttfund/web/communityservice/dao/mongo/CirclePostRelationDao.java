package ttfund.web.communityservice.dao.mongo;

import com.mongodb.bulk.BulkWriteResult;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.utils.CommonUtils;

import java.util.*;

@Repository
public class CirclePostRelationDao extends BarMongBaseDao {

    /**
     * 根据map更新
     */
    public boolean updateMany(List<Map<String, Object>> mapList, String queryField) {
        boolean result = false;
        List<Pair<Query, Update>> pairList = null;
        if (!CollectionUtils.isEmpty(mapList)) {
            pairList = new ArrayList<>();
            for (Map<String, Object> map : mapList) {
                if (!CollectionUtils.isEmpty(map) && map.containsKey(queryField) && map.get(queryField) != null) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where(queryField).is(map.get(queryField)));
                    Update update = new Update();
                    Set<Map.Entry<String, Object>> entrySet = map.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        if (!queryField.equals(entry.getKey())) {
                            update.set(entry.getKey(), entry.getValue());
                        }
                    }
                    Pair<Query, Update> pair = Pair.of(query, update);
                    pairList.add(pair);
                }
            }
        }

        if (!CollectionUtils.isEmpty(pairList)) {
            BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, BarMongodbConfig.TABLE_CIRCLEPOSTRELATION);
            bulkOperations.updateMulti(pairList);
            BulkWriteResult writeResult = bulkOperations.execute();
            result = writeResult.wasAcknowledged();
        }

        return result;
    }

    public <T> List<T> getListByPostIds(List<String> fields, Class<T> tClass, List<String> postIds) {
        List<T> result = null;
        if (!CollectionUtils.isEmpty(postIds)) {
            Query query = new Query();
            query.addCriteria(Criteria.where("postId").in(postIds));
            query.cursorBatchSize(100000);

            if (!CollectionUtils.isEmpty(fields)) {
                for (String a : fields) {
                    query.fields().include(a);
                }
            }

            result = mongoTemplate.find(query, tClass, BarMongodbConfig.TABLE_CIRCLEPOSTRELATION);
        }

        return result;
    }

    public List<Document> getPostCountByCircleIds(List<String> circleIds) {
        List<Document> result = null;
        if (!CollectionUtils.isEmpty(circleIds)) {
            Criteria criteria = Criteria.where("circleId").in(circleIds).and("state").is(0).and("postDel").is(0).and("postTtjjdel").is(0).and("isDel").is(0).and("postTimepoint").ne(null);
            MatchOperation match = Aggregation.match(criteria);

            GroupOperation group = Aggregation.group("circleId").count().as("count");


            AggregationResults<Document> aggregationResults = mongoTemplate.aggregate(Aggregation.newAggregation(match, group), BarMongodbConfig.TABLE_CIRCLEPOSTRELATION, Document.class);

            result = aggregationResults.getMappedResults();


        }

        return result;
    }

    public <T> List<T> getList(List<String> fields, Class<T> tClass, Date updateTime, int batchReadCount) {
        List<T> result = null;

        Query query = new Query();
        query.addCriteria(Criteria.where("updateTime").gt(updateTime));
        query.cursorBatchSize(batchReadCount);
        query.limit(batchReadCount);

        if (!CollectionUtils.isEmpty(fields)) {
            for (String a : fields) {
                query.fields().include(a);
            }
        }

        query.with(Sort.by(Sort.Order.asc("updateTime")));

        result = mongoTemplate.find(query, tClass, BarMongodbConfig.TABLE_CIRCLEPOSTRELATION);
        return result;
    }

    public <T> List<T> getTagList(List<String> fields, Class<T> tClass, List<String> circleIds, int limit) {
        List<T> result = null;

        if (CollectionUtils.isEmpty(circleIds)) {
            return result;
        }

        Query query = new Query();
        Criteria criteria = Criteria
                .where("circleId").in(circleIds)
                .and("tag").is(1)
                .and("state").is(0)
                .and("postDel").is(0)
                .and("postTtjjdel").is(0)
                .and("isDel").is(0)
                .and("postTimepoint").gt(CommonUtils.getTimePointOneYearAgo());

        query.addCriteria(criteria);
        query.cursorBatchSize(limit);
        query.limit(limit);

        if (!CollectionUtils.isEmpty(fields)) {
            for (String a : fields) {
                query.fields().include(a);
            }
        }

        result = mongoTemplate.find(query, tClass, BarMongodbConfig.TABLE_CIRCLEPOSTRELATION);

        return result;
    }

    public <T> List<T> getTagListByAll(List<String> fields, Class<T> tClass, int limit) {
        List<T> result = null;

        Query query = new Query();
        Criteria criteria = Criteria
                .where("tag").is(1)
                .and("state").is(0)
                .and("postDel").is(0)
                .and("postTtjjdel").is(0)
                .and("isDel").is(0)
                .and("postTimepoint").gt(CommonUtils.getTimePointOneYearAgo());

        query.addCriteria(criteria);
        query.cursorBatchSize(limit);
        query.limit(limit);

        query.with(Sort.by(Sort.Order.desc("postTimepoint")));

        if (!CollectionUtils.isEmpty(fields)) {
            for (String a : fields) {
                query.fields().include(a);
            }
        }

        result = mongoTemplate.find(query, tClass, BarMongodbConfig.TABLE_CIRCLEPOSTRELATION);

        return result;
    }

    public <T> List<T> getUsefulByPostIds(List<String> fields, Class<T> tClass, List<String> postIds) {
        List<T> result = null;
        if (!CollectionUtils.isEmpty(postIds)) {
            Query query = new Query();
            query.addCriteria(Criteria
                    .where("postId").in(postIds)
                    .and("state").is(0)
                    .and("postDel").is(0)
                    .and("postTtjjdel").is(0)
                    .and("isDel").is(0)
            );
            query.cursorBatchSize(100000);

            if (!CollectionUtils.isEmpty(fields)) {
                for (String a : fields) {
                    query.fields().include(a);
                }
            }

            result = mongoTemplate.find(query, tClass, BarMongodbConfig.TABLE_CIRCLEPOSTRELATION);
        }

        return result;
    }

}
