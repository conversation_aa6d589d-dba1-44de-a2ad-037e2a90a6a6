package ttfund.web.communityservice.dao.mongo;

import com.mongodb.bulk.BulkWriteResult;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.ReplyInfoView;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * ReplyInfo表 持久层操作实体
 */
@Repository
public class ReplyInfoMongoDao extends BarMongBaseDao {

    public MongoTemplate getTemplate(){
        return mongoTemplate;
    }

    public List<ReplyInfoView> getList(long topicid, int count) {
        List<ReplyInfoView> result = null;
        Query query = new Query();
        query.addCriteria(Criteria.where("TOPICID").is(topicid).and("DEL").is(0).and("TTJJDEL").is(0).and("ISENABLED").is(1).and("HUIFUIDLIST").is(""));
        query.with(Sort.by(Sort.Order.desc("TIME")));
        query.cursorBatchSize(count).limit(count);
        result = mongoTemplate.find(query, ReplyInfoView.class, BarMongodbConfig.TABLE_REPLY_INFO);
        return result;
    }

    /**
     * 根据map更新，map里需带_id
     */
    public boolean upsert(Map<String, Object> map) {
        boolean result = false;
        if (!CollectionUtils.isEmpty(map) && map.containsKey("_id") && StringUtils.hasLength(String.valueOf(map.get("_id")))) {
            Query query = new Query();
            query.addCriteria(Criteria.where("_id").is(String.valueOf(map.get("_id"))));
            Update update = new Update();
            Set<Map.Entry<String, Object>> entrySet = map.entrySet();
            for (Map.Entry<String, Object> entry : entrySet) {
                if (!"_id".equals(entry.getKey())) {
                    update.set(entry.getKey(), entry.getValue());
                }
            }
            result = mongoTemplate.upsert(query, update, BarMongodbConfig.TABLE_REPLY_INFO).wasAcknowledged();
        }
        return result;
    }

    /**
     * 根据map更新，map里需带_id -批量
     */
    public boolean upsertMany(List<Map<String, Object>> mapList) {
        boolean result = false;
        List<Pair<Query, Update>> pairList = null;
        if (!CollectionUtils.isEmpty(mapList)) {
            pairList = new ArrayList<>();
            for (Map<String, Object> map : mapList) {
                if (!CollectionUtils.isEmpty(map) && map.containsKey("_id") && StringUtils.hasLength(String.valueOf(map.get("_id")))) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where("_id").is(String.valueOf(map.get("_id"))));
                    Update update = new Update();
                    Set<Map.Entry<String, Object>> entrySet = map.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        if (!"_id".equals(entry.getKey())) {
                            update.set(entry.getKey(), entry.getValue());
                        }
                    }
                    Pair<Query, Update> pair = Pair.of(query, update);
                    pairList.add(pair);
                }
            }
        }

        if (!CollectionUtils.isEmpty(pairList)) {
            BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, BarMongodbConfig.TABLE_REPLY_INFO);
            bulkOperations.upsert(pairList);
            BulkWriteResult writeResult = bulkOperations.execute();
            result = writeResult.wasAcknowledged();
        }

        return result;
    }

}
