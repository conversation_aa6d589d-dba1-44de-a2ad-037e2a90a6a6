package ttfund.web.communityservice.dao.mongo;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.FundPostExtras;
import ttfund.web.communityservice.bean.jijinBar.post.TopicDetailsForKafka;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.Date;
import java.util.List;

/**
 * PostInfo_Extend 持久层操作实体
 */
@Repository
public class PostInfoExtendDao extends BarMongBaseDao {

    public MongoTemplate getTemplate() {
        return mongoTemplate;
    }

    public FundPostExtras getByTid(Long tid) {
        FundPostExtras result = null;
        if (tid != null) {
            Query query = new Query();
            query.addCriteria(Criteria.where("TID").is(tid));
            result = mongoTemplate.findOne(query, FundPostExtras.class, BarMongodbConfig.TABLE_PostInfo_Extend);
        }
        return result;
    }

    public FundPostExtras findOne(int postId) {
        FundPostExtras extras = new FundPostExtras();
        Query query = new Query();
        query.addCriteria(Criteria.where("TID").is(postId));
        extras = mongoTemplate.findOne(query, FundPostExtras.class, "PostInfo_Extend");
        return extras;
    }

    public TopicDetailsForKafka findOne(String topicId) {
        TopicDetailsForKafka topicDetails = new TopicDetailsForKafka();
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(topicId));
        topicDetails = mongoTemplate.findOne(query, TopicDetailsForKafka.class, "FundTopic");
        return topicDetails;
    }

    public void insert(FundPostExtras info) {
        if (info != null) {
            mongoTemplate.insert(info, BarMongodbConfig.TABLE_PostInfo_Extend);
        }
    }

    public <T> List<T> getListByPubTime(Class<T> clazz, List<String> fields, int limit, Date start) {
        List<T> result = null;
        if (start != null) {
            Query query = new Query();
            query.addCriteria(Criteria.where("PUBTIME").gte(start));
            query.with(Sort.by(Sort.Order.asc("PUBTIME")));
            query.limit(limit).cursorBatchSize(limit);
            if (!CollectionUtils.isEmpty(fields)) {
                fields.forEach(a -> query.fields().include(a));
            }

            result = mongoTemplate.find(query, clazz, BarMongodbConfig.TABLE_PostInfo_Extend);
        }
        return result;
    }

}
