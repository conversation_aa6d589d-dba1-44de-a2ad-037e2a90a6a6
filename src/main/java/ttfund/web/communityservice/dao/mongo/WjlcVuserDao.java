package ttfund.web.communityservice.dao.mongo;

import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.WjlcVuserDo;

import java.util.List;

/**
 * 万家理财V用户DAO
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@Repository("wjlcVuserDao")
public class WjlcVuserDao extends BarMongBaseDao {

    private static final String COLLECTION_NAME = "WJLCVUser";

    /**
     * 获取所有有效的大V用户
     */
    public List<WjlcVuserDo> getAllActiveVUsers() {
        Query query = new Query();
        Criteria criteria = Criteria.where("IsDel").is(0); // 未删除的用户
        query.addCriteria(criteria);
        
        return mongoTemplate.find(query, WjlcVuserDo.class, COLLECTION_NAME);
    }

    /**
     * 根据UID查询用户
     */
    public WjlcVuserDo findByUid(String uid) {
        Query query = new Query();
        Criteria criteria = Criteria.where("UID").is(uid)
                .and("IsDel").is(0);
        query.addCriteria(criteria);
        
        return mongoTemplate.findOne(query, WjlcVuserDo.class, COLLECTION_NAME);
    }

    /**
     * 根据ID查询用户
     */
    public WjlcVuserDo findById(String id) {
        Query query = new Query();
        Criteria criteria = Criteria.where("_id").is(id)
                .and("IsDel").is(0);
        query.addCriteria(criteria);
        
        return mongoTemplate.findOne(query, WjlcVuserDo.class, COLLECTION_NAME);
    }
}
