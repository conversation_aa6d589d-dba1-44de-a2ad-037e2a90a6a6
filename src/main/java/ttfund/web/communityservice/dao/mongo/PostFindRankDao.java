package ttfund.web.communityservice.dao.mongo;

import com.mongodb.bulk.BulkWriteResult;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.PostFindRankModel;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public class PostFindRankDao extends BarMongBaseDao {

    public MongoTemplate getTemplate() {
        return mongoTemplate;
    }

    public List<PostFindRankModel> getListForUserRelatedPost() {
        List<PostFindRankModel> result = null;
        Query query = new Query();
        query.addCriteria(Criteria.where("ID").gte(0).and("TIME").gte(DateUtil.calendarDateByDays(-14)));
        query.with(Sort.by(Sort.Order.desc("RANKSCORE")));
        query.limit(1000).cursorBatchSize(1000);
        result = mongoTemplate.find(query, PostFindRankModel.class, BarMongodbConfig.TABLE_POSTFINDRANK);
        return result;
    }

    public List<PostFindRankModel> getListForFundBarNicePost() {
        List<PostFindRankModel> result = null;
        Query query = new Query();
        query.addCriteria(Criteria.where("CONTENTLENGTH").gt(50).and("TIME").gte(DateUtil.calendarDateByDays(-14)));
        query.with(Sort.by(Sort.Order.desc("RANKSCORE")));
        query.limit(200).cursorBatchSize(200);
        result = mongoTemplate.find(query, PostFindRankModel.class, BarMongodbConfig.TABLE_POSTFINDRANK);
        return result;
    }

    /**
     * 根据map更新，map里需带_id -批量
     */
    public boolean upsertMany(List<Map<String, Object>> mapList) {
        boolean result = false;
        List<Pair<Query, Update>> pairList = null;
        if (!CollectionUtils.isEmpty(mapList)) {
            pairList = new ArrayList<>();
            for (Map<String, Object> map : mapList) {
                if (!CollectionUtils.isEmpty(map) && map.containsKey("_id") && StringUtils.hasLength(String.valueOf(map.get("_id")))) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where("_id").is(String.valueOf(map.get("_id"))));
                    Update update = new Update();
                    Set<Map.Entry<String, Object>> entrySet = map.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        if (!"_id".equals(entry.getKey())) {
                            update.set(entry.getKey(), entry.getValue());
                        }
                    }
                    Pair<Query, Update> pair = Pair.of(query, update);
                    pairList.add(pair);
                }
            }
        }

        if (!CollectionUtils.isEmpty(pairList)) {
            BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, BarMongodbConfig.TABLE_POSTFINDRANK);
            bulkOperations.upsert(pairList);
            BulkWriteResult writeResult = bulkOperations.execute();
            result = writeResult.wasAcknowledged();
        }

        return result;
    }

}
