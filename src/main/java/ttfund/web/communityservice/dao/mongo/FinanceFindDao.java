package ttfund.web.communityservice.dao.mongo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.finance.LCAuthorInfoModel;

import java.util.List;

/**
 * 理财发现页作者信息
 */
@Repository
public class FinanceFindDao extends BarMongBaseDao {

    private static final Logger logger = LoggerFactory.getLogger(FinanceFindDao.class);

    /**
     * 获取所有作者信息
     *
     * @return
     */
    public List<LCAuthorInfoModel> findAll() {
        return mongoTemplate.findAll(LCAuthorInfoModel.class);
    }
}
