package ttfund.web.communityservice.dao.mongo;

import com.mongodb.bulk.BulkWriteResult;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * QAScoreInfo表 持久层操作实体
 */
@Repository
public class QAScoreInfoDao extends BarMongBaseDao {

    public MongoTemplate getTemplate() {
        return this.mongoTemplate;
    }

    public void removeByQidList(List<String> qidList) {
        if (!CollectionUtils.isEmpty(qidList)) {
            Query query = new Query();
            query.addCriteria(Criteria.where("QID").in(qidList));
            mongoTemplate.remove(query, BarMongodbConfig.TABLE_QASCOREINFO);
        }
    }

    /**
     * 根据map更新，map里需带_id -批量
     */
    public boolean upsertMany(List<Map<String, Object>> mapList) {
        boolean result = false;
        List<Pair<Query, Update>> pairList = null;
        if (!CollectionUtils.isEmpty(mapList)) {
            pairList = new ArrayList<>();
            for (Map<String, Object> map : mapList) {
                if (!CollectionUtils.isEmpty(map) && map.containsKey("_id") && StringUtils.hasLength(String.valueOf(map.get("_id")))) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where("_id").is(String.valueOf(map.get("_id"))));
                    Update update = new Update();
                    Set<Map.Entry<String, Object>> entrySet = map.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        if (!"_id".equals(entry.getKey())) {
                            update.set(entry.getKey(), entry.getValue());
                        }
                    }
                    Pair<Query, Update> pair = Pair.of(query, update);
                    pairList.add(pair);
                }
            }
        }

        if (!CollectionUtils.isEmpty(pairList)) {
            BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, BarMongodbConfig.TABLE_QASCOREINFO);
            bulkOperations.upsert(pairList);
            BulkWriteResult writeResult = bulkOperations.execute();
            result = writeResult.wasAcknowledged();
        }

        return result;
    }

}
