package ttfund.web.communityservice.dao.mongo;

import com.eastmoney.particle.common.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.bson.BsonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumVideoArtcleApprovalState;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumVideoArticleSource;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.FileSet;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.GetChannelInfoApiResult;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.RoomCoverResourceModel;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.VideoArticleModel;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.VideoInfo;
import ttfund.web.communityservice.config.appconfig.ApiUrl;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.service.LangKeLvbCooperationApiServiceImpl;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;
import ttfund.web.communityservice.utils.StringUtil;
import ttfund.web.communityservice.utils.TwoTuple;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class VideoArticleDao extends MongBaseDao {

    String CollectionName = "VideoArticle";


    private String logpre = "[VideoArticleDao]=>";
    private static final Logger logger = LoggerFactory.getLogger(VideoArticleDao.class);

    @Autowired
    ApiUrl apiUrl;

    //基金吧mongdb
    @Autowired
    @Qualifier(BarMongodbConfig.appinfo_mongo_conn_beanname)
    private MongoTemplate appinfoMongoTemplate;

    @Autowired
    private LangKeLvbCooperationApiServiceImpl langKeLvbCooperationApiService;


    /**
     * 大咖秀直播中列表
     *
     * @return
     */
    public List<VideoArticleModel> getLiveList() {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("LiveStartTime").lte(DateUtil.getNowDate())
                    .and("LiveEndTime").gte(DateUtil.getNowDate())
                    .and("ApprovalState").is(EnumVideoArtcleApprovalState.AUDIT.getValue())
                    .and("Source").is(EnumVideoArticleSource.BIGSHOW.getValue())
                    .and("IsDeleted").is(0)
                    .and("IsClosed").is(0)
            );
            List<VideoArticleModel> list = appinfoMongoTemplate.find(query, VideoArticleModel.class, CollectionName);
            logger.info("{}getLiveList=>mongsql:{},数据为:{}", logpre, JsonUtils.toJsonString(query), JsonUtils.toJsonString(list));

            return list;
        } catch (Exception e) {
            throw e;
        }

    }

    /**
     * 获取大咖秀已经结束且房间号为空的数据
     *
     * @return
     */
    public List<VideoArticleModel> getEndLiveAndNoRoomNumber() throws JsonProcessingException {

        try {
            //构造查询条件
            Query query = new Query();
            query.addCriteria(Criteria.where("LiveEndTime").lte(DateUtil.getNowDate())
                    .and("IsDeleted").is(0)
                    .and("RoomNumber").is(BsonNull.VALUE)
            );
            List<VideoArticleModel> list = appinfoMongoTemplate.find(query, VideoArticleModel.class, CollectionName);
            logger.info(logpre + "getEndLiveAndNoRoomNumber=>mongsql：" + JacksonUtil.obj2String(query) + ",数据为:" + JacksonUtil.obj2String(list));

            return list;
        } catch (Exception e) {
            throw e;
        }

    }

    /**
     * 数据批量更新插入
     *
     * @param list
     * @return
     */
    public boolean insertOrUpdate(List<VideoArticleModel> list) {
        return this.upsertBulk(list, VideoArticleModel.class, CollectionName, appinfoMongoTemplate);
    }

    /**
     * 根据房间号获取视频信息
     *
     * @param roomNumber
     * @return
     */
    public VideoArticleModel getByRoomNumber(String roomNumber) {
        Query query = new Query();
        query.addCriteria(Criteria.where("RoomNumber").is(roomNumber));
        return appinfoMongoTemplate.findOne(query, VideoArticleModel.class, CollectionName);
    }

    /**
     * 根据多个房间号获取财富号视频文章信息
     *
     * @param roomNumbers
     * @return
     */
    public List<VideoArticleModel> getByRoomNumber(List<String> roomNumbers) {
        if (CollectionUtils.isEmpty(roomNumbers)) return null;
        Query query = new Query();
        query.addCriteria(Criteria.where("RoomNumber").in(roomNumbers));
        return appinfoMongoTemplate.find(query, VideoArticleModel.class, CollectionName);
    }

    /**
     * 获取时间戳
     *
     * @param order
     * @param dateTime
     * @param str
     * @return
     */
    public long getTimePoint(int order, Date dateTime, String str) {
        if (dateTime == null) dateTime = DateUtil.getMinDate();
        if (str == null) str = "";

        Long result = Long.parseLong(order + String.valueOf(DateUtil.getUnixTime(dateTime)).substring(0, 10) + StringUtil.subStrFromEnd(str, 5, "0"));
        return result;
    }

    public TwoTuple<List<VideoInfo>, String> getViedoInfo(String channelId, RoomCoverResourceModel resourceModel) throws Exception {

        TwoTuple<List<VideoInfo>, String> returnresult = new TwoTuple<>(null, "");

        List<VideoInfo> listVideo = null;
        String videoImageUrl = null;
        String record_img_url = "";
        try {
            if (!StringUtil.isNull(channelId)) {
                GetChannelInfoApiResult result = langKeLvbCooperationApiService.getChannelDetailReturnResponse(channelId);
                if (result != null && result.getResult() == 1 && result.getData() != null
                        && result.getData().getId() > 0 && result.getData().getRecord_hls() != null) {

                    listVideo = new ArrayList<>();

                    //videoImageUrl = record_img_url = (result.getData().getRecord_img_url() == null ? result.getData().getYun_record_img_url() : result.getData().getRecord_img_url());
                    record_img_url = (result.getData().getRecord_img_url() == null ? result.getData().getYun_record_img_url() : result.getData().getRecord_img_url());

                    //视频封面
                    if (result.getData().getRoom_cover_resource() != null) {
                        RoomCoverResourceModel resource = result.getData().getRoom_cover_resource();
                        if (StringUtils.hasLength(resource.getH_cover())) {
                            videoImageUrl = resource.getH_cover();
                        } else if (StringUtils.hasLength(resource.getCover())) {
                            videoImageUrl = resource.getCover();
                        } else {
                            videoImageUrl = resource.getFrame_cover();
                        }
                        if (resourceModel != null) {
                            resourceModel.setCover(resource.getCover());
                            resourceModel.setH_cover(resource.getH_cover());
                            resourceModel.setV_cover(resource.getV_cover());
                            resourceModel.setFrame_cover(resource.getFrame_cover());
                        }
                    }

                    //先判断是否有回放视频，如果有回放视频则设置回放视频地址
                    if (result.getData().getRecord_hls().getVideo_files() != null &&
                            result.getData().getRecord_hls().getVideo_files().size() > 0) {
                        /*回放*/
                        List<FileSet> videos = result.getData().getRecord_hls().getVideo_files();
                        if (!CollectionUtils.isEmpty(videos)) {
                            for (FileSet item : videos) {
                                VideoInfo info = new VideoInfo();

                                info.setVideoImageUrl((StringUtil.isNull(item.getImage_url()) ? record_img_url : item.getImage_url()));
                                info.setVideoSize(item.getVideo_size());
                                info.setVideoSrc(item.getVideo_url());
                                info.setVideoTime(item.getVideo_duration());
                                info.setVideoTitle(result.getData().getName());
                                info.setVideoType(result.getData().getType());
                                info.setVideoSrcType(2);
                                listVideo.add(info);
                            }
                            //如果有视频封面，取视频封面做为封面图或者取其云封面做封面图
                            //videoImageUrl = listVideo.get(0).getVideoImageUrl();
                        }
                    } else if (!StringUtil.isNull(result.getData().getHls_downstream_address())) {
                        /*直播中*/
                        VideoInfo info = new VideoInfo();
                        info.setVideoImageUrl(record_img_url);
                        info.setVideoSrc(result.getData().getHls_downstream_address());
                        info.setVideoTitle(result.getData().getName());
                        info.setVideoType(result.getData().getType());
                        info.setVideoSrcType(1);
                        listVideo.add(info);
                    }

                }
            }
        } catch (Exception ex) {
            listVideo = new ArrayList<>();
            VideoInfo videoInfo = new VideoInfo();
            videoInfo.setVideoImageUrl(record_img_url);
            listVideo.add(videoInfo);
            throw new Exception(ex);
        }
        returnresult.first = listVideo;
        returnresult.second = videoImageUrl;
        return returnresult;
    }


    /**
     * 根据跟新时间增量获取数据
     *
     * @param lastUpdateTime 上次更新时间
     * @param source         1:直播  2：财富号文章
     * @return
     */
    public List<VideoArticleModel> getByLastUpdateTime(Date lastUpdateTime, int source, int limit) {
        if (lastUpdateTime == null) return null;
        Query query = new Query();
        query.addCriteria(Criteria.where("LastUpdateTime").gte(lastUpdateTime).and("Source").is(source));
        query.with(Sort.by(
                Sort.Order.asc("LastUpdateTime")//支持多个，多个用逗号隔开
        ));
        query.limit(limit);
        return appinfoMongoTemplate.find(query, VideoArticleModel.class, CollectionName);
    }

//    /// <summary>
//    /// 根据多个房间号获取财富号视频文章信息
//    /// </summary>
//    /// <param name="roomNumbers"></param>
//    /// <returns></returns>
//    public List<VideoArticleModel> getByRoomNumber(List<String> roomNumbers) {
//
//        if (CollectionUtils.isEmpty(roomNumbers)) return null;
//        Query query =new Query();
//        query.addCriteria(Criteria.where("RoomNumber").in(roomNumbers));
//
//        return barMongoTemplate.find(query, VideoArticleModel.class, CollectionName);
//
//    }

    /**
     * 删除数据
     *
     * @param ids
     * @return
     */
    public boolean deleteByids(List<String> ids) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(ids));
        appinfoMongoTemplate.remove(query, CollectionName);
        return true;
    }
}
