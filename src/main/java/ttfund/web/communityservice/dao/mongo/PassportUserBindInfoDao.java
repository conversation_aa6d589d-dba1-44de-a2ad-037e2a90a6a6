package ttfund.web.communityservice.dao.mongo;

import com.mongodb.bulk.BulkWriteResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserBindInfo;
import ttfund.web.communityservice.config.dataconfig.UserAccMongoConstantConfig;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * 通行证用户交易账号绑定信息
 */

@Repository
public class PassportUserBindInfoDao extends BarMongBaseDao {

    private String collectionName = "PassportUserBindInfo";

    /**
     * 用户账户
     */
    @Autowired
    @Qualifier(UserAccMongoConstantConfig.USER_ACC_MONGO_BEAN_NAME)
    private MongoTemplate userAccMongoTemplate;

    /**
     * 获取待推送列表
     */
    public PassportUserBindInfo getInfo(String uid) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(uid));
        PassportUserBindInfo obj = userAccMongoTemplate.findOne(query, PassportUserBindInfo.class, collectionName);
        return obj;
    }

    /**
     * 获取待推送列表
     */
    public List<PassportUserBindInfo> getByCustomerNos(List<String> customerNos) {
        if (CollectionUtils.isEmpty(customerNos)) {
            return new ArrayList<>();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("CUSTOMERNO").in(customerNos).and("ISENABLED").is("1"));
        return userAccMongoTemplate.find(query, PassportUserBindInfo.class, collectionName);
    }

    public <T> List<T> getListByUpdateTime(Class<T> tClass, List<String> fields, Date updateTime, int count) {
        Query query = new Query();
        query.addCriteria(Criteria.where("UPDATETIME").gt(updateTime).and("CUSTOMERNO").nin(Arrays.asList("")));
        if (!CollectionUtils.isEmpty(fields)) {
            for (String a : fields) {
                query.fields().include(a);
            }
        }

        query.with(Sort.by(Sort.Order.asc("UPDATETIME")));
        query.cursorBatchSize(count);
        query.limit(count);

        return userAccMongoTemplate.find(query, tClass, collectionName);

    }

    /**
     * 根据map更新，map里需带_id -批量
     */
    public boolean upsertMany(List<Map<String, Object>> mapList, String queryField) {
        boolean result = false;
        List<Pair<Query, Update>> pairList = null;
        if (!CollectionUtils.isEmpty(mapList)) {
            pairList = new ArrayList<>();
            for (Map<String, Object> map : mapList) {
                if (!CollectionUtils.isEmpty(map) && map.containsKey(queryField) && map.get(queryField) != null) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where(queryField).is(map.get(queryField)));
                    Update update = new Update();
                    Set<Map.Entry<String, Object>> entrySet = map.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        if (!queryField.equals(entry.getKey())) {
                            update.set(entry.getKey(), entry.getValue());
                        }
                    }
                    Pair<Query, Update> pair = Pair.of(query, update);
                    pairList.add(pair);
                }
            }
        }

        if (!CollectionUtils.isEmpty(pairList)) {
            BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collectionName);
            bulkOperations.upsert(pairList);
            BulkWriteResult writeResult = bulkOperations.execute();
            result = writeResult.wasAcknowledged();
        }

        if (!CollectionUtils.isEmpty(pairList)) {
            BulkOperations bulkOperations = userAccMongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collectionName);
            bulkOperations.upsert(pairList);
            BulkWriteResult writeResult = bulkOperations.execute();
            result = writeResult.wasAcknowledged();
        }

        return result;
    }


    public List<PassportUserBindInfo> getByUids(List<String> uids) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(uids).and("ISENABLED").is("1"));
        query.cursorBatchSize(100000).limit(100000);
        return userAccMongoTemplate.find(query, PassportUserBindInfo.class, collectionName);
    }

    /**
     * 根据map更新，map里需带_id
     */
    public boolean upsertOne(Map<String, Object> map, String queryField) {
        boolean result = false;
        if (!CollectionUtils.isEmpty(map) && map.containsKey(queryField) && map.get(queryField) != null) {
            Query query = new Query();
            query.addCriteria(Criteria.where(queryField).is(map.get(queryField)));
            Update update = new Update();
            Set<Map.Entry<String, Object>> entrySet = map.entrySet();
            for (Map.Entry<String, Object> entry : entrySet) {
                if (!queryField.equals(entry.getKey())) {
                    update.set(entry.getKey(), entry.getValue());
                }
            }
            result = mongoTemplate.upsert(query, update, collectionName).wasAcknowledged();
            result = userAccMongoTemplate.upsert(query, update, collectionName).wasAcknowledged();
        }
        return result;
    }

    public <T> List<T> listByCustomerNos(Class<T> tClass, List<String> fields, List<String> customerNos) {
        List<T> result = null;

        if (CollectionUtils.isEmpty(customerNos)) {
            return result;
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("CUSTOMERNO").in(customerNos).and("ISENABLED").is("1"));
        if (!CollectionUtils.isEmpty(fields)) {
            for (String a : fields) {
                query.fields().include(a);
            }
        }

        query.cursorBatchSize(100000);
        query.limit(100000);

        return userAccMongoTemplate.find(query, tClass, collectionName);
    }

}
