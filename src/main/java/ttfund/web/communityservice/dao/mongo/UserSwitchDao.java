package ttfund.web.communityservice.dao.mongo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.data.UserSwitch;
import ttfund.web.communityservice.config.dataconfig.UserAccMongoConstantConfig;

import java.util.List;

@Repository
public class UserSwitchDao {

    @Autowired
    @Qualifier(UserAccMongoConstantConfig.USER_ACC_MONGO_BEAN_NAME)
    private MongoTemplate userAccMongoTemplate;

    public <T> T getOneByKeyIsValue(String key, Object value, List<String> fields, Class<T> tClass) {
        T result = null;

        Query query = new Query();
        query.addCriteria(Criteria.where(key).is(value));

        if (!CollectionUtils.isEmpty(fields)) {
            fields.forEach(a -> query.fields().include(a));
        }

        result = userAccMongoTemplate.findOne(query, tClass, UserAccMongoConstantConfig.TABLE_USERSWITCH);
        return result;
    }

    /**
     * 根据开关类型和开关状态获取数据
     *
     * @param type
     * @param value
     * @return
     */
    public List<UserSwitch> getSwitchList(Integer type, Integer value) {

        Query query = new Query(
            Criteria.where("type").is(type)
                .and("value").is(value)
        );

        return userAccMongoTemplate.find(query, UserSwitch.class, UserAccMongoConstantConfig.TABLE_USERSWITCH);
    }
}
