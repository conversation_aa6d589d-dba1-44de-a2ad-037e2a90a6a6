package ttfund.web.communityservice.dao.mongo;

import com.mongodb.client.result.DeleteResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.finance.LCPostInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.finance.WJLCPostInfoModel;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;
import java.util.List;

/**
 * 稳健理财专区 - 理财同路人帖子信息
 */
@Repository
public class WJLCPostInfoDao extends BarMongBaseDao {

    private static final Logger logger = LoggerFactory.getLogger(WJLCPostInfoDao.class);

    @Value("${finance.find.range.days:60}")
    private int rangeDays;

    @Value("${finance.find.added.delete.days:7}")
    private int addedDeleteDays;

    /**
     * 根据通行证ID删除帖子
     *
     * @return
     */
    public DeleteResult deleteByUsers(List<String> userList) {

        Query query = new Query(
                Criteria.where("UID").in(userList)
        );

        return mongoTemplate.remove(query, WJLCPostInfoModel.class);
    }

    /**
     * 获取单独添加的帖子
     *
     * @return
     */
    public List<LCPostInfoModel> getSingleAddedPost() {

        Query query = new Query(
                Criteria.where("SOURCE").is(2)
        );

        return mongoTemplate.find(query, LCPostInfoModel.class);
    }

    /**
     * 根据帖子ID删除帖子
     *
     * @return
     */
    public DeleteResult deleteByPostIds(List<Long> postList) {

        Query query = new Query(
                Criteria.where("ID").in(postList)
        );

        return mongoTemplate.remove(query, WJLCPostInfoModel.class);
    }

    /**
     * 根据帖子ID删除帖子
     *
     * @return
     */
    public boolean upsertBulk(List<WJLCPostInfoModel> postList) {
        return upsertBulk(postList, WJLCPostInfoModel.class, mongoTemplate);
    }

    /**
     * 一年前的帖子删除
     *
     * @return
     */
    public void deleteOldPost() {

        // 删除一年前的帖子
        Date date = DateUtil.calendarDateByYears(-1);

        Query query = new Query(
                Criteria.where("TIME").lt(date)
        );

        DeleteResult result = mongoTemplate.remove(query, WJLCPostInfoModel.class);
        logger.info("删除一年前帖子数量：{}", result.getDeletedCount());

    }

    /**
     * 获取全部帖子
     *
     * @param pageSize
     * @param pageIndex
     * @param sortField1
     * @param sortField2
     * @param daysOffset
     * @return
     */
    public List<WJLCPostInfoModel> findAllPost(int pageIndex, int pageSize, String sortField1, String sortField2,
        Integer daysOffset) {

        Criteria criteria = Criteria.where("ISDEL").is(0);

        if (daysOffset != null) {
            criteria.and("TIME").gte(DateUtil.calendarDateByDays(daysOffset));
        }

        Query query = new Query().addCriteria(
                    criteria
                )
                .with(Sort.by(
                    Sort.Order.desc(sortField1),
                    Sort.Order.desc(sortField2)
                ))
                .skip((long) (pageIndex - 1) * pageSize)
                .limit(pageSize);

        query.fields()
                .include("ID")
                .include("UID")
                .include("QID")
                .include("CODE");

        return mongoTemplate.find(query, WJLCPostInfoModel.class);
    }
}
