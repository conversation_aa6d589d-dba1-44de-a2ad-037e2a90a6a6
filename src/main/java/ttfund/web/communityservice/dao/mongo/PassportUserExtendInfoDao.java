package ttfund.web.communityservice.dao.mongo;

import com.mongodb.bulk.BulkWriteResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.config.dataconfig.UserAccMongoConstantConfig;

import java.util.*;

/**
 * PassportUserExtendInfo表 持久层操作实体
 */
@Repository
public class PassportUserExtendInfoDao {


    public static final String COLLECTIONNAME = "PassportUserExtendInfo";

    @Qualifier(UserAccMongoConstantConfig.USER_ACC_MONGO_BEAN_NAME)
    @Autowired
    private MongoTemplate userAccMongoTemplate;

    @Autowired
    @Qualifier(BarMongodbConfig.bar_mongo_conn_beanname)
    protected MongoTemplate mongoTemplate;

    /**
     * 根据map更新，map里需带_id -批量
     */
    public void upsertMany(List<Map<String, Object>> mapList) {
        List<Pair<Query, Update>> pairList = null;
        if (!CollectionUtils.isEmpty(mapList)) {
            pairList = new ArrayList<>();
            for (Map<String, Object> map : mapList) {
                if (!CollectionUtils.isEmpty(map) && map.containsKey("_id") && StringUtils.hasLength(String.valueOf(map.get("_id")))) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where("_id").is(String.valueOf(map.get("_id"))));
                    Update update = new Update();
                    Set<Map.Entry<String, Object>> entrySet = map.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        if (!"_id".equals(entry.getKey())) {
                            update.set(entry.getKey(), entry.getValue());
                        }
                    }
                    Pair<Query, Update> pair = Pair.of(query, update);
                    pairList.add(pair);
                }
            }
        }

        if (!CollectionUtils.isEmpty(pairList)) {
            BulkOperations bulkOperations = userAccMongoTemplate.
                    bulkOps(BulkOperations.BulkMode.UNORDERED, COLLECTIONNAME);
            bulkOperations.upsert(pairList);
            BulkWriteResult writeResult = bulkOperations.execute();
        }
        if (!CollectionUtils.isEmpty(pairList)) {
            BulkOperations bulkOperations = mongoTemplate.
                    bulkOps(BulkOperations.BulkMode.UNORDERED, COLLECTIONNAME);
            bulkOperations.upsert(pairList);
            BulkWriteResult writeResult = bulkOperations.execute();
        }

    }

    public void setDelByUpdateTime(Date updateTime) {
        Query query = new Query();
        query.addCriteria(Criteria.where("UpdateTime").lt(updateTime));
        Update update = new Update();
        update.set("Del", 1);

        userAccMongoTemplate.updateMulti(query, update, COLLECTIONNAME);
        mongoTemplate.updateMulti(query, update, COLLECTIONNAME);
    }

}
