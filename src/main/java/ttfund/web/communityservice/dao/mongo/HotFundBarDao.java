package ttfund.web.communityservice.dao.mongo;

import com.mongodb.bulk.BulkWriteResult;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 热门基金吧 HotFundBar表
 */
@Repository
public class HotFundBarDao extends BarMongBaseDao {

    public MongoTemplate getTemplate() {
        return mongoTemplate;
    }

    /**
     * 获取所有热门基金吧code
     */
    public List<String> getAllCodes() {
        List<String> result = null;
        Query query = new Query();
        query.fields().include("CODE");
        List<Document> docList = mongoTemplate.find(query, Document.class, BarMongodbConfig.TABLE_HOTFUNDBAR);
        if (!CollectionUtils.isEmpty(docList)) {
            result = docList.stream().map(o -> o.getString("CODE")).collect(Collectors.toList());
        }
        return result;
    }

    /**
     * 根据吧code批量删除
     */
    public void deleteByCodes(List<String> codes) {
        if (!CollectionUtils.isEmpty(codes)) {
            Query query = new Query();
            query.addCriteria(Criteria.where("CODE").in(codes));
            mongoTemplate.remove(query, BarMongodbConfig.TABLE_HOTFUNDBAR);
        }
    }

    /**
     * 根据map更新，map里需带_id -批量
     */
    public boolean upsertMany(List<Map<String, Object>> mapList) {
        boolean result = false;
        List<Pair<Query, Update>> pairList = null;
        if (!CollectionUtils.isEmpty(mapList)) {
            pairList = new ArrayList<>();
            for (Map<String, Object> map : mapList) {
                if (!CollectionUtils.isEmpty(map) && map.containsKey("_id") && StringUtils.hasLength(String.valueOf(map.get("_id")))) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where("_id").is(String.valueOf(map.get("_id"))));
                    Update update = new Update();
                    Set<Map.Entry<String, Object>> entrySet = map.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        if (!"_id".equals(entry.getKey())) {
                            update.set(entry.getKey(), entry.getValue());
                        }
                    }
                    Pair<Query, Update> pair = Pair.of(query, update);
                    pairList.add(pair);
                }
            }
        }

        if (!CollectionUtils.isEmpty(pairList)) {
            BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, BarMongodbConfig.TABLE_HOTFUNDBAR);
            bulkOperations.upsert(pairList);
            BulkWriteResult writeResult = bulkOperations.execute();
            result = writeResult.wasAcknowledged();
        }

        return result;
    }

    /**
     * 根据得分倒序获得热门基金吧
     *
     * @param size 数量
     * @return
     */
    public List<String> getHotFundBarByScore(int size) {

        List<String> result = new ArrayList<>();

        Query query = new Query()
                .with(
                        Sort.by(
                                Sort.Order.desc("SCORE")
                        )
                ).limit(size);

        query.fields().include("CODE");

        List<Document> mongoList = mongoTemplate.find(query, Document.class, BarMongodbConfig.TABLE_HOTFUNDBAR);

        if (!CollectionUtils.isEmpty(mongoList)) {
            result.addAll(mongoList.stream().map(w -> w.getString("CODE")).distinct().collect(Collectors.toList()));
        }
        return result;
    }

}
