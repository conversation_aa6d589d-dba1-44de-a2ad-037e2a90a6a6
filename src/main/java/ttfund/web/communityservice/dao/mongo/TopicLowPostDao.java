package ttfund.web.communityservice.dao.mongo;

import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.mongo.TopicLowPost;

import java.util.List;

/**
 * 话题水帖表 持久层操作实体
 */
@Repository
public class TopicLowPostDao extends BarMongBaseDao {

    /**
     * 保存
     */
    public boolean saveAll(List<TopicLowPost> list) {
        return upsertBulk(list, TopicLowPost.class, mongoTemplate);
    }
}
