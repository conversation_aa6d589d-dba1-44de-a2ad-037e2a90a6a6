package ttfund.web.communityservice.dao.mongo;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class JJGSFundManagerRelationDao extends BarMongBaseDao {

    public MongoTemplate getTemplate() {
        return mongoTemplate;
    }

    /**
     * 获取基金公司用户对应的通行证ID
     */
    public List<String> getCompanyUserIds() {
        List<String> result = new ArrayList<>();
        Query query = new Query();
        query.addCriteria(Criteria.where("FundVType").is("402").and("UID").nin(Arrays.asList(null, "")));
        query.fields().include("UID");
        List<Document> docs = mongoTemplate.find(query, Document.class, BarMongodbConfig.TABLE_JJGSFUNDMANAGERRELATION);
        if (!CollectionUtils.isEmpty(docs)) {
            result = docs.stream().map(a -> a.getString("UID")).collect(Collectors.toList());
        }

        return result;
    }

}
