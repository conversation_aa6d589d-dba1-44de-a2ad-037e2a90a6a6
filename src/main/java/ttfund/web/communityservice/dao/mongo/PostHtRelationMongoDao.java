package ttfund.web.communityservice.dao.mongo;

import com.mongodb.bulk.BulkWriteResult;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public class PostHtRelationMongoDao extends BarMongBaseDao {

    /**
     * 根据map更新 -批量
     */
    public boolean upsertManyBySetWithSetOnInsertFields(List<Map<String, Object>> mapList, List<String> setOnInsertFields, String queryField) {
        boolean result = false;
        List<Pair<Query, Update>> pairList = null;
        if (!CollectionUtils.isEmpty(mapList)) {
            pairList = new ArrayList<>();
            for (Map<String, Object> map : mapList) {
                if (!CollectionUtils.isEmpty(map) && map.containsKey(queryField) && map.get(queryField) != null) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where(queryField).is(map.get(queryField)));
                    Update update = new Update();
                    Set<Map.Entry<String, Object>> entrySet = map.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        if (!queryField.equals(entry.getKey())) {
                            if (!CollectionUtils.isEmpty(setOnInsertFields) && setOnInsertFields.contains(entry.getKey())) {
                                update.setOnInsert(entry.getKey(), entry.getValue());
                            } else {
                                update.set(entry.getKey(), entry.getValue());
                            }
                        }
                    }
                    Pair<Query, Update> pair = Pair.of(query, update);
                    pairList.add(pair);
                }
            }
        }

        if (!CollectionUtils.isEmpty(pairList)) {
            BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, BarMongodbConfig.TABLE_POSTHTRELATION);
            bulkOperations.upsert(pairList);
            BulkWriteResult writeResult = bulkOperations.execute();
            result = writeResult.wasAcknowledged();
        }

        return result;
    }


}
