package ttfund.web.communityservice.dao.mongo;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.List;

@Repository
public class LcLabelInfoDao extends BarMongBaseDao {

    public <T> List<T> listByState(Class<T> clazz, List<String> fields, List<Integer> stateList) {
        List<T> result = null;
        if (CollectionUtils.isEmpty(stateList)) {
            return result;
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("state").in(stateList));

        if (!CollectionUtils.isEmpty(fields)) {
            fields.forEach(a -> query.fields().include(a));
        }
        query.with(Sort.by(Sort.Order.asc("sort"), Sort.Order.desc("updateTime")));
        query.limit(100000).cursorBatchSize(100000);

        result = mongoTemplate.find(query, clazz, BarMongodbConfig.TABLE_LCLABELINFO);
        return result;
    }

}
