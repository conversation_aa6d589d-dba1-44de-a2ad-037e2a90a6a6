package ttfund.web.communityservice.dao.mongo;

import com.mongodb.client.result.DeleteResult;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.mongo.QualityPostTopicRelation;

import java.util.List;

/**
 * 优质帖-话题关联表 持久层操作实体
 */
@Repository
public class QualityPostTopicRelationDao extends BarMongBaseDao {

    /**
     * 保存
     */
    public boolean saveAll(List<QualityPostTopicRelation> list) {
        return upsertBulk(list, QualityPostTopicRelation.class, mongoTemplate);
    }

    /**
     * 根据帖子id删除
     */
    public boolean removeByPostIds(List<String> postIds) {
        Query query = new Query(
            Criteria.where("PostId").in(postIds)
        );
        DeleteResult result = mongoTemplate.remove(query, QualityPostTopicRelation.class);
        return result.wasAcknowledged();
    }
}
