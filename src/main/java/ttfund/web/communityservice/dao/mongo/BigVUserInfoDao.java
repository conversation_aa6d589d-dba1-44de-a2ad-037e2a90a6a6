package ttfund.web.communityservice.dao.mongo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.user.BigVUserInfo;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.List;

@Repository
public class BigVUserInfoDao extends MongBaseDao {

    @Autowired
    @Qualifier(BarMongodbConfig.BIGV_MONGO_CONN_BEANNAME)
    private MongoTemplate mongoTemplate;

    /**
     * 获取大V用户
     */
    public List<BigVUserInfo> getBigVList(String[] vTypeStatus) {

        Query query = new Query(
                Criteria.where("vtype").ne("")
                        .and("vtypestatus").in(vTypeStatus)
        );

        return mongoTemplate.find(query, BigVUserInfo.class, BarMongodbConfig.TABLE_BIGVUSERINFO);
    }
}
