package ttfund.web.communityservice.dao.mongo;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.config.ActivityCardConfig;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.Date;
import java.util.List;

/**
 * FundBarAdConfig表 持久层操作实体
 */
@Repository
public class FundBarAdConfigDao extends BarMongBaseDao {

    /**
     * 根据更新时间获取
     */
    public List<ActivityCardConfig> getList(Date updateTime, int count, boolean sortDesc) {
        List<ActivityCardConfig> result = null;
        if (updateTime != null && count > 0) {
            Query query = new Query();
            query.addCriteria(Criteria.where("UpdateTime").gt(updateTime));
            if (sortDesc) {
                query.with(Sort.by(Sort.Order.desc("UpdateTime")));
            } else {
                query.with(Sort.by(Sort.Order.asc("UpdateTime")));
            }
            query.limit(count);
            query.cursorBatchSize(count);

            result = mongoTemplate.find(query, ActivityCardConfig.class, BarMongodbConfig.TABLE_FUND_BAR_AD_CONFIG);
        }
        return result;
    }

}
