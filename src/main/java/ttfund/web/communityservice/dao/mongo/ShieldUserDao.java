package ttfund.web.communityservice.dao.mongo;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.Date;

/**
 * ShieldUser 持久层操作实体
 */
@Repository
public class ShieldUserDao extends BarMongBaseDao {

    public MongoTemplate getTemplate() {
        return mongoTemplate;
    }

    /**
     * 根据 EUTIME 删除数据
     */
    public void delByEutime(Date date) {
        if (date != null) {
            Query query = new Query();
            query.addCriteria(Criteria.where("EUTIME").lte(date));
            mongoTemplate.remove(query, BarMongodbConfig.TABLE_SHIELDUSER);
        }
    }
}
