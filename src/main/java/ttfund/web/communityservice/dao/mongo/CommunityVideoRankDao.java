package ttfund.web.communityservice.dao.mongo;

import com.mongodb.bulk.BulkWriteResult;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 */
@Repository
public class CommunityVideoRankDao extends BarMongBaseDao {

    /**
     * 根据map更新
     */
    public boolean upsertMany(List<Map<String, Object>> mapList, List<String> setOnInsertFields) {
        boolean result = false;
        List<Pair<Query, Update>> pairList = null;
        if (!CollectionUtils.isEmpty(mapList)) {
            pairList = new ArrayList<>();
            String queryField = "_id";
            for (Map<String, Object> map : mapList) {
                if (!CollectionUtils.isEmpty(map) && map.containsKey(queryField) && map.get(queryField) != null) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where(queryField).is(map.get(queryField)));
                    Update update = new Update();
                    Set<Map.Entry<String, Object>> entrySet = map.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        if (!queryField.equals(entry.getKey())) {
                            if (!CollectionUtils.isEmpty(setOnInsertFields) && setOnInsertFields.contains(entry.getKey())) {
                                update.setOnInsert(entry.getKey(), entry.getValue());
                            } else {
                                update.set(entry.getKey(), entry.getValue());
                            }
                        }
                    }
                    Pair<Query, Update> pair = Pair.of(query, update);
                    pairList.add(pair);
                }
            }
        }

        if (!CollectionUtils.isEmpty(pairList)) {
            BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, BarMongodbConfig.TABLE_COMMUNITYVIDEORANK);
            bulkOperations.upsert(pairList);
            BulkWriteResult writeResult = bulkOperations.execute();
            result = writeResult.wasAcknowledged();
        }

        return result;
    }

    /**
     * 根据map更新
     */
    public boolean updateManyWhenSetScore(List<Map<String, Object>> mapList, List<String> includeFields) {
        boolean result = false;
        List<Pair<Query, Update>> pairList = null;
        if (!CollectionUtils.isEmpty(mapList)) {
            pairList = new ArrayList<>();
            String queryField = "_id";
            for (Map<String, Object> map : mapList) {
                if (!CollectionUtils.isEmpty(map) && map.get(queryField) != null) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where(queryField).is(map.get(queryField)).and("sourceType").ne(1));
                    Update update = new Update();
                    Set<Map.Entry<String, Object>> entrySet = map.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        if (!queryField.equals(entry.getKey()) && includeFields.contains(entry.getKey())) {
                            update.set(entry.getKey(), entry.getValue());
                        }
                    }
                    Pair<Query, Update> pair = Pair.of(query, update);
                    pairList.add(pair);
                }
            }
        }

        if (!CollectionUtils.isEmpty(pairList)) {
            BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, BarMongodbConfig.TABLE_COMMUNITYVIDEORANK);
            bulkOperations.updateMulti(pairList);
            BulkWriteResult writeResult = bulkOperations.execute();
            result = writeResult.wasAcknowledged();
        }

        return result;
    }

    public void deleteManyByTimePoint(long timePoint) {
        Query query = new Query();
        query.addCriteria(Criteria.where("timePoint").lt(timePoint));

        mongoTemplate.remove(query, BarMongodbConfig.TABLE_COMMUNITYVIDEORANK);
    }

}
