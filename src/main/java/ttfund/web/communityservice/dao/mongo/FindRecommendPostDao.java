package ttfund.web.communityservice.dao.mongo;

import com.mongodb.client.result.DeleteResult;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.PostRecommendModel;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.StringUtil;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public class FindRecommendPostDao extends BarMongBaseDao {

    /**
     * 发现页mongdb 表名
     */
    private String collectionName = "RecommendPost";

    /**
     * 获取分组名
     *
     * @param groupName
     * @return
     */
    public String getFindScoreFiledName(String groupName) {
        if (!StringUtil.isNull(groupName)) {
            groupName = groupName.toUpperCase();
        } else {
            return "";
        }
        return "FINDSCORE_" + groupName;
    }


    /**
     * 根据发帖时间判断 该帖子是否是新发的帖子
     *
     * @param postTime
     * @return
     */
    public Boolean isNewPost(Date postTime) {


        //当前日期
        Date curDate = DateUtil.getNowDate("yyyy-MM-dd");
        //发帖日期
        Date postDate = DateUtil.getDate(postTime, "yyyy-MM-dd");

        Date t1 = DateUtil.calendarDateByHour(curDate, 14);
        Date t11 = DateUtil.calendarDateByHour(postDate, 15);


//        var t1 = DateTime.Parse("14:00");
//        var t11 = postTime.Date.AddHours(15);

        Date t2 = DateUtil.calendarDateByHour(curDate, 21);
        Date t22 = DateUtil.calendarDateByHour(postDate, 22);

//        var t2 = DateTime.Parse("21:00");
//        var t22 = postTime.Date.AddHours(22);
        //当前时间
        Date curDateTime = DateUtil.getNowDate();
        if ((postDate.compareTo(t1) < 1 && curDateTime.compareTo(t11) > 0) ||
                (postTime.compareTo(t2) < 1 && curDateTime.compareTo(t22) > 0)) {
            return false;
        }
        return true;
    }

    /**
     * 更新mongdb  数据,存在更新，不存在插入
     *
     * @param map
     * @return
     */
    public boolean upsert(Map<String, Object> map) {
        return upsert(map, collectionName);
    }

    /**
     * 根据ID 删除数据
     *
     * @param id
     * @return
     */
    public boolean remove(String id) {
        return removeById(id, collectionName);
    }

    /**
     * 更新单条数据
     *
     * @param map
     * @return
     */
    public boolean updateFirst(Map<String, Object> map) {
        return updateFirst(map, collectionName);
    }

    /**
     * 根据跟新时间删除
     *
     * @param timeponit
     * @return
     */
    public boolean remove(long timeponit) {
        Query query = new Query();
        query.addCriteria(Criteria.where("TIMEPOINT").lte(timeponit));
        DeleteResult deleteResult = mongoTemplate.remove(query, collectionName);
        return deleteResult != null && deleteResult.getDeletedCount() > 0;
    }


    /**
     * 根据时间获错获取数据
     *
     * @param startTimePoint
     * @param endTimePoint
     * @param pageSize
     * @return
     */
    public List<PostRecommendModel> get(long startTimePoint, long endTimePoint, int pageSize) {

        Query query = new Query();
        query.addCriteria(Criteria.where("TIMEPOINT").gte(startTimePoint).lte(endTimePoint));
        query.limit(pageSize);
        query.with(Sort.by(Sort.Order.asc("TIMEPOINT")));
        List<PostRecommendModel> list = mongoTemplate.find(query, PostRecommendModel.class, collectionName);

        return list;
    }


    /**
     * 根据帖子ID获取指定分组得分
     *
     * @param postSet
     * @param groupName
     * @param postScoreMap
     * @return
     */
    public void findByID(Set<Long> postSet, String groupName, Map<Long, Long> postScoreMap) {

        if (CollectionUtils.isEmpty(postSet)) {
            return;
        }

        String fieldName = getFindScoreFiledName(groupName);

        Query query = new Query(
                Criteria.where("ID").in(postSet)
                        .and(fieldName).exists(true)
        );

        query.fields()
                .include("ID")
                .include(fieldName);

        List<Map> maps = mongoTemplate.find(query, Map.class, collectionName);


        if (!CollectionUtils.isEmpty(maps)) {
            for (Map item : maps) {
                Long id = Long.parseLong(item.get("ID").toString());
                Long score = Long.parseLong(item.get(fieldName).toString());
                postScoreMap.put(id, score);
            }
        }
    }

    /**
     * 增量获取得分有变化的帖子
     *
     * @param beginDate
     * @param oldUserList
     * @param newUserList
     * @param addedPost
     * @param size
     * @param groupName
     * @return
     */
    public Date getIncrementalPost(Date beginDate, List<String> oldUserList, List<String> newUserList,
                                   Set<Long> addedPost, int size, String groupName, Map<Long, Long> scoreMap) {

        String fieldName = getFindScoreFiledName(groupName);

        Query query = new Query()
                .addCriteria(
                        new Criteria().andOperator(
                                new Criteria().orOperator(
                                        new Criteria().andOperator(
                                                new Criteria().orOperator(
                                                        Criteria.where("UID").in(oldUserList),
                                                        Criteria.where("ID").in(addedPost)
                                                ),
                                                new Criteria().where(fieldName).exists(true),
                                                new Criteria("UPDATETIME").gte(beginDate)
                                        ),
                                        new Criteria().andOperator(
                                                new Criteria().orOperator(
                                                        Criteria.where("UID").in(newUserList),
                                                        Criteria.where("ID").in(addedPost)
                                                ),
                                                new Criteria().where(fieldName).exists(true)
                                        )
                                )
                        )
                ).with(Sort.by("UPDATETIME"))
                .limit(size);

        query.fields()
                .include("ID")
                .include("UPDATETIME")
                .include(fieldName);

        List<Map> maps = mongoTemplate.find(query, Map.class, collectionName);

        if (!CollectionUtils.isEmpty(maps)) {
            for (Map item : maps) {
                Long id = Long.parseLong(item.get("ID").toString());
                Long score = Long.parseLong(item.get(fieldName).toString());
                scoreMap.put(id, score);
            }
            return (Date) maps.get(maps.size() - 1).get("UPDATETIME");
        }

        return null;
    }

    public List<PostRecommendModel> getListSortByFindscore(int count) {

        Query query = new Query();
        query.limit(count);
        query.with(Sort.by(Sort.Order.desc("FINDSCORE")));
        List<PostRecommendModel> list = mongoTemplate.find(query, PostRecommendModel.class, collectionName);

        return list;
    }

    public List<Document> getListSortByTimepoint(int count) {

        Query query = new Query();
        query.limit(count);
        query.with(Sort.by(Sort.Order.desc("TIMEPOINT")));
        List<Document> list = mongoTemplate.find(query, Document.class, collectionName);

        return list;
    }
}
