package ttfund.web.communityservice.dao.mongo;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.List;

@Repository
public class WaterfallPostConfigDao extends BarMongBaseDao {

    public <T> List<T> getList(List<String> fields, Class<T> tClass, List<String> postIds) {
        List<T> result = null;

        if (CollectionUtils.isEmpty(postIds)) {
            return result;
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(postIds).and("isDel").is(0));
        if (!CollectionUtils.isEmpty(fields)) {
            for (String a : fields) {
                query.fields().include(a);
            }
        }
        query.with(Sort.by(Sort.Order.desc("sort")));

        query.cursorBatchSize(100000).limit(100000);

        result = mongoTemplate.find(query, tClass, BarMongodbConfig.TABLE_WATERFALLPOSTCONFIG);

        return result;
    }

}
