package ttfund.web.communityservice.dao.mongo;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.Map;
import java.util.Set;

@Repository
public class SubAccountDtoModelPortfolioDao extends BarMongBaseDao {

    public MongoTemplate getTemplate() {
        return mongoTemplate;
    }

    /**
     * 根据map更新，map里需带_id
     */
    public boolean upsert(Map<String, Object> map) {
        boolean result = false;
        if (!CollectionUtils.isEmpty(map) && map.containsKey("_id") && StringUtils.hasLength(String.valueOf(map.get("_id")))) {
            Query query = new Query();
            query.addCriteria(Criteria.where("_id").is(String.valueOf(map.get("_id"))));
            Update update = new Update();
            Set<Map.Entry<String, Object>> entrySet = map.entrySet();
            for (Map.Entry<String, Object> entry : entrySet) {
                if (!"_id".equals(entry.getKey())) {
                    update.set(entry.getKey(), entry.getValue());
                }
            }
            result = mongoTemplate.upsert(query, update, BarMongodbConfig.TABLE_SubAccountDtoModelPortfolio).wasAcknowledged();
        }
        return result;
    }
}