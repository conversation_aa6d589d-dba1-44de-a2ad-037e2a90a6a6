package ttfund.web.communityservice.dao.mongo;

import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.ai.AiMultiSummaryModel;

import java.util.Date;
import java.util.List;

/**
 * AI多机构总结DAO
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@Repository
public class AiMultiSummariesDao extends BarMongBaseDao {

    private static final String COLLECTION_NAME = "ai_multi_summaries";

    /**
     * 保存AI多机构总结
     */
    public AiMultiSummaryModel save(AiMultiSummaryModel summary) {
        return mongoTemplate.save(summary, COLLECTION_NAME);
    }

    /**
     * 根据日期和时间段查询总结
     */
    public AiMultiSummaryModel findByDateAndTimeSlot(Date processDate, String timeSlot) {
        Query query = new Query();
        Criteria criteria = Criteria.where("processDate").is(processDate)
                .and("timeSlot").is(timeSlot);
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, AiMultiSummaryModel.class, COLLECTION_NAME);
    }

    /**
     * 根据任务ID查询总结
     */
    public AiMultiSummaryModel findByTaskId(String taskId) {
        Query query = new Query(Criteria.where("taskId").is(taskId));
        return mongoTemplate.findOne(query, AiMultiSummaryModel.class, COLLECTION_NAME);
    }

    /**
     * 根据日期查询所有总结
     */
    public List<AiMultiSummaryModel> findByDate(Date processDate) {
        Query query = new Query(Criteria.where("processDate").is(processDate));
        return mongoTemplate.find(query, AiMultiSummaryModel.class, COLLECTION_NAME);
    }

    /**
     * 查找最近的一条总结数据（按创建时间倒序）
     */
    public AiMultiSummaryModel findLatestSummary() {
        Query query = new Query();
        query.with(org.springframework.data.domain.Sort.by(
                org.springframework.data.domain.Sort.Direction.DESC, "createTime"));
        query.limit(1);
        return mongoTemplate.findOne(query, AiMultiSummaryModel.class, COLLECTION_NAME);
    }
}
