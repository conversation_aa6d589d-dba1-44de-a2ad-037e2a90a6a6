package ttfund.web.communityservice.dao.kafka;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.ttfund.web.base.helper.DateHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.search.AppFunctionNewModel;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 功能数据推送 dao
 *
 * @author：liyaogang
 * @date：2023/8/4 9:54
 */
@Repository
public class PushAppToKafkaDao {

    @Resource
    @Qualifier(KafkaConfig.bloc_kafka_temp_beanname)
    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(String topic, List<JsonNode> appFunctions) throws JsonProcessingException {
        if (appFunctions == null) {
            appFunctions = new ArrayList<>();
        }
        String key = DateUtil.dateToStr(new Date(), DateHelper.FORMAT_YYYY_MM_DD);
        kafkaTemplate.send(topic, key, JacksonUtil.obj2String(appFunctions));
    }
}
