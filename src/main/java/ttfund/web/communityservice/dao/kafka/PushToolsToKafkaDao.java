package ttfund.web.communityservice.dao.kafka;

import com.ttfund.web.core.register.AppCore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.web.util.HtmlUtils;
import ttfund.web.communityservice.bean.messagepush.AnswerOrAdoptedMsgRequest;
import ttfund.web.communityservice.bean.messagepush.EnumRemindType;
import ttfund.web.communityservice.bean.messagepush.JijinbaServicePushModel;
import ttfund.web.communityservice.bean.messagepush.KafkaLikeModel4FundModel;
import ttfund.web.communityservice.bean.messagepush.QARemindRequestModel;
import ttfund.web.communityservice.bean.messagepush.RemindGuba2PostAbout;
import ttfund.web.communityservice.bean.messagepush.RemindGuba2RequestModel;
import ttfund.web.communityservice.bean.messagepush.RemindGubaRequestModel;
import ttfund.web.communityservice.bean.messagepush.SendPointMsgRequest;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.utils.DaoUtil;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;
import ttfund.web.communityservice.utils.MD5Util;
import ttfund.web.communityservice.utils.NullUtil;
import ttfund.web.communityservice.utils.StringUtil;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Repository
public class PushToolsToKafkaDao {
    private static final Logger logger = LoggerFactory.getLogger(PushToolsToKafkaDao.class);

    //基金吧kafka
    @Autowired
    @Qualifier(KafkaConfig.fundbar_kafka_temp_beanname)
    private KafkaTemplate<String, String> kafkaTemplate;
    private String logPre = "PushToolsToKafkaDao=>";

    private String source = "fundserviceremind";

    @Resource
    AppCore appCore;

    @Autowired
    private App app;

    private static final String TAB_REGEX = "<[^>]*?>";

    private static final Pattern TAB_REGEX_PATTERN = Pattern.compile(TAB_REGEX, Pattern.CASE_INSENSITIVE);

    private static final String USER_REGEX = "\\[at=(.*?)\\]([\\s\\S]*?)\\[/at\\]";

    private static final Pattern USER_REGEX_PATTERN = Pattern.compile(USER_REGEX);

    private static final String TOPIC_REGEX = "\\[topic_name=(.*?)##topic_id=(.*?)\\]";

    private static final Pattern TOPIC_REGEX_PATTERN = Pattern.compile(TOPIC_REGEX);

    /**
     * 提到我的
     *
     * @param model
     */
    public void MentionMe(RemindGuba2RequestModel model) {
        try {
            JijinbaServicePushModel pushModel = new JijinbaServicePushModel();
            model.Content = StringUtil.decode(model.getContent());
            RemindGuba2PostAbout temppostabout = JacksonUtil.deserialize(model.getPostAbout(), RemindGuba2PostAbout.class);
            String tempInfoTypeName = DaoUtil.getInfoTypeNamenew(model.getPostType(), model.getInfoType());
            String PassportImg = DaoUtil.getPassportImg(model.getReplyPassportID());
            String tempTitle = removeHtml(model.getTitle());
            String Title = DaoUtil.strSub(model.ReplyPassportName + "的" + tempInfoTypeName + ":" + tempTitle.replace(",", "，"), 50);

            String tempContent = removeHtml(model.Content);
            String Content = DaoUtil.strSub(model.ReplyPassportName + "的" + tempInfoTypeName + ":" + tempContent.replace(",", "，"), 50);

            List<String> AlertParam = new ArrayList<>();
            AlertParam.add(model.getReplyPassportName());
            List<String> DataParam = null;
            List<String> LinkParam = null;
            if (model.getPostType() == 49) {
                String temppagename = DaoUtil.getQAPageNamenew(model.getPostType());
                LinkParam = new ArrayList<>();
                LinkParam.add(temppagename);
                LinkParam.add(model.getPostID());
                LinkParam.add(temppostabout.qid);
                LinkParam.add("");
                LinkParam.add("false");

                DataParam = new ArrayList<>();
                DataParam.add(model.getReplyPassportName());
                DataParam.add(model.getPassportName());
                DataParam.add(Title);
                DataParam.add(PassportImg);

                pushModel.setRemindType(EnumRemindType.MENTIONMEQA);
            } else if (model.PostType == 50) {
                String temppagename = DaoUtil.getQAPageNamenew(model.PostType);
                LinkParam = new ArrayList<>();
                String[] arrLink = {temppagename, model.PostID, temppostabout.qid, temppostabout.aid, "false"};
                LinkParam.addAll(Arrays.asList(arrLink));

                DataParam = new ArrayList<>();
                String[] arrDataParam = {model.ReplyPassportName, model.PassportName, Content, PassportImg};
                DataParam.addAll(Arrays.asList(arrDataParam));

                pushModel.RemindType = EnumRemindType.MENTIONMEQA;
            } else {
                LinkParam = new ArrayList<>();
                String[] arrLink = {model.PostID};
                LinkParam.addAll(Arrays.asList(arrLink));

                pushModel.RemindType = EnumRemindType.MENTIONME;
                DataParam = new ArrayList<>();
                String[] arrDataParam = {model.ReplyPassportName, model.PassportName, Content, PassportImg};
                DataParam.addAll(Arrays.asList(arrDataParam));
            }

            pushModel.Pid = String.format("%s_%s_%s_%s_%s", model.SourceType, model.SourceID, model.PostID, model.PassportID, model.ReplyPassportID);//来源id + 回复id + 用户id
            pushModel.Source = model.Source;
            pushModel.PassportId = model.PassportID;
            pushModel.AlertParam = AlertParam;
            pushModel.DataParam = DataParam;
            pushModel.LinkParam = LinkParam;

            String cacheName = "Push_MentionMe_" + pushModel.Pid;
            String cacheResult = appCore.redisuserread.get(cacheName);
            if (!NullUtil.isNull(cacheResult)) {
                return;
            }

            String json = JacksonUtil.obj2String(pushModel);
            pushModelToKafka(pushModel);

            appCore.redisuserwrite.set(cacheName, "1", 60 * 60 * 24 * 90l);

            String pushCountKey = String.format(BarRedisKey.POST_AT_ME_PUSH_COUNT, model.PassportID);
            Date now = new Date();
            Date tomorrow = DateUtil.strToDate(DateUtil.dateToStr(DateUtil.calendarDateByDays(now, 1), "yyyy-MM-dd"), "yyyy-MM-dd");
            app.barredis.incrBy(pushCountKey, 1L, (tomorrow.getTime() - now.getTime()) / 1000);

            logger.info(logPre + "【MentionMe提到我的】推送的数据为：" + json);
        } catch (Exception ex) {
            try {
                logger.error(logPre + "【MentionMe提到我的】【消息推送】  参数:" + JacksonUtil.obj2String(model) + " 异常:" + ex.getMessage(), ex);

            } catch (Exception e) {

            }
        }

    }

    /**
     * 回复我的
     *
     * @param model
     */
    public void ReplyMy(RemindGuba2RequestModel model) {
        try {


            if (model == null || NullUtil.isNull(model.EID)) return;

            String cacheName = "Push_ReplyMy_" + model.EID;
            String cacheResult = appCore.redisuserread.get(cacheName);
            if (!NullUtil.isNull(cacheResult)) {
                return;
            }


            RemindGuba2PostAbout temppostabout = JacksonUtil.deserialize(model.PostAbout, RemindGuba2PostAbout.class);
            String tempInfoTypeName = DaoUtil.getInfoTypeNamenew(model.PostType, model.InfoType);
            String PassportImg = DaoUtil.getPassportImg(model.ReplyPassportID);
            String ReplyContent = DaoUtil.strSub(removeHtml(model.ReplyContent).replace(",", "，"), 50);
            String Content = DaoUtil.strSub("我的" + tempInfoTypeName + ":" + removeHtml(model.Content).replace(",", "，"), 50);

            List<String> AlertParam = new ArrayList<>();
            String[] arrAlertParam = {model.ReplyPassportName, tempInfoTypeName};
            AlertParam.addAll(Arrays.asList(arrAlertParam));

            List<String> DataParam = new ArrayList<>();
            String[] arrDataParam = {model.ReplyPassportName, ReplyContent, Content, PassportImg};
            DataParam.addAll(Arrays.asList(arrDataParam));

            List<String> LinkParam = null;
            JijinbaServicePushModel pushModel = new JijinbaServicePushModel();
            if (model.PostType == 49 && temppostabout != null && temppostabout.qid != null) {
                String temppagename = DaoUtil.getQAPageNamenew(model.PostType);
                LinkParam = new ArrayList<>();
                String[] arrLinkParam = {temppagename, model.PostID, temppostabout.qid, "", "false"};
                LinkParam.addAll(Arrays.asList(arrLinkParam));

                pushModel.setRemindType(EnumRemindType.ReplyMyQA);
            } else if (model.PostType == 50 && temppostabout != null && temppostabout.qid != null && temppostabout.aid != null) {
                String temppagename = DaoUtil.getQAPageNamenew(model.PostType);
                LinkParam = new ArrayList<>();
                String[] arrLinkParam = {temppagename, model.PostID, temppostabout.qid, temppostabout.aid, "false"};
                LinkParam.addAll(Arrays.asList(arrLinkParam));

                pushModel.setRemindType(EnumRemindType.ReplyMyQA);
            } else {
                LinkParam = new ArrayList<>();
                String[] arrLinkParam = {model.PostID};
                LinkParam.addAll(Arrays.asList(arrLinkParam));

                pushModel.setRemindType(EnumRemindType.ReplyMy);
            }
            //  pushModel.setPid(String.format("%s_%s_%s_%s", model.SourceType, model.SourceID, model.PostID, model.PassportID));//来源id + 回复id + 用户id
            pushModel.setPid(model.getEID());
            pushModel.setSource(model.getSource());
            pushModel.setPassportId(model.getPassportID());
            pushModel.setAlertParam(AlertParam);
            pushModel.setDataParam(DataParam);
            pushModel.setLinkParam(LinkParam);

            String json = JacksonUtil.serialize(pushModel);
            pushModelToKafka(pushModel);
            //设置缓存，防止重复推送
            appCore.redisuserwrite.set(cacheName, "1", 60 * 60 * 24 * 180l);
            logger.info(logPre + "【ReplyMy】推送的数据为：" + json);
        } catch (Exception ex) {
            try {
                logger.error(logPre + "【ReplyMy】【消息推送】  参数:" + JacksonUtil.serialize(model) + " 异常:" + ex.getMessage(), ex);
            } catch (Exception e) {

            }
        }
    }

    /// <summary>
    /// 点赞我的
    /// </summary>
    /// <param name="model"></param>
    public void LikeMe(KafkaLikeModel4FundModel model) {

        try {
            if (model == null || NullUtil.isNull(model.EID)) return;

            String cacheName = "Push_LikeMe_" + model.EID;
            String cacheResult = appCore.redisuserread.get(cacheName);
            if (!NullUtil.isNull(cacheResult)) {
                logger.info(logPre + "LikeMeREPEAT=>" + JacksonUtil.serialize(model));
                return;
            }

            RemindGuba2PostAbout temppostabout = JacksonUtil.deserialize(model.PostAbout, RemindGuba2PostAbout.class);
            String tempInfoTypeName = DaoUtil.getInfoTypeNamenew(model.PostType, model.InfoType);
            String PassportImg = DaoUtil.getPassportImg(model.ReplyPassportID);
            String Content = DaoUtil.strSub("我的"
                    + tempInfoTypeName + ":"
                    + removeHtml(model.Content).replace(",", "，"), 50);

            List<String> AlertParam = new ArrayList<>();
            String[] arrAlertParam = {
                    model.ReplyPassportName, tempInfoTypeName
            };
            AlertParam.addAll(Arrays.asList(arrAlertParam));

            List<String> DataParam = new ArrayList<>();
            String[] arrDataParam = {
                    model.ReplyPassportName, tempInfoTypeName, Content, PassportImg
            };
            DataParam.addAll(Arrays.asList(arrDataParam));


            List<String> LinkParam = null;
            JijinbaServicePushModel pushModel = new JijinbaServicePushModel();
            if (model.PostType == 49) {
                String temppagename = DaoUtil.getQAPageNamenew(model.PostType);
                LinkParam = new ArrayList<>();
                String[] arrLinkParam = {temppagename, model.PostID, temppostabout.qid, "", "false"};
                LinkParam.addAll(Arrays.asList(arrLinkParam));

                pushModel.RemindType = EnumRemindType.LikeMeQA;
            } else if (model.PostType == 50) {
                String temppagename = DaoUtil.getQAPageNamenew(model.PostType);

                LinkParam = new ArrayList<>();
                String[] arrLinkParam = {temppagename, model.PostID, temppostabout.qid, temppostabout.aid, "false"};
                LinkParam.addAll(Arrays.asList(arrLinkParam));

                pushModel.RemindType = EnumRemindType.LikeMeQA;
            } else {

                LinkParam = new ArrayList<>();
                String[] arrLinkParam = {model.PostID};
                LinkParam.addAll(Arrays.asList(arrLinkParam));

                pushModel.RemindType = EnumRemindType.LikeMe;
            }
            pushModel.Pid = model.EID;
            pushModel.Source = model.Source;
            pushModel.PassportId = model.PassportID;

            pushModel.AlertParam = AlertParam;
            pushModel.DataParam = DataParam;
            pushModel.LinkParam = LinkParam;


            String json = JacksonUtil.serialize(pushModel);
            appCore.redisuserwrite.set(cacheName, "1", 60 * 60 * 24L);
            pushModelToKafka(pushModel);
            logger.info(logPre + "【LikeMe】推送的数据为：" + json);
        } catch (Exception ex) {
            try {
                logger.error(logPre + "【LikeMe】 参数:" + JacksonUtil.serialize(model) + " 异常：" + ex.getMessage(), ex);
            } catch (Exception e) {

            }
        }

    }

    /// <summary>
    /// 关注我的
    /// </summary>
    /// <param name="model"></param>
    public void FollowMe(RemindGubaRequestModel model) {
        try {

            if (model == null || NullUtil.isNull(model.EID)) return;

            String cacheName = "FollowMe_" + model.EID;
            String cacheResult = appCore.redisuserread.get(cacheName);
            if (!NullUtil.isNull(cacheResult)) {
                logger.info(logPre + "FollowMeREPEAT=>" + JacksonUtil.serialize(model));
                return;
            }

            String PassportImg = DaoUtil.getPassportImg(model.ReplyPassportID);

            JijinbaServicePushModel pushModel = new JijinbaServicePushModel();
            pushModel.RemindType = EnumRemindType.FollowMe;
            pushModel.Pid = model.EID;
            pushModel.Source = model.Source;
            pushModel.PassportId = model.PassportID;

            pushModel.AlertParam = new ArrayList<>();
            String[] arrAlertParam = {model.ReplyPassportName};
            pushModel.AlertParam.addAll(Arrays.asList(arrAlertParam));


            pushModel.DataParam = new ArrayList<>();
            String[] arrDataParam = {model.ReplyPassportName, PassportImg};
            pushModel.DataParam.addAll(Arrays.asList(arrDataParam));

            pushModel.LinkParam = new ArrayList<>();
            String[] arrLinkParam = {model.PassportID};
            pushModel.LinkParam.addAll(Arrays.asList(arrLinkParam));


            String json = JacksonUtil.serialize(pushModel);
            appCore.redisuserwrite.set(cacheName, "1", 60 * 60 * 24L);
            pushModelToKafka(pushModel);
            logger.info(logger + "【FollowMe】【关注我的】 推送的数据为：" + json);
        } catch (Exception ex) {
            try {
                logger.error(logger + "【FollowMe】【关注我的】 url=> 参数:" + JacksonUtil.serialize(model) + " 异常:" + ex.getMessage(), ex);
            } catch (Exception e) {

            }

        }


    }

    /// <summary>
    /// 回答我的
    /// </summary>
    /// <param name="model"></param>
    public void AnswerMe(QARemindRequestModel model) {
        try {

            if (model == null || NullUtil.isNull(model.EID)) return;

            String cacheName = "Push_AnswerMe_" + model.EID;
            String cacheResult = appCore.redisuserread.get(cacheName);
            if (!NullUtil.isNull(cacheResult)) {
                return;
            }

            String PassportImg = DaoUtil.getPassportImg(model.TriggerPassportID);
            String tempContent = removeHtml(model.Content);
            String Content = DaoUtil.strSub("我的提问:" + tempContent.replace(",", "，"), 50);
            JijinbaServicePushModel pushModel = new JijinbaServicePushModel();
            pushModel.RemindType = EnumRemindType.AnswerMe;
            pushModel.Pid = model.EID;
            pushModel.Source = model.Source;
            pushModel.PassportId = model.PassportID;

            String[] arrAlertParam = {model.TriggerPassportName};
            pushModel.AlertParam = Arrays.asList(arrAlertParam);

            String[] arrDataParam = {model.TriggerPassportName, Content, PassportImg};
            pushModel.DataParam = Arrays.asList(arrDataParam);


            String[] arrLinkParam = {model.PostID, model.Qid};
            pushModel.LinkParam = Arrays.asList(arrLinkParam);


            String json = JacksonUtil.serialize(pushModel);
            appCore.redisuserwrite.set(cacheName, "1", 60 * 60 * 24 * 180L);
            pushModelToKafka(pushModel);
            logger.info(logPre + "【AnswerMe】【回答我的】 推送的数据为：" + json);

        } catch (Exception ex) {
            try {
                logger.error(logPre + "【AnswerMe】回答我的【消息推送】参数:" + JacksonUtil.serialize(model) + " 异常:" + ex.getMessage(), ex);
            } catch (Exception e) {

            }
        }
    }


    /**
     * 采纳我的
     *
     * @param param
     * @return
     */
    public boolean adoptedPushMsg(AnswerOrAdoptedMsgRequest param) {


        JijinbaServicePushModel pushModel = new JijinbaServicePushModel();
        try {

            if (param == null || NullUtil.isNull(param.EID)) return true;

            String cacheName = "Push_AnswerMe_" + param.EID;
            String cacheResult = appCore.redisuserread.get(cacheName);
            if (!NullUtil.isNull(cacheResult)) {
                return true;
            }

            String PassportImg = DaoUtil.getPassportImg(param.TriggerPassportID);
            String tempParamContent2 = "";
            if (param.RemindTypeChild == 301) {
                tempParamContent2 = "";
            } else if (param.RemindTypeChild == 302) {
                tempParamContent2 = "系统到期";
            }
            String tempContent = DaoUtil.getContentREGEX(DaoUtil.replaceTOPIC(param.Content));
            String Content = DaoUtil.strSub("我的回答:" + tempContent.replace(",", "，"), 50);

            pushModel.RemindType = EnumRemindType.QAADOPT;
            pushModel.Pid = param.EID;
            pushModel.Source = "gubaremind";
            pushModel.PassportId = param.PassportID;

            String[] arrAlertParam = {param.TriggerPassportName};
            pushModel.AlertParam = Arrays.asList(arrAlertParam);

            String[] arrDataParam = {param.TriggerPassportName, tempParamContent2, Content, PassportImg};
            pushModel.DataParam = Arrays.asList(arrDataParam);

            String[] arrLinkParam = {param.PostID, param.Qid, param.Aid, "false"};
            pushModel.LinkParam = Arrays.asList(arrLinkParam);


            String json = JacksonUtil.serialize(pushModel);
            pushModelToKafka(pushModel);
            appCore.redisuserwrite.set(cacheName, "1", 60 * 60 * 24 * 30L);
            logger.info(logPre + "adoptedPushMsg=>AdoptedPushMsg{" + JacksonUtil.serialize(pushModel) + "}");

            return true;
        } catch (Exception ex) {
            logger.error(logPre + "adoptedPushMsg=>推送消息：异常" + ex.getMessage(), ex);
            return false;
        }
    }


    /**
     * 积分回退  悬赏退回 消息提醒
     *
     * @param param
     * @return
     */
    public boolean sendRefundPushMsg(SendPointMsgRequest param) {

        try {
            JijinbaServicePushModel pushModel = new JijinbaServicePushModel();
            String PassportImg = DaoUtil.getPassportImg(param.PassportID);
            String tempParamContent2 = "";
            if (param.RemindTypeChild == 501) {
                tempParamContent2 = String.format("【积分退回】抱歉，您的提问因审核不通过而被删除，%s积分已退回，请前往积分中心查看。", param.PointNum);
            } else if (param.RemindTypeChild == 502) {
                tempParamContent2 = String.format("【积分退回】抱歉，您的悬赏问题到期未产生满意回答，%s积分已退回，请前往积分中心查看。", param.PointNum);
            } else if (param.RemindTypeChild == 503) {
                tempParamContent2 = String.format("【积分退回】您的悬赏提问中，部分奖励因回答用户未开通交易账号而发放失败，%s积分已退回，可前往积分中心查看。", param.PointNum);
            }
            String tempContent = removeHtml(param.Content);
            String Content = DaoUtil.strSub("我的提问:" + tempContent.replace(",", "，"), 50);

            pushModel.RemindType = EnumRemindType.QABOUNTY;
            pushModel.Pid = param.EID;
            pushModel.Source = source;
            pushModel.PassportId = param.PassportID;

            String[] arrAlertParam = {""};
            pushModel.AlertParam = Arrays.asList(arrAlertParam);

            String[] arrDataParam = {param.PassportName, tempParamContent2, Content, PassportImg};
            pushModel.DataParam = Arrays.asList(arrDataParam);

            String[] arrLinkParam = {param.PostID, param.Qid};
            pushModel.LinkParam = Arrays.asList(arrLinkParam);

            pushModelToKafka(pushModel);
            logger.info(logPre + "sendRefundPushMsg=>【退财富币】 ,推送数据:" + JacksonUtil.serialize(pushModel) + ",参数为:" + JacksonUtil.serialize(param));
            return true;
        } catch (Exception ex) {
            logger.error(logPre + "sendRefundPushMsg=>退财富币 ，,异常信息：" + ex.getMessage(), ex);
            return false;
        }
    }


    /**
     * 奖励发放 消息推送
     *
     * @param request
     * @return
     */
    public boolean sendRewardPushMsg(SendPointMsgRequest request) {
        try {

            String PassportImg = DaoUtil.getPassportImg(request.PassportID);
            String tempParamContent2 = "";

            if (request.RemindTypeChild == 401) {
                tempParamContent2 = String.format("【积分发放成功】您的回答奖励%s积分已发放成功，您可在积分中心查看明细。", request.PointNum);
            } else if (request.RemindTypeChild == 402) {
                tempParamContent2 = String.format("【积分发放失败】很抱歉因您尚未开通交易帐号，最佳回答奖励%s积分发放失败。", request.PointNum);
            }
            String tempContent = removeHtml(request.Content);
            String Content = DaoUtil.strSub("我的回答:" + tempContent.replace(",", "，"), 50);
            JijinbaServicePushModel pushModel = new JijinbaServicePushModel();
            pushModel.RemindType = EnumRemindType.QAAWARD;
            pushModel.Pid = request.EID;
            pushModel.Source = source;
            pushModel.PassportId = request.PassportID;


            String[] arrAlertParam = {request.PassportName};
            pushModel.AlertParam = Arrays.asList(arrAlertParam);

            String[] arrDataParam = {request.PassportName, tempParamContent2, Content, PassportImg};
            pushModel.DataParam = Arrays.asList(arrDataParam);

            String[] arrLinkParam = {request.PostID, request.Qid, request.Aid, "false"};
            pushModel.LinkParam = Arrays.asList(arrLinkParam);

            pushModelToKafka(pushModel);
            logger.info(logPre + "sendRewardPushMsg=>【财富币发放】 ,推送数据:" + JacksonUtil.serialize(pushModel));

            return true;
        } catch (Exception ex) {
            logger.error(logPre + "sendRewardPushMsg=>财富币发放 ，,异常信息：" + ex.getMessage(), ex);
            return false;
        }
    }


    private void pushModelToKafka(JijinbaServicePushModel model) {

        try {
            if (model == null) return;
            String PushTopic = KafkaTopicName.JIJINBA_SERVICE_PUSH;
            model.setPid(MD5Util.generateHash(model.getPid()));
            //推送消息
            String json = JacksonUtil.serialize(model);
            kafkaTemplate.send(PushTopic, model.Pid, json);

        } catch (Exception ex) {
            logger.error(logPre + "pushModelToKafka=>" + ex.getMessage(), ex);
        }
    }

    public static String removeHtml(String content) {

        // HTML 解码
        content = HtmlUtils.htmlUnescape(content);

        // 处理at用户
        Matcher matcher = USER_REGEX_PATTERN.matcher(content);
        while (matcher.find()) {
            content = content.replace(matcher.group(0), matcher.group(2));
        }

        // 处理话题
        matcher = TOPIC_REGEX_PATTERN.matcher(content);
        while (matcher.find()) {
            content = content.replace(matcher.group(0), String.format("#%s#", matcher.group(1)));
        }

        // 处理HTML标签
        matcher = TAB_REGEX_PATTERN.matcher(content);
        while (matcher.find()) {
            content = content.replace(matcher.group(0), "");
        }

        return content;
    }
}
