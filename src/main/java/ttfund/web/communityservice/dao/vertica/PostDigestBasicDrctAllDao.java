package ttfund.web.communityservice.dao.vertica;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.constant.VerticaConstant;
import ttfund.web.communityservice.mapper.vertica.PostDigestBasicDrctAllMapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public class PostDigestBasicDrctAllDao {

    @Autowired
    private PostDigestBasicDrctAllMapper postDigestBasicDrctAllMapper;

    @DS(VerticaConstant.DS_RESEARCH_VERTICA)
    public List<Map<String, Object>> getListByUpdatetime(Date updateTime, int batchReadCount) {
        return postDigestBasicDrctAllMapper.getListByUpdatetime(updateTime, batchReadCount);
    }

    @DS(VerticaConstant.DS_RESEARCH_VERTICA)
    public int updateOne(Map<String, Object> map) {
        return postDigestBasicDrctAllMapper.updateOne(map);
    }

    @DS(VerticaConstant.DS_RESEARCH_VERTICA)
    public List<Map<String, Object>> getByPostId(String fields, Long postId) {
        return postDigestBasicDrctAllMapper.getByPostId(fields, postId);
    }

}
