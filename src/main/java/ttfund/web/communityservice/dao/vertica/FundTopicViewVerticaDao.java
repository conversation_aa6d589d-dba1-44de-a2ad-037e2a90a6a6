package ttfund.web.communityservice.dao.vertica;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.data.TopicInsightDo;
import ttfund.web.communityservice.constant.VerticaConstant;
import ttfund.web.communityservice.mapper.vertica.FundTopicViewVerticaMapper;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <PERSON>yuang
 * @date : 2024-08-19 14:49
 * @description : 话题洞见表
 */
@Repository
public class FundTopicViewVerticaDao {

    @Autowired
    private FundTopicViewVerticaMapper fundTopicViewVerticaMapper;

    @DS(VerticaConstant.DS_RESEARCH_VERTICA)
    public List<TopicInsightDo> select(Date updateTime) {
        return fundTopicViewVerticaMapper.select(updateTime);
    }
}
