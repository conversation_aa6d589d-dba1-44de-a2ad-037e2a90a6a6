package ttfund.web.communityservice.dao.vertica;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.data.TopicWordDo;
import ttfund.web.communityservice.constant.VerticaConstant;
import ttfund.web.communityservice.mapper.vertica.PostHtRelationBasicAllMapper;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024-08-22 15:36
 * @description :
 */
@Repository
public class PostHtRelationBasicAllDao {

    @Autowired
    private PostHtRelationBasicAllMapper postHtRelationBasicAllMapper;

    @DS(VerticaConstant.DS_RESEARCH_VERTICA)
    public List<TopicWordDo> getTopicWordCloud() {
        return postHtRelationBasicAllMapper.getTopicWordCloud();
    }
}
