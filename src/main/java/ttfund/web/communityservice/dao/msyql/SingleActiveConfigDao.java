package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import ttfund.web.communityservice.bean.jijinBar.post.config.SingleActiveConfigModel;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.SingleActiveConfigMapper;

import java.util.List;

@Repository
@Transactional
public class SingleActiveConfigDao {

    @Autowired
    private SingleActiveConfigMapper singleActiveConfigMapper;

    /**
     * 获取配置信息
     *
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<SingleActiveConfigModel> getAll() {

        QueryWrapper<SingleActiveConfigModel> wrapper = new QueryWrapper<SingleActiveConfigModel>()
                .eq("Del", 0);

        return singleActiveConfigMapper.selectList(wrapper);
    }
}
