package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.SensitiveWordConfigMapper;

import java.util.List;

/**
 * 表tb_sensitiveword_config 持久层操作实体
 */
@Repository
public class SensitiveWordConfigDao {

    @Autowired
    private SensitiveWordConfigMapper sensitiveWordConfigMapper;

    @DS(BarMysqlConfig.dsbarread)
    public List<String> getAll() {
        return sensitiveWordConfigMapper.getAll();
    }

}
