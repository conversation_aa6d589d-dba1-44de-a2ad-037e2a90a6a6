package ttfund.web.communityservice.dao.msyql;


import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.data.EMSetPostItem;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.EmSetTopMapper;

import java.util.List;

@Repository
@Transactional
public class EmSetTopDao {

    @Autowired
    private EmSetTopMapper emSetTopMapper;

    /**
     * 增量获更新帖子的基金代码
     *
     * @return
     */
    @DS(BarMysqlConfig.dsbarwrite)
    public int insertOrUpdate(List<EMSetPostItem> list) {
        if (!CollectionUtils.isEmpty(list)) {
            return emSetTopMapper.insertOrUpdate(list);
        }
        return 0;
    }
}
