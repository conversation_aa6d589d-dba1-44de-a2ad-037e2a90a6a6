package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.data.CirclePostRelation;
import ttfund.web.communityservice.config.appconfig.DynamicDataSourceConfig;
import ttfund.web.communityservice.mapper.community.CirclePostRelationMysqlMapper;
import ttfund.web.communityservice.utils.CommonUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public class CirclePostRelationMysqlDao {

    @Autowired
    private CirclePostRelationMysqlMapper circlePostRelationMysqlMapper;

    @DS(DynamicDataSourceConfig.DS_COMMUNITYWRITE)
    public int replaceMany(List<CirclePostRelation> modelList) {
        int result = 0;
        if (CollectionUtils.isEmpty(modelList)) {
            return result;
        }

        result = circlePostRelationMysqlMapper.replaceMany(modelList);
        return result;
    }

    @DS(DynamicDataSourceConfig.DS_COMMUNITYWRITE)
    public int updateManyByPostId(List<CirclePostRelation> modelList) {
        int result = 0;
        if (CollectionUtils.isEmpty(modelList)) {
            return result;
        }

        result = circlePostRelationMysqlMapper.updateManyByPostId(modelList);
        return result;
    }

    @DS(DynamicDataSourceConfig.DS_COMMUNITYREAD)
    public List<CirclePostRelation> getListByPostIds(List<String> postIds) {
        List<CirclePostRelation> result = null;
        if (!CollectionUtils.isEmpty(postIds)) {
            result = circlePostRelationMysqlMapper.getListByPostIds(postIds);
        }

        return result;
    }

    @DS(DynamicDataSourceConfig.DS_COMMUNITYREAD)
    public List<Map<String, Object>> getPostCountByCircleIds(List<String> circleIds) {
        List<Map<String, Object>> result = null;
        if (!CollectionUtils.isEmpty(circleIds)) {
            result = circlePostRelationMysqlMapper.getPostCountByCircleIds(circleIds);
        }

        return result;
    }

    @DS(DynamicDataSourceConfig.DS_COMMUNITYREAD)
    public List<CirclePostRelation> getList(Date updateTime, int batchReadCount) {
        List<CirclePostRelation> result = null;
        if (updateTime == null) {
            return result;
        }

        result = circlePostRelationMysqlMapper.getList(updateTime, batchReadCount);
        return result;
    }

    @DS(DynamicDataSourceConfig.DS_COMMUNITYREAD)
    public List<CirclePostRelation> getTagList(List<String> circleIds) {
        List<CirclePostRelation> result = null;
        if (CollectionUtils.isEmpty(circleIds)) {
            return result;
        }

        result = circlePostRelationMysqlMapper.getTagList(circleIds, CommonUtils.getTimePointOneYearAgo());
        return result;
    }

    @DS(DynamicDataSourceConfig.DS_COMMUNITYREAD)
    public List<CirclePostRelation> getTagListByAll(int limit) {
        List<CirclePostRelation> result = null;

        result = circlePostRelationMysqlMapper.getTagListByAll(CommonUtils.getTimePointOneYearAgo(), limit);
        return result;
    }

    @DS(DynamicDataSourceConfig.DS_COMMUNITYREAD)
    public List<CirclePostRelation> getUsefulByPostIds(List<String> postIds) {
        List<CirclePostRelation> result = null;
        if (!CollectionUtils.isEmpty(postIds)) {
            result = circlePostRelationMysqlMapper.getUsefulByPostIds(postIds);
        }

        return result;
    }

}
