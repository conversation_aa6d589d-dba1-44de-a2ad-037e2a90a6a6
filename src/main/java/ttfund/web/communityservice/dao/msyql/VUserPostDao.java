package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ttfund.web.base.helper.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.BigVCalDataModel;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.BigVDataUserModel;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.VCompanyRetModel;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.VUserPostInfoMapper;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;

/**
 * 大V用户帖子信息操作 mysql
 *
 * <AUTHOR>
 */
@Repository
public class VUserPostDao {

    private static final Logger logger = LoggerFactory.getLogger(VUserPostDao.class);

    @Autowired
    VUserPostInfoMapper vUserPostInfoMapper;

    /**
     * 获取所有的大V用户的文章量
     * vUids count应该>0 故省去原代码中部分逻辑
     *
     * <AUTHOR>
     */
    @DS(BarMysqlConfig.dsbarread)
    public Map<String, Long> getPostNumsFromDb(List<String> codes, List<String> vUids, Date date) {
        try {
            List<Map<String, Object>> uIdPostNums = vUserPostInfoMapper.getPostNums(codes, vUids, date);
            //统一放到一个map中
            Map<String, Long> map = new HashMap<>();
            for (Map<String, Object> uIdPostNum : uIdPostNums) {
                if (uIdPostNum.keySet().size() == 2) {
                    map.put(uIdPostNum.get("UID").toString(), Long.parseLong(uIdPostNum.get("PostNums").toString()));
                }
            }
            return getMaps(vUids, map);
        } catch (Exception e) {
            logger.error("获取大V用户帖子信息异常---db异常   getPostNumsFromDb==>>{}", e.getMessage());
            return null;
        }
    }


    /**
     * 统计大V用户某天的帖子数量
     * vUids count应该>0 故省去原代码中部分逻辑
     *
     * <AUTHOR>
     */
    @DS(BarMysqlConfig.dsbarread)
    public Map<String, Long> getPostNumsForDay(List<String> codes, List<String> vUids, Date date) {
        try {
            List<Map<String, Object>> uIdPostNums = vUserPostInfoMapper.getPostNumsForOneDay(codes, vUids, date);
            //统一放到一个map中
            Map<String, Long> map = new HashMap<>();
            for (Map<String, Object> uIdPostNum : uIdPostNums) {
                if (uIdPostNum.keySet().size() == 2) {
                    map.put(uIdPostNum.get("UID").toString(), Long.parseLong(uIdPostNum.get("PostNums").toString()));
                }
            }
            return getMaps(vUids, map);
        } catch (Exception e) {
            logger.error("统计大V用户某天的帖子数量异常---getPostNumsForDay==>>{}", e.getMessage());
            return null;
        }
    }


    /**
     * 获取大v用户帖子的点击总量
     *
     * @param codes
     * @param vUids
     * @param date
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public Map<String, Long> getPostClickNumsFromDb(List<String> codes, List<String> vUids, Date date) {
        try {
            List<Map<String, Object>> uIdPostClickNums = vUserPostInfoMapper.getPostClickNums(codes, vUids, date);
            //统一放到一个map中
            Map<String, Long> map = new HashMap<>();
            for (Map<String, Object> uIdClickNum : uIdPostClickNums) {
                if (uIdClickNum.keySet().size() == 2) {
                    map.put(uIdClickNum.get("UID").toString(), Long.parseLong(uIdClickNum.get("ClickNums").toString()));
                }
            }
            return getMaps(vUids, map);
        } catch (Exception e) {
            logger.error("获取大V用户帖子的点击总量异常---db异常   getPostClickNumsFromDb==>>{}", e.getMessage());
            return null;
        }
    }


    /**
     * 获取大V用户每个帖子的点赞量
     *
     * @param codes
     * @param vUids
     * @param date
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public Map<String, Long> getPostLikeNumsFromDb(List<String> codes, List<String> vUids, Date date) {
        try {
            List<Map<String, Object>> uIdPostLikeNums = vUserPostInfoMapper.getPostLikeNums(codes, vUids, date);
            //统一放到一个map中
            Map<String, Long> map = new HashMap<>();
            for (Map<String, Object> uIdLikeNum : uIdPostLikeNums) {
                if (uIdLikeNum.keySet().size() == 2) {
                    map.put(uIdLikeNum.get("UID").toString(), Long.parseLong(uIdLikeNum.get("likeNums").toString()));
                }
            }
            return getMaps(vUids, map);
        } catch (Exception e) {
            logger.error("获取大V用户帖子信息异常---db异常   getPostLikeNumsFromDb==>>{}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取大V用户每个帖子的评论量
     */
    @DS(BarMysqlConfig.dsbarread)
    public Map<String, Long> getPostCommentNumsFromDb(List<String> codes, List<String> vUids, Date date) {
        try {
            List<Map<String, Object>> uIdPostCommentNums = vUserPostInfoMapper.getPostCommentNums(codes, vUids, date);
            //统一放到一个map中
            Map<String, Long> map = new HashMap<>();
            for (Map<String, Object> uIdCommNum : uIdPostCommentNums) {
                if (uIdCommNum.keySet().size() == 2) {
                    map.put(uIdCommNum.get("UID").toString(), Long.parseLong(uIdCommNum.get("commentNums").toString()));
                }
            }
            return getMaps(vUids, map);
        } catch (Exception e) {
            logger.error("获取大V用户帖子信息异常---db异常   getPostCommentNumsFromDb==>>{}", e.getMessage());
            return null;
        }
    }

    /**
     * 补充查询不到的大V用户信息
     *
     * @param vUids
     * @param map
     * @return
     */
    public Map<String, Long> getMaps(List<String> vUids, Map<String, Long> map) {
        for (String userId : vUids) {
            if (!map.containsKey(userId)) {
                map.put(userId, 0L);
            }
        }
        return map;
    }


    /**
     * 获取要计算的大V用户 (弃用)
     *
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<BigVDataUserModel> getCalBigVDataUser() {
        return vUserPostInfoMapper.getCalBigVDataUser();
    }

    /**
     * 获取用户上一天的大V数据
     *
     * @param uid
     * @param cdate
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getLastDayBigVData(String uid, Date cdate) {
        return vUserPostInfoMapper.getLastDayBigVData(uid, cdate);
    }

    /**
     * 获取用户最新阅读总数、点赞总数、收到评论总数、帖子总数
     *
     * @param uid
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getTotalBigVData(String uid) {
        return vUserPostInfoMapper.getTotalBigVData(uid);
    }


    /**
     * 获取用户最新发送评论数总数、昨日发送评论数、昨日发帖数
     *
     * @param uid
     * @param cdate
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getTotalBigVCommentData(String uid, Date cdate) {
        //往后加一天
        Date edate = DateUtil.calendarDateByDays(cdate, 1);
        return vUserPostInfoMapper.getTotalBigVCommentData(uid, cdate, edate);
    }

    /**
     * 获取用户粉丝数，当天粉丝增加数，当天粉丝取关数
     *
     * @param uid
     * @param dtime
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getUserFansNum(String uid, Date dtime) {
        //往后加一天
        Date etime = DateUtil.calendarDateByDays(dtime, 1);
        return vUserPostInfoMapper.getUserFansNum(uid, dtime, etime);
    }

    /**
     * 保存  直接使用实体类保存
     *
     * @param datas
     * @return
     */
    @DS(BarMysqlConfig.dsbarwrite)
    public boolean saveCalBigVData(Map<String, Object> datas) {
        boolean res = true;
        try {
            if (datas.size() > 0) {
                BigVCalDataModel model = new BigVCalDataModel();
                model.setUID((String) datas.get("uid"));
                model.setCDate((Date) datas.get("cdate"));
                model.setTotalClickNum((Integer) datas.getOrDefault("totalclicknum", 0));
                model.setClickNum((Integer) datas.getOrDefault("clicknum", 0));
                model.setTotalLikeNum((Integer) datas.getOrDefault("totallikenum", 0));
                model.setLikeNum((Integer) datas.getOrDefault("likenum", 0));
                model.setTotalSendCommentNum((Integer) datas.getOrDefault("totalsendcommentnum", 0));
                model.setSendCommentNum((Integer) datas.getOrDefault("sendcommentnum", 0));
                model.setTotalReceiveCommentNum((Integer) datas.getOrDefault("totalreceivecommentnum", 0));
                model.setReceiveCommentNum((Integer) datas.getOrDefault("receivecommentnum", 0));
                model.setTotalPostNum((Integer) datas.getOrDefault("totalpostnum", 0));
                model.setPostNum((Integer) datas.getOrDefault("postnum", 0));
                model.setTotalFansNum((Integer) datas.getOrDefault("totalfansnum", 0));
                model.setFansAddNum((Integer) datas.getOrDefault("fansaddnum", 0));
                model.setFansOffNum((Integer) datas.getOrDefault("fansoffnum", 0));
                model.setCreateTime(DateHelper.getNowDate());
                model.setUpdateTime(DateHelper.getNowDate());
                model.setIsDel(0);
                //保存
                res = vUserPostInfoMapper.saveCalBigVData(model.getUID(), model.getCDate(), model.getTotalClickNum(),
                        model.getClickNum(), model.getTotalLikeNum(), model.getLikeNum(), model.getTotalSendCommentNum(),
                        model.getSendCommentNum(), model.getTotalReceiveCommentNum(), model.getReceiveCommentNum(),
                        model.getTotalPostNum(), model.getPostNum(), model.getTotalFansNum(), model.getFansAddNum(),
                        model.getFansOffNum(), model.getIsDel(), model.getCreateTime(), model.getUpdateTime());
            }
        } catch (Exception e) {
            logger.error("保存大V相关数据计算结果入mysql失败，uid：{}，error：{}", datas.get("uid"), e.getMessage(), e);
        }
        return res;
    }


    @DS(BarMysqlConfig.dsbarread)
    public List<VCompanyRetModel> getVCompanyRet() {
        List<VCompanyRetModel> res = new ArrayList<>();
        res = vUserPostInfoMapper.getVCompanyRelation();
        return res;
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getCalBigVData(Date lastYear) {
        return vUserPostInfoMapper.getBigVData(lastYear);
    }

}
