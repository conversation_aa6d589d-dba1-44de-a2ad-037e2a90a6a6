package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.ProfitEntity;
import ttfund.web.communityservice.bean.jijinBar.user.FundUserProfitkafkaModel;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.ProfitMapper;

import java.util.Date;
import java.util.List;

/**
 * tb_profit 表持久层操作实体
 */
@Repository
public class ProfitDao {

    @Autowired
    private ProfitMapper profitMapper;

    @DS(BarMysqlConfig.dsbarread)
    public List<ProfitEntity> getProfitsIncrementFromMysql(String code, String uid, int batchReadCount, Date updateTime) {
        return profitMapper.getProfitsIncrementFromMysql(code, uid, batchReadCount, updateTime);
    }

    /**
     * 根据时间增量获取收益率
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<ProfitEntity> getProfitsIncrement(Date lastUpdate, int batchReadCount) {

        QueryWrapper<ProfitEntity> query = new QueryWrapper<ProfitEntity>()
                .eq("LENGTH(FCODE)", 6)
                .ge("UpdateTime", lastUpdate)
                .orderByAsc("UpdateTime")
                .last(String.format(" limit %s", batchReadCount));

        return profitMapper.selectList(query);
    }

    /**
     * 根据时间获取增量跟新的基金代码
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<ProfitEntity> getList(Date lastUpdateTime, long batchCount) {

        QueryWrapper<ProfitEntity> wrapper = new QueryWrapper<ProfitEntity>()
                .ge("UpdateTime", lastUpdateTime)
                .orderByAsc("UpdateTime")
                .last(String.format(" limit %s", batchCount));

        return profitMapper.selectList(wrapper);
    }

    /**
     * 更新用户持仓数据
     */
    @DS(BarMysqlConfig.dsbarwrite)
    public int upsertMany(List<FundUserProfitkafkaModel> list) {
        int result = 0;
        if (!CollectionUtils.isEmpty(list)) {
            result = profitMapper.upsertMany(list);
        }
        return result;
    }

    /**
     * 更新用户持仓数据
     */
    @DS(BarMysqlConfig.dsbarwrite)
    public int updateMany(List<FundUserProfitkafkaModel> list) {
        int result = 0;
        if (!CollectionUtils.isEmpty(list)) {
            result = profitMapper.updateMany(list);
        }
        return result;
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<ProfitEntity> getListByUpdateTime(Date updateTime, int batchReadCount) {
        return profitMapper.getListByUpdateTime(updateTime, batchReadCount);
    }


    @DS(BarMysqlConfig.dsbarread)
    public List<ProfitEntity> getListByUids(List<String> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return null;
        }

        return profitMapper.getListByUids(uids);
    }

}
