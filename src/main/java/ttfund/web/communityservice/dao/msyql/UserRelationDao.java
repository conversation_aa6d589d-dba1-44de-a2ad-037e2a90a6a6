package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.messagepush.UserRelationKafkaModel;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.UserRelationMapper;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * tb_userrelation表 持久层操作实体
 */
@Repository
public class UserRelationDao {

    @Autowired
    private UserRelationMapper userRelationMapper;

    /**
     * 获取用户关注数量
     */
    @DS(BarMysqlConfig.dsbarread)
    public int getUserFansCountByUserId(String userId) {
        int result = 0;
        if (StringUtils.hasLength(userId)) {
            result = userRelationMapper.getUserFansCountByUserId(userId);
        }
        return result;
    }

    /**
     * 获取用户关注数量
     */
    @DS(BarMysqlConfig.dsbarread)
    public Map<String, Integer> getUserFansCountByUserIds(List<String> userIds) {
        Map<String, Integer> result = new HashMap<>();
        if (!CollectionUtils.isEmpty(userIds)) {
            List<Map<String, Object>> list = userRelationMapper.getUserFansCountByUserIds(userIds);
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(a -> result.put((String) a.get("OBJID"), Integer.parseInt(String.valueOf(a.get("COUNT")))));
            }
        }
        return result;
    }

    /**
     * 获取用户粉丝
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<String> getUserFans(String uid){
        return userRelationMapper.getUserFans(uid);
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getUserRelationList(Date updateTime, int batchReadCount) {
        return userRelationMapper.getUserRelationList(updateTime, batchReadCount);
    }

    /**
     * 根据用户ID 获取关注列表
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<String> getUserFollowListByUserId(String userid) {
        return userRelationMapper.getUserFollowListByUserId(userid);
    }

    /**
     * 获取用户和被关注人是否相互关注
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getUserFollowRelation(List<String> userFollows, String userId) {
        return userRelationMapper.getUserFollowRelation(userFollows, userId);
    }

    /**
     * 获取用户粉丝数超过每个值的用户 增量
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getUserByFansCount(String objId, int fansCount, int batchReadCount) {
        List<Map<String, Object>> result = null;

        result = userRelationMapper.getUserByFansCount(objId, fansCount, batchReadCount);

        return result;
    }


    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getByUpdateTime(Date updateTime, int batchReadCount) {
        List<Map<String, Object>> result = null;

        result = userRelationMapper.getByUpdateTime(updateTime, batchReadCount);

        return result;
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getUserByFansCountAndObjIds(int fansCount, List<String> objIds) {
        List<Map<String, Object>> result = null;

        result = userRelationMapper.getUserByFansCountAndObjIds(fansCount, objIds);

        return result;
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getTotalFansCountByUids(List<String> uids) {
        return userRelationMapper.getTotalFansCountByUids(uids);
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getPeriodBeFollowedCountByUids(List<String> uids, Date start, Date end) {
        return userRelationMapper.getPeriodBeFollowedCountByUids(uids, start, end);
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getPeriodBeUnFollowedCountByUids(List<String> uids, Date start, Date end) {
        return userRelationMapper.getPeriodBeUnFollowedCountByUids(uids, start, end);
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getTotalFollowCountByUids(List<String> uids) {
        return userRelationMapper.getTotalFollowCountByUids(uids);
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getPeriodFollowCountByUids(List<String> uids, Date start, Date end) {
        return userRelationMapper.getPeriodFollowCountByUids(uids, start, end);
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getPeriodUnFollowCountByUids(List<String> uids, Date start, Date end) {
        return userRelationMapper.getPeriodUnFollowCountByUids(uids, start, end);
    }

    @DS(BarMysqlConfig.dsbarwrite)
    public int insertOrResume(UserRelationKafkaModel relation) {
        int result = 0;
        if (relation != null) {
            result = userRelationMapper.insertOrResume(relation);
        }
        return result;
    }

    @DS(BarMysqlConfig.dsbarwrite)
    public int disabled(UserRelationKafkaModel info)
    {
        info.IsEnabled = false;
        return userRelationMapper.update(info);
    }

    @DS(BarMysqlConfig.dsbarread)
    public Map<String, Object> getLatestUpdateTime() {
        return userRelationMapper.getLatestUpdateTime();
    }

}
