package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.data.CircleExtendInfo;
import ttfund.web.communityservice.config.appconfig.DynamicDataSourceConfig;
import ttfund.web.communityservice.mapper.community.CircleExtendInfoMapper;

@Repository
public class CircleExtendInfoDao {

    @Autowired
    private CircleExtendInfoMapper circleExtendInfoMapper;

    @DS(DynamicDataSourceConfig.DS_COMMUNITYWRITE)
    public CircleExtendInfo getBySelectForUpdate(String circleId) {
        return circleExtendInfoMapper.getBySelectForUpdate(circleId);
    }

    @DS(DynamicDataSourceConfig.DS_COMMUNITYWRITE)
    public int updateCurMembers(String circleId, Integer curMembers) {
        return circleExtendInfoMapper.updateCurMembers(circleId, curMembers);
    }

    @DS(DynamicDataSourceConfig.DS_COMMUNITYWRITE)
    public int updateCurPosts(String circleId, Integer curPosts) {
        return circleExtendInfoMapper.updateCurPosts(circleId, curPosts);
    }

}
