package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.CalBigVDataUserModel;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.WjlcVuserDo;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.dao.mongo.WjlcVuserDao;
import ttfund.web.communityservice.mapper.barread.CalBigVDataUserMapper;

import java.util.ArrayList;
import java.util.List;

/**
 * tb_calbigvdatauser表DAO
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@Repository
public class CalBigVDataUserDao {

    @Autowired
    private CalBigVDataUserMapper calBigVDataUserMapper;

    @Autowired
    private WjlcVuserDao wjlcVuserDao;

    /**
     * 获取所有有效的大V用户（从MongoDB获取）
     */
    public List<CalBigVDataUserModel> getAllActiveBigVUsers() {
        List<CalBigVDataUserModel> result = new ArrayList<>();

        try {
            // 从MongoDB的WJLCVUser集合获取大V用户
            List<WjlcVuserDo> wjlcUsers = wjlcVuserDao.getAllActiveVUsers();

            // 转换为CalBigVDataUserModel格式
            for (WjlcVuserDo wjlcUser : wjlcUsers) {
                CalBigVDataUserModel bigVUser = new CalBigVDataUserModel();
                bigVUser.setUID(wjlcUser.getUid());           // 使用uid关联数据
                bigVUser.setNickName(wjlcUser.getRemark());   // 使用remark作为NickName
                result.add(bigVUser);
            }

        } catch (Exception e) {
            // 如果MongoDB查询失败，降级到MySQL查询
            try {
                result = calBigVDataUserMapper.getAllActiveBigVUsers();
            } catch (Exception mysqlEx) {
                // 记录异常但不抛出，返回空列表
                System.err.println("获取大V用户失败，MongoDB和MySQL都不可用: " + e.getMessage() + ", " + mysqlEx.getMessage());
            }
        }

        return result;
    }

    /**
     * 根据UID获取大V用户信息
     */
    @DS(BarMysqlConfig.dsbarread)
    public CalBigVDataUserModel getBigVUserByUid(String uid) {
        return calBigVDataUserMapper.getBigVUserByUid(uid);
    }
}
