package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.caifuhao.FundManagerLivePostResultModel;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.FundManagerLivePostResultMapper;
import ttfund.web.communityservice.utils.CommonUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 基金经理直播发帖结果表tb_fund_manager_live_post_result 持久层操作实体
 */
@Repository
public class FundManagerLivePostResultDao {


    @Autowired
    private FundManagerLivePostResultMapper mapper;

    /**
     * 通过直播配置id获取
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<String> getByIdList(List<String> ids) {
        List<String> result = null;
        if (!CollectionUtils.isEmpty(ids)) {
            result = mapper.getByIdList(ids);
        }
        if (result == null) {
            result = new ArrayList<>();
        }
        return result;
    }

    /**
     * 插入多个，内部会分批
     */
    @DS(BarMysqlConfig.dsbarwrite)
    public void insertMany(List<FundManagerLivePostResultModel> list) {
        if (!CollectionUtils.isEmpty(list)) {
            List<List<FundManagerLivePostResultModel>> batchList = CommonUtils.toSmallList2(list, 50);
            for (List<FundManagerLivePostResultModel> oneBatch : batchList) {
                if (!CollectionUtils.isEmpty(oneBatch)) {
                    mapper.insertMany(oneBatch);
                }
            }
        }
    }

}
