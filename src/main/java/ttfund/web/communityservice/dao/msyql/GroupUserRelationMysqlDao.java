package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.data.GroupUserRelation;
import ttfund.web.communityservice.config.appconfig.DynamicDataSourceConfig;
import ttfund.web.communityservice.mapper.community.GroupUserRelationMysqlMapper;

import java.util.List;

@Repository
public class GroupUserRelationMysqlDao {

    @Autowired
    private GroupUserRelationMysqlMapper groupUserRelationMysqlMapper;

    @DS(DynamicDataSourceConfig.DS_COMMUNITYREAD)
    public List<GroupUserRelation> getListByGroupIds(List<Integer> groupIds) {
        return groupUserRelationMysqlMapper.getListByGroupIds(groupIds);
    }

    @DS(DynamicDataSourceConfig.DS_COMMUNITYREAD)
    public List<String> getAllUserIdsByGroupId(Integer groupId) {
        return groupUserRelationMysqlMapper.getAllUserIdsByGroupId(groupId);
    }

    @DS(DynamicDataSourceConfig.DS_COMMUNITYREAD)
    public List<GroupUserRelation> getSpecialMembersByAll(Integer groupType) {
        List<GroupUserRelation> result = null;
        if (groupType == null) {
            return result;
        }
        result = groupUserRelationMysqlMapper.getSpecialMembersByAll(groupType);
        return result;
    }

}
