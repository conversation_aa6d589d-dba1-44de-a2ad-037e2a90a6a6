package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.data.SetModule;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.SetModuleMapper;

import java.util.List;

@Repository
public class SetModuleDao {


    @Autowired
    private SetModuleMapper setModuleMapper;

    /**
     * 获取模块的位置列表
     *
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<SetModule> getDiaplayLocationList() {

        QueryWrapper<SetModule> wrapper = new QueryWrapper<>();
        wrapper.eq("State", 1);
        return setModuleMapper.selectList(wrapper);
    }

}
