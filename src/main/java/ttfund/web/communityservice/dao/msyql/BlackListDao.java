package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.guba.BlackList;
import ttfund.web.communityservice.bean.jijinBar.user.BlackListEntity;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.BlackListMapper;

import java.util.Date;
import java.util.List;

/**
 * TB_BLACKLIST表持久层操作实体
 */
@Repository
public class BlackListDao {

    @Autowired
    private BlackListMapper blackListMapper;

    @DS(BarMysqlConfig.dsbarwrite)
    public int disabled(BlackList black) {
        black.IsEnabled = false;
        return update(black);
    }

    @DS(BarMysqlConfig.dsbarwrite)
    public int update(BlackList black) {
        int result = 0;
        if (black != null) {
            result = blackListMapper.update(black);
        }
        return result;
    }

    @DS(BarMysqlConfig.dsbarwrite)
    public int insertOrUpdate(BlackList black) {
        int result = 0;
        if (black != null) {
            result = blackListMapper.insertOrUpdate(black);
        }
        return result;
    }

    /**
     * 获取用户黑名单
     *
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<BlackListEntity> getBlackListByUpdateTime(Date updateTime, int batchReadCount) {
        return blackListMapper.getBlackListByUpdateTime(updateTime, batchReadCount);
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<BlackListEntity> getBlackListRecordByUids(List<String> uidList) {
        return blackListMapper.getBlackListRecordByUids(uidList);
    }

}
