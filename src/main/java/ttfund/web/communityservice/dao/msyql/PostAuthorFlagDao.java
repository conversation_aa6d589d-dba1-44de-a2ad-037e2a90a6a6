package ttfund.web.communityservice.dao.msyql;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.kafka.common.protocol.types.Field;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.PostAuthorFlag;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumAuthorType;
import ttfund.web.communityservice.mapper.barread.PostAuthorFlagMapper;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;

import java.util.*;

@Repository
public class PostAuthorFlagDao {
    @Autowired
    private PostAuthorFlagMapper authorFlagMapper;

    /**
     * 根据更新时间获取指定数量的用户标签数据
     * @param updateTime
     * @param limit
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<PostAuthorFlag> getListByUpdateTime(Date updateTime,int limit) {
        QueryWrapper<PostAuthorFlag> wrapper = new QueryWrapper<>();
        //条件
        wrapper.ge("updatetime",updateTime);
        //排序
        wrapper.orderBy(true,true,"updatetime");
        //每页大小
        wrapper.last(String.format(" limit %s",limit));

        List<PostAuthorFlag> list=authorFlagMapper.selectList(wrapper);
        return  list;
    }

    /**
     * 根据通行证ID 获取列表
     * @param pid
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<PostAuthorFlag> getListByPid(String pid) {
        /*
        select  * from tb_postauthorflag tp  where uid ='8587025480413230' and del =0
        */
        QueryWrapper<PostAuthorFlag> wrapper = new QueryWrapper<>();
        //条件 int[] tockentypes  = {2,3,4,5};

        ArrayList<Integer> tockentypes = new ArrayList<Integer>(Arrays.asList(2,3,4,5));
        wrapper.eq("uid",pid);
        wrapper.eq("del",0);
        wrapper.in("tockentype",tockentypes);
        //排序
        wrapper.orderBy(true,true,"updatetime");
        List<PostAuthorFlag> list=authorFlagMapper.selectList(wrapper);
        return  list;
    }
    @DS(BarMysqlConfig.dsbarread)
    public List<PostAuthorFlag> getListByPidNew(String uid){
        return  authorFlagMapper.getListByPidNew(uid);
    }

    /**
     * 获取所有的配置数据
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public  List<PostAuthorFlag> getRecommendUser(){

        ArrayList<Integer> tockentypes = new ArrayList<Integer>(Arrays.asList(
                EnumAuthorType.YZTHJAuthor.getValue(),
                EnumAuthorType.AnswerHJAuthor.getValue(),
                EnumAuthorType.JPAuthor.getValue(),
                EnumAuthorType.SPAuhor.getValue(),
                EnumAuthorType.BlackUser.getValue()));
        QueryWrapper<PostAuthorFlag> wrapper = new QueryWrapper<>();
        wrapper.eq("del",0);
        wrapper.in("tockentype",tockentypes);
        List<PostAuthorFlag> list= authorFlagMapper.selectList(wrapper);
        return  list;
    }

    /**
     * 用户类型MAP
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public  Map<String, Integer> initUserTypesToDic(){
        Map<String,Integer> mapper= new HashMap<>();
        List<PostAuthorFlag> listPostAuthor= getRecommendUser();
        if(!CollectionUtils.isEmpty(listPostAuthor)){
            for (PostAuthorFlag item : listPostAuthor){
                String key = item.uid+"_"+item.tockentype;
                if (!mapper.containsKey(key)) {
                    mapper.put(key,new Integer(item.tockentype));
                }
            }
        }
        return  mapper;
    }

    /**
     * 获取所有的解盘作者
     *
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<PostAuthorFlag> getJPAuthor() {
        QueryWrapper<PostAuthorFlag> wrapper = new QueryWrapper<>();
        wrapper.eq("del", 0);
        wrapper.in("tockentype", EnumAuthorType.JPAuthor.getValue());
        return authorFlagMapper.selectList(wrapper);
    }
}
