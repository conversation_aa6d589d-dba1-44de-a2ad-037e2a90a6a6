package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.guba.PostInfoKafka;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.NotjijinPostinfoMapper;

/**
 * tb_notjijinpostinfo 表持久层操作实体
 */
@Repository
public class NotjijinPostinfoDao {

    @Autowired
    private NotjijinPostinfoMapper notjijinPostinfoMapper;

    @DS(BarMysqlConfig.dsbarwrite)
    public int disabledNotJijin(int id, Integer del) {
        PostInfoKafka post = getNotJijin(id);
        if (post == null) return 0;
        post.IsEnabled = false;
        post.Del = del;
        return updateNotJijin(post);
    }

    @DS(BarMysqlConfig.dsbarread)
    public PostInfoKafka getNotJijin(int id) {
        PostInfoKafka result = null;
        if (id > 0) {
            result = notjijinPostinfoMapper.getNotJijin(id);
        }
        return result;
    }


    @DS(BarMysqlConfig.dsbarwrite)
    public int updateNotJijin(PostInfoKafka info) {
        int result = 0;
        if (info != null) {
            result = notjijinPostinfoMapper.updateNotJijin(info);
        }
        return result;
    }

    @DS(BarMysqlConfig.dsbarwrite)
    public int resumeNotJijin(PostInfoKafka post) {
        post.IsEnabled = true;
        return updateNotJijin(post);
    }

    @DS(BarMysqlConfig.dsbarwrite)
    public int insertOrUpdateNotJijin(PostInfoKafka post) {
        int result = 0;
        if (post != null) {
            result = notjijinPostinfoMapper.insertOrUpdateNotJijin(post);
        }
        return result;
    }

    @DS(BarMysqlConfig.dsbarwrite)
    public int updateNotJijinExtend(PostInfoKafka info){
        int result = 0;
        if (info != null) {
            result = notjijinPostinfoMapper.updateNotJijinExtend(info);
        }
        return result;
    }
}
