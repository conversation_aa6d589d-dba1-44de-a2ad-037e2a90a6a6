package ttfund.web.communityservice.dao.msyql;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import ttfund.web.communityservice.bean.jijinBar.PostAuthorFlag;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.FaxianWeightModel;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.FaxianWeightModelMapper;

import java.util.ArrayList;
import java.util.List;

@Repository
public class FaxianWeightDao {
    @Autowired
    private FaxianWeightModelMapper faxianWeightModelMapper;

    /**
     * 读取配置的所有信息
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<FaxianWeightModel> getAll(){
        QueryWrapper<FaxianWeightModel> wrapper = new QueryWrapper<>();
        //条件
        wrapper.ge("IsDel",0);
        //排序
        wrapper.orderByAsc("ID");
        List<FaxianWeightModel> list=faxianWeightModelMapper.selectList(wrapper);
        return  list;
    }

    /**
     * 获取配置
     * @return
     */
    public List<FaxianWeightModel> getDefaultConfig(){

        List<FaxianWeightModel> WeightConfigModelGroupList= new ArrayList<>();
        FaxianWeightModel weightConfigModel = new FaxianWeightModel();

        weightConfigModel.setClickNum_Last3h(2.0);
        weightConfigModel.setReplyNum_Last3h ( 4.0);
        weightConfigModel.setLikeNum_Last3h ( 3.0);
        weightConfigModel.setClickNum_Total ( 4.0);
        weightConfigModel.setReplyNum_Total ( 3.0);
        weightConfigModel.setLikeNum_Total ( 2.0);
        weightConfigModel.setFineAuthor ( 10.0);
        weightConfigModel.setBlackAuthor ( -10.0);
        weightConfigModel.setNewPost ( 2.0);
        weightConfigModel.setOldPost ( 1.0);
        weightConfigModel.setHisBlackAuthor(-1500.0);
        weightConfigModel.setTimeDecayJiePanAuthorIncr((double)1 / 3);
        weightConfigModel.setTimeDecayJiePanAuthorDec( 0.5);
        weightConfigModel.setTimeDecayOtherAuthorFFN( 6);
        weightConfigModel.setTimeDecayOtherAuthorFFNVal( 0.6);
        weightConfigModel.setTimeDecayOtherAuthorFFN2( 14);
        weightConfigModel.setTimeDecayOtherAuthorFFN2Val( 0.4);
        weightConfigModel.setTimeDecayOtherAuthorChenShu( 0.9);
        weightConfigModel.setGroupName("A");

        WeightConfigModelGroupList.add(weightConfigModel);
        return  WeightConfigModelGroupList;
    }

}
