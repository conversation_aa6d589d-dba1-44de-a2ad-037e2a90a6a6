package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.PassportUserInfoMapper;

import java.util.List;

/**
 * tb_passport_user_info 表持久层操作实体
 */
@Repository
@Transactional
public class PassportUserInfoMysqlDao {

    @Autowired
    private PassportUserInfoMapper passportUserInfoMapper;

    /**
     * 更新财富号ID 操作
     *
     * @param passportid
     * @param caifuhaoid
     * @return
     */
    @DS(BarMysqlConfig.dsbarwrite)
    public int updatePassportUserCFHID(String passportid, String caifuhaoid) {
        return passportUserInfoMapper.updatePassportUserCFHID(passportid, caifuhaoid);
    }

    @DS(BarMysqlConfig.dsbarwrite)
    public int syncPassportUserToMySql(PassportUserInfoModelNew userInfo) {
        return passportUserInfoMapper.syncPassportUserToMySql(userInfo);
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<PassportUserInfoModelNew> queryPassportUserByRegisterTime(PassportUserInfoModelNew userInfo, int size) {
        return passportUserInfoMapper.queryPassportUserByRegisterTime(userInfo, size);
    }

}
