package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.QA.AnswerEntity;
import ttfund.web.communityservice.bean.jijinBar.post.QA.AnswerExtensionModel;
import ttfund.web.communityservice.bean.jijinBar.post.QA.AnswerModel;
import ttfund.web.communityservice.bean.jijinBar.post.QA.FundAnswerInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.QA.PingZhongADTEntity;
import ttfund.web.communityservice.bean.jijinBar.post.QA.QuestionAnswerAcceptCount;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.enums.AdoptTypeEnum;
import ttfund.web.communityservice.mapper.barread.AnswerMapper;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class AnswerDao {
    @Autowired
    AnswerMapper answerMapper;

    /**
     * 根据回答ID 回去回答信息
     *
     * @param aid
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public AnswerModel get(String aid) {
        return answerMapper.get(aid);
    }

    /**
     * 获取基金吧问答 回答消息
     *
     * @param qid             问题ID
     * @param isAdopted       是否被采纳，1：采纳，0：未采纳
     * @param isEnable        是否有效 1：有效，0 无效
     * @param auditStatusType 审核状态：1审核，2：通不过
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<FundAnswerInfoModel> getList(String qid, int isAdopted, int isEnable, int auditStatusType) {
        List<FundAnswerInfoModel> result = null;
        if (StringUtils.hasLength(qid)) {
            result = answerMapper.getList(qid, isAdopted, isEnable, auditStatusType);
        }
        return result;
    }

    /**
     * 读取基金答扩展信息
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<AnswerExtensionModel> getFundAnswerExtensionList(int pageIndex, Date updateTime, int batchReadCount) {
        List<AnswerExtensionModel> dataList = answerMapper.getFundAnswerExtensionList(pageIndex, updateTime, batchReadCount);
        if (!CollectionUtils.isEmpty(dataList)) {
            for (AnswerExtensionModel item : dataList) {
                item.set_id(item.getQID() + item.getAID());
            }
        }
        return dataList;
    }

    /**
     * 读取基金回答信息
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<FundAnswerInfoModel> getFundAnswerList(Date updateTime, int batchReadCount) {
        List<FundAnswerInfoModel> dataList = answerMapper.getFundAnswerList(updateTime, batchReadCount);
        return dataList;
    }

    /**
     * 统计问题的回答数
     */
    @DS(BarMysqlConfig.dsbarread)
    public Map<String, Integer> getQuestionAnswerCount(List<String> qIdList) {
        List<Map<String, Object>> dataList = answerMapper.getQuestionAnswerCount(qIdList);
        Map<String, Integer> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            map = new HashMap<>(dataList.size() * 4 / 3 + 1);
            for (Map<String, Object> item : dataList) {
                map.put((String)item.get("QID"), Integer.parseInt(item.get("AnswerCount").toString()));
            }
        }
        return map;
    }

    /**
     * 根据问题ID，获取回答采纳统计
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getAcceptCount(Date updateTime, int batchReadCount) {
        return answerMapper.getAcceptCount(updateTime, batchReadCount);
    }

    /**
     * 设置审核状态
     */
    @DS(BarMysqlConfig.dsbarwrite)
    public int setAuditState(String aid, int state, boolean isenable) {
        int result = 0;
        if (StringUtils.hasLength(aid)) {
            result = answerMapper.setAuditState(aid, state, isenable);
        }
        return result;
    }

    @DS(BarMysqlConfig.dsbarwrite)
    public int insertOrUpdate(AnswerEntity answer) {
        int result = 0;
        if (answer != null) {
            result = answerMapper.insertOrUpdate(answer);
        }
        return result;
    }

    /**
     * 被动设置最佳答案
     */
    @DS(BarMysqlConfig.dsbarwrite)
    public int setPassiveBestAnswer(List<String> aids) {
        int result = 0;
        if (!CollectionUtils.isEmpty(aids)) {
            result = answerMapper.setAdopedType(aids, AdoptTypeEnum.PASSIVE.getValue());
        }
        return result;
    }

    /**
     * 主动设置最佳答案
     */
    @DS(BarMysqlConfig.dsbarwrite)
    public int setActiveBestAnswer(List<String> aids) {
        int result = 0;
        if (!CollectionUtils.isEmpty(aids)) {
            result = answerMapper.setAdopedType(aids, AdoptTypeEnum.ACTIVE.getValue());
        }
        return result;
    }

    /**
     * 根据更新时间获取指定数量的回答
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<PingZhongADTEntity> getListByUpdateTime(Date updateTime, int limit) {
        return answerMapper.getListByUpdateTime(updateTime, limit);
    }

    /**
     * 根据问题ID
     */
    @DS(BarMysqlConfig.dsbarread)
    public List<QuestionAnswerAcceptCount> getAnswerCount(List<String> listQids) {
        List<QuestionAnswerAcceptCount> result = null;
        if (!CollectionUtils.isEmpty(listQids)) {
            result = answerMapper.getAnswerCount(listQids);
        }
        return result;
    }

    /**
     * 根据帖子ID 获取回答消息
     *
     * @param postId
     * @return
     */
    @DS(BarMysqlConfig.dsbarread)
    public AnswerModel getByPostId(String postId) {
        return answerMapper.getByPostId(postId);
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getListOfAnswerWithQuestionByArticleIds(String selectFields, List<Long> articleIds) {
        List<Map<String, Object>> result = null;
        if (CollectionUtils.isEmpty(articleIds)) {
            return result;
        }

        result = answerMapper.getListOfAnswerWithQuestionByArticleIds(selectFields, articleIds);
        return result;
    }

    @DS(BarMysqlConfig.dsbarread)
    public List<Map<String, Object>> getListByQids(String selectFields, List<String> qids) {
        List<Map<String, Object>> result = null;
        if (CollectionUtils.isEmpty(qids)) {
            return result;
        }

        result = answerMapper.getListByQids(selectFields, qids);
        return result;
    }
}
