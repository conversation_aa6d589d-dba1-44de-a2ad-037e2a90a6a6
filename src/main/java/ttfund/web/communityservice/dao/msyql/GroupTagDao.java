package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.data.GroupTagDto;
import ttfund.web.communityservice.config.appconfig.DynamicDataSourceConfig;
import ttfund.web.communityservice.mapper.community.GroupTagMapper;

import java.util.List;

/**
 *
 */
@Repository
public class GroupTagDao {

    @Autowired
    private GroupTagMapper groupTagMapper;

    @DS(DynamicDataSourceConfig.DS_COMMUNITYREAD)
    public List<GroupTagDto> getListByGroupIdsAndSystemTypes(List<Integer> groupIds, List<Integer> systemTypes) {
        List<GroupTagDto> result = null;
        if (CollectionUtils.isEmpty(groupIds) || CollectionUtils.isEmpty(systemTypes)) {
            return result;
        }
        result = groupTagMapper.getListByGroupIdsAndSystemTypes(groupIds, systemTypes);
        return result;
    }

}
