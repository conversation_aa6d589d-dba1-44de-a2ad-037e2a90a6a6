package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.config.HotpostWeightConfigModel;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.HotPostWeightConfigMapper;

import java.util.List;

/**
 * tb_hotpost_weight_config表 持久层操作实体
 */
@Repository
public class HotPostWeightConfigDao {

    @Autowired
    private HotPostWeightConfigMapper hotPostWeightConfigMapper;


    @DS(BarMysqlConfig.dsbarread)
    public List<HotpostWeightConfigModel> getAll() {
        return hotPostWeightConfigMapper.getAll();
    }

}
