package ttfund.web.communityservice.dao.msyql;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.favor.UserFavorBean;
import ttfund.web.communityservice.bean.jijinBar.PostAuthorFlag;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumAuthorType;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.config.dataconfig.FavorMysqlConfig;
import ttfund.web.communityservice.mapper.barread.PostAuthorFlagMapper;
import ttfund.web.communityservice.mapper.favor.UserFavorMapper;

import java.util.*;

@Repository
public class UserFavorDao {

    @Autowired
    private UserFavorMapper userFavorMapper;

    /**
     * 获取自选基金
     *
     * @param table
     * @param userId
     * @param fundCode
     * @param size
     * @param updateTime
     * @return
     */
    @DS(FavorMysqlConfig.FAVOR_READ_CONNECTION)
    public List<UserFavorBean> getUserFavorFund(String table, String userId, String fundCode, int size, String updateTime) {
        return userFavorMapper.getUserFavorFund(table, userId, fundCode, size, updateTime);
    }

    /**
     * 获取自选组合
     *
     * @param userId
     * @param code
     * @param size
     * @param updateTime
     * @return
     */
    @DS(FavorMysqlConfig.FAVOR_READ_CONNECTION)
    public List<UserFavorBean> getUserFavorCombine(String userId, String code, int size, String updateTime) {
        return userFavorMapper.getUserFavorCombine(userId, code, size, updateTime);
    }

    /**
     * 获取自选投顾
     *
     * @param userId
     * @param code
     * @param size
     * @param updateTime
     * @return
     */
    @DS(FavorMysqlConfig.FAVOR_READ_CONNECTION)
    public List<UserFavorBean> getUserFavorInvest(String userId, String code, int size, String updateTime) {
        return userFavorMapper.getUserFavorInvest(userId, code, size, updateTime);
    }
}
