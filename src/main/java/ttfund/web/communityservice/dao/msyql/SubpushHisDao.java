package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.data.SubPushHis;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.SubpushHisMapper;

/**
 * tb_subpush_his持久层操作实体
 */
@Repository
public class SubpushHisDao {

    @Autowired
    private SubpushHisMapper subpushHisMapper;

    /**
     * 插入数据
     * @param model
     */
    @DS(BarMysqlConfig.dsbarwrite)
    public void insert(SubPushHis model) {
        if (model != null) {
            subpushHisMapper.insert(model);
        }
    }

}
