package ttfund.web.communityservice.dao.msyql;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.guba.PostInfoExtendKafka;
import ttfund.web.communityservice.config.dataconfig.BarMysqlConfig;
import ttfund.web.communityservice.mapper.barread.PostInfoExtendMysqlMapper;

@Repository
public class PostInfoExtendMysqlDao {

    @Autowired
    private PostInfoExtendMysqlMapper postInfoExtendMysqlMapper;

    @DS(BarMysqlConfig.dsbarwrite)
    public int insertOrUpdate(PostInfoExtendKafka postExtend) {
        int result = 0;
        if (postExtend != null) {
            result = postInfoExtendMysqlMapper.insertOrUpdate(postExtend);
        }
        return result;
    }

    @DS(BarMysqlConfig.dsbarwrite)
    public int insertOrUpdateF3(PostInfoExtendKafka postExtend) {
        int result = 0;
        if (postExtend != null) {
            result = postInfoExtendMysqlMapper.insertOrUpdateF3(postExtend);
        }
        return result;
    }

}
