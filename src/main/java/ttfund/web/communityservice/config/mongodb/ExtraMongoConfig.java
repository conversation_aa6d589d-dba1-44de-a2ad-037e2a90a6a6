package ttfund.web.communityservice.config.mongodb;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;


/**
 * 基金经理大V MongoDb
 */
@Configuration
@ConfigurationProperties(prefix = BarMongodbConfig.extra_mongo_conn)
@Component
public class ExtraMongoConfig extends AbstractMongoConfig {
    @Bean(name = BarMongodbConfig.extra_mongo_conn_beanname)
    @Override
    public MongoTemplate getMongoTemplate() throws Exception {
        return new MongoTemplate(mongoDbFactory(),getConverter());
    }

}