package ttfund.web.communityservice.config.mongodb;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;


/**
 * appinfo  mongdb 新闻资讯
 */
@Configuration
@ConfigurationProperties(prefix = BarMongodbConfig.appinfo_mongo_conn)
@Component
public class AppinfoMongoConfig extends AbstractMongoConfig {
    @Bean(name = BarMongodbConfig.appinfo_mongo_conn_beanname)
    @Override
    @RefreshScope
    public MongoTemplate getMongoTemplate() throws Exception {
        return new MongoTemplate(mongoDbFactory(),getConverter());
    }

}