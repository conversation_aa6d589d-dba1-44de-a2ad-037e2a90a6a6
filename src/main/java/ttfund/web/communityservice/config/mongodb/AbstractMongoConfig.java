package ttfund.web.communityservice.config.mongodb;

import com.mongodb.client.MongoClients;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post.VideoArticleJob;
import ttfund.web.communityservice.utils.StringUtil;

@Data
public abstract class AbstractMongoConfig {

    private String logpre = "[AbstractMongoConfig]=>";
    private static final Logger logger = LoggerFactory.getLogger(AbstractMongoConfig.class);
    /**
     * mongdb链接串
     */
    private String uri;
    /**
     * 数据库名
     */
    private String database;

    /*
     * Method that creates MongoDbFactory Common to both of the MongoDb
     * connections
     */
    public MongoDatabaseFactory mongoDbFactory() throws Exception {
//        logger.info(logpre+"初始化mongdb链接  【url】="+uri+",【database】="+database);
        if (StringUtil.isNull(database)) {
            return new SimpleMongoClientDatabaseFactory(uri);

        } else {
            return new SimpleMongoClientDatabaseFactory(MongoClients.create(uri), database);
        }
    }

    /*
     * Factory method to create the MongoTemplate
     */
    abstract public MongoTemplate getMongoTemplate() throws Exception;

    /**
     * remove _class
     * @return
     * @throws Exception
     */
    public MappingMongoConverter getConverter() throws Exception {
        //remove _class
        MappingMongoConverter converter =
                new MappingMongoConverter(mongoDbFactory(), new MongoMappingContext());
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        return  converter;
    }

}
