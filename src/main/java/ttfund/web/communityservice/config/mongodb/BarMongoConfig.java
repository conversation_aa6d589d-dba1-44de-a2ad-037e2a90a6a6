package ttfund.web.communityservice.config.mongodb;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

/**
 * 基金吧只读链接
 */
@Configuration
@ConfigurationProperties(prefix = BarMongodbConfig.bar_mongo_conn)
@Component
public class BarMongoConfig extends AbstractMongoConfig {
    @Bean(name = BarMongodbConfig.bar_mongo_conn_beanname)
    @Override
    public MongoTemplate getMongoTemplate() throws Exception {
        return new MongoTemplate(mongoDbFactory(),getConverter());
    }
}
