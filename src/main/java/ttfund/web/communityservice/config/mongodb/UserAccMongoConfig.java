package ttfund.web.communityservice.config.mongodb;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.config.dataconfig.UserAccMongoConstantConfig;

@Configuration
@ConfigurationProperties(prefix = UserAccMongoConstantConfig.USER_ACC_MONGO_CONNECT)
@Component
public class UserAccMongoConfig extends AbstractMongoConfig {

    @Bean(name = UserAccMongoConstantConfig.USER_ACC_MONGO_BEAN_NAME)
    @Override
    public MongoTemplate getMongoTemplate() throws Exception {
        return new MongoTemplate(mongoDbFactory(), getConverter());
    }
}
