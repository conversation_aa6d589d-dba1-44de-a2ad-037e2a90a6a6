package ttfund.web.communityservice.config.redis;

/**
 * 基金吧 Redis key
 */
public class BarRedisKey {

    /**
     * 吧信息
     */
    public static String asp_net_service_fundBarInfo_barcode = "Asp_Net_Service_FundBarInfo_%s";

    /**
     * 热帖速递
     */
    public static final String HOT_POST_EXPRESS = "HOT_POST_EXPRESS";

    /**
     * 理财页-发现-热帖
     */
    public static final String FINANCE_FIND_HOT_POST = "FINANCE_FIND_HOT_POST_";

    /**
     * 解盘作者
     */
    public static final String ANALYSIS_MARKET_AUTHOR = "bar_service_java_analysis_market_author";

    // 全量更新数据中最大的eseqid
    public static final String FULL_PUSH_KEYWORD_MAX_ESEQID = "FULL_PUSH_KEYWORD_PRE_MAX_ESEQID";

    /**
     * 一周内异动基金
     */
    public static final String ABNORMAL_ACTION_FUND_LIST = "bar_service_java_abnormal_action_fund_list";

    // 增量更新数据中最大的eseqid
    public static final String INCR_PUSH_KEYWORD_MAX_ESEQID = "INCR_PUSH_KEYWORD_PRE_MAX_ESEQID";

    // 上一次全量更新时间
    public static final String PRE_FULL_WRITE_TIME = "PRE_FULL_WRITE_TIME";

    // 上一次增量更新时间
    public static final String PRE_INCR_WRITE_TIME = "PRE_INCR_WRITE_TIME";

    /**
     * 帖子详情（新）
     */
    public static final String FUND_GUBA_SERVICE_POSTINFO_DETAIL_NEW = "fund_guba_service_postinfo_detail_";

    /**
     * 大V 基金公司关系 key
     */
    public static final String BIG_V_COMPANY_RELATION = "bigV_company_relation_list";

    /**
     * 直播中的大咖秀列表 List<string>
     */
    public static final String VIDEO_ARTICLE_LIVE_LIST = "fund_news_service_videoarticlelivelist";

    /**
     * 近一周热帖缓存
     */
    public static final String HOT_POST_WEEK = "fund_guba_service_hotpost_week";

    /**
     * 热门帖子小程序缓存
     */
    public static final String HOT_POST_SMALL_APP = "fund_guba_service_hotpostsmallapp";

    /**
     * 定投日记话题下帖子id
     */
    public static final String FIXED_INVESTMENT_TOPIC = "fixed_investment_post_ids_";

    public static final String FUND_GUBA_SERVICE_POST_COUNT = "fund_guba_service_postcount_%s";

    /**
     * 单品吧帖子新（按基金代码分组）
     */
    public static final String FUND_GUBA_SERVICE_FUND_POST_INFO_POST_LIST_LIST_NEW = "fund_guba_service_fundpostinfo_postlist_%s";

    /**
     * 基金吧帖子统计
     */
    public static final String FUND_POST_COUNT_INFO = "fund_post_count_%s";

    /**
     * 单品吧活动卡片配置-新版
     */
    public static final String FUND_BAR_ACTIVITY_CARD_CONFIG = "fund_bar_activity_card_config";

    /**
     * 圈子活动卡片配置缓存-新版
     */
    public static final String CIRCLE_ACTIVITY_CARD_CONFIG = "circle_activity_card_config";

    /**
     * 单品吧底部公告位配置
     */
    public static final String FUND_BAR_ANNOUNCEMENT_CONFIG = "fund_bar_announcement_config";

    /**
     * 用户个人主页帖子列表（按照用户分组）
     */
    public static final String FUND_GUBA_SERVICE_USER_POST_INFO_POST_LIST_NEW = "fund_guba_service_userpostinfo_postlist_%s";

    /**
     * 三方话题
     */
    public static final String FUND_GUBA_SERVICE_COMPETIOR_TOPIC = "competior_topic_%s_%s";

    /**
     * 优质贴分发吧
     */
    public static final String HIGH_QUALITY_POST_DISTRIBUTE_BAR = "HighQualityPostDistribute_%s";

    /**
     * 大咖秀详细信息
     */
    public static final String FUND_BAR_SERVICE_BIG_SHOT_SHOW_DETAIL = "fund_bar_service_big_shot_show_detail_%s";

    /**
     * 大咖秀列表
     */
    public static final String FUND_BAR_SERVICE_BIG_SHOT_SHOW_LIST = "fund_bar_service_big_shot_show_list";

    /**
     * 基金经理所在吧最后发帖时间
     */
    public static final String ASP_NET_SERVICE_MRGPOSTUPDATETIME_BAR = "Asp_Net_Service_MrgPostUpdateTime_%s";

    /**
     * 用户黑名单kafka列表
     */
    public static final String FUND_GUBA_SERVICE_USERBLACKLIST_KAFKA = "fund_guba_service_userblacklist_kafka_%s";

    public static final String fund_guba_service_hotpost_week = "fund_guba_service_hotpost_week";

    /**
     * 单品吧活动配置
     */
    public static final String SINGLE_ACTIVE_CONFIG = "fund_guba_service_SingleActiveConfig";

    /**
     * 帖子隐藏列表
     */
    public static final String FUND_GUBA_HIDE_POST = "Asp_Net_Service_Confg_HidePost";

    /**
     * 悬赏财富币默认展示配置
     */
    public static final String REWARD_CFB_CONFIG = "Asp_Net_Fund_Service_RewardCfbConfig";

    /**
     * 基金吧打赏财富币
     */
    public static final String REWARD_DS_CFB_CONFIG = "Asp_Net_Fund_Service_RewardQADSCfbConfig";

    /**
     * 基金吧信息缓存
     */
    public static final String FUND_BAR_INFO = "Asp_Net_Service_FundBarInfo_%s";

    public static final String FUNDFAVORSUBACCCOUNTLIST = "FundFavorSubAccCountList_%s";

    public static final String FUND_SERVICE_JIJINBA_SUBACC_TOPICTOPPOST = "fund_service_jijinba_subacc_topictoppost";

    public static final String FUND_GUBA_SERVICE_FINDRECOMMENDPOST = "fund_guba_service_findrecommendpost";

    public static final String FUND_GUBA_SERVICE_FINDRECOMMENDPOST_GROUP = "fund_guba_service_findrecommendpost_group_%s";

    /**
     * 基金吧问答悬赏中的帖子
     */
    public static final String FUND_QA_REWARDDING_COUNT = "Asp_Net_Service_FundQARewarddingCount_%s";

    /**
     * 基金吧品种动态缓存
     */
    public static final String FUND_BAR_DETAIL_DYNAMIC_COUNT = "Asp_Net_Service_FundJJBPZDTCount_%s";

    /**
     * 股吧帖子置顶列表
     */
    public static final String ASP_NET_SERVICE_GUBA_SETTOP = "Asp_Net_Service_guba_settop";

    /**
     * 基金官方号缓存信息
     */
    public static final String ASP_NET_FUND_OPERATIONALUSERINFO = "Asp_Net_Fund_OperationalUserInfo";

    /**
     * 基金吧帖子相关统计
     */
    public static final String ZHB_POST_COUNT = "fund_service_zhbpostcount_";

    /**
     * 关键字
     */
    public static final String KEYWORD_TYPE = "fund_guba_service_keywordtype_";

    /**
     * 稳健理财专区-理财同路人-最热
     */
    public static final String PRUDENT_FINANCE_HOT_POST = "PRUDENT_FINANCE_HOT_POST_";

    /**
     * 稳健理财专区-理财同路人-最新
     */
    public static final String PRUDENT_FINANCE_LAST_POST = "PRUDENT_FINANCE_LAST_POST_";

    /**
     * 活动推广配置缓存  帖子广告
     */
    public static final String FUND_GUBA_SERVICE_ACTIVITYCONFIG_UNIQUE = "fund_guba_service_activityconfig_unique";

    /**
     * 活动推广配置缓存  资讯广告
     */
    public static final String FUND_GUBA_SERVICE_ACTIVITYCONFIG_UNIQUE_NEWS = "fund_guba_service_activityconfig_unique_news";

    /**
     * 活动推广配置缓存  全部广告
     */
    public static final String FUND_GUBA_SERVICE_ACTIVITYCONFIG_UNIQUE_ALL = "fund_guba_service_activityconfig_unique_all";

    /**
     * 帖子正文页/话题banner广告位屏蔽配置
     */
    public static final String HIDEACTIVITYCONFIG_ALL = "HideActivityConfig_all";

    //推荐模块
    public static final String FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND = "fund_guba_service_community_recommend";

    // 自定义模块
    public static final String FUND_GUBA_SERVICE_COMMUNITY_USERDEFINED = "fund_guba_service_community_userdefined";

    // 热门话题
    public static final String FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_TOPIC = "fund_guba_service_community_recommend_topic";

    // 热门话题
    public static final String FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_TOPIC_DEFAULT = "fund_guba_service_community_recommend_topic_default";

    // 人气用户
    public static final String FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_HOTUSER = "fund_guba_service_community_recommend_hotuser";

    // 人气用户
    public static final String FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_HOTUSER_DEFAULT = "fund_guba_service_community_recommend_hotuser_default";

    // 热门基金吧
    public static final String FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_HOTBAR = "fund_guba_service_community_recommend_hotbar";

    // 热门基金吧
    public static final String FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_HOTBAR_DEFAULT = "fund_guba_service_community_recommend_hotbar_default";

    // 自定义模块明细列表
    public static final String FUND_GUBA_SERVICE_COMMUNITY_USERDEFINED_DETAILS = "fund_guba_service_community_userdefined_details";

    // 定投页大v
    public static final String FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_BIGV = "fund_guba_service_community_recommend_bigv";

    public static final String FUND_GUBA_SERVICE_SETTIPS = "fund_guba_service_settips";

    public static final String FUND_GUBA_SERVICE_POSTTOP = "fund_guba_service_posttop";

    /**
     * 基金吧帖子置顶
     */
    public static final String FUND_GUBA_SERVICE_FUNDBARTOP = "fund_guba_service_fundbartop_";

    /**
     * 社区-发现页-对应位置推荐帖子
     */
    public static final String FUND_SERVICE_RECOMMEND_FAXIAN_POST = "fund_guba_service_community_recommend_post";

    /**
     * 社区-今日精华帖
     */
    public static final String FUND_SERVICE_TODAY_ELITE_POST = "fund_service_today_elitepost";

    /**
     * 定投页 置顶帖
     */
    public static final String FUND_GUBA_SERVICE_DINGTOUTOP = "fund_guba_service_dingtoutop";

    /**
     * 自选推荐帖
     */
    public static final String FUND_SERVICE_FAVOR_RECOMMEND_POST = "fund_guba_service_favor_recommend_post";

    /**
     * 基金吧问答问题采纳数  {0}：为QID(问题ID)
     */
    public static final String QA_ACCEPT_COUNT = "Asp_Net_Service_QA_Accept_Count_{0}";


    /**
     * 模拟组合管理人关注榜
     */
    public static final String MODEL_PORTFOLIO_MANAGER_FANS_RANK = "ModelPortfolioManagerFansRank";

    //优秀答主榜单
    public static final String FUND_GUBA_SERVICE_EXCELLENTANSWERERRANK = "fund_guba_service_ExcellentAnswererRank";

    //帖子艾特我推送次数 一天中
    public static final String POST_AT_ME_PUSH_COUNT = "post_at_me_push_count_%s";

    /**
     * 圈子帖子列表-全部
     */
    public static final String CIRCLE_POST_LIST_ALL = "circle_post_list_all";

    /**
     * 圈子帖子列表-精华贴
     */
    public static final String CIRCLE_POST_LIST_TAG = "circle_post_list_tag_%s";

    /**
     * 圈子帖子列表-精华贴  全部
     */
    public static final String CIRCLE_POST_LIST_TAG_ALL = "circle_post_list_tag";

    //智能摘要发评论job  --通过艾特召唤  记录评论处理
    public static final String AI_SUMMARY_BY_AT = "ai_summary_by_at_%s";

    /**
     * 话题洞见推送job 推送记录
     */
    public static final String TOPICINSIGHTPUSHJOB_RECORD = "TopicInsightPushJob_record_%s_%s";

    /**
     * 理财页标签列表
     */
    public static final String FINANCING_PAGE_LABEL_LIST = "financing_page_label_list";

    /**
     * 理财页标签下帖子集合
     */
    public static final String FINANCING_PAGE_POST_OF_LABEL = "financing_page_post_of_label_%s_";

    /**
     * 圈子配置
     */
    public static final String CIRCLE_CONFIG = "circle_config";

    /**
     * 圈子内用户定制标签
     */
    public static final String CIRCLE_USER_CUSTOM_TAG = "circle_user_custom_tag_%s";

    /**
     * 发现页帖子源-按通道
     */
    public static final String FIND_PAGE_SOURCE_POSTS = "find_page_source_posts_%s";

    /**
     * 发现页资讯源
     */
    public static final String FIND_PAGE_SOURCE_NEWS = "find_page_source_news";

    /**
     * 视频帖隐藏
     */
    public static final String VIDEO_POST_HIDE_LOCATION = "video_post_hide_location_%s";

    /**
     * 图片中的基金
     */
    public static final String POST_IMAGE_FUNDS = "post_image_funds_%s";

    /**
     * 关联组合的帖子
     */
    public static final String POST_WITH_PORTFOLIO = "post_with_portfolio";

    /**
     * 知识星球最新帖子
     */
    public static final String KNOWLEDGE_PLANET_LATEST_POST = "knowledge_planet_latest_post";

    /**
     * 知识星球最新帖子 精华
     */
    public static final String KNOWLEDGE_PLANET_LATEST_ELITE_POST = "knowledge_planet_latest_elite_post";

    /**
     * 发帖数最多的用户列表
     */
    public static final String USER_POST_TOP = "user_post_top";

    /**
     * 用户白名单(财富币发放无上限)
     */
    public static final String FUND_GUBA_SERVICE_QAWHITELIST = "fund_guba_service_qawhitelist";

    /**
     * 单品吧首页无问答推荐
     */
    public static final String FUND_GUBA_SERVICE_QUESTIONCONFIGS = "fund_guba_service_questionconfigs";

    /**
     * 知识星球提问贴是否被回答
     */
    public static final String KNOWLEDGE_PLANET_QUESTION_BEEN_ANSWERED = "knowledge_planet_question_been_answered_%s";

}
