package ttfund.web.communityservice.config.kafka;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import ttfund.web.communityservice.config.appconfig.AppConstant;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 股吧帖子相关kafka配置   新地址  浦江
 */
@Configuration
public class GubaKafkaPostConfigNewPJ {


    @Autowired
    AppConstant appConstant;


    /**
     * 生产者的配置
     **/
    public Map<String, Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();
        // 集群的服务器地址
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, appConstant.gubarpost_kafka_bootstrap_servers_new_pj);
        //  消息缓存
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 40960);
        // 生产者空间不足时，send()被阻塞的时间，默认60s
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 6000);
        // 生产者重试次数
        props.put(ProducerConfig.RETRIES_CONFIG, 0);
        // 指定ProducerBatch（消息累加器中BufferPool中的）可复用大小
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 4096);
        // 生产者会在ProducerBatch被填满或者等待超过LINGER_MS_CONFIG时发送
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        // key 和 value 的序列化
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");

        return props;
    }


    /**
     * @description: 生产者工厂
     **/

    public ProducerFactory<String, String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }

    /**
     * @description: 消费者配置
     **/
    public Map<String, Object> consumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, appConstant.gubarpost_kafka_bootstrap_servers_new_pj);

        // key 和 value 的反序列化
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        return props;
    }


    /**
     * @description: 消费者工厂
     **/
//    @Bean
    public ConsumerFactory<String, String> consumerFactory() {
        return new DefaultKafkaConsumerFactory<>(consumerConfigs());
    }

    /**
     * @description: kafka 监听容器工厂
     **/
    @Bean(name = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_pj)
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        // 设置消费者工厂
        factory.setConsumerFactory(consumerFactory());
        return factory;
    }

    /**
     * @description: 消费者配置
     **/
    public Map<String, Object> consumerConfigsForPostClickMsgJob() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, appConstant.gubarpost_kafka_bootstrap_servers_new_pj);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 100);

        // key 和 value 的反序列化
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        return props;
    }

    /**
     * 帖子动效数量同步job 定制配置
     **/
    public Properties consumerConfigsForPostClickMsgJobNew() {
        Properties props = new Properties();
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");

        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, appConstant.gubarpost_kafka_bootstrap_servers_new_pj);
        // 消费者组
        props.put(ConsumerConfig.GROUP_ID_CONFIG, appConstant.kafkaConsumerGroupIdPostclickmsgjob);
        // key 和 value 的反序列化
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");

        //指定是否自动提交偏移
        props.put("enable.auto.commit", false);

        //指定消费者两次poll的最大时间间隔，否则会认为该消费者消费能力不行，踢出消费者组
        props.put("max.poll.interval.ms", 24 * 3600 * 1000);

        //指定单次最多拉取数量
        //props.put("max.poll.records", 500);

        //指定消费者最大心跳间隔
        props.put("session.timeout.ms", 10000);

        return props;
    }

    /**
     * @description: kafka 监听容器工厂
     **/
    @Bean(name = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_pj_ForPostClickMsgJob)
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> kafkaListenerContainerFactoryForPostClickMsgJob() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        // 设置消费者工厂
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(consumerConfigsForPostClickMsgJob()));
        return factory;
    }

}
