package ttfund.web.communityservice.config.kafka;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import ttfund.web.communityservice.config.appconfig.AppConstant;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 活动kafka配置
 */
@Configuration
public class ActivityKafkaConfig {

    @Autowired
    private AppConstant appConstant;

    public Map<String, Object> consumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, appConstant.kafkaBootstrapServersActivity);
        // key 和 value 的反序列化
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");

        return props;
    }

    public ConsumerFactory<String, String> consumerFactory() {
        return new DefaultKafkaConsumerFactory<>(consumerConfigs());
    }

    @Bean(name = KafkaConfig.KAFKA_LISTENER_CONTAINER_FACTORY_ACTIVITY)
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        // 设置消费者工厂
        factory.setConsumerFactory(consumerFactory());
        //factory.setBatchListener(true);
        return factory;
    }

    /**
     * 债基实盘大赛报名成功自动加入圈子job 定制配置
     **/
    public Properties consumerConfigsForDebenturePortfolioCompetitionAutoJoinCircleJob() {
        Properties props = new Properties();
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");

        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, appConstant.kafkaBootstrapServersActivity);
        // 消费者组
        props.put(ConsumerConfig.GROUP_ID_CONFIG, appConstant.kafkaConsumerGroupIdDebenturePortfolioCompetitionAutoJoinCircleJob);
        // key 和 value 的反序列化
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");

        //指定是否自动提交偏移
        props.put("enable.auto.commit", false);

        //指定消费者两次poll的最大时间间隔，否则会认为该消费者消费能力不行，踢出消费者组
        props.put("max.poll.interval.ms", 24 * 3600 * 1000);

        //指定单次最多拉取数量
        //props.put("max.poll.records", 500);

        //指定消费者最大心跳间隔
        props.put("session.timeout.ms", 10000);

        return props;
    }

}

