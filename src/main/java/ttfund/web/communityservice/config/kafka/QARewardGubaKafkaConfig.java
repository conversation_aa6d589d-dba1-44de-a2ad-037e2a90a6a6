package ttfund.web.communityservice.config.kafka;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import ttfund.web.communityservice.config.appconfig.AppConstant;

import java.util.HashMap;
import java.util.Map;

/**
 * 基金吧问答奖励发放信息推送给股吧
 */
@Configuration
public class QARewardGubaKafkaConfig {


    @Autowired
    AppConstant appConstant;
    /** 生产者的配置
     * @description:
     * @author: wjt
     * @date: 2022/06/02 11:01
     **/
    public Map<String, Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();
        // 集群的服务器地址
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,appConstant.GUBARQAREWARD_KAFKA_BOOTSTRAP_SERVERS);
        //  消息缓存
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 40960);
        // 生产者空间不足时，send()被阻塞的时间，默认60s
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 6000);
        // 生产者重试次数
        props.put(ProducerConfig.RETRIES_CONFIG, 0);
        // 指定ProducerBatch（消息累加器中BufferPool中的）可复用大小
        props.put(ProducerConfig.BATCH_SIZE_CONFIG,  4096);
        // 生产者会在ProducerBatch被填满或者等待超过LINGER_MS_CONFIG时发送
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        // key 和 value 的序列化
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        // 客户端id
        props.put(ProducerConfig.CLIENT_ID_CONFIG,appConstant.GUBARQAREWARD_KAFKA_PRODUCER_CLIENT_ID);
        return props;
    }


    /**
     * @description: 生产者工厂
     * @author: wjt
     * @date: 2022/06/02 11:01
     **/
    @Bean(name = KafkaConfig.kafkaProducerContainerFactory_qareward)
    public ProducerFactory<String, String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }

    /**
     * @description: KafkaTemplate
     * @author: wjt
     * @date: 2022/06/02 11:01
     **/
    @Bean(KafkaConfig.gubarqareward_kafka_temp_beanname)
    public KafkaTemplate<String, String> fundBarkafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    /**
     * @description: 消费者配置
     * @author: wjt
     * @date:2022/06/02 11:01
     **/
    public Map<String, Object> consumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,"earliest");
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,appConstant.GUBARQAREWARD_KAFKA_BOOTSTRAP_SERVERS);
        // 消费者组
        props.put(ConsumerConfig.GROUP_ID_CONFIG, appConstant.GUBARQAREWARD_KAFKA_CONSUMER_GROUP_ID);
        // key 和 value 的反序列化
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        return props;
    }


    /**
     * @description: 消费者工厂
     * @author: wjt
     * @date: 2022/06/02 11:01
     **/
    public ConsumerFactory<String, String> consumerFactory() {
        return new DefaultKafkaConsumerFactory<>(consumerConfigs());
    }

    /**
     * @description: kafka 监听容器工厂
     * @author: wjt
     * @date:2022/06/02 11:01
     **/
    @Bean(name = KafkaConfig.kafkaListenerContainerFactory_qareward)
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        // 设置消费者工厂
        factory.setConsumerFactory(consumerFactory());
        return factory;
    }
}
