//package ttfund.web.communityservice.config.elasticsearch;
//
//import org.springframework.core.convert.support.GenericConversionService;
//import org.springframework.data.elasticsearch.core.convert.MappingElasticsearchConverter;
//import org.springframework.data.elasticsearch.core.document.Document;
//import org.springframework.data.elasticsearch.core.mapping.ElasticsearchPersistentEntity;
//import org.springframework.data.elasticsearch.core.mapping.ElasticsearchPersistentProperty;
//import org.springframework.data.mapping.context.MappingContext;
//import org.springframework.lang.Nullable;
//
//public class CustomMappingEsConverter extends MappingElasticsearchConverter {
//
//    public CustomMappingEsConverter(MappingContext<? extends ElasticsearchPersistentEntity<?>, ElasticsearchPersistentProperty> mappingContext, GenericConversionService conversionService) {
//        super(mappingContext, conversionService);
//    }
//
//    @Override
//    public Document mapObject(@Nullable Object source) {
//        Document target = Document.create();
//        if (source != null) {
//            this.write(source, target);
//        }
//        target.remove("_class"); // << workaround to remove those _class field in elasticsearch
//        return target;
//    }
//}