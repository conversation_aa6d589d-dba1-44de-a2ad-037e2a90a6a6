package ttfund.web.communityservice.config.elasticsearch;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.Requests;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.client.indices.PutMappingRequest;
import org.elasticsearch.common.collect.MapBuilder;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.data.elasticsearch.ElasticsearchException;
import org.springframework.data.elasticsearch.annotations.Setting;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.geo.CustomGeoModule;
import org.springframework.data.elasticsearch.core.mapping.ElasticsearchPersistentEntity;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.data.mapping.MappingException;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import scala.util.hashing.Hashing;
import ttfund.web.communityservice.utils.BeanUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ElasticsearchExtendTemplate extends ElasticsearchRestTemplate {

    public RestClient restClient;

    public ElasticsearchRestTemplate elasticsearchRestTemplate;
    public ElasticsearchExtendTemplate(RestHighLevelClient client) {
        super(client);

        if (client != null) {
            restClient = client.getLowLevelClient();
        }
    }

    /**
     * 保存数据
     *
     * @param entity
     * @param <T>
     * @return
     */
    public <T> boolean save(T entity) throws Exception {

        Object id = BeanUtil.getByIdAnnotationValue(entity.getClass(), entity);
        if (id == null) throw new Exception("id 不能为null");
        Class<?> classz = entity.getClass();
        String indexName = this.getPersistentEntityFor(classz).getIndexName();
        String typeName = this.getPersistentEntityFor(classz).getIndexType();
        IndexQuery indexQuery = new IndexQueryBuilder()
                .withId(id.toString())
                .withObject(entity)
                .withIndexName(indexName)
                .withType(typeName).build();
        this.index(indexQuery);

        return true;

    }

    /**
     * 批量保存数据
     * @param list
     * @param classz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> boolean saveAll(List<T> list, Class<T> classz) throws Exception {

        if (CollectionUtils.isEmpty(list)) throw new Exception("list 为空");
        List<IndexQuery> queries = new ArrayList<>();
        int counter=0;
        for (T entity : list) {
            Object id = BeanUtil.getByIdAnnotationValue(classz, entity);
            if (id == null) throw new Exception("id 不能为null");

            String indexName = this.getPersistentEntityFor(classz).getIndexName();
            String typeName = this.getPersistentEntityFor(classz).getIndexType();
            IndexQuery indexQuery = new IndexQueryBuilder()
                    .withId(id.toString())
                    .withObject(entity)
                    .withIndexName(indexName)
                    .withType(typeName).build();
            queries.add(indexQuery);

            //500条 批量提交一次
            if (counter != 0 && counter % 500 == 0) {
                this.bulkIndex(queries);
                queries.clear();
            }
            counter++;
        }

        //最后再做提交数据
        int size = queries.size();
        if (size >0) {
            this.bulkIndex(queries);
        }
        return true;
    }


    /***================判断索引是否存在=========================*/

    public <T> boolean indexExistsOld(Class<T> clazz) {
        return this.indexExistsOld(this.getPersistentEntityFor(clazz).getIndexName());
    }

    public boolean indexExistsOld(String indexName) {
          GetIndexRequest request=new GetIndexRequest(indexName);
          request.indicesOptions(null);
        try {
            return this.getClient().indices().exists(request, RequestOptions.DEFAULT);
        } catch (IOException var4) {
            throw new ElasticsearchException("Error while for indexExists request: " + request.toString(), var4);
        }
    }

    /**===========================创建索引支持低版本 支持es6.x=================================**/

    /**
     * 创建索引
     * @param clazz
     * @param <T>
     * @return
     */
//    public <T> boolean createIndexOld(Class<T> clazz) {
//        return this.createIndexIfNotCreatedOld(clazz);
//    }

    /**
     * 如果不存在创建所索引，存在返回
     * @param clazz
     * @param <T>
     * @return
     */
//    private <T> boolean createIndexIfNotCreatedOld(Class<T> clazz) {
//        Boolean indexExist=this.indexExistsOld(this.getPersistentEntityFor(clazz).getIndexName());
//        if(indexExist) return  true;
//        return  this.createIndexWithSettingsOld(clazz);
//    }

    /**
     * 根据设置创建索引
     * @param clazz
     * @param <T>
     * @return
     */
//    private <T> boolean createIndexWithSettingsOld(Class<T> clazz) {
//        if (clazz.isAnnotationPresent(Setting.class)) {
//            String settingPath = ((Setting)clazz.getAnnotation(Setting.class)).settingPath();
//            if (StringUtils.hasText(settingPath)) {
//                String settings = ResourceUtilExtend.readFileFromClasspath(settingPath);
//                if (StringUtils.hasText(settings)) {
//                    return this.createIndexOld((String)this.getPersistentEntityFor(clazz).getIndexName(), settings);
//                }
//            } else {
//               // logger.info("settingPath in @Setting has to be defined. Using default instead.");
//            }
//        }
//        return this.createIndexOld((String)this.getPersistentEntityFor(clazz).getIndexName(), this.getDefaultSettingsOld(this.getPersistentEntityFor(clazz)));
//    }

    /**
     * 获取默认索引设置
     * @param persistentEntity
     * @param <T>
     * @return
     */
    private <T> Map getDefaultSettingsOld(ElasticsearchPersistentEntity<T> persistentEntity) {
        return (Map)(persistentEntity.isUseServerConfiguration() ? new HashMap() : (new MapBuilder()).put("index.number_of_shards", String.valueOf(persistentEntity.getShards())).put("index.number_of_replicas", String.valueOf(persistentEntity.getReplicas())).put("index.refresh_interval", persistentEntity.getRefreshInterval()).put("index.store.type", persistentEntity.getIndexStoreType()).map());
    }

    /**
     * 创建索引 6.x 版本
     * @param indexName
     * @param settings
     * @return
     */
    public boolean createIndexOld(String indexName, Object settings) {
        CreateIndexRequest request = new CreateIndexRequest(indexName);
        if (settings instanceof String) {
            request.settings(String.valueOf(settings), Requests.INDEX_CONTENT_TYPE);
        } else if (settings instanceof Map) {
            request.settings((Map)settings);
        } else if (settings instanceof XContentBuilder) {
            request.settings((XContentBuilder)settings);
        }

        try {
            return this.getClient().indices().create(request, RequestOptions.DEFAULT).isAcknowledged();
        } catch (IOException var5) {
            throw new ElasticsearchException("Error for creating index: " + request.toString(), var5);
        }
    }


    /**====================索引mapping========================**/


    /**
     * 根据类设置mapping
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> boolean putMappingOld(Class<T> clazz) {
        return this.putMappingOld(clazz, this.buildMapping(clazz));
    }

    public <T> boolean putMappingOld(Class<T> clazz, Object mapping) {
        return this.putMappingOld(this.getPersistentEntityFor(clazz).getIndexName(), this.getPersistentEntityFor(clazz).getIndexType(), mapping);
    }

    public boolean putMappingOld(String indexName, String type, Object mapping) {
        Assert.notNull(indexName, "No index defined for putMapping()");
        Assert.notNull(type, "No type defined for putMapping()");
        PutMappingRequest request = new PutMappingRequest(new String[]{indexName});
        if (mapping instanceof String) {
            request.source(String.valueOf(mapping), XContentType.JSON);
        } else if (mapping instanceof Map) {
            request.source((Map)mapping);
        } else if (mapping instanceof XContentBuilder) {
            request.source((XContentBuilder)mapping);
        }

        try {
            return this.getClient().indices().putMapping(request, RequestOptions.DEFAULT).isAcknowledged();
        } catch (IOException var6) {
            throw new ElasticsearchException("Failed to put mapping for " + indexName, var6);
        }
    }

}
