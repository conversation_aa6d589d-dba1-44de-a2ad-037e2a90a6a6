package ttfund.web.communityservice.config.triton;

import com.eastmoney.particle.tritoninference.TritonInferenceClient;
import com.eastmoney.particle.tritoninference.TritonInferenceClientConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class TritonInferenceConfig {

    @Value("${recall.triton.url:#{null}}")
    private String tritonUrl;

    @Bean
    public TritonInferenceClientConfig createTritonInferenceClientConfig1() {
        TritonInferenceClientConfig tritonInferenceClientConfig = new TritonInferenceClientConfig();
        tritonInferenceClientConfig.setPrimaryUrl(tritonUrl);
        return tritonInferenceClientConfig;
    }

    @Bean
    public TritonInferenceClient createTritonInferenceClient1(TritonInferenceClientConfig tritonInferenceClientConfig) {
        TritonInferenceClient tritonInferenceClient = new TritonInferenceClient();
        tritonInferenceClient.setTritonInferenceClientConfig(tritonInferenceClientConfig);
        return tritonInferenceClient;
    }
}
