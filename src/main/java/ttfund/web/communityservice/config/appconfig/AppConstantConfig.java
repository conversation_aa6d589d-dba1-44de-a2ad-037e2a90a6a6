package ttfund.web.communityservice.config.appconfig;

/**
 * @Author: liu
 * @Date: 2021/9/15 18:08
 * @Description:
 */
public class AppConstantConfig {


    /**
     * kafka 配置信息(股吧)
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS = "kafka.bootstrap.servers";
    public static final String KAFKA_CONSUMER_GROUP_ID = "kafka.consumer.group.id";
    public static final String KAFKA_PRODUCER_CLIENT_ID = "kafka_producer_client_id";
    public static final String KAFKA_CONSUMER_GROUP_ID_POST_HT_RELATION_KAFKA_MYSQL_JOB = "kafka.consumer.fundbar.group.id.PostHtRelationKafkaMysqlJob";
    public static final String KAFKA_CONSUMER_GROUP_ID_ROBOT_POST_COLLECT_KAFKA_JOB = "kafka.consumer.fundbar.group.id.PostInfoCollectJob";


    /**
     * kafka 配置信息(IM)
     */
    public static final String IM_KAFKA_BOOTSTRAP_SERVERS = "im.kafka.bootstrap.servers";
    public static final String IM_KAFKA_CONSUMER_GROUP_ID = "im.kafka.consumer.group.id";
    public static final String IM_KAFKA_PRODUCER_CLIENT_ID = "im.kafka.producer.client.id";
    public static final String IM_KAFKA_TOPIC = "im.kafka.topic";


    /**
     * IM appkey
     */
    public static final String IM_APPKEY = "im.appkey";

    /**
     * IM 发送消息地址
     */
    public static final String IM_URL = "imSendChannelCommonMessageUrl";

    /**
     * IM 频道前缀
     */
    public static final String IM_CHANNELID_PREFIX = "im.channelid.prefix";
    ;

    public static final String IM_CONTENT_TYPE = "im.content.type";
    /**
     * 调用Im的方式
     */
    public static final String IM_SEND_METHOD = "im.send.method";

    /**
     * ===============redis配置常量===============
     */
    public final static String redis_bar_connsr_write = "redis.bar.connsr.write";
    public final static String redis_api_connsr_read = "redis.api.connsr.read";
    public final static String redis_api_connsr_write = "redis.api.connsr.write";
    public final static String redis_global_connsr_read = "redis.global.connsr_read";


    /**
     * ===============基金吧elasticsearch配置  对应appolo 节点配置，为了支持多数据源===============
     */

    public final static String recommendpost_htid = "recommendpost.htid";
    public final static String recommendpost_htid_postcliknum = "recommendpost.htpostclicknum";
    /**
     * ===============redis 行情配置===============
     */
    public final static String redis_hq_connsr_read = "redis.hq.connsr_read";

    /**
     * ==============================基金吧kafka 配置=============================================
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_FundBAR = "kafka.bootstrap.fundbar.servers";
    public static final String KAFKA_BOOTSTRAP_SERVERS_FUNDBAROLD = "kafka.bootstrap.fundbarold.servers";

    /**
     * 基金吧新servers
     *
     * <AUTHOR>
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_FundBAR2 = "kafka.bootstrap.fundbar2.servers";
    public static final String KAFKA_CONSUMER_GROUP_ID_FundBAR = "kafka.consumer.fundbar.group.id";
    public static final String KAFKA_PRODUCER_CLIENT_ID_FundBAR = "kafka_producer_client_fundbar_id";

    public static final String KAFKA_CONSUMER_FUNDBAR_CUSTOMER_GROUP = "kafka.consumer.fundbar.customer.group";
    public static final String KAFKA_FUNDBAR_TASK_TYPE = "kafka.fundbar.task.type";

    /**
     * ==============================基金吧kafka 配置=============================================
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_FundProduct = "kafka.bootstrap.fundproduct.servers";
    public static final String KAFKA_CONSUMER_FUNDPRODUCT_CUSTOMER_GROUP = "kafka.consumer.fundproduct.customer.group";


    /**
     * ==============================股吧kafka配置 Post相关 ==================
     * Message_Push_FollowMe
     * Message_Push_Rely
     * Message_Push_Post
     * =============================================
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_Gubapost = "kafka.bootstrap.gubapost.servers";
    public static final String KAFKA_CONSUMER_GROUP_ID_Gubapost = "kafka.consumer.gubapost.group.id";
    public static final String KAFKA_PRODUCER_CLIENT_ID_Gubapost = "kafka_producer_client_gubapost";


    /**
     * ==============================股吧kafka配置 点赞相关 =============================================
     * Message_Push_Like
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_LIKE = "kafka.bootstrap.gubalike.servers";
    public static final String KAFKA_CONSUMER_GROUP_ID_LIKE = "kafka.consumer.gubalike.group.id";
    public static final String KAFKA_PRODUCER_CLIENT_ID_LIKE = "kafka_producer_client_gubalike";


    /**
     * ==============================股吧kafka配置 基金吧问答相关 =============================================
     * Message_Push_QA
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_QA = "kafka.bootstrap.gubaqa.servers";
    /**
     * ===============redis 新闻资讯 写===============
     */
    public final static String redis_news_connsr_write = "redis.news.connsr.write";

    /**
     * ===============redis 个性化（自选、持仓）===============
     */
    public final static String REDIS_PERSONALIZED_CONNSR_WRITE = "redis.personalized.connsr.write";


    /**
     * ===============vertica 大数据===============
     */
    public final static String VERTICA_BIGDATA_CONNSR = "vertica.bigdata.url";

    public static final String investmentadviser_redirect_url = "investmentadviser.redirect.url";

    public static final String TG_REDIRECT_URL = "tg.redirect.url";


    /**
     * 过滤的基金吧
     */
    public final static String filterCode = "zf,cfhpl,jjft";
    public final static String config_gubahostnewserver = "guba.host.new.server";

    /**
     * 基金经理动态自动发帖job每次拉取的基金经理动态数量限制。该值可以控制发帖数量，如股吧端压力大超时多，可以调小
     */
    public static final String fundManagerNewsUploadPostJob_size_limit = "fundManagerNewsUploadPostJob.size.limit";

    public static final String KAFKA_CONSUMER_GROUP_ID_QA = "kafka.consumer.gubaqa.group.id";
    public static final String KAFKA_PRODUCER_CLIENT_ID_QA = "kafka_producer_client_gubaqa";

    /**
     * ==============================股吧kafka配置 基金吧问答（万国）相关 =============================================
     * Message_Push_QA_WanGuo
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_QA_WANGUO = "kafka.bootstrap.gubaqawg.servers";
    public static final String KAFKA_CONSUMER_GROUP_ID_QA_WANGUO = "kafka.consumer.gubaqawg.group.id";
    public static final String KAFKA_PRODUCER_CLIENT_ID_QA_WANGUO = "kafka_producer_client_gubaqawg";


    /**
     * ==============================基金吧问答奖励发放信息推送给股吧 =============================================
     * Message_Push_QA_WanGuo
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_QA_REWARD = "kafka.bootstrap.gubaqareward.servers";
    public static final String KAFKA_CONSUMER_GROUP_ID_QA_REWARD = "kafka.consumer.gubaqareward.group.id";
    public static final String KAFKA_PRODUCER_CLIENT_ID_QA_REWARD = "kafka_producer_client_gubaqareward";
    /**
     * 奖励发放taskid
     */
    public static final String REWARDSTASKID = "RewardsTaskId";
    /**
     * 回退计积分taskid
     */
    public static final String REFUNDTASKID = "RefundTaskId";


    /**
     * 财富币发放接口
     */
    public static final String POINTSERVERURL = "PointServerUrl";

    /**
     * 财富币发放接口-新版
     */
    public static final String POINT_SERVER_URL_NEW = "PointServerUrlNew";

    /**
     * ==============================自选kafka配置=============================================
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_FAVOR = "kafka.bootstrap.favor.servers";
    public static final String KAFKA_CONSUMER_GROUP_ID_FAVOR = "kafka.consumer.favor.group.id";

    /**
     * 获取帖子活动配置url-雨燕配置后台（唐青山、马天荟）
     */
    public static final String POST_ACTIVITY_CONFIG_URL = "post.activity.config.url";

    /**
     * 获取帖子活动配置url-低代码平台（刘子榆、陈达昆）
     */
    public static final String POST_ACTIVITY_CONFIG_URL_NEW = "post.activity.config.url.new";

    public static final String batchPostReadCount = "batchPostReadCount";

    public static final String robotLikePushUrl = "robotLikePushUrl";

    public static final String DEAL_POST_ASYNC_SIZE_LIMIT = "deal.post.async.size.limit";


    public static final String FIND_CONFIG = "findConfig";

    /**
     * 财富号接口地址（翁一鸣）
     */
    public static final String CAI_FU_HAO_API_ADDRESS = "cai.fu.hao.api.address";

    /**
     * 获取基金码表
     */
    public static final String QUOTATION_CODE_TABLE_URL = "quotation.code.table.url";

    public static final String TOPIC_SYNC_GUBA_URL = "topic.sync.guba.url";

    public static final String TOPIC_SYNC_GUBA_QUERY_URL = "topic.sync.guba.query.url";
    /**
     * 循环中断开关（用以跳出部分服务中的循环）
     */
    public static final String CYCLIC_INTERRUPT_SWITCH = "cyclic.interrupt.switch";

    public static final String APP_HOME_POST_KEEP_DAYS = "AppHomePostKeepDays";

    public static final String APP_HOME_POST_TYPE = "AppHomePostType";

    public static final String INVESTMENT_DIARY_TOPIC = "investment.diary.topic";

    public final static String config_gubahostnew = "config.gubahostnew";

    public static final String BATCH_COUNT = "BatchCount";

    /**
     * ==============================集团kafka配置=============================================
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_BLOC = "kafka.bootstrap.bloc.servers";

    public static final String KAFKA_PRODUCER_CLIENT_ID_BLOC = "kafka.producer.id.bloc";

    public static final String BAR_ES_OPERATION_URL = "bar.es.operation.url";

    /**
     ========================== es新地址（8.6.2集群）========================================
     */
    public static final String ES_NEW_8 = "es8.url";

    public static final String ES_USERNAME = "es8.user.name";

    public static final String ES_PASSWORD = "es8.password";
    /**
     * 打赏失败退财富币任务Id
     */
    public static final String POST_REWARD_TASK_ID = "PostRewardTaskId";

    /**
     ========================== es旧版本写 开关========================================
     */
    public static final String ES_FUND_PRODUCT_SWITCH = "es6.fundproduct.switch";

    public static final String ES_FUND_BAR_SWITCH = "es6.fundbar.switch";

    public static final String ES_APP_NEWS_FOLLOW_SWITCH = "es6.app.news.follow.switch";

    /**
     * 问答分数
     */
    public static final String QA_SCORE_CONFIG = "QAScoreConfig";
    /**
     * 帖子提取图片时的图片域名白名单
     */
    public static final String WHITE_LIST_FOR_DOMAIN_NAME_POST_PIC_EXTRACT = "white.list.for.domain.name.post.pic.extract";

    /**
     * prometheus推送监控指标地址
     */
    public static final String PROMETHEUS_PUSH_METRICS_HOST = "prometheusPushMetrics.host";

    public static final String SEND_DONGDONG_TOKEN = "sendDongDong.token";

    public static final String SEND_DONGDONG_ACCOUNT = "sendDongDong.account";

    public static final String SEND_DONGDONG_HOST = "sendDongDong.host";

    public static final String SEND_TEXTMESSAGE_TOKEN = "sendTextMessage.token";

    public static final String SEND_TEXTMESSAGE_HOST = "sendTextMessage.host";

    public static final String SEND_DONGDONG_FLAG = "sendDongDong.flag";

    public static final String SEND_TEXTMESSAGE_FLAG = "sendTextMessage.flag";

    /**
     * 配置接口地址 顾新明
     */
    public static final String WEBCONFIG_API_ADDR = "webconfig.api.addr";

    public static final String HOT_FUND_BAR_CODES = "hotFundBarCodes";

    public static final String HOT_FUND_BAR_CONFIG = "hotFundBarConfig";

    /**
     * hanlp在线学习词汇
     */
    public static final String HANLP_LEARN_WORDS = "hanlp.learn.words";

    /**
     * hanlp 运行开关
     */
    public static final String HANLP_FLAG = "hanlp.flag";

    public static final String HOT_POST_CONFIG = "hotPostConfig";

    public static final String POPULARITY_RANK_CONFIG = "popularityRankConfig";

    public static final String KAFKA_CONSUMER_GROUP_ID_PASSPORTUSERINFOCFHVJOB = "kafka.consumer.group.id.PassportUserInfoCFHVJob";

    public static final String REPLY_RANK_CONFIG = "replyRankConfig";

    public static final String FIND_EXCLUDE_CODE_CONFIG = "findExcludeCodeConfig";

    public static final String TOP_POST_URL = "topPostUrl";

    public static final String HOT_TOPIC_CONFIG = "hotTopicConfig";

    public static final String RANK_POST_CONFIG = "rankPostConfig";

    public static final String FIND_RANK_POST_CONFIG = "findRankPostConfig";

    public static final String NEWS_SEARCH_WORD_API_URL = "newsSearchWordApiUrl";

    public static final String NEWS_SIGN_MEDIAS_API_URL = "newsSignMediasApiUrl";

    public static final String GUBA_FUND_TOPIC_LIST_API = "gubaFundTopicListApi";

    public static final String OPERATIONAL_USER_INFO_API = "operationalUserInfoApi";

    public static final String INTELLIGENT_SUMMARY_REPLY_UID = "intelligentSummaryReplyUid";

    public static final String INTELLIGENT_ANSWER_UID = "intelligentAnswerUid";

    public static final String KAFKA_CONSUMER_GROUP_ID_SUBACCOUNTBUSINESSJOB = "kafka.consumer.group.id.SubAccountBusinessJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_PASSPORTUSERINFOJOB = "kafka.consumer.group.id.PassportUserInfoJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_CFHAUTOREGISTERPUSHJOB = "kafka.consumer.group.id.CFHAutoRegisterPushJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_PASSPORTUSERTRADEBINDJOB = "kafka.consumer.group.id.PassportUserTradeBindJob";

    public static final String KAFKA_BOOTSTRAP_SERVERS_TRADEBIND = "kafka.bootstrap.tradebind.servers";

    public static final String KAFKA_CONSUMER_GROUP_ID_MODELPORTFOLIOINFOKAFKACONSUMER = "kafka.consumer.group.id.ModelPortfolioInfoKafkaConsumer";

    public static final String KAFKA_CONSUMER_GROUP_ID_SUBACCOUNTBUSINESSMODELPORTFOLIOJOB = "kafka.consumer.group.id.SubAccountBusinessModelPortfolioJob";

    /**
     * ==============================股吧kafka配置 Post相关 新地址 ==================
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_GUBAPOST_NEW = "kafka.bootstrap.gubapostnew.servers";

    public static final String POST_AT_ME_PUSH_COUNT_LIMIT = "postAtMePushCountLimit";

    public static final String MARKET_API_ADDRESS = "marketApiAddress";

    /**
     * ==============================股吧kafka配置 Post相关 新地址  周浦==================
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_GUBAPOST_NEW_ZP = "kafka.bootstrap.gubapostnew.zp.servers";

    /**
     * ==============================股吧kafka配置 Post相关 新地址  浦江==================
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_GUBAPOST_NEW_PJ = "kafka.bootstrap.gubapostnew.pj.servers";

    public static final String KAFKA_CONSUMER_GROUP_ID_BLACKLISTMSGJOB = "kafka.consumer.group.id.BlackListMsgJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_RELATIONMSGJOB = "kafka.consumer.group.id.RelationMsgJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_USERBLACKLISTKAFKAJOB = "kafka.consumer.group.id.UserBlacklistKafkaJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_USERRELATIONFOLLOWKAFKAJOBNEWJOB = "kafka.consumer.group.id.UserRelationFollowKafkaJobNewJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_BARRELATIONCHANGEJOB = "kafka.consumer.group.id.BarRelationChangeJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_POSTLIKEMSGJOB = "kafka.consumer.group.id.PostLikeMsgJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_POSTCLICKMSGJOB = "kafka.consumer.group.id.PostClickMsgJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_REPLYLIKEMSGJOB = "kafka.consumer.group.id.ReplyLikeMsgJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_POSTDYNAMICPERFORMANCECOUNTJOB = "kafka.consumer.group.id.PostDynamicPerformanceCountJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_FUNDTOPICBUSINESSJOB = "kafka.consumer.group.id.FundTopicBusinessJob";

    public static final String FUND_TOPIC_CONFIG = "FundTopicConfig";

    public static final String KAFKA_CONSUMER_GROUP_ID_RECOMMENDBUSINESSJOB = "kafka.consumer.group.id.RecommendBusinessJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_REPLYMSGJOB = "kafka.consumer.group.id.ReplyMsgJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_QAMSGJOB = "kafka.consumer.group.id.QAMsgJob";

    public static final String KAFKA_CONSUMER_GROUP_ID_POSTMSGJOB = "kafka.consumer.group.id.PostMsgJob";

    public static final String AI_COMMENT_OPEN = "ai.comment.open";

    public static final String AI_COMMENT_ID = "ai.comment.id";

    public static final String USER_PROFILE_WEB_API = "userProfileWebApi";

    public static final String FUNDUSERPROFITKAFKAMYSQLJOB_EXPIRE = "fundUserProfitKafkaMysqlJobExpire";

    public static final String FUNDUSERPROFITKAFKAMYSQLJOB_BATCHUIDCOUNT = "fundUserProfitKafkaMysqlJobBatchUidCount";

    public static final String FUNDUSERPROFITKAFKAMYSQLJOB_BATCHWRITECOUNT = "fundUserProfitKafkaMysqlJobBatchWriteCount";

    public static final String KAFKA_CONSUMER_GROUP_ID_FUNDUSERPROFITKAFKAMYSQLJOB = "kafka.consumer.group.id.FundUserProfitKafkaMysqlJob";

    public static final String TT_AGENT_API_ADDRESS = "ttAgentApiAddress";

    public static final String PASSPORT_API_ADDRESS_ZP_V2 = "passportApiAddressZpV2";
    public static final String PASSPORT_API_ADDRESS_PJ_V2 = "passportApiAddressPjV2";
    public static final String PASSPORT_API_ADDRESS = "passportApiAddress";
    public static final String PASSPORT_WIDGET_APP_ID = "passportWidgetAppId";
    public static final String PASSPORT_WIDGET_APP_SECRET = "passportWidgetAppSecret";

    public static final String KAFKA_CONSUMER_GROUP_ID_TOPICFOLLOWJOB = "kafka.consumer.group.id.TopicFollowJob";

    /**
     * ==============================股吧kafka配置 问答相关 新地址  周浦=============================================
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_QA_NEW_ZP = "kafka.bootstrap.gubaqa.new.zp.servers";

    /**
     * ==============================股吧kafka配置 问答相关 新地址  浦江=============================================
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_QA_NEW_PJ = "kafka.bootstrap.gubaqa.new.pj.servers";

    /**
     * 浪客接口地址
     */
    public static final String LANGKE_LVB_API_ADDRESS = "langKeLvbApiAddress";

    /**
     * 浪客接口地址
     */
    public static final String LANGKE_LVB_COOPERATION_API_ADDRESS = "langKeLvbCooperationApiAddress";


    /**
     * ==============================cms kafka配置==================
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_CMS = "kafka.bootstrap.cms.servers";

    public static final String KAFKA_CONSUMER_GROUP_ID_FINDPAGESOURCENEWSSYNCJOB = "kafka.consumer.group.id.FindPageSourceNewsSyncJob";

    /**
     * 账户服务接口地址
     */
    public static final String ACCOUNT_API_ADDRESS = "accountApiAddress";

    public static final String KAFKA_CONSUMER_GROUP_ID_MESSAGEPUSHREPLYATMEJOB = "kafka.consumer.group.id.MessagePushReplyAtMeJob";

    /**
     * ==============================活动 kafka配置==================
     */
    public static final String KAFKA_BOOTSTRAP_SERVERS_ACTIVITY = "kafka.bootstrap.activity.servers";

    public static final String KAFKA_CONSUMER_GROUP_ID_DEBENTUREPORTFOLIOCOMPETITIONAUTOJOINCIRCLEJOB = "kafka.consumer.group.id.DebenturePortfolioCompetitionAutoJoinCircleJob";

    /**
     * 智能客服问答接口
     */
    public static final String ZNKF_API_ADDRESS = "znkfApiAddress";

    /**
     * fusion接口地址
     */
    public static final String FUSION_PORTAL_API_ADDRESS = "fusionPortalApiAddress";

    public static final String KAFKA_CONSUMER_GROUP_ID_AIARTICLEINTELLIGENTINFOBYNEWSJOB = "kafka.consumer.group.id.AiArticleIntelligentInfoByNewsJob";

    /**
     * 文章智能信息 资讯 字数限制
     */
    public static final String ARTICLE_INTELLIGENT_INFO_BY_NEWS_CHAR_COUNT = "articleIntelligentInfoByNewsCharCount";

}
