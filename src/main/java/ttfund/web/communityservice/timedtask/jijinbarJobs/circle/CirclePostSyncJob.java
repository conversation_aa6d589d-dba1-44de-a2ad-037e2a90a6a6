package ttfund.web.communityservice.timedtask.jijinbarJobs.circle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.CirclePostRelation;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.msyql.CircleExtendInfoDao;
import ttfund.web.communityservice.dao.msyql.CirclePostRelationMysqlDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 圈子帖子同步job
 * 备注：    需求 #678045 【基金吧6.15】新增圈子功能
 */
@JobHandler("CirclePostSyncJob")
@Component
public class CirclePostSyncJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(CirclePostSyncJob.class);

    private static String selectFields = "ID, TITLE, UID, TYPE, CODE, CODELIST, TIME, TIMEPOINT, DEL, TTJJDEL, UPDATETIME";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private CirclePostRelationMysqlDao circlePostRelationMysqlDao;

    @Autowired
    private CircleExtendInfoDao circleExtendInfoDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint1 = null;
            String initBreakpoint2 = null;
            Integer batchReadCount1 = null;
            Integer batchReadCount2 = null;
            String circleIds = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint1 = jsonObject.getString("initBreakpoint1");
                initBreakpoint2 = jsonObject.getString("initBreakpoint2");
                batchReadCount1 = jsonObject.getInteger("batchReadCount1");
                batchReadCount2 = jsonObject.getInteger("batchReadCount2");
                circleIds = jsonObject.getString("circleIds");
            }

            if (batchReadCount1 == null) {
                batchReadCount1 = 5000;
            }

            if (batchReadCount2 == null) {
                batchReadCount2 = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint1：{}，initBreakpoint2：{}，batchReadCount1：{}，batchReadCount2：{}，circleIds：{}",
                initBreakpoint1,
                initBreakpoint2,
                batchReadCount1,
                batchReadCount2,
                circleIds
            );

            if (StringUtils.hasLength(initBreakpoint1) || StringUtils.hasLength(initBreakpoint2)) {

                if (StringUtils.hasLength(initBreakpoint1)) {
                    userRedisDao.set(UserRedisConfig.CIRCLEPOSTSYNCJOB_BREAKPOINT, initBreakpoint1, 30 * 24 * 3600L);
                    logger.info("第零步，初始化断点。initBreakpoint1：{}", initBreakpoint1);
                }

                if (StringUtils.hasLength(initBreakpoint2)) {
                    userRedisDao.set(UserRedisConfig.CIRCLEPOSTSYNCJOB_DEALCIRCLEPOSTCOUNT_BREAKPOINT, initBreakpoint2, 30 * 24 * 3600L);
                    logger.info("第零步，初始化断点。initBreakpoint2：{}", initBreakpoint2);
                }

                return ReturnT.SUCCESS;
            }

            deal(batchReadCount1);

            if (StringUtils.hasLength(circleIds)) {
                dealCirclePostCountByCircleIds(CommonUtils.toList(circleIds, ","));
            } else {
                dealCirclePostCount(batchReadCount2);
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount) {

        String breakpointName = UserRedisConfig.CIRCLEPOSTSYNCJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("第一步，读取断点。断点:{}", breakpoint);

        List<Map<String, Object>> list = postInfoNewDao.getListByUpdateTime(selectFields, breakpointDate, batchReadCount);

        logger.info("第二步，读取帖子。数量:{}，头部列表：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        Set<String> existPostIds = new HashSet<>();
        Set<String> circleIds = new HashSet<>();
        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = (Date)list.get(list.size() - 1).get("UPDATETIME");

            List<String> ids = list.stream().map(a -> a.get("ID").toString()).collect(Collectors.toList());
            List<List<String>> batchList = CommonUtils.toSmallList2(ids, 200);
            for (List<String> batch : batchList) {
                List<CirclePostRelation> tempList = circlePostRelationMysqlDao.getListByPostIds(batch);
                if (!CollectionUtils.isEmpty(tempList)) {
                    existPostIds.addAll(tempList.stream().map(a -> a.getPostId()).collect(Collectors.toList()));
                    circleIds.addAll(tempList.stream().map(a -> a.getCircleId()).collect(Collectors.toList()));
                }
            }

            list = list.stream().filter(a -> existPostIds.contains(a.get("ID").toString())).collect(Collectors.toList());

        }

        logger.info("第三步，过滤帖子。数量:{}，头部列表：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        List<CirclePostRelation> updateList = null;
        CirclePostRelation model = null;
        if (!CollectionUtils.isEmpty(list)) {
            updateList = new ArrayList<>(list.size());

            list.forEach(a -> {
                    a.put("POSTID", Long.parseLong(a.get("ID").toString()));
                    a.put("UPDATETIME", new Date());
                }
            );

            for (Map<String, Object> a : list) {
                model = new CirclePostRelation();
                model.setPostId(a.get("ID").toString());
                model.setPostTitle((String)a.get("TITLE"));
                model.setPostTimepoint((Long)a.get("TIMEPOINT"));
                model.setPostTime((Date)a.get("TIME"));
                model.setPostUid((String)a.get("UID"));
                model.setPostDel((Integer)a.get("DEL"));
                model.setPostTtjjdel((Integer)a.get("TTJJDEL"));

                model.setUpdateTime(new Date());

                updateList.add(model);
            }

            List<List<CirclePostRelation>> batchList = CommonUtils.toSmallList2(updateList, 200);
            for (List<CirclePostRelation> batch : batchList) {
                circlePostRelationMysqlDao.updateManyByPostId(batch);
            }

        }

        logger.info("第四步，写库帖子。数量:{}，头部列表：{}",

            CollectionUtils.isEmpty(updateList) ? 0 : updateList.size(),
            CollectionUtils.isEmpty(updateList) ? null : JSON.toJSONStringWithDateFormat(updateList.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(circleIds)) {
            List<Map<String, Object>> documents = circlePostRelationMysqlDao.getPostCountByCircleIds(circleIds.stream().collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(documents)) {
                for (Map<String, Object> a : documents) {
                    circleExtendInfoDao.updateCurPosts(a.get("circleId").toString(), Integer.parseInt(a.get("count").toString()));
                }
            }

            logger.info("第五步，统计圈子帖子数。数量:{}，头部列表：{}",
                CollectionUtils.isEmpty(documents) ? 0 : documents.size(),
                CollectionUtils.isEmpty(documents) ? null : JSON.toJSONStringWithDateFormat(documents.get(0), DateUtil.datePattern)
            );
        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        logger.info("第六步，更新断点。断点：{}", breakpoint);

    }

    private void dealCirclePostCount(int batchReadCount) {
        String breakpointName = UserRedisConfig.CIRCLEPOSTSYNCJOB_DEALCIRCLEPOSTCOUNT_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("dealCirclePostCount-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("dealCirclePostCount-第一步，读取断点。断点:{}", breakpoint);

        List<CirclePostRelation> list = circlePostRelationMysqlDao.getList(breakpointDate, batchReadCount);

        logger.info("dealCirclePostCount-第二步，读取圈子帖子关系。数量:{}，头部列表：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = list.get(list.size() - 1).getUpdateTime();

            List<String> circleIds = list.stream().map(a -> a.getCircleId()).distinct().collect(Collectors.toList());

            List<Map<String, Object>> documents = null;
            List<List<String>> batchList = CommonUtils.toSmallList2(circleIds, 100);
            for (List<String> batch : batchList) {
                documents = circlePostRelationMysqlDao.getPostCountByCircleIds(batch.stream().collect(Collectors.toList()));
                if (!CollectionUtils.isEmpty(documents)) {
                    for (Map<String, Object> a : documents) {
                        circleExtendInfoDao.updateCurPosts(a.get("circleId").toString(), Integer.parseInt(a.get("count").toString()));
                    }
                }

            }

            logger.info("dealCirclePostCount-第三步，统计圈子帖子数。数量:{}，头部列表：{}",
                CollectionUtils.isEmpty(circleIds) ? 0 : circleIds.size(),
                CollectionUtils.isEmpty(documents) ? null : JSON.toJSONStringWithDateFormat(documents.get(0), DateUtil.datePattern)
            );
        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        logger.info("dealCirclePostCount-第四步，更新断点。断点：{}", breakpoint);

    }

    private void dealCirclePostCountByCircleIds(List<String> circleIds) {

        logger.info("dealCirclePostCountByCircleIds-第一步，打印参数。circleIds：{}", circleIds);

        if (!CollectionUtils.isEmpty(circleIds)) {

            List<Map<String, Object>> documents = null;
            List<List<String>> batchList = CommonUtils.toSmallList2(circleIds, 100);
            for (List<String> batch : batchList) {
                documents = circlePostRelationMysqlDao.getPostCountByCircleIds(batch.stream().collect(Collectors.toList()));
                if (!CollectionUtils.isEmpty(documents)) {
                    for (Map<String, Object> a : documents) {
                        circleExtendInfoDao.updateCurPosts(a.get("circleId").toString(), Integer.parseInt(a.get("count").toString()));
                    }
                }

            }

            logger.info("dealCirclePostCountByCircleIds-第二步，统计圈子帖子数。数量:{}，头部列表：{}",
                CollectionUtils.isEmpty(circleIds) ? 0 : circleIds.size(),
                CollectionUtils.isEmpty(documents) ? null : JSON.toJSONStringWithDateFormat(documents.get(0), DateUtil.datePattern)
            );
        }

    }

}
