package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.caifuhao.CFHBriefInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.caifuhao.CFHProductReadModel;
import ttfund.web.communityservice.bean.jijinBar.post.caifuhao.SubAccDtoProReadModel;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.CFHProductReadDao;
import ttfund.web.communityservice.dao.mongo.CfhListDao;
import ttfund.web.communityservice.dao.mongo.SubAccountDtoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.enums.EnumFundType;
import ttfund.web.communityservice.enums.EnumReadType;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 财富号产品解读job
 */
@JobHandler("CFHProductReadBizJob")
@Component
public class CFHProductReadBizJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(CFHProductReadBizJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private CFHProductReadDao cfhProductReadDao;

    @Autowired
    private CfhListDao cfhListDao;

    @Autowired
    private SubAccountDtoDao subAccountDtoDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            Integer batchReadCount = null;
            String initBreakpoint = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                batchReadCount = jsonObject.getInteger("batchReadCount");
                initBreakpoint = jsonObject.getString("initBreakpoint");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("零，打印参数。batchReadCount：{}，initBreakpoint：{}",
                    batchReadCount,
                    initBreakpoint);

            if (StringUtils.hasLength(initBreakpoint)) {
                if (StringUtils.hasLength(initBreakpoint)) {
                    userRedisDao.set(UserRedisConfig.CFHPRODUCTREADBIZJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                }

                logger.info("零，初始化断点。initBreakpoint：{}",
                        initBreakpoint);

                return ReturnT.SUCCESS;
            }

            logicDeal(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;

    }

    public void logicDeal(int batchReadCount) throws Exception {

        try {
            String breakpointName = UserRedisConfig.CFHPRODUCTREADBIZJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("零，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("一，读取断点。断点:{}", breakpoint);

            //读取财富号mongo里的解读文章列表
            List<Integer> readTypes = Arrays.asList(EnumReadType.MarketAnalysis.getType(), EnumReadType.ProductAnalysis.getType()
                    , EnumReadType.MonthlyReport.getType(), EnumReadType.ProfitAndLoss.getType(), EnumReadType.Vedio.getType()
                    , EnumReadType.Audio.getType(), EnumReadType.Idea.getType(), EnumReadType.ImportantRemind.getType());
            List<CFHProductReadModel> proReadlist = cfhProductReadDao.getList(breakpointDate, batchReadCount, readTypes, EnumFundType.ActualSubAccount);

            logger.info("二，读取财富号mongo解读文章列表。数量：{}，头部id列表：{}",
                    proReadlist == null ? 0 : proReadlist.size(),
                    proReadlist == null ? 0 : proReadlist.stream().map(a -> a._id).limit(20).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(proReadlist)) {

                breakpointDate = proReadlist.get(proReadlist.size() - 1).UpdateTime;

                List<String> cfhids = proReadlist.stream().map(item -> item.CFHID).distinct().collect(Collectors.toList());
                List<CFHBriefInfoModel> cfhInfoList = null;
                Map<String, String> cfhIdToPassportIdMap = null;
                //读取mongo财富号信息
                if (!CollectionUtils.isEmpty(cfhids)) {

                    cfhInfoList = cfhListDao.getList(cfhids);

                    logger.info("三，读取mongo财富号信息。数量：{}，头部id列表：{}",
                            cfhInfoList == null ? 0 : cfhInfoList.size(),
                            cfhInfoList == null ? 0 : cfhInfoList.stream().map(a -> a._id).limit(20).collect(Collectors.toList()));

                    if (!CollectionUtils.isEmpty(cfhInfoList)) {
                        cfhIdToPassportIdMap = new HashMap<>(cfhInfoList.size() * 4 / 3 + 1);
                        for (CFHBriefInfoModel item : cfhInfoList) {
                            cfhIdToPassportIdMap.put(item.CFHID, item.RelatedUid);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(cfhInfoList) && !CollectionUtils.isEmpty(cfhIdToPassportIdMap)) {
                    for (CFHProductReadModel item : proReadlist) {
                        item.PassportID = cfhIdToPassportIdMap.get(item.CFHID);
                    }
                }

                //通行证账号不为空的产品解读 审核通过以后解读内容不能做更改（曹建波） ，剔除持仓页位置的解读
                proReadlist = proReadlist.stream().filter(item -> StringUtils.hasLength(item.PassportID)
                        && item.ShowPosition != null && item.ShowPosition == 0).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(proReadlist)) {
                    List<SubAccDtoProReadModel> subAccDtoList = new ArrayList();
                    List<String> fundCodeList = null;
                    for (CFHProductReadModel item : proReadlist) {
                        fundCodeList = item.FundCodeArry;
                        if (!CollectionUtils.isEmpty(fundCodeList)) {
                            for (String code : fundCodeList) {
                                SubAccDtoProReadModel model = new SubAccDtoProReadModel();
                                model.AppTime = DateUtil.dateToStr(item.CreateTime, "yyyy-MM-dd HH:mm:ss");
                                model.TIMEPOINT = DateUtil.getTimePoint(item.CreateTime);
                                model.AppType = "FUNDPRODUCTREAD";//类型为基金产品解读
                                model.PID = item.PassportID;
                                model.ProductRead = item;
                                model._id = String.format("PROREAD_%s", item._id);
                                model.SubAccountNo = code;
                                model.DEL = getDel(item.IsShow == null ? 0 : item.IsShow, item.Status == null ? 0 : item.Status);
                                subAccDtoList.add(model);
                            }
                        }
                    }

                    //mongo删除需要删除的解读数据
                    List<String> delList = subAccDtoList.stream().filter(item -> item.DEL == 1).map(o -> o._id).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(delList)) {
                        subAccountDtoDao.removeByIds(delList);
                    }

                    logger.info("四，mongo删除需要删除的解读数据。数量：{}，头部id列表：{}",
                            delList == null ? 0 : delList.size(),
                            delList == null ? 0 : delList.stream().limit(20).collect(Collectors.toList()));

                    //mongo新增或修改需要需要upsert的解读数据
                    List<SubAccDtoProReadModel> updateList = subAccDtoList.stream().filter(item -> item.DEL == 0).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(updateList)) {
                        List<Map<String, Object>> mapList = new ArrayList<>(updateList.size());
                        for (SubAccDtoProReadModel a : updateList) {
                            mapList.add(CommonUtils.beanToMap(a));
                        }
                        List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 200);
                        for (List<Map<String, Object>> batch : batchList) {
                            subAccountDtoDao.upsertMany(batch);
                        }
                    }
                    logger.info("五，mongo写入需要需要upsert的解读数据。数量：{}，头部id列表：{}",
                            updateList == null ? 0 : updateList.size(),
                            updateList == null ? 0 : updateList.stream().limit(20).collect(Collectors.toList()));
                }

                //更新断点
                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);
                logger.info("六，更新断点。断点：{}", breakpoint);
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }

    /**
     * 根据是否展示或者状态获取 是否删除
     */
    private int getDel(int ishow, int status) {
        if (ishow == 0 || status != 1) {
            return 1;
        }
        return 0;
    }

}
