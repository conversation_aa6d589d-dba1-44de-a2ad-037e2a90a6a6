package ttfund.web.communityservice.timedtask.jijinbarJobs.robot;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.QA.ZnhdQaModel;
import ttfund.web.communityservice.dao.msyql.ZnhdQaDao;
import ttfund.web.communityservice.enums.EnumReplyPostState;
import ttfund.web.communityservice.enums.EnumZnhdState;
import ttfund.web.communityservice.service.QaApiServiceImpl;
import ttfund.web.communityservice.service.entity.AddAnswerNoRequest;
import ttfund.web.communityservice.utils.DateUtil;

import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;

/**
 * 智能互动回答
 */
@JobHandler("AnswerQuestionJob")
@Component
public class AnswerQuestionJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(AnswerQuestionJob.class);

    @Autowired
    private ZnhdQaDao znhdQaDao;

    @Autowired
    private QaApiServiceImpl qaService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            Integer batchReadCount = null;
            String qaAnswerApiPwd = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                batchReadCount = jsonObject.getInteger("batchReadCount");
                qaAnswerApiPwd = jsonObject.getString("qaAnswerApiPwd");
            }

            if (batchReadCount == null) {
                batchReadCount = 1000;
            }

            if (qaAnswerApiPwd == null) {
                qaAnswerApiPwd = "amp3ZDIwMjAwMzE3";
            }

            logger.info("第零步，打印参数。batchReadCount：{}，qaAnswerApiPwd：{}",
                batchReadCount,
                qaAnswerApiPwd
            );

            logicDeal(batchReadCount, qaAnswerApiPwd);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void logicDeal(int batchReadCount, String qaAnswerApiPwd) {
        try {

            List<ZnhdQaModel> list = znhdQaDao.getList(
                Arrays.asList(EnumZnhdState.SHEN_ZHUANG_TAI_TONG_GUO.getValue(), EnumZnhdState.JI_JIN_BA_HOU_TAI_HUI_FU.getValue()),
                EnumReplyPostState.WEI_HUI_FU.getValue(),
                batchReadCount
            );

            logger.info("1.读取数据。数量:{}，头部数据：{}",
                list == null ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

            if (!CollectionUtils.isEmpty(list)) {
                int i = 0;

                for (ZnhdQaModel item : list) {

                    i++;

                    int updateSucess = znhdQaDao.updateState(EnumReplyPostState.YI_HUI_FU.getValue(), "", item.ID);
                    String aid = null;
                    if (updateSucess > 0) {
                        //调用股吧回答接口
                        if (StringUtils.hasLength(item.Code) && StringUtils.hasLength(item.Answer) && StringUtils.hasLength(item.DistributionPassportId)) {

                            //回复人用户ID，为了兼容自定义回复人ID
                            String huifuUserId = (!StringUtils.hasLength(item.AnPassportId) ? item.DistributionPassportId : item.AnPassportId);

                            aid = addAnswerNo(huifuUserId, item.GubaCode, item.Code, item.QID, item.Answer, item.AnswerPic, qaAnswerApiPwd);

                            if (StringUtils.hasLength(aid)) {
                                //更新回复状态
                                znhdQaDao.updateState(EnumReplyPostState.YI_HUI_FU.getValue(), aid, item.ID);

                            } else {
                                znhdQaDao.updateState(EnumReplyPostState.WEI_HUI_FU.getValue(), "", item.ID);
                            }

                        }
                    }

                    logger.info("2.处理数据-详情-第{}/{}个。id：{}，数据:{}，生成回答id：{}",
                        i,
                        list.size(),
                        item.ID,
                        JSON.toJSONStringWithDateFormat(item, DateUtil.datePattern),
                        aid
                    );
                }
            }

            logger.info("2.处理数据完成。数量:{}，头部数据：{}",
                list == null ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }

    /**
     * 回答问题
     *
     * @param passportid    回答者ID
     * @param gubaCode      所在吧
     * @param ext           拓展字段，实盘吧传具体代码
     * @param qid           问题ID
     * @param answerContent 回答内容
     * @param pic
     */
    private String addAnswerNo(String passportid,
                               String gubaCode,
                               String ext,
                               String qid,
                               String answerContent,
                               String pic,
                               String qaAnswerApiPwd
    ) throws Exception {
        String result = "";
        if (!StringUtils.hasLength(passportid) || !StringUtils.hasLength(gubaCode) || !StringUtils.hasLength(qid) || !StringUtils.hasLength(answerContent)) {
            return result;
        }

        String ipBeiJing = "***************";
        String ipDefault = "***************";
        String defaultUid = "5182094458464904";

        if (passportid.equals(defaultUid)) {
            ipDefault = ipBeiJing;
        }

        AddAnswerNoRequest request = new AddAnswerNoRequest();
        request.setCode(gubaCode);
        request.setContent(URLEncoder.encode(answerContent, "utf-8"));
        request.setPic(StringUtils.hasLength(pic) ? pic : "");
        request.setQid(qid);
        request.setUid(passportid);
        request.setPwd(qaAnswerApiPwd);
        if (StringUtils.hasLength(ext)) {
            //如果实盘吧，策略吧，投顾吧 则转换为外码
            if (ext.startsWith("43-") || ext.startsWith("48-")) {
                request.setExt(ext.substring(3));
            } else if (ext.startsWith("58-")) {//投顾吧
                request.setExt(ext);
            } else {
                request.setExt("");
            }
        }

        request.setIp(ipDefault);
        request.setVersion("300");

        String html = qaService.addAnswerNoReturnString(request);
        if (StringUtils.hasLength(html)) {
            JSONObject res = JSON.parseObject(html);
            if (res != null && res.getJSONObject("re") != null && res.getJSONObject("re").getString("aid") != null) {
                //回答ID
                result = res.getJSONObject("re").getString("aid");
            }
        }

        return result;
    }

}
