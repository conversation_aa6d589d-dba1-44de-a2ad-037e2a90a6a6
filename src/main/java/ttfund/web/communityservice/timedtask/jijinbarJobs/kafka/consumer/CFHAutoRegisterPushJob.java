package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.MessagePushInfoSource;
import ttfund.web.communityservice.bean.messagepush.EnumRemindType;
import ttfund.web.communityservice.bean.messagepush.JijinbaServicePushModel;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.Arrays;

/**
 * 财富号用户自动注册推送Kafka
 */
@Component
public class CFHAutoRegisterPushJob {

    private static final Logger logger = LoggerFactory.getLogger(CFHAutoRegisterPushJob.class);

    private static final String KAFKA_LISTENER_ID = "KAFKA_LISTENER_ID_CFHAutoRegisterPushJob";

    //基金吧kafka
    @Autowired
    @Qualifier(KafkaConfig.fundbar_kafka_temp_beanname)
    private KafkaTemplate<String, String> kafkaTemplate;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.EM_CFH_AUTOREGISTER}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_CFHAUTOREGISTERPUSHJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_fundproduct)
    private void onListen(ConsumerRecord<String, String> record) {
        handleOnMessage((record));
    }


    public void handleOnMessage(ConsumerRecord<String, String> record) {

        try {
            logger.info("收到的消息：" + JacksonUtil.obj2String(record.value()));

            if (StringUtils.isEmpty(record.value())) {
                return;
            }

            JSONObject model = JSON.parseObject(record.value());
            if (model != null) {
                JijinbaServicePushModel pushModel = new JijinbaServicePushModel();
                pushModel.PassportId = model.getString("uid");
                pushModel.Pid = model.getString("uid") + "_" + EnumRemindType.CFHAutoRegister;
                pushModel.RemindType = EnumRemindType.CFHAutoRegister;
                pushModel.AlertParam = Arrays.asList("");
                pushModel.CustomerNo = "";
                pushModel.DataParam = Arrays.asList("");
                pushModel.DeviceId = "";
                pushModel.Fcode = "";
                pushModel.LinkParam = Arrays.asList(model.getString("uid"));
                pushModel.Source = String.valueOf(MessagePushInfoSource.FundJijinBarService.getValue());


                //推送消息
                String json = JacksonUtil.serialize(model);
                kafkaTemplate.send(KafkaTopicName.JIJINBA_SERVICE_PUSH, json);

            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }
}
