package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumerDoubleActive;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer.TopicFollowJob;

/**
 * 帖子动效数量同步job
 * 需求：#668693 【基金吧6.14.1】新增帖子特殊动效
 */
@Slf4j
@Component
public class TopicFollowJobDoubleActive {

    public static final String KAFKA_LISTENER_ID = "TopicFollowJobDoubleActive";

    @Autowired
    private TopicFollowJob topicFollowJob;


    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.USER_COLLECTION_TOPIC}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_TOPICFOLLOWJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_qa_new_pj)
    private void onListen(ConsumerRecord<String, String> record) {
        topicFollowJob.deal(record, log);
    }

}
