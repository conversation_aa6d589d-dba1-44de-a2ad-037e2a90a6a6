package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.config.CodeListItem;
import ttfund.web.communityservice.bean.jijinBar.post.config.SingleActiveConfigModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.SingleActiveConfigDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 单品吧活动配置
 */
@JobHandler(value = "singleActiveConfigBusinessJob")
@Component
public class SingleActiveConfigBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SingleActiveConfigBusinessJob.class);

    @Autowired
    private SingleActiveConfigDao singleActiveConfigDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String param) throws JsonProcessingException {

        try {

            setToCache();

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void setToCache() {
        try {

            List<SingleActiveConfigModel> list = singleActiveConfigDao.getAll();

            logger.info("1.读取数据。数据：{}", JacksonUtil.obj2String(list));

            long now = DateUtil.getNowDate().getTime();
            if (!CollectionUtils.isEmpty(list)) {

                list = list.stream()
                        .filter(a -> a.StartTime.getTime() <= now && now <= a.EndTime.getTime())
                        .collect(Collectors.toList());

                logger.info("2.筛选数据。数据：{}", JacksonUtil.obj2String(list));

                if (list.size() > 0) {
                    for (SingleActiveConfigModel item : list) {
                        if (!StringUtils.isEmpty(item.CodeList)) {

                            List<CodeListItem> codeList = JacksonUtil.string2Obj(item.CodeList, List.class, CodeListItem.class);
                            String codes = String.join(",", codeList.stream().map(a -> a.Code).collect(Collectors.toList()));
                            item.CodeList = codes;
                        }
                    }

                    logger.info("3.设置数据。数据：{}", JacksonUtil.obj2String(list));
                    app.barredis.set(BarRedisKey.SINGLE_ACTIVE_CONFIG, JacksonUtil.obj2String(list));
                }
            } else {
                app.barredis.del(BarRedisKey.SINGLE_ACTIVE_CONFIG);
                logger.info("2.删除数据。");
            }


        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

}
