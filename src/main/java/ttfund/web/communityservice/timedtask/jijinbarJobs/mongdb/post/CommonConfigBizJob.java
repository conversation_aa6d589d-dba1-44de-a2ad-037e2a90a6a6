package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.config.CommonConfigApiDataModel;
import ttfund.web.communityservice.bean.jijinBar.post.config.CommonConfigApiResponse;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.CommonConfigDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Url统一配置服务job
 */
@JobHandler("CommonConfigBizJob")
@Component
public class CommonConfigBizJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(CommonConfigBizJob.class);

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private CommonConfigDao commonConfigDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            String initBreakpoint = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
            }

            logger.info("第零步，打印参数。initBreakpoint：{}", initBreakpoint);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.COMMONCONFIGBIZJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            syncCommonConfig();

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void syncCommonConfig() {
        try {
            String breakpointName = UserRedisConfig.COMMONCONFIGBIZJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = "";

                logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint, DATE_FORMAT);

            logger.info("第一步，读取断点。断点:{}", breakpoint);

            String url = appConstant.webconfigApiAddr + "/webconfig/GetCommonConfigList";
            if (StringUtils.hasLength(breakpoint)) {
                url = url + "?updateDate=" + URLEncoder.encode(breakpoint, "utf-8");
            }

            String html = HttpHelper.requestGet(url);

            List<CommonConfigApiDataModel> list = null;
            if (StringUtils.hasLength(html)) {
                CommonConfigApiResponse response = JSON.parseObject(html, CommonConfigApiResponse.class);
                if (response != null && !CollectionUtils.isEmpty(response.getDatas())) {
                    list = response.getDatas();
                }
            }
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(a -> a.set_id(a.getCode()));
            }

            logger.info("第二步，读取数据。数量:{}，头部id列表：{}",
                    list == null ? 0 : list.size(),
                    list == null ? null : list.stream().map(a -> a.get_id()).limit(20).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(list)) {

                breakpointDate = new Date();

                List<Map<String, Object>> mapList = new ArrayList<>(list.size());
                Map<String, Object> map = null;
                for (CommonConfigApiDataModel a : list) {
                    map = CommonUtils.beanToMap(a);
                    mapList.add(map);
                }
                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 200);
                for (List<Map<String, Object>> batch : batchList) {
                    commonConfigDao.upsertMany(batch);
                }
            }

            logger.info("第三步，数据写库。数量:{}，头部id列表：{}",
                    list == null ? 0 : list.size(),
                    list == null ? null : list.stream().map(a -> a.get_id()).limit(20).collect(Collectors.toList()));

            breakpoint = DateUtil.dateToStr(breakpointDate, DATE_FORMAT);
            userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

            logger.info("第四步，更新断点。断点：{}", breakpoint);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }

}
