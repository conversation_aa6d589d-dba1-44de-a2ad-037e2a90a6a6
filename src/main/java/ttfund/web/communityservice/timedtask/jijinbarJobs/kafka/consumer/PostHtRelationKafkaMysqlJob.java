package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostHtRelationModel;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.PostTopicRetDao;
import ttfund.web.communityservice.utils.JacksonUtil;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Zhuyuang
 * @date : 2024-11-13 14:52
 * @description :
 */
@Component
public class PostHtRelationKafkaMysqlJob {

    private static Logger logger = LoggerFactory.getLogger(PostHtRelationKafkaMysqlJob.class);

    private static final String KAFKA_LISTENER_ID = "listener_id_PostHtRelationKafkaMysqlJob";

    @Resource
    private PostTopicRetDao postTopicRetDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.EM_FUND_POST_HT_RELATION}
        , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_POST_HT_RELATION_KAFKA_MYSQL_JOB + "}"
        , containerFactory = KafkaConfig.kafkaListenerContainerFactory_qa)
    public void onListen(ConsumerRecord<String, String> msg) {
        try {
            if (msg != null && StringUtils.hasLength(msg.value())) {
                PostHtRelationModel item = JacksonUtil.string2Obj(msg.value(), PostHtRelationModel.class);
                if (item != null) {
                    if (StringUtils.hasLength(item.actiontype) && "delete".equalsIgnoreCase(item.actiontype)) {
                        logger.info("htid:{} need delete", item.htid);
                        item.Del = 1;
                    }
                    List<PostHtRelationModel> list = Arrays.asList(item);
                    boolean result = postTopicRetDao.insertOrUpdate2(list);
                    if (!result) {
                        result = postTopicRetDao.insertOrUpdate2(list);
                        if (!result) {
                            logger.error("save data failed.");
                        }
                    }
                    if (result) {
                        logger.info("save data success. htid:{}", item.htid);
                    }
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }
}
