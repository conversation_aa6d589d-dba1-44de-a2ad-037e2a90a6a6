package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.data.CodeInfoEntity;
import ttfund.web.communityservice.bean.jijinBar.data.SetTopEntity;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.SetTopDao;
import ttfund.web.communityservice.enums.EnumTopType;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 帖子置顶缓存
 */
@JobHandler(value = "SetTopRedisBusinessJob")
@Component
public class SetTopRedisBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SetTopRedisBusinessJob.class);

    @Autowired
    private SetTopDao setTopDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String param) throws JsonProcessingException {

        try {
            // 首页帖子置顶同步到Redis
            syncSetTop();
            // 基金吧帖子置顶同步到Redis
            syncSetFundBarTop();
            // 推荐帖子-发现栏目
            syncRecommendPostInfo();
            // 定投页 置顶帖 缓存
            syncRecommendDTTopInfo();
            // 推荐帖子-自选栏目
            syncRecommendFavorPostInfo();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 首页帖子置顶同步到Redis
     */
    private void syncSetTop() {

        try {
            List<SetTopEntity> list = setTopDao.getSetTopList()
                    .stream()
                    .filter(i -> i.Type.contains(String.valueOf(EnumTopType.APP_HOMEPAGE.getValue())))
                    .collect(Collectors.toList());

            logger.info("syncSetTop-读库。数量：{}，头部数据：{}",
                    CollectionUtils.isEmpty(list) ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JacksonUtil.obj2String(list.get(0))
            );

            if (!CollectionUtils.isEmpty(list)) {
                app.barredis.set(BarRedisKey.FUND_GUBA_SERVICE_POSTTOP, JacksonUtil.obj2String(list));
            } else {
                app.barredis.del(BarRedisKey.FUND_GUBA_SERVICE_POSTTOP);
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 基金吧帖子置顶同步到Redis
     */
    private void syncSetFundBarTop() {

        try {
            List<SetTopEntity> list = setTopDao.getSetTopList()
                    .stream()
                    .filter(i -> i.Type.contains(String.valueOf(EnumTopType.FUND_BAR.getValue())))
                    .collect(Collectors.toList());

            logger.info("syncSetFundBarTop-读库。数量：{}，头部数据：{}",
                    CollectionUtils.isEmpty(list) ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JacksonUtil.obj2String(list.get(0))
            );

            Map<String, List<SetTopEntity>> fundBarTopDic = new HashMap<>();

            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            for (SetTopEntity item : list) {
                if (StringUtils.isEmpty(item.CodeList)) {
                    continue;
                }

                try {
                    List<CodeInfoEntity> codeList = JacksonUtil.string2Obj(item.CodeList, List.class,
                            CodeInfoEntity.class);

                    if (!CollectionUtils.isEmpty(codeList)) {
                        item.CodeList = "";
                        String[] fundcodes = codeList.stream()
                                .map(s -> s.Code)
                                .collect(Collectors.toList())
                                .toArray(new String[]{});

                        for (String f : fundcodes) {
                            List<SetTopEntity> value = fundBarTopDic.getOrDefault(f, new ArrayList<>());
                            value.add(item);
                            fundBarTopDic.put(f, value);
                        }
                    }
                } catch (Exception ex) {
                    logger.error(ex.getMessage(), ex);
                }
            }

            for (String key : fundBarTopDic.keySet()) {
                app.barredis.set(BarRedisKey.FUND_GUBA_SERVICE_FUNDBARTOP + key,
                        JacksonUtil.obj2String(fundBarTopDic.get(key)), 300L);
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 推荐帖子同步到Redis
     */
    private void syncRecommendPostInfo() {

        try {
            List<SetTopEntity> list = setTopDao.getRecommendPostList();

            logger.info("syncRecommendPostInfo-读库。数量：{}，头部数据：{}",
                    CollectionUtils.isEmpty(list) ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JacksonUtil.obj2String(list.get(0))
            );

            if (!CollectionUtils.isEmpty(list)) {

                List<SetTopEntity> reclist = list.stream()
                        .filter(a -> a.DisplayLocation > 0)
                        .collect(Collectors.toList());

                //今日精华帖
                List<SetTopEntity> todayEliteList = list.stream()
                        .sorted(Comparator.comparingInt(o -> o.DisplayLocation))
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(reclist)) {
                    app.barredis.set(BarRedisKey.FUND_SERVICE_RECOMMEND_FAXIAN_POST, JacksonUtil.obj2String(reclist), 300L);
                } else {

                }

                if (!CollectionUtils.isEmpty(todayEliteList)) {
                    for (SetTopEntity item : todayEliteList) {
                        if (item.DisplayLocation > 0) {
                            item.EliteOrderTime = DateUtil.getTimePoint(DateUtil.calendarDateByYears(item.CreateTime, -5));
                        } else {
                            item.EliteOrderTime = DateUtil.getTimePoint(item.CreateTime);
                        }
                    }
                    Boolean setResult = app.barredis.set(BarRedisKey.FUND_SERVICE_TODAY_ELITE_POST, JacksonUtil.obj2String(todayEliteList));
                    if (setResult != null && !setResult) {
                        app.barredis.set(BarRedisKey.FUND_SERVICE_TODAY_ELITE_POST, JacksonUtil.obj2String(todayEliteList));
                    }
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 定投页 置顶帖 缓存
     */
    private void syncRecommendDTTopInfo() {

        try {
            List<SetTopEntity> list = setTopDao.getSetTopList()
                    .stream()
                    .filter(i -> i.Type.contains(String.valueOf(EnumTopType.AUTOMATIC_INVESTMENT_PLAN.getValue())))
                    .collect(Collectors.toList());

            logger.info("syncRecommendDTTopInfo-读库。数量：{}，头部数据：{}",
                    CollectionUtils.isEmpty(list) ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JacksonUtil.obj2String(list.get(0))
            );

            if (!CollectionUtils.isEmpty(list)) {
                String value = String.join(",",
                        list.stream()
                                .sorted((o1, o2) -> o2.UpdateTime.compareTo(o1.UpdateTime))
                                .map(m -> String.valueOf(m.TId))
                                .collect(Collectors.toList())
                );
                app.barredis.set(BarRedisKey.FUND_GUBA_SERVICE_DINGTOUTOP, value);
            } else {
                app.barredis.set(BarRedisKey.FUND_GUBA_SERVICE_DINGTOUTOP, "");
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 自选推荐-配置缓存
     */
    private void syncRecommendFavorPostInfo() {

        try {
            List<SetTopEntity> list = setTopDao.getRecommendFavorPostList();

            logger.info("syncRecommendFavorPostInfo-读库。数量：{}，头部数据：{}",
                    CollectionUtils.isEmpty(list) ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JacksonUtil.obj2String(list.get(0))
            );

            if (!CollectionUtils.isEmpty(list)) {

                List<SetTopEntity> reclist = list.stream()
                        .filter(a -> a.DisplayLocation > 0)
                        .collect(Collectors.toList());

                //今日精华帖
                List<SetTopEntity> todayEliteList = list.stream()
                        .sorted(Comparator.comparingInt(o -> o.DisplayLocation))
                        .collect(Collectors.toList());


                if (!CollectionUtils.isEmpty(reclist)) {
                    app.barredis.set(BarRedisKey.FUND_SERVICE_FAVOR_RECOMMEND_POST, JacksonUtil.obj2String(reclist), 300L);
                } else {
                }

                if (!CollectionUtils.isEmpty(todayEliteList)) {
                    for (SetTopEntity item : todayEliteList) {
                        if (item.DisplayLocation > 0) {
                            item.EliteOrderTime = DateUtil.getTimePoint(DateUtil.calendarDateByYears(item.CreateTime, -5));
                        } else {
                            item.EliteOrderTime = DateUtil.getTimePoint(item.CreateTime);
                        }
                    }
                    Boolean setResult = app.barredis.set(BarRedisKey.FUND_SERVICE_TODAY_ELITE_POST, JacksonUtil.obj2String(todayEliteList));
                    if (setResult != null && !setResult) {
                        app.barredis.set(BarRedisKey.FUND_SERVICE_TODAY_ELITE_POST, JacksonUtil.obj2String(todayEliteList));
                    }
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }
}
