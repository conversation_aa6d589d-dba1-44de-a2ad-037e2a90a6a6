package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.useracc;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.hold.UserHoldBean;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.PersonalizedRedisConstantConfig;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 全量用户持仓同步至个性化Redis
 */
@JobHandler(value = "userHoldFullJob")
@Component
public class UserHoldFullJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(UserHoldFullJob.class);

    @Autowired
    private App app;

    @Autowired
    private UserRedisDao userRedisDao;

    @Override
    public ReturnT<String> execute(String param) {

        logger.info("持仓同步任务开始");

        List<String> userList = new ArrayList<>();
        if (!StringUtils.isEmpty(param)) {
            userList.addAll(Arrays.asList(param.split(",")));
        }

        ReturnT<String> result = ReturnT.SUCCESS;
        int size = 50000;
        int count = 0;
        Date breakTime = userRedisDao.getBreakTime("UserHoldFullJob");

        Connection con = null;
        PreparedStatement ps = null;
        ResultSet resultSet = null;
        List<UserHoldBean> userHoldList = new ArrayList<>();

        try {

            // 查询持仓数据
            String sql = getSql(userList);
            con = app.bigdataVertica.getconn();
            ps = con.prepareStatement(sql);
            resultSet = ps.executeQuery();

            //获取键名
            ResultSetMetaData md = resultSet.getMetaData();

            //获取行的数量
            int columnCount = md.getColumnCount();

            while (resultSet.next()) {
                UserHoldBean bean = new UserHoldBean();
                for (int i = 1; i <= columnCount; i++) {
                    if ("FCode".equals(md.getColumnLabel(i))) {
                        bean.setFcode(resultSet.getString(i));
                    } else if ("C_PASSPORTID".equals(md.getColumnLabel(i))) {
                        bean.setPassportId(resultSet.getString(i));
                    }
                }
                userHoldList.add(bean);

                if (userHoldList.size() == size) {
                    // 保存数据
                    count = saveData(userHoldList, count);

                    userHoldList.clear();

                    // 到8点还没执行完，终止
                    if (Calendar.getInstance().get(Calendar.HOUR_OF_DAY) >= 8 && breakTime != null) {
                        logger.error("任务超时终止，已处理数量：" + count);
                        return ReturnT.FAIL;
                    }
                }
            }

            if (userHoldList.size() > 0) {
                // 保存数据
                count = saveData(userHoldList, count);
            }

            userRedisDao.setBreakTime("UserHoldFullJob", DateUtil.getNowDate());

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        } finally {
            try {
                if (resultSet != null) {
                    resultSet.close();
                }
                if (ps != null) {
                    ps.close();
                }
                if (con != null) {
                    con.close();
                }
            } catch (SQLException e) {
                logger.error(e.getMessage(), e);
                result = ReturnT.FAIL;
            }
        }

        logger.info("持仓同步任务结束，同步数量：" + count);

        return result;
    }

    /**
     * 数据存入Redis
     *
     * @param userHoldList
     * @param count
     */
    private int saveData(List<UserHoldBean> userHoldList, int count) {

        count += userHoldList.size();
        logger.info("查询到数据：" + userHoldList.size() + ",总数：" + count);

        Map<String, List<UserHoldBean>> userMap = userHoldList.stream()
                .collect(Collectors.groupingBy(w -> w.getPassportId()));

        logger.info("分组完的数据：" + userMap.size());

        // 存入Redis
        for (Map.Entry<String, List<UserHoldBean>> entry : userMap.entrySet()) {
            String passportId = entry.getKey();
            String cacheKey = PersonalizedRedisConstantConfig.JAVA_SERVICE_HOLD_PASSPORT + passportId;
            app.personalizedRedisWrite.set(cacheKey, JacksonUtil.obj2StringNoException(entry.getValue()), 36 * 3600L);
        }

        return count;
    }

    /**
     * 获取SQL
     *
     * @param userList
     * @return
     */
    private String getSql(List<String> userList) {

        String sql = " select c.C_PASSPORTID, a.FCode " +
                " from retention.UserHoldFundNow_Stat_All_APP a " +
                " inner join ( " +
                "    select fcode, maincode " +
                "         from fund.CODEALL_ALL_BASIC_APP " +
                "         where ptype in (1, 2, 3) " +
                "           and fundtype != '005') b " +
                " on a.fcode = b.fcode " +
                " left join emuser.USER_TB_PASSPORT_ALL_BASIC_SYN c " +
                " on a.customerno = c.C_CUSTOMERNO " +
                " where cardid = 0 " +
                " and asset >= 50 " +
                " and c.C_PASSPORTID is not null ";

        if (!CollectionUtils.isEmpty(userList)) {
            sql += " and c.C_PASSPORTID in ('" + String.join("','", userList) + "')";
        }

        sql += " order by c.C_PASSPORTID";

        return sql;
    }
}
