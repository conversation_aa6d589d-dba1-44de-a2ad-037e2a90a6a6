package ttfund.web.communityservice.timedtask.jijinbarJobs.search;


import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumCFHArticleApprovalState;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumFollowPostType;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumPostType;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.post.elitepost.ElitePostModel;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.CFHArticleModel;
import ttfund.web.communityservice.bean.jijinBar.search.AppNewsFollowInfo;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.dao.mongo.CFHArticleDao;
import ttfund.web.communityservice.dao.mongo.ElitePostDao;
import ttfund.web.communityservice.dao.msyql.JjbconfigDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.dao.search.FollowPostDao;
import ttfund.web.communityservice.utils.*;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

@JobHandler(value = "FollowPostBizJob")
@Component
public class FollowPostBizJob extends IJobHandler {

    @Autowired
    AppConstant appConstant;

    private static final Logger logger = LoggerFactory.getLogger(FollowPostBizJob.class);

    private String logpre = "[FollowPostBizJob]=> ";

    private String logpre8 = "[FollowPostBizJob for es8]=> ";

    @Autowired
    UserRedisDao userRedisDao;

    @Autowired
    ElitePostDao elitePostDao;

    @Autowired
    FollowPostDao followPostDao;

    @Autowired
    JjbconfigDao jjbconfigDao;

    @Autowired
    CFHArticleDao cfhArticleDao;

    public ReturnT<String> execute(java.lang.String param) {
        try {
            ElitePost();
            SyncFollowPostEs();
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 设置精华帖
     */
    private void ElitePost() {
        String breakPoint = "AppFollow_ElitePost";
        logger.info(logpre + "=>ElitePost=>开始执行");
        try {

            int batchCount = 9000;
            Date lastUpdateTime = userRedisDao.getBreakTime(breakPoint, DateUtil.calendarDateByHour(-1));
            List<Integer> types = new ArrayList<>();
            types.add(EnumPostType.General.getValue());
            types.add(EnumPostType.CombineBarPost.getValue());
            types.add(EnumPostType.PolicyBarPost.getValue());

            List<ElitePostModel> list = elitePostDao.getList(lastUpdateTime, types, batchCount);

            if (!CollectionUtils.isEmpty(list)) {
                lastUpdateTime = list.stream().max(Comparator.comparing(ElitePostModel::getUPDATETIME))
                        .get().getUPDATETIME();
                List<String> ids = new ArrayList<>();
                for (ElitePostModel item : list) {
                    String _id = item.ID + "_" + EnumFollowPostType.POST.getValue();
                    ids.add(_id);
                }

                //双写es8
                if (ids.size() > 0) {
                    List<AppNewsFollowInfo> esList = followPostDao.getByIdFromES8(ids);
                    if (esList != null && esList.size() > 0) {
                        //只更新未删除的帖子
                        esList = esList.stream().filter(a -> a.getDel() == 0).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(esList)) {
                            for (AppNewsFollowInfo item : esList) {
                                item.setIsElite(1);
                            }
                            int updateCount = esList.size();
                            boolean saveResult = followPostDao.save8(esList);
                            if (!saveResult) {
                                saveResult = followPostDao.save8(esList);
                                if (!saveResult) {
                                    logger.info(logpre8 + "ElitePost=>保存失败：涉及数据=>" + JacksonUtil.obj2String(esList));
                                }
                            }
                            if (saveResult) {
                                logger.info(logpre8 + "ElitePost=>保存成功：涉及数量=>" + updateCount);
                            }
                        }
                    }
                }
                userRedisDao.setBreakTime(breakPoint, lastUpdateTime);

            }
        } catch (Exception ex) {
            logger.error(logpre + "ElitePost=>" + ex.getMessage(), ex);
        }
        logger.info(logpre + "=>ElitePost=>结束执行");
    }

    /**
     * SyncFollowPostEs
     */
    public void SyncFollowPostEs() {
        logger.info(logpre + "SyncFollowPostEs=>同步基金吧帖子【开始】");

        try {
            /*
             * 1.基金吧帖子数据
             * 2.精华帖数据
             * 3.财富号文章数据
             */
            int postCount = 0;//帖子数量
            int cfhArticleCount = 0;//财富号文章数量

            List<AppNewsFollowInfo> followList = new ArrayList<>();
            //基金吧帖子信息
            List<AppNewsFollowInfo> postList = GetPostList();
            if (!CollectionUtils.isEmpty(postList)) {
                postCount = postList.size();
                followList.addAll(postList);
            }
            ////财富号文章信息
            List<AppNewsFollowInfo> cfhArticle = GetCFHArticel();
            if (!CollectionUtils.isEmpty(cfhArticle)) {
                cfhArticleCount = cfhArticle.size();
                followList.addAll(cfhArticle);
            }
            //建立索引
            if (!CollectionUtils.isEmpty(followList)) {
                //写es8
                boolean esBack = followPostDao.save8(followList);
                if (esBack) {
                    logger.info(logpre8 + "SyncFollowPostEs_SaveCount=>" + followList.size());
                } else {
                    logger.error(logpre8 + "SyncFollowPostEs_SaveFaild=>" + followList.size());
                }
            }
            logger.info("开始同步基金吧帖子--[end],帖子数量:{" + postCount + "},财富号文章数量:{" + cfhArticleCount + "},总数量：{" + (followList == null ? 0 : followList.size()) + "}");
        } catch (Exception ex) {
            logger.error(logpre + ex.getMessage(), ex);
        }

    }

    private List<AppNewsFollowInfo> GetPostList() {
        List<AppNewsFollowInfo> followList = new ArrayList<>();

        try {
            int batchCount = 20000;
            String currentMethodName = "AppFollow_GetPostListNew";
            Date lastTime = userRedisDao.getBreakTime(currentMethodName, DateUtil.calendarDateByHour(-2));
            if (lastTime != null) {
                lastTime = DateUtil.calendarDateBySecond(-2);
            }
            Date time = DateUtil.calendarDateByMonth(-1);
            String postTypesStr = EnumPostType.General.getValue() + "," + EnumPostType.CombineBarPost.getValue() + "," + EnumPostType.PolicyBarPost.getValue();

            String codes = "";
            List<String> codeList = jjbconfigDao.getCodeTypes();
            if (!CollectionUtils.isEmpty(codeList)) {
                codes = String.join(",", codeList);
            }
            List<PostInfoNewModel> list = followPostDao.getFollowPost(lastTime, time, postTypesStr, codes, batchCount);
            logger.info(logpre + "本轮【帖子】开始时间：" + DateUtil.dateToStr(lastTime));

            if (!CollectionUtils.isEmpty(list)) {
                for (PostInfoNewModel item : list) {
                    try {
                        //过滤转发帖,财富号评论吧帖
                        if (!NullUtil.isNull(item.CODE)
                                && !Arrays.asList(AppConstantConfig.filterCode.split(",")).contains(item.CODE)) {
                            //判断是否是精华帖子
                            int isElite = (elitePostDao.isElitePost(String.valueOf(item.ID)) ? 1 : 0); //0;//
                            AppNewsFollowInfo model = new AppNewsFollowInfo();
                            {
                                model.MyId = item.ID + "_" + EnumFollowPostType.POST.getValue();
                                model.Id = String.valueOf(item.ID);
                                model.CFHId = "";
                                model.Code = item.CODE;
                                model.Del = getPostDel(item.DEL, item.ISENABLED, item.TTJJDEL);
                                model.Pic = getImgList(item.PIC);
                                model.ShowTime = item.TIME;
                                model.Type = EnumFollowPostType.POST.getValue();
                                model.SubType = item.TYPE;
                                model.Title = item.TITLE;
                                model.Uid = item.UID;
                                model.UpdateTime = item.UPDATETIME;
                                model.TimePoint = getTimePoint(item.TIME, String.valueOf(item.ID));
                                model.IsElite = isElite;
                                model.DataUpdateTime = DateUtil.getNowDate();
                                model.AllPic = getImgList(item.ALLPIC);
                                model.IsEnabled = item.ISENABLED;
                                model.TtjjDel = item.TTJJDEL;
                            }
                            followList.add(model);
                        }
                    } catch (Exception ex) {
                        logger.error(logpre + "GetPostList=>" + ex.getMessage(), ex);
                    }
                }
                lastTime = list.stream().max(Comparator.comparing(o -> o.UPDATETIME))
                        .get().UPDATETIME;
                userRedisDao.setBreakTime(currentMethodName, lastTime);
            }
        } catch (Exception ex) {
            logger.error(logpre + "GetPostList=>" + ex.getMessage(), ex);
        }
        return followList;
    }

    private int getPostDel(int del, int isenable, int ttjjdel) {
        if (del == 1 || isenable == 0 || ttjjdel == 1) {
            return 1;
        }
        return 0;
    }

    private List<String> getImgList(String pic) {
        List<String> list = new ArrayList<>();
        if (NullUtil.isNull(pic)) return list;
        list = Arrays.asList(pic.split(","));
        return list;
    }

    private long getTimePoint(Date dateTime, String str) {
        if (dateTime == null) dateTime = DateUtil.getMinDate();
        if (str == null) str = "";

        Long result = Long.parseLong(String.valueOf(DateUtil.getUnixTime(dateTime)).substring(0, 9) + StringUtil.subStrFromEnd(str, 8, "0"));
        return result;
    }

    /**
     * 获取财富号文章
     *
     * @return
     */
    private List<AppNewsFollowInfo> GetCFHArticel() {
        List<AppNewsFollowInfo> followList = new ArrayList<>();
        try {
            int batchCount = 10000;
            String currentMethodName = "AppFollow_GetCFHArticel";
            Date lastTime = userRedisDao.getBreakTime(currentMethodName, DateUtil.calendarDateByDays(-3));


            List<CFHArticleModel> list = cfhArticleDao.getList(lastTime, batchCount);
            if (!CollectionUtils.isEmpty(list)) {
                logger.info(logpre+"GetCFHArticel=>maxupdatetime：" +DateUtil.dateToStr(list.stream().max(Comparator.comparing(CFHArticleModel::getUpdateTime))
                        .get().getUpdateTime()));

                for (CFHArticleModel item : list) {
                    try {
                        AppNewsFollowInfo model = new AppNewsFollowInfo();
                        model.MyId =item.ArtCode +"_"+EnumFollowPostType.CFHArt.getValue();
                        model.Id =String.valueOf(item.ArtCode);
                        model.CFHId =item.AuthorId;
                        model.Code ="";
                        model.Del =getCFHArticleState(item.IsDeleted, item.ApprovalState);
                        model.Pic =getPic(item.ListImage);//当一张图片展示
                        model.ShowTime =item.Showtime;
                        model.Type =EnumFollowPostType.CFHArt.getValue();
                        model.SubType =-1;
                        model.Title =item.Title;
                        model.Uid =item.RelatedUid;
                        model.UpdateTime =item.UpdateTime;
                        model.TimePoint =getTimePoint(item.Showtime, String.valueOf(item.ArtCode));
                        model.DataUpdateTime =DateUtil.getNowDate();
                        followList.add(model);
                    } catch (Exception ex) {
                        logger.error(logpre+"GetCFHArticel=>"+ex.getMessage(),ex);
                    }
                }

                lastTime = list.stream().max(Comparator.comparing(CFHArticleModel::getUpdateTime))
                        .get().getUpdateTime();
                userRedisDao.setBreakTime(currentMethodName,lastTime);
            }

        } catch (Exception ex) {
            logger.error(logpre+"GetCFHArticel=>"+ex.getMessage(),ex);
        }
        return followList;
    }


    private int getCFHArticleState(Integer isDelete, Integer approvalState)
    {
        //最终审核通过的文章返回删除状态，其它未最终审核通过的文章直接返回删除状态
        if (approvalState != null &&
                (approvalState == EnumCFHArticleApprovalState.AduitPass.getValue()
                        || approvalState == EnumCFHArticleApprovalState.FirstAduitPass.getValue()
                        || approvalState == EnumCFHArticleApprovalState.FirstPublishThenAduit.getValue())) {
            return (isDelete == null ? 0 : isDelete);
        } else {
            return 1;
        }
    }

    public List<String> getPic(String listImage) {
        List<String> list = new ArrayList<>();
        if (!NullUtil.isNull(listImage)) {
            //先判断图片是否可用
            boolean isEnablePic = picIsEnabled(listImage);
            if (isEnablePic) {
                list.add(listImage);
            }
        }
        return list;
    }

    /**
     * 判断图片是否可用
     *
     * @param pic
     * @return
     */
    private boolean picIsEnabled(String pic) {
        URL url = null;
        InputStream is = null;
        BufferedImage img = null;
        try {
            if (isWhiteUrl(pic)) {
                return true;
            } else {
                url = new URL(pic);
                is = url.openStream();
                img = ImageIO.read(is);
                return imageSizeEnable(img);
            }

        } catch (FileNotFoundException e) {
            logger.warn(logpre + "picIsEnabled=>【picIsEnabled】图片地址：" + pic + "【图片不存在】");
            return false;
        } catch (Exception e) {
            logger.error(logpre + "picIsEnabled=>【picIsEnabled】图片地址：" + pic + "【异常信息】" + e.getMessage(), e);
            return false;
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (Exception ex) {
                logger.error(logpre + "picIsEnabled=>【picIsEnabled】图片地址：" + pic + "【异常信息】" + ex.getMessage(), ex);
            }
        }


    }

    /**
     * 是否是白名单用户
     *
     * @param pic
     * @return
     */
    private boolean isWhiteUrl(String pic) {
        String list = appConstant.whitepicurllist;
        if (list != null) {
            String[] tempList = list.split(",");
            for (String item : tempList) {
                if (pic.contains(item)) {
                    return true;
                }
            }
            return false;
        }
        return false;
    }


    private boolean imageSizeEnable(BufferedImage img) {
        return img != null && img.getWidth() > 50 && img.getHeight() > 50;
    }

}
