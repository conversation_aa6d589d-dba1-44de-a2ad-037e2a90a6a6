package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.dao.mongo.VUserPostInfoDao;
import ttfund.web.communityservice.dao.msyql.VUserPostDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 同步旧大V用户历史数据到mongo新表 BigVCalDataNew
 *
 * @author：liyaogang
 * @date：2023/4/25 10:24
 */
@JobHandler("syncUserHistoryDataJob")
@Component
public class SyncUserHistoryDataJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SyncUserHistoryDataJob.class);

    //操作mysql的dao
    @Autowired
    VUserPostDao vUserPostDao;

    //操作mongo的dao
    @Autowired
    VUserPostInfoDao vUserPostInfoDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        //近一年
        Date lastYear = DateUtil.calendarDateByYears(new Date(), -1);
        try {
            List<Map<String, Object>> hisData = vUserPostDao.getCalBigVData(lastYear);
            int batch = 5000;
            int index = 0;
            if (hisData != null && hisData.size() > 0) {
                for (Map<String, Object> data : hisData) {
                    Map<String, Object> newData = new HashMap<>();
                    for (String key : data.keySet()) {
                        newData.put(key.toLowerCase(),data.get(key));
                    }
                    boolean res = vUserPostInfoDao.saveCalBigVDataMongo(newData);
                    index++;
                    logger.info("用户：{}日期：{}的数据同步mongo完成，结果：{}", newData.get("uid"), newData.get("cdate"), res);
                    //如果数据量太大
                    if (index == batch) {
                        Thread.sleep(3000);
                        index = 0;
                        logger.info("数据分批次存取休眠");
                    }
                }
            }
        } catch (Exception e) {
            logger.error("同步旧大V用户历史数据到mongo新表，任务失败，error：{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
