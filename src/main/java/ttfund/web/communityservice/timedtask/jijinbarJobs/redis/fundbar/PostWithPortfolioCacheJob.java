package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.PostWithPortfolio;
import ttfund.web.communityservice.bean.jijinBar.post.FundPostExtras;
import ttfund.web.communityservice.bean.jijinBar.post.FundPostExtrasProP;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.PostInfoExtendDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 关联实盘帖子集合写缓存job
 */
@Slf4j
@JobHandler("PostWithPortfolioCacheJob")
@Component
public class PostWithPortfolioCacheJob extends IJobHandler {

    private static final String FIELDS = " ID,TITLE,CONTENT,TIME,TIMEPOINT,DEL,TTJJDEL ";

    @Autowired
    private PostInfoExtendDao postInfoExtendDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            Integer batchReadCount = null;
            Integer intervalTime = null;//负值
            Long expire = null;
            String keyword = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                batchReadCount = jsonObject.getInteger("batchReadCount");
                intervalTime = jsonObject.getInteger("intervalTime");
                expire = jsonObject.getLong("expire");
                keyword = jsonObject.getString("keyword");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (intervalTime == null) {
                intervalTime = -7 * 24 * 3600;
            }
            if (expire == null) {
                expire = 14 * 24 * 3600L;
            }

            log.info("0.打印参数。batchReadCount：{}，intervalTime：{}，expire：{}，keyword：{}",
                batchReadCount,
                intervalTime,
                expire,
                keyword
            );

            deal(batchReadCount, intervalTime, expire, keyword);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(Integer batchReadCount, Integer intervalTime, Long expire, String keyword) {

        Date start = DateUtil.calendarDateBySecond(intervalTime);

        log.info("1.读取区间。start：{}", DateUtil.dateToStr(start));

        List<FundPostExtras> list = new ArrayList<>();
        while (true) {
            List<FundPostExtras> tempList = postInfoExtendDao.getListByPubTime(FundPostExtras.class, null, batchReadCount, start);
            if (!CollectionUtils.isEmpty(tempList)) {
                list.addAll(tempList);
            }

            if (CollectionUtils.isEmpty(tempList) || tempList.size() < batchReadCount) {
                break;
            }

            start = tempList.get(tempList.size() - 1).PUBTIME;
        }

        log.info("2.读取数据。数量：{}，数据：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        List<PostWithPortfolio> cacheList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            Map<String, PostWithPortfolio> map = new HashMap<>();
            PostWithPortfolio model = null;
            Map<String, Object> post = null;
            Set<String> alreadyDealSet = new HashSet<>();
            for (FundPostExtras a : list) {
                if (!CollectionUtils.isEmpty(a.PROP)) {
                    for (FundPostExtrasProP b : a.PROP) {
                        if (!alreadyDealSet.contains(a._id) && b.TYPE == 60 && !CollectionUtils.isEmpty(b.CODE)) {
                            for (String c : b.CODE) {
                                if (map.get(c) == null) {
                                    model = new PostWithPortfolio();
                                    model.setPortfolioId(c);
                                    model.setPosts(new ArrayList<>());
                                    map.put(c, model);
                                }

                                model = map.get(c);
                                post = new HashMap<>();
                                post.put("postId", a._id);

                                model.getPosts().add(post);
                            }

                            alreadyDealSet.add(a._id);
                        }
                    }
                }
            }

            log.info("3.按组合id分组。数量：{}，数据：{}",
                CollectionUtils.isEmpty(map) ? 0 : map.size(),
                CollectionUtils.isEmpty(map) ? null : JSON.toJSONStringWithDateFormat(map.values().stream().findFirst().orElse(null), DateUtil.datePattern)
            );

            Set<String> postIds = new HashSet<>();
            for (PostWithPortfolio a : map.values()) {
                if (!CollectionUtils.isEmpty(a.getPosts())) {
                    for (Map<String, Object> b : a.getPosts()) {
                        postIds.add((String)b.get("postId"));
                    }
                }
            }

            Map<String, Boolean> matchMap = new HashMap<>();
            Map<String, PostInfoNewModel> postInfoMap = new HashMap<>();
            List<List<Long>> batchList = CommonUtils.toSmallList2(postIds.stream().map(a -> Long.parseLong(a)).collect(Collectors.toList()), 200);
            for (List<Long> batch : batchList) {
                List<PostInfoNewModel> tempList = postInfoNewDao.getByIds(batch, FIELDS);
                if (!CollectionUtils.isEmpty(tempList)) {
                    tempList.forEach(a -> {
                        boolean match = false;
                        if (keyword == null) {
                            match = true;
                        }
                        if (!match && StringUtils.hasLength(a.TITLE)) {
                            match = a.TITLE.contains(keyword);
                        }
                        if (!match && StringUtils.hasLength(a.CONTENT)) {
                            match = a.CONTENT.contains(keyword);
                        }

                        matchMap.put(String.valueOf(a.ID), match);
                    });
                    tempList.forEach(o -> postInfoMap.put(String.valueOf(o.ID), o));
                }
            }

            log.info("4.获取帖子信息。数量：{}，数据：{}",
                CollectionUtils.isEmpty(postInfoMap) ? 0 : postInfoMap.size(),
                CollectionUtils.isEmpty(postInfoMap) ? null : JSON.toJSONStringWithDateFormat(postInfoMap.values().stream().findFirst().orElse(null), DateUtil.datePattern)
            );

            for (Map.Entry<String, PostWithPortfolio> entry : map.entrySet()) {
                List<Map<String, Object>> oldPosts = entry.getValue().getPosts();
                if (!CollectionUtils.isEmpty(oldPosts)) {
                    List<Map<String, Object>> newPosts = new ArrayList<>();
                    for (Map<String, Object> a : oldPosts) {
                        PostInfoNewModel postInfo = postInfoMap.get(a.get("postId"));
                        if (postInfo != null && Objects.equals(postInfo.DEL, 0) && Objects.equals(postInfo.TTJJDEL, 0) && matchMap.getOrDefault(a.get("postId"), false)) {
                            a.put("title", postInfo.TITLE);
                            a.put("timePoint", postInfo.TIMEPOINT);
                            newPosts.add(a);
                        }
                    }

                    newPosts.sort((o1, o2) -> Long.compare(Long.parseLong(o2.get("timePoint").toString()), Long.parseLong(o1.get("timePoint").toString())));
                    if (newPosts.size() > 1) {
                        newPosts = newPosts.subList(0, 1);
                    }
                    entry.getValue().setPosts(newPosts);
                }
            }

            cacheList = map.values().stream()
                .filter(a -> !CollectionUtils.isEmpty(a.getPosts()))
                .sorted(Comparator.comparing(PostWithPortfolio::getPortfolioId))
                .collect(Collectors.toList());

            log.info("5.基于帖子信息处理。数量：{}，数据：{}",
                CollectionUtils.isEmpty(cacheList) ? 0 : cacheList.size(),
                CollectionUtils.isEmpty(cacheList) ? null : JSON.toJSONStringWithDateFormat(cacheList.get(0), DateUtil.datePattern)
            );

        }

        app.barredis.set(BarRedisKey.POST_WITH_PORTFOLIO, JSON.toJSONStringWithDateFormat(cacheList, DateUtil.datePattern), expire);

        log.info("6.写缓存。数量：{}，数据：{}",
            CollectionUtils.isEmpty(cacheList) ? 0 : cacheList.size(),
            CollectionUtils.isEmpty(cacheList) ? null : JSON.toJSONStringWithDateFormat(cacheList.get(0), DateUtil.datePattern)
        );

    }

}
