package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumerDoubleActive;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.core.register.AppCore;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.guba.BlackList;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.utils.redis.RedisUtils;

/**
 * 用户黑名单kafka 缓存
 * kafka -> redis
 */
@Component
public class UserBlacklistKafkaJobDoubleActive {

    private static Logger logger = LoggerFactory.getLogger(UserBlacklistKafkaJobDoubleActive.class);

    public static final String KAFKA_LISTENER_ID = "UserBlacklistKafkaJobDoubleActive";

    @Autowired
    private AppCore appCore;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.GubaUserBlackList4FundQueue}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_USERBLACKLISTKAFKAJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_pj)
    private void onListen(ConsumerRecord<String, String> record) {
        blackListMsg((record));
    }


    /**
     * 黑名单
     */
    public void blackListMsg(ConsumerRecord<String, String> record) {

        try {

            logger.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            BlackList model = JSON.parseObject(record.value(), BlackList.class);

            String cacheKey = String.format(BarRedisKey.FUND_GUBA_SERVICE_USERBLACKLIST_KAFKA, model.UID);

            switch (model.action_type) {
                case "del"://删除
                    RedisUtils.lrem(appCore.redisuserwrite, cacheKey, 0, model.TargetUID);
                    break;

                case "add"://添加
                    //先删除，再添加，去重
                    RedisUtils.lrem(appCore.redisuserwrite, cacheKey, 0, model.TargetUID);
                    RedisUtils.rpush(appCore.redisuserwrite, cacheKey, model.TargetUID);
                    break;
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }
}
