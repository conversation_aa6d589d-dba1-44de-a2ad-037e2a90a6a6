package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka;

import com.fasterxml.jackson.databind.JsonNode;
import com.mysql.cj.util.StringUtils;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.appconfig.AppEelasticsearchConfig;
import ttfund.web.communityservice.dao.kafka.PushAppToKafkaDao;
import ttfund.web.communityservice.utils.JacksonUtil;


import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 搜功能数据推送kafka
 * <p> 全量数据推送 时间：每两周一次 </p>
 *
 * @author：liyaogang
 * @date：2023/8/4 9:35
 */
@JobHandler("appFunctionPushJob")
@Component
public class AppFunctionPushJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(AppFunctionPushJob.class);

    @Resource
    PushAppToKafkaDao pushAppToKafkaDao;

    @Resource
    AppConstant appConstant;

    private static final String TOPIC_APP_FUNCTION = "bd-fundsearch-function";

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        logger.info("搜索功能数据开始推送kafka。");
        try {
            String paramJson = "{\n" +
                    "    \"size\": 1000,\n" +
                    "    \"from\": 0,\n" +
                    "    \"query\": {\n" +
                    "        \"match_all\": {}\n" +
                    "    }\n" +
                    "}";
            String url = appConstant.BAR_ES_OPERATION_URL
                    + "/" + AppEelasticsearchConfig.java_es_index_unite_app_function
                    + "/" + AppEelasticsearchConfig.java_es_index_unite_app_function_type
                    + "/_search";
            String html = HttpHelper.requestPostJson(url, paramJson, 5000);
            if (StringUtils.isNullOrEmpty(html)) {
                return ReturnT.SUCCESS;
            }
            JsonNode jsonNode = JacksonUtil.deserialize(html, JsonNode.class);
            if (jsonNode != null && jsonNode.size() > 0) {
                JsonNode hits = jsonNode.get("hits");
                if (hits != null && hits.size() > 0) {
                    JsonNode listNode = hits.get("hits");
                    List<JsonNode> appList = new ArrayList<>();
                    if (listNode != null && listNode.size() > 0) {
                        List<JsonNode> jsonNodes = JacksonUtil.deserialize(JacksonUtil.obj2String(listNode), List.class, JsonNode.class);
                        if (jsonNodes != null && jsonNodes.size() > 0) {
                            for (JsonNode node : jsonNodes) {
                                appList.add(node.get("_source"));
                            }
                        }
                    }
                    //push
                    pushAppToKafkaDao.sendMessage(TOPIC_APP_FUNCTION, appList);
                    logger.info("本周推送功能数据共{}条。", appList.size());
                }
            }
        } catch (Exception e) {
            logger.error("推送功能数据失败，error:{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
