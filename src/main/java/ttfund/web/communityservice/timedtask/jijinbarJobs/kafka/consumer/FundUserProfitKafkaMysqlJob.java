package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.ttfund.web.base.helper.CacheHelper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.mongo.FundUserProfitPostModel;
import ttfund.web.communityservice.bean.jijinBar.user.FundUserProfitkafkaModel;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.msyql.ProfitDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户交易持仓数据同步kafka-Mysql
 */
@Component
public class FundUserProfitKafkaMysqlJob {

    private static Logger logger = LoggerFactory.getLogger(FundUserProfitKafkaMysqlJob.class);

    private static final String KAFKA_LISTENER_ID = "FundUserProfitKafkaMysqlJob";

    private static final List<String> FIELDS = Arrays.asList("ID", "FINDSCORE", "TIME", "TIMEPOINT", "TYPE", "YUANID", "UID", "CODE");

    private static final String POSTS_CACHE_KEY = "FundUserProfitKafkaMysqlJob_posts_%s";

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private ProfitDao profitDao;

    @Autowired
    private PostDao postDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.FUND_USER_PROFIT}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_FUNDUSERPROFITKAFKAMYSQLJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_fundproduct)
    public void hanlderKafka(ConsumerRecord<String, String> record) {

        int length = 100;
        if (StringUtils.hasLength(record.value()) && record.value().length() < length) {
            length = record.value().length();
        }

        try {

            logger.info("打印。partition：{}，offset：{}，timestamp：{}，key：{}，数据：{}",
                    record.partition(),
                    record.offset(),
                    record.timestamp(),
                    record.key(),
                    !StringUtils.hasLength(record.value()) ? record.value() : record.value().substring(0, length)
            );

            //用户持仓数据
            List<FundUserProfitkafkaModel> kafkaUserProfitList = JacksonUtil.string2Obj(record.value(), List.class, FundUserProfitkafkaModel.class);

            /*
             * 建议优化方案：
             * 1.设置mongdb或缓存 pid_code 为主键，单独一张表（用户收益）
             * 2.用户持仓帖子数据进行增量更新（1.发帖时增量，2.用户持仓更新时增量）
             *
             * *******************************************************************
             * 当前逻辑
             * 0.先把数据保存到Mysql
             * 1.根据用户PID 和 code 获取 该用户的发帖列表
             * 2.更新用户持仓帖子表
             * 3.kafka 推送持仓数据时更新
             * 4.新发帖时更新
             *
             */


            //只保留近一年有发帖的用户数据

            if (CollectionUtils.isEmpty(kafkaUserProfitList)) {
                return;
            }

            List<String> totalUids = kafkaUserProfitList
                    .stream()
                    .filter(a -> StringUtils.hasLength(a.PID))
                    .map(a -> a.PID)
                    .distinct()
                    .collect(Collectors.toList());
            initUserPosts(totalUids, appConstant.fundUserProfitKafkaMysqlJobBatchUidCount, appConstant.fundUserProfitKafkaMysqlJobExpire, false);


            List<FundUserProfitkafkaModel> upsertList = new ArrayList<>();
            List<FundUserProfitkafkaModel> updateList = new ArrayList<>();

            List<FundUserProfitPostModel> posts = null;

            for (FundUserProfitkafkaModel item : kafkaUserProfitList) {
                item.generateHoldMoth();

                if (item.HasShare == false) {
                    updateList.add(item);

                    logger.info("打印详情。partition：{}，offset：{}，timestamp：{}，key：{}，数据：{}",
                            record.partition(),
                            record.offset(),
                            record.timestamp(),
                            record.key(),
                            JacksonUtil.obj2String(item)
                    );

                    continue;
                }

                posts = getUserPostsByCache(item.PID, item.FCODE);
                if (!CollectionUtils.isEmpty(posts)) {
                    upsertList.add(item);

                    logger.info("打印详情。partition：{}，offset：{}，timestamp：{}，key：{}，数据：{}",
                            record.partition(),
                            record.offset(),
                            record.timestamp(),
                            record.key(),
                            JacksonUtil.obj2String(item)
                    );
                }
            }

            if (!CollectionUtils.isEmpty(updateList)) {
                List<List<FundUserProfitkafkaModel>> batchList = CommonUtils.toSmallList2(updateList, appConstant.fundUserProfitKafkaMysqlJobBatchWriteCount);
                for (List<FundUserProfitkafkaModel> batch : batchList) {
                    profitDao.updateMany(batch);
                }
            }

            if (!CollectionUtils.isEmpty(upsertList)) {
                List<List<FundUserProfitkafkaModel>> batchList = CommonUtils.toSmallList2(upsertList, appConstant.fundUserProfitKafkaMysqlJobBatchWriteCount);
                for (List<FundUserProfitkafkaModel> batch : batchList) {
                    profitDao.upsertMany(batch);
                }
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);

            logger.info("报错。partition：{}，offset：{}，timestamp：{}，key：{}，数据：{}",
                    record.partition(),
                    record.offset(),
                    record.timestamp(),
                    record.key(),
                    !StringUtils.hasLength(record.value()) ? record.value() : record.value().substring(0, length)
            );
        }

    }


    public void initUserPosts(List<String> uids, Integer batchUidCount, Long expire, boolean force) {
        if (CollectionUtils.isEmpty(uids)) {
            return;
        }

        List<String> notInUids = new ArrayList<>();
        String cacheKey = null;
        LinkedMultiValueMap<String, FundUserProfitPostModel> userPostMap = null;
        for (String uid : uids) {
            if (!force) {
                cacheKey = String.format(POSTS_CACHE_KEY, uid);
                userPostMap = CacheHelper.get(cacheKey);

                if (userPostMap != null) {
                    continue;
                }
            }

            notInUids.add(uid);
        }

        if (CollectionUtils.isEmpty(notInUids)) {
            return;
        }

        List<FundUserProfitPostModel> posts = null;
        List<List<String>> batchList = CommonUtils.toSmallList2(notInUids, batchUidCount);
        for (List<String> batch : batchList) {
            posts = postDao.getPostsByUids(FIELDS,
                    FundUserProfitPostModel.class,
                    batch
            );

            LinkedMultiValueMap<String, FundUserProfitPostModel> userPostListMap = new LinkedMultiValueMap<>();
            if (!CollectionUtils.isEmpty(posts)) {
                for (FundUserProfitPostModel a : posts) {
                    userPostListMap.add(a.UID, a);
                }
            }

            for (Map.Entry<String, List<FundUserProfitPostModel>> entry : userPostListMap.entrySet()) {

                userPostMap = new LinkedMultiValueMap<>();
                cacheKey = String.format(POSTS_CACHE_KEY, entry.getKey());
                posts = entry.getValue();
                if (!CollectionUtils.isEmpty(posts)) {
                    for (FundUserProfitPostModel a : posts) {
                        userPostMap.add(a.CODE, a);
                    }
                }

                CacheHelper.put(cacheKey, userPostMap, expire);
            }
        }
    }

    public List<FundUserProfitPostModel> getUserPostsByCache(String uid, String code) {
        List<FundUserProfitPostModel> result = null;

        String cacheKey = String.format(POSTS_CACHE_KEY, uid);
        LinkedMultiValueMap<String, FundUserProfitPostModel> userPostMap = CacheHelper.get(cacheKey);
        if (userPostMap != null) {
            result = userPostMap.get(code);
        }
        return result;
    }
}
