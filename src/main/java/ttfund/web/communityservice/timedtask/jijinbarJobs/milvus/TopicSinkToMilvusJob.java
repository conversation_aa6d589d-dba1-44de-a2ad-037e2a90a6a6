package ttfund.web.communityservice.timedtask.jijinbarJobs.milvus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.eastmoney.particle.common.model.Result;
import com.eastmoney.particle.milvus.MilvusClient;
import com.eastmoney.particle.milvus.model.collection.CollectionLoadRequest;
import com.eastmoney.particle.milvus.model.vector.*;
import com.eastmoney.particle.tritoninference.TritonInferenceClient;
import com.eastmoney.particle.tritoninference.model.inference.InferenceRequest;
import com.eastmoney.particle.tritoninference.model.inference.InferenceResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.FundTopic;
import ttfund.web.communityservice.config.dataconfig.MilvusConstConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.msyql.FundTopicDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 基金话题同步至milvus
 * <p>
 * 需求 #666770 【基金吧6.14】APP智能化发文方案
 */
@Slf4j
@JobHandler("TopicSinkToMilvusJob")
@Component
public class TopicSinkToMilvusJob extends IJobHandler {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();


    private static final String TAB_REGEX = "<[^>]*?>";
    private static final Pattern TAB_REGEX_PATTERN = Pattern.compile(TAB_REGEX);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private FundTopicDao fundTopicDao;

    @Autowired
    private TritonInferenceClient tritonInferenceClient;

    @Autowired
    private MilvusClient milvusClient;


    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            List<String> filterPattern = new ArrayList<>();
            Long expire = null; //话题删除时间  毫秒数
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                JSONArray jsonArray = jsonObject.getJSONArray("filterPattern");
                if (!CollectionUtils.isEmpty(jsonArray)) {
                    jsonArray.forEach(a -> filterPattern.add((String) a));
                }
                expire = jsonObject.getLong("expire");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            log.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，expire：{}",
                    initBreakpoint,
                    batchReadCount,
                    expire
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.TOPICSINKTOMILVUSJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            deal(batchReadCount, filterPattern, expire);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount, List<String> filterPattern, Long expire) {

        String breakpointName = UserRedisConfig.TOPICSINKTOMILVUSJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            log.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("第一步，读取断点。断点:{}", breakpoint);

        List<FundTopic> dataList = fundTopicDao.getByUpdateTime(breakpointDate, batchReadCount);

        log.info("第二步，读取数据。数量:{}，头部id列表：{}",
                dataList == null ? 0 : dataList.size(),
                CollectionUtils.isEmpty(dataList) ? null : JSON.toJSONStringWithDateFormat(dataList.get(0), DateUtil.datePattern));

        if (!CollectionUtils.isEmpty(dataList)) {

            breakpointDate = dataList.get(dataList.size() - 1).UpdateTime;

            Map<String, Object> map = null;
            Integer deleteId = null;
            boolean bool = false;
            int i = 0;
            for (FundTopic a : dataList) {
                i++;

                ArrayNode data = getVector(a.htid);
                boolean exist = (data != null && data.size() > 0);
                if (!exist && a.DEL == 0) {

                    bool = filter(a, filterPattern);
                    if (bool) {
                        List<Double> vector = infer(String.format("%s %s", a.name, a.introduction));
                        if (vector == null) {
                            continue;
                        }

                        map = new HashMap<>();
                        map.put("id", a.htid);
                        map.put("showtime", a.CreateTime.getTime());
                        map.put("embedding", vector);
                        ArrayNode arrayNode = OBJECT_MAPPER.createArrayNode().addPOJO(map);
                        insertVector(arrayNode);
                    }

                    log.info("第三步，数据写库详情-插入。第{}/{}个。条件：{}，数据：{}", i, dataList.size(), bool, JSON.toJSONString(map));

                } else if (exist && a.DEL == 1) {

                    deleteVector(a.htid);
                    deleteId = a.htid;

                    log.info("第三步，数据写库详情-DEL删除。第{}/{}个。数据：{}", i, dataList.size(), deleteId);

                } else {
                    log.info("第三步，数据写库详情-忽略。第{}/{}个。数据：{}", i, dataList.size(), a.htid);
                }
            }

            log.info("第三步，数据写库完成。总数量:{}，删除：{}，插入：{}",
                    dataList == null ? 0 : dataList.size(),
                    deleteId,
                    map == null ? null : JSON.toJSONStringWithDateFormat(map, DateUtil.datePattern)
            );

            CollectionLoadRequest request = new CollectionLoadRequest();
            request.setDbName(MilvusConstConfig.DB_WEB);
            request.setCollectionName(MilvusConstConfig.COLLECTION_TOPICS);
            milvusClient.load(request);


            log.info("第四步，数据库load操作完成。总数量:{}，删除：{}，插入：{}",
                    dataList == null ? 0 : dataList.size(),
                    deleteId,
                    map == null ? null : JSON.toJSONStringWithDateFormat(map, DateUtil.datePattern)
            );

        }

        if (expire != null) {
            long showtime = System.currentTimeMillis() - expire;
            deleteVectorByShowtime(showtime);

            CollectionLoadRequest request = new CollectionLoadRequest();
            request.setDbName(MilvusConstConfig.DB_WEB);
            request.setCollectionName(MilvusConstConfig.COLLECTION_TOPICS);
            milvusClient.load(request);

            log.info("第五步，数据库清除过期数据完成。showtime:{}", showtime);
        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        log.info("第六步，更新断点。断点：{}", breakpoint);
    }


    private boolean filter(FundTopic fundTopic, List<String> filterPattern) {

        if (!StringUtils.hasLength(fundTopic.name)) {
            return false;
        }

        if (StringUtils.hasLength(fundTopic.name)) {
            Matcher matcher = TAB_REGEX_PATTERN.matcher(fundTopic.name);
            while (matcher.find()) {
                fundTopic.name = fundTopic.name.replace(matcher.group(0), "");
            }
        }

        if (!CollectionUtils.isEmpty(filterPattern)
                && StringUtils.hasLength(fundTopic.name)
                && filterPattern.stream().filter(a -> fundTopic.name.contains(a)).findFirst().orElse(null) != null) {
            return false;
        }

        return true;
    }

    public List<Double> infer(String query) {

        List<Double> result = null;

        InferenceRequest request = new InferenceRequest();

        List<InferenceRequest.RequestInput> inputs = new ArrayList<>();
        InferenceRequest.RequestInput input = new InferenceRequest.RequestInput();
        input.setName("text");
        input.setDataType("BYTES");
        input.setShape(Arrays.asList(1L, 1L));
        input.setData(new ObjectMapper().createArrayNode().add(query));
        inputs.add(input);
        request.setInputs(inputs);

        Result<InferenceResponse> response = tritonInferenceClient.inference("m3e-base", "1", request);

        if (response != null && response.getData() != null
                && !CollectionUtils.isEmpty(response.getData().getOutputs())
                && response.getData().getOutputs().get(0).getData() != null
                && response.getData().getOutputs().get(0).getData().size() > 0
        ) {
            ArrayNode data = response.getData().getOutputs().get(0).getData();
            result = new ArrayList<>();
            for (JsonNode a : data) {
                result.add(a.asDouble());
            }
        }
        return result;
    }


    private void deleteVector(int htid) {
        VectorDeleteRequest request = new VectorDeleteRequest();
        request.setDbName(MilvusConstConfig.DB_WEB);
        request.setCollectionName(MilvusConstConfig.COLLECTION_TOPICS);
        request.setFilter(String.format("id in [%s]", htid));

        Result<VectorDeleteResponse> response = milvusClient.delete(request);
    }

    private void deleteVectorByShowtime(long showtime) {
        VectorDeleteRequest request = new VectorDeleteRequest();
        request.setDbName(MilvusConstConfig.DB_WEB);
        request.setCollectionName(MilvusConstConfig.COLLECTION_TOPICS);
        request.setFilter(String.format("showtime < %s", showtime));

        Result<VectorDeleteResponse> response = milvusClient.delete(request);
    }

    private ArrayNode getVector(int htid) {
        VectorGetRequest request = new VectorGetRequest();
        request.setDbName(MilvusConstConfig.DB_WEB);
        request.setCollectionName(MilvusConstConfig.COLLECTION_TOPICS);
        request.setId(Arrays.asList(htid));

        Result<VectorGetResponse> response = milvusClient.get(request);

        if (response != null && response.getData() != null && response.getData().getData() != null) {
            return response.getData().getData();
        }

        return null;
    }

    private void insertVector(ArrayNode data) {
        VectorInsertRequest request = new VectorInsertRequest();
        request.setDbName(MilvusConstConfig.DB_WEB);
        request.setCollectionName(MilvusConstConfig.COLLECTION_TOPICS);
        request.setData(data);

        Result<VectorInsertResponse> response = milvusClient.insert(request);

    }

}
