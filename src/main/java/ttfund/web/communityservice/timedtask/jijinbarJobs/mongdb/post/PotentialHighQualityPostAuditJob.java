package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.HighQualityPostDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 潜在优质贴审核job
 */
@JobHandler("PotentialHighQualityPostAuditJob")
@Component
public class PotentialHighQualityPostAuditJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(PotentialHighQualityPostAuditJob.class);

    private String verticaTableName = "CONTENT.JJB_TB_POST_QUILITY_BASIC_APP_MED";

    private static String SQL_QUERY = "select POSTID, ISSZT, EUTIME from %s where ISSZT = '1' and EUTIME >= ? order by EUTIME asc limit ?";

    private static final List<String> SET_FIELDS = null;

    private static final String SELECT_FIELDS = "ID,TITLE,UID,TIME";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private HighQualityPostDao highQualityPostDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {
            String initBreakpoint = null;
            Integer batchReadCount = null;
            String verticaTableName = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                verticaTableName = jsonObject.getString("verticaTableName");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (!StringUtils.hasLength(verticaTableName)) {
                verticaTableName = "CONTENT.JJB_TB_POST_QUILITY_BASIC_APP_MED";
            }
            if (StringUtils.hasLength(verticaTableName)) {
                this.verticaTableName = verticaTableName;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，verticaTableName：{}",
                    initBreakpoint,
                    batchReadCount,
                    verticaTableName);

            if (StringUtils.hasLength(initBreakpoint)) {
                DateUtil.strToDate(initBreakpoint);
                userRedisDao.set(UserRedisConfig.POTENTIALHIGHQUALITYPOSTAUDITJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);

                logger.info("第零步，初始化断点。breakpoint：{}", initBreakpoint);
                return ReturnT.SUCCESS;
            }

            syncFromVerticaToMongo(batchReadCount);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 同步数据  从vertica到mongo
     * 把研究（亢林春）那边用模型识别为潜在优质贴的数据同步到社区，给社区审核后台审核，审核通过后就是最终能展示的优质贴
     */
    private void syncFromVerticaToMongo(int batchReadCount) throws Exception {

        String breakpointName = UserRedisConfig.POTENTIALHIGHQUALITYPOSTAUDITJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);
        if (!StringUtils.hasLength(breakpoint)) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-60));

            logger.error("第一步，获取断点为空，使用默认断点。断点：{}", breakpoint);
        }

        logger.info("第一步，打印断点。断点：{}", breakpoint);

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        List<Map<String, Object>> list = getFromVertica(breakpointDate, batchReadCount);

        logger.info("第二步，获取数据。数量：{}，头部id列表：{}",
                list == null ? 0 : list.size(),
                list == null ? null : list.stream().map(a -> (String) a.get("POSTID")).limit(50).collect(Collectors.toList())
        );

        if (!CollectionUtils.isEmpty(list)) {
            breakpointDate = (Date) (list.get(list.size() - 1).get("EUTIME"));

            List<PostInfoNewModel> postInfoList = new ArrayList<>();
            List<Integer> ids = list.stream()
                    //.filter(a -> a.get("ISSZT") != null && "1".equals(a.get("ISSZT")))
                    .map(a -> Integer.parseInt((String) a.get("POSTID")))
                    .collect(Collectors.toList());
            List<List<Integer>> batchList = CommonUtils.toSmallList2(ids, 200);
            for (List<Integer> batch : batchList) {
                List<PostInfoNewModel> tempList = postInfoNewDao.getPostInfoNewByIds(batch, SELECT_FIELDS);
                if (!CollectionUtils.isEmpty(tempList)) {
                    postInfoList.addAll(tempList);
                }
            }

            List<Map<String, Object>> insertList = new ArrayList<>();
            Object temp = null;
            Map<String, Object> tempMap = null;
            for (PostInfoNewModel item : postInfoList) {

                tempMap = new HashMap<>();
                tempMap.put("_id", String.valueOf(item.ID));
                tempMap.put("state", 0);
                tempMap.put("createTime", new Date());
                tempMap.put("updateTime", new Date());
                tempMap.put("title", item.TITLE);
                tempMap.put("uid", item.UID);
                tempMap.put("time", item.TIME);


                insertList.add(tempMap);
            }

            logger.info("第三步，处理数据。数量：{}，头部id列表：{}",
                    insertList == null ? 0 : insertList.size(),
                    insertList == null ? null : insertList.stream().map(a -> (String) a.get("_id")).limit(50).collect(Collectors.toList())
            );

            if (!CollectionUtils.isEmpty(insertList)) {
                List<List<Map<String, Object>>> batchList1 = CommonUtils.toSmallList2(insertList, 200);
                for (List<Map<String, Object>> batch1 : batchList1) {
                    highQualityPostDao.upsertManyBySetOnInsertWithSetFields(batch1, SET_FIELDS);
                }
            }

            logger.info("第四步，数据写库。数量：{}，头部id列表：{}",
                    insertList == null ? 0 : insertList.size(),
                    insertList == null ? null : insertList.stream().map(a -> (String) a.get("_id")).limit(50).collect(Collectors.toList())
            );

        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);
        logger.info("第五步，更新断点。断点：{}", breakpoint);

    }

    private List<Map<String, Object>> getFromVertica(Date time, int batchReadCount) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();
        Connection conn = null;
        PreparedStatement preparedStatement = null;
        try {

            conn = app.bigdataVertica.getconn();

            String preparedSql = String.format(SQL_QUERY, verticaTableName);
            preparedStatement = conn.prepareStatement(preparedSql);
            int i = 0;
            preparedStatement.setObject(++i, time);
            preparedStatement.setObject(++i, batchReadCount);
            ResultSet resultSet = preparedStatement.executeQuery();
            Map<String, Object> tempMap = null;
            Object temp = null;

            //获取键名
            ResultSetMetaData md = resultSet.getMetaData();

            //获取行的数量
            int columnCount = md.getColumnCount();

            while (resultSet.next()) {
                tempMap = new HashMap<>();
                for (int j = 1; j <= columnCount; j++) {
                    temp = resultSet.getObject(j);
                    tempMap.put(md.getColumnLabel(j), temp);
                }
                result.add(tempMap);
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            throw ex;
        } finally {
            try {
                if (preparedStatement != null) {
                    preparedStatement.close();
                }
                if (conn != null) {
                    conn.close();
                }
            } catch (Exception ex) {
                logger.error(ex.getMessage(), ex);
            }
        }

        return result;
    }
}
