package ttfund.web.communityservice.timedtask.jijinbarJobs.postOperate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import oracle.sql.TIMESTAMP;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.guba.ReqeustCodeDealRes;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.LiveShowSummaryPostDao;
import ttfund.web.communityservice.dao.oracle.research.LiveShowFullSummaryDao;
import ttfund.web.communityservice.dao.oracle.research.LiveShowPartSummaryDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.service.BigShotShowApiServiceImpl;
import ttfund.web.communityservice.service.PostOptApiService;
import ttfund.web.communityservice.service.common.CommonDataCache;
import ttfund.web.communityservice.service.entity.PostArticleRequest;
import ttfund.web.communityservice.service.entity.PostArticleResponse;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 智能回答job
 */
@Slf4j
@JobHandler("LiveShowSummaryPostJob")
@Component
public class LiveShowSummaryPostJob extends IJobHandler {

    private static String TEMPLATE_TITLE = "【AI速看】%s";

    private static String HTML_TEMPLATE_CONTENT = "<span>%s</span>";

    private static String HTML_TEMPLATE_SUMMARY_MAIN = "<p style=\"font-size:18px;margin:0!important;\">本文由AI总结直播《%s》生成</p>" +
        "<p ref=\"useP\" style=\"font-weight: bold; background-color: #f5f5f5; border-radius: 5px; padding: 12px; font-size: 14px; color: #9B9B9B; margin: 12px 0;line-height: 21px;\">" +
        "<span ref=\"use\" style=\"color: #9B9B9B;\">全文摘要：%s</span>" +
        "</p>";

    private static String HTML_TEMPLATE_IMG = "<div xmlns=\"http://www.w3.org/1999/xhtml\" class=\"wrap-img\" ref=\"pictureBlock\">" +
        "<span class=\"fund_video_img\" withdesc=\"true\" showId=\"%s\" src=\"%s\" alt=\"\" data-imglabel=\"\" />" +
        "</div>" +
        "<br />";

    private static String HTML_TEMPLATE_SUMMARY_PART = "<span style=\"themeColor:themeColor\" ref=\"normal\">" +
        "%s" +
        "</span>";

    private static String HTML_TEMPLATE_SUMMARY_PART_ITEM = "<p style=\"font-size:18px;font-weight:bold;margin:0 0 4px!important;\">%s</p>" +
        "<p style=\"font-size:16px;margin:0 0 12px!important;\" class=\"ai_content\">%s</p>";

    private static String CLAIM = "<p style=\"font-size:12px;color:#808080 !important;margin:24px 0 0!important;\">免责声明：本内容由AI生成。 针对AI生成的内容，不代表天天基金的立场、态度或观点，也不能作为专业性建议或意见，仅供参考。您须自行对其中包含的数字、时间以及各类事实性描述等内容进行核实。</p>";

    private static String IP = "************";

    private static String UID = "6051037243792258";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private LiveShowFullSummaryDao liveShowFullSummaryDao;

    @Autowired
    private LiveShowPartSummaryDao liveShowPartSummaryDao;

    @Autowired
    private LiveShowSummaryPostDao liveShowSummaryPostDao;

    @Autowired
    private BigShotShowApiServiceImpl bigShotShowApiService;

    @Autowired
    private CommonDataCache commonDataCache;

    @Autowired
    private PostOptApiService postOptApiService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            Long sleepInterval = null;
            String uid = null;
            String ip = null;
            String claim = null;
            Integer productCount = null;
            List<Integer> productType = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                sleepInterval = jsonObject.getLong("sleepInterval");
                uid = jsonObject.getString("uid");
                ip = jsonObject.getString("ip");
                claim = jsonObject.getString("claim");
                productCount = jsonObject.getInteger("productCount");
                JSONArray jsonArray = jsonObject.getJSONArray("productType");
                if (!CollectionUtils.isEmpty(jsonArray)) {
                    productType = jsonArray.stream().map(a -> (Integer)a).collect(Collectors.toList());
                }
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            if (sleepInterval == null) {
                sleepInterval = 30 * 1000L;
            }

            if (productCount == null) {
                productCount = 5;
            }
            if (productType == null) {
                productType = Arrays.asList(0);
            }

            if (uid != null) {
                UID = uid;
            }
            if (ip != null) {
                IP = ip;
            }
            if (claim != null) {
                CLAIM = claim;
            }

            log.info("0.打印参数。initBreakpoint：{}，batchReadCount：{}，sleepInterval：{}，productCount：{}，productType：{}，uid：{}，ip：{}，claim：{}",
                initBreakpoint,
                batchReadCount,
                sleepInterval,
                productCount,
                productType,
                UID,
                IP,
                CLAIM
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.LIVESHOWSUMMARYPOSTJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("0.初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            deal(batchReadCount, sleepInterval, productCount, productType);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount, Long sleepInterval, int productCount, List<Integer> productType) throws Exception {
        String breakpointName = UserRedisConfig.LIVESHOWSUMMARYPOSTJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            log.error("0.读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("1.读取断点。断点:{}", breakpoint);


        List<Map<String, Object>> dataList = liveShowFullSummaryDao.getListByUpdatetime("*", batchReadCount, breakpointDate);

        log.info("2.读取数据。数量:{}，头部列表：{}",
            dataList == null ? 0 : dataList.size(),
            CollectionUtils.isEmpty(dataList) ? null : JSON.toJSONStringWithDateFormat(dataList.get(0), DateUtil.datePattern));

        if (!CollectionUtils.isEmpty(dataList)) {

            breakpointDate = ((TIMESTAMP)dataList.get(dataList.size() - 1).get("UPDATETIME")).dateValue();
        }

        if (!CollectionUtils.isEmpty(dataList)) {
            Set<String> alreadySet = new HashSet<>();

            List<String> channelIds = dataList.stream().map(a -> a.get("CHANNEL_ID").toString()).collect(Collectors.toList());
            List<List<String>> batchList = CommonUtils.toSmallList2(channelIds, 50);
            for (List<String> batch : batchList) {
                List<Document> tempList = liveShowSummaryPostDao.getListByChannelIds(Document.class, null, batch);
                if (!CollectionUtils.isEmpty(tempList)) {
                    tempList.forEach(a -> alreadySet.add(a.getString("_id")));
                }
            }

            dataList = dataList.stream().filter(o -> !alreadySet.contains(o.get("CHANNEL_ID").toString())).collect(Collectors.toList());
        }

        log.info("3.去重。数量:{}，头部列表：{}",
            dataList == null ? 0 : dataList.size(),
            CollectionUtils.isEmpty(dataList) ? null : JSON.toJSONStringWithDateFormat(dataList.get(0), DateUtil.datePattern));

        if (!CollectionUtils.isEmpty(dataList)) {

            LinkedMultiValueMap<String, Map<String, Object>> partSummaryMap = new LinkedMultiValueMap<>();
            Map<String, JSONObject> bigShotShowMap = new HashMap<>();

            List<String> channelIds = dataList.stream().map(a -> a.get("CHANNEL_ID").toString()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(channelIds)) {
                List<List<String>> batchList = CommonUtils.toSmallList2(channelIds, 50);
                for (List<String> batch : batchList) {
                    List<Map<String, Object>> tempList = liveShowPartSummaryDao.getUsefulListByChannelIds("*", batch);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        tempList.forEach(a -> partSummaryMap.add(a.get("CHANNEL_ID").toString(), a));
                    }
                }

                for (List<String> batch : batchList) {
                    List<JSONObject> tempList = bigShotShowApiService.getBigShotShowByIdsForConfig(JSONObject.class, "1", batch);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        tempList.forEach(a -> bigShotShowMap.put(String.valueOf(a.get("roomNumber")), a));
                    }
                }
            }

            List<Map<String, Object>> partSummary = null;
            JSONObject bigShotShow = null;
            ReqeustCodeDealRes barInfo = null;
            String channelId = null;
            int i = 0;
            for (Map<String, Object> a : dataList) {
                i++;

                channelId = a.get("CHANNEL_ID").toString();

                partSummary = partSummaryMap.get(channelId);
                if (CollectionUtils.isEmpty(partSummary)) {
                    log.info("4.处理-第{}/{}个。房间号：{}，错误信息：{}",
                        i,
                        dataList.size(),
                        channelId,
                        "分段摘要为空"
                    );

                    continue;
                }

                bigShotShow = bigShotShowMap.get(channelId);
                if (bigShotShow == null) {

                    log.info("4.处理-第{}/{}个。房间号：{}，错误信息：{}",
                        i,
                        dataList.size(),
                        channelId,
                        "大咖秀信息为空"
                    );
                    continue;
                }

                List<ReqeustCodeDealRes> barInfoList = null;
                String liveTitle = bigShotShow.get("title").toString();
                String liveShareImg = bigShotShow.get("shareImg").toString();
                List<Map<String, Object>> relatedFunds = new ArrayList<>();
                JSONArray jsonArray = bigShotShow.getJSONArray("relatedFunds");
                if (!CollectionUtils.isEmpty(jsonArray) && !CollectionUtils.isEmpty(productType)) {
                    relatedFunds = jsonArray
                        .stream()
                        .map(o -> (JSONObject)o)
                        .filter(o -> productType.contains(o.get("productType")))
                        .limit(productCount)
                        .collect(Collectors.toList());
                }

                if (CollectionUtils.isEmpty(relatedFunds)) {
                    relatedFunds = new ArrayList<>();
                    JSONObject defaultFund = new JSONObject();
                    defaultFund.put("fundCode", "jjdt");
                    relatedFunds.add(defaultFund);
                }

                if (!CollectionUtils.isEmpty(relatedFunds)) {
                    barInfoList = relatedFunds
                        .stream()
                        .map(o -> commonDataCache.reqeustCodeDeal(o.get("fundCode").toString()))
                        .filter(o -> o != null && StringUtils.hasLength(o.getGuba_code()))
                        .collect(Collectors.toList());
                }

                if (CollectionUtils.isEmpty(barInfoList)) {

                    log.info("4.处理-第{}/{}个。房间号：{}，错误信息：{}",
                        i,
                        dataList.size(),
                        channelId,
                        "吧列表为空"
                    );
                    continue;
                }

                String title = generatePostTitle(liveTitle);
                String content = generatePostContent(a, partSummary, bigShotShow);

                if (!StringUtils.hasLength(title)) {

                    log.info("4.处理-第{}/{}个。房间号：{}，错误信息：{}",
                        i,
                        dataList.size(),
                        channelId,
                        "生成标题为空"
                    );
                    continue;
                }

                if (!StringUtils.hasLength(content)) {

                    log.info("4.处理-第{}/{}个。房间号：{}，错误信息：{}",
                        i,
                        dataList.size(),
                        channelId,
                        "生成正文为空"
                    );
                    continue;
                }

                Map<String, Object> postResult = addPost(title, content, UID, barInfoList);

                log.info("4.发贴-第{}/{}个。房间号：{}，帖子id：{}，错误信息：{}",
                    i,
                    dataList.size(),
                    channelId,
                    postResult.get("postId"),
                    postResult.get("message")
                );


                if (postResult.get("postId") != null) {
                    Map<String, Object> intoDbMap = new HashMap<>();
                    intoDbMap.put("_id", channelId);
                    intoDbMap.put("channelId", channelId);
                    intoDbMap.put("postId", postResult.get("postId").toString());
                    intoDbMap.put("createTime", new Date());
                    intoDbMap.put("updateTime", new Date());
                    liveShowSummaryPostDao.upsertManyBySetWithSetOnInsertFields(Arrays.asList(intoDbMap), Arrays.asList("createTime"), "_id");

                    log.info("5.写库-第{}/{}个。房间号：{}，帖子id：{}，错误信息：{}",
                        i,
                        dataList.size(),
                        channelId,
                        postResult.get("postId"),
                        postResult.get("message")
                    );
                }

                Thread.sleep(sleepInterval);

            }

            log.info("6.发帖完成。数量:{}，头部id列表：{}",
                dataList == null ? 0 : dataList.size(),
                CollectionUtils.isEmpty(dataList) ? null : JSON.toJSONStringWithDateFormat(dataList.get(0), DateUtil.datePattern));

        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        log.info("7.更新断点。断点：{}", breakpoint);
    }

    private String generatePostTitle(String liveTitle) {
        String result = "";
        if (!StringUtils.hasLength(liveTitle)) {
            return result;
        }

        result = String.format(TEMPLATE_TITLE, liveTitle);
        return result;
    }

    private String generatePostContent(Map<String, Object> fullSummaryItem, List<Map<String, Object>> partSummaryList, Map<String, Object> bigShotShowItem) {
        String liveTitle = (String)bigShotShowItem.get("title");
        String liveShareImg = (String)bigShotShowItem.get("shareImg");
        String liveShowId = (String)bigShotShowItem.get("showId");

        String fullSummary = (String)fullSummaryItem.get("FULL_SUMMARY");

        String partTitle = null;
        String partSummary = null;

        if (!StringUtils.hasLength(liveTitle) || !StringUtils.hasLength(liveShareImg)
            || !StringUtils.hasLength(liveShowId) || !StringUtils.hasLength(fullSummary)) {
            return "";
        }

        String contentFullSummary = String.format(HTML_TEMPLATE_SUMMARY_MAIN, liveTitle, fullSummary);
        String contentImg = String.format(HTML_TEMPLATE_IMG, liveShowId, liveShareImg);
        String contentPartSummary = "";

        int num = 0;
        for (Map<String, Object> a : partSummaryList) {
            num++;

            partTitle = (String)a.get("PART_TITLE");
            partSummary = (String)a.get("PART_SUMMARY");

            if (!StringUtils.hasLength(partTitle) || !StringUtils.hasLength(partSummary)) {
                return "";
            }

            contentPartSummary += String.format(HTML_TEMPLATE_SUMMARY_PART_ITEM, num + " " + partTitle, partSummary);
        }

        contentPartSummary = String.format(HTML_TEMPLATE_SUMMARY_PART, contentPartSummary);

        return String.format(HTML_TEMPLATE_CONTENT, contentFullSummary + contentImg + contentPartSummary + CLAIM);
    }

    private Map<String, Object> addPost(String title, String text, String uid, List<ReqeustCodeDealRes> barInfoList) {
        Map<String, Object> result = new HashMap<>();
        if (!StringUtils.hasLength(title) || !StringUtils.hasLength(text) || !StringUtils.hasLength(uid) || CollectionUtils.isEmpty(barInfoList)) {
            result.put("postId", null);
            result.put("message", "参数不满足");

            return result;
        }

        PostArticleRequest request = new PostArticleRequest();
        request.setTitle(title);
        request.setText(text);
        request.setUid(uid);
        request.setCode(barInfoList.get(0).getGuba_code());
        request.setCodelist(generateCodeList(barInfoList == null ? null : barInfoList.stream().skip(1).collect(Collectors.toList())));
        request.setBizfrom("ai_fundpost_0");

        PostArticleResponse postArticleResponse = postOptApiService.postArticleReturnResponse(request);
        if (postArticleResponse == null) {
            result.put("message", "股吧接口返回为空");
        } else if (postArticleResponse.main_post_id == null || postArticleResponse.main_post_id == 0L) {
            result.put("message", postArticleResponse.me);
        } else {
            result.put("postId", postArticleResponse.main_post_id);
        }

        return result;
    }

    private String generateCodeList(List<ReqeustCodeDealRes> barInfoList) {
        String result = "";
        if (!CollectionUtils.isEmpty(barInfoList)) {
            result = String.join(",", barInfoList.stream().map(a -> a.getGuba_code()).collect(Collectors.toList()));
        }
        return result;
    }

}
