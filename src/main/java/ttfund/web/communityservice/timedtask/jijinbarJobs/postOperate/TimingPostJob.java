package ttfund.web.communityservice.timedtask.jijinbarJobs.postOperate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.CirclePostRelation;
import ttfund.web.communityservice.bean.jijinBar.data.GroupPostRelation;
import ttfund.web.communityservice.bean.jijinBar.data.GroupSceneKnowledgePlanet;
import ttfund.web.communityservice.bean.jijinBar.data.GroupTagPostRelationDto;
import ttfund.web.communityservice.bean.jijinBar.post.FundPostExtras;
import ttfund.web.communityservice.bean.jijinBar.post.FundPostExtrasProP;
import ttfund.web.communityservice.bean.jijinBar.post.postInfo.TimingPostInfoModel;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.PostInfoExtendDao;
import ttfund.web.communityservice.dao.mongo.TimingPostInfoDao;
import ttfund.web.communityservice.dao.msyql.CirclePostRelationMysqlDao;
import ttfund.web.communityservice.dao.msyql.GroupPostRelationMysqlDao;
import ttfund.web.communityservice.dao.msyql.GroupSceneKnowledgePlanetMysqlDao;
import ttfund.web.communityservice.dao.msyql.GroupTagPostRelationDao;
import ttfund.web.communityservice.dao.msyql.KnowledgePlanetPdfDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.service.entity.PostShipanRequest;
import ttfund.web.communityservice.service.GubaThirdPartOptApiServiceImpl;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.MD5Util;

import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * 定时发帖job
 */
@JobHandler("TimingPostJob")
@Component
public class TimingPostJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(TimingPostJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private TimingPostInfoDao timingPostInfoDao;

    @Autowired
    private PostInfoExtendDao postInfoExtendDao;

    @Autowired
    private CirclePostRelationMysqlDao circlePostRelationMysqlDao;

    @Autowired
    private GroupSceneKnowledgePlanetMysqlDao groupSceneKnowledgePlanetMysqlDao;

    @Autowired
    private GroupPostRelationMysqlDao groupPostRelationMysqlDao;

    @Autowired
    private GroupTagPostRelationDao groupTagPostRelationDao;

    @Autowired
    private KnowledgePlanetPdfDao knowledgePlanetPdfDao;

    @Autowired
    private GubaThirdPartOptApiServiceImpl gubaThirdPartOptApiService;

    @Qualifier("timingPostExecutor")
    @Autowired
    private ThreadPoolExecutor executor;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        //初始断点
        String initBreakpoint = null;
        //单次读取数量
        Integer count = null;
        //向前扫描的时间区间，单位秒
        Integer beforeInterval = null;
        //向后扫描的时间区间，单位秒
        Integer afterInterval = null;
        //开启异步模式处理的数据数量限制
        Integer asyncLimit = null;

        if (StringUtils.hasLength(s)) {
            JSONObject jsonObject = JSON.parseObject(s);
            initBreakpoint = jsonObject.getString("initBreakpoint");
            count = jsonObject.getInteger("count");
            beforeInterval = jsonObject.getInteger("beforeInterval");
            afterInterval = jsonObject.getInteger("afterInterval");
            asyncLimit = jsonObject.getInteger("asyncLimit");
        }

        if (count == null) {
            count = 1000;
        }
        if (beforeInterval == null) {
            beforeInterval = -5 * 60;
        }
        if (afterInterval == null) {
            afterInterval = 0;
        }
        if (asyncLimit == null) {
            asyncLimit = 100;
        }

        logger.info("第零步，打印参数。initBreakpoint：{}，count：{}，beforeInterval：{}，afterInterval：{}，asyncLimit：{}", initBreakpoint, count, beforeInterval, afterInterval, asyncLimit);

        if (StringUtils.hasLength(initBreakpoint)) {
            userRedisDao.set(UserRedisConfig.TIMINGPOSTJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);

            logger.info("第零步，初始化断点。断点：{}", initBreakpoint);
            return ReturnT.SUCCESS;
        }

        timingPost(beforeInterval, afterInterval, count, asyncLimit);

        return ReturnT.SUCCESS;
    }

    /**
     * 定时发帖
     */
    private void timingPost(int beforeInterval, int afterInterval, int count, int asyncLimit) {

        try {

            Date now = new Date();

            String breakpointName = UserRedisConfig.TIMINGPOSTJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);
            if (!StringUtils.hasLength(breakpoint)) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-10));

                logger.error("第一步，读取断点为空，使用默认断点。断点：{}", breakpoint);
            }

            logger.info("第一步，获取断点。断点：{}", breakpoint);

            Date breakpointDate = DateUtil.strToDate(breakpoint);
            Date beforeDate = DateUtil.calendarDateBySecond(breakpointDate, beforeInterval);
            Date afterDate = DateUtil.calendarDateBySecond(afterInterval);

            List<TimingPostInfoModel> list = timingPostInfoDao.getList(beforeDate, afterDate, count, true);

            logger.info("第二步，读取数据。beforeDate：{}，afterDate：{}，数量：{}，头部id列表：{}", DateUtil.dateToStr(beforeDate), DateUtil.dateToStr(afterDate), list == null ? 0 : list.size(), list == null ? null : list.stream().limit(20).map(a -> a.get_id()).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(list)) {
                if (list.size() < asyncLimit) {
                    for (int i = 0; i < list.size(); i++) {
                        dealUploadAndSavePost(i + 1, list.size(), list.get(i));
                    }
                } else {
                    dealAsync(list);
                }
            }

            if (!CollectionUtils.isEmpty(list)) {
                breakpointDate = list.get(list.size() - 1).getExpectTime();
            }

            if (breakpointDate.compareTo(now) > 0) {
                breakpointDate = now;
            }

            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

            logger.info("第六步，更新断点。断点：{}", breakpoint);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    public void dealAsync(List<TimingPostInfoModel> list) throws Exception {
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        String taceid = MDC.get(CoreConstant.logtraceid);
        for (int i = 0; i < list.size(); i++) {
            TimingPostInfoModel item = list.get(i);
            int finalI = i;
            executor.submit(() -> {
                try {
                    MDC.put(CoreConstant.logtraceid, taceid);

                    dealUploadAndSavePost(finalI + 1, list.size(), item);

                } catch (Exception ex) {
                    logger.error(ex.getMessage(), ex);
                } finally {
                    MDC.remove(CoreConstant.logtraceid);
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        executor.shutdown();
    }

    private void dealUploadAndSavePost(int i, int size, TimingPostInfoModel item) throws Exception {
        uploadPost(item);

        logger.info("第三步，发帖-第{}/{}个。数据：{}", i, size, item.printString());

        updateTimingPostInfoInDb(item);

        logger.info("第四步，数据写库-{}-第{}/{}个。数据：{}", BarMongodbConfig.TABLE_TIMINGPOSTINFO, i, size, item.printString());

        updatePostInfoExtendInDb(item);

        logger.info("第五步，数据写库-{}-第{}/{}个。数据：{}", BarMongodbConfig.TABLE_PostInfo_Extend, i, size, item.printString());
    }

    private void uploadPost(TimingPostInfoModel info) throws Exception {
        if (info != null && StringUtils.hasLength(info.getUid()) && StringUtils.hasLength(info.getCode()) && StringUtils.hasLength(info.getTitle()) && StringUtils.hasLength(info.getText())) {

            PostShipanRequest request = new PostShipanRequest();
            request.setUid(info.getUid());
            request.setCode(info.getCode());
            request.setJtype(info.getJtype());
            request.setTitle(StringUtils.hasLength(info.getTitle()) ? URLEncoder.encode(info
                .getTitle(), "utf-8") : "");
            request.setText(StringUtils.hasLength(info.getText()) ? URLEncoder.encode(info
                .getText(), "utf-8") : "");
            request.setPic(info.getPic());
            request.setIsTopic(info.getIsTopic() == null ? 0 : info.getIsTopic());
            request.setIp(info.getIp() == null ? "" : info.getIp());
            request.setApprovalfirst(1);
            request.setPlat("Web");
            request.setVersion("100");
            request.setDeviceid(MD5Util.generateHash(info.getUid()));
            String html = gubaThirdPartOptApiService.postShipanInTimeReturnString(request);

            info.setState(2);
            info.setActualTime(new Date());

            if (StringUtils.hasLength(html)) {
                JSONObject res = JSON.parseObject(html);
                Integer postId = res.getInteger("main_post_id");
                if (postId != null && postId != 0) {
                    info.setState(1);
                    info.setPostId(String.valueOf(postId));
                } else {
                    String message = res.getString("me");
                    if (StringUtils.hasLength(message)) {
                        info.setMessage(message);
                    } else {
                        info.setMessage("默认错误信息-发帖接口没有返回帖子id");
                    }
                }
            } else {
                info.setMessage("默认错误信息-发帖接口返回为空");
            }
        }
    }

    public void updateTimingPostInfoInDb(TimingPostInfoModel info) {
        if (info != null && StringUtils.hasLength(info.get_id())) {
            Map<String, Object> map = new HashMap<>();
            map.put("_id", info.get_id());
            map.put("state", info.getState());
            map.put("actualTime", info.getActualTime());
            if (StringUtils.hasLength(info.getPostId())) {
                map.put("postId", info.getPostId());
            }
            if (StringUtils.hasLength(info.getMessage())) {
                map.put("message", info.getMessage());
            }
            map.put("updateTime", new Date());

            timingPostInfoDao.updateMany(Arrays.asList(map));
        }
    }

    public void updatePostInfoExtendInDb(TimingPostInfoModel info) {
        if (info != null && StringUtils.hasLength(info.getPostId()) && !CollectionUtils.isEmpty(info.getExtraList())) {

            FundPostExtras fundPostExtras = new FundPostExtras();
            fundPostExtras._id = info.getPostId();
            fundPostExtras.TID = Long.parseLong(info.getPostId());
            fundPostExtras.PROP = info.getExtraList();
            fundPostExtras.PUBTIME = new Date();

            postInfoExtendDao.insert(fundPostExtras);

            for (FundPostExtrasProP a : info.getExtraList()) {
                if (a.TYPE == 80 && !CollectionUtils.isEmpty(a.CODE)) {
                    CirclePostRelation relation = new CirclePostRelation();
                    relation.setCircleId(a.CODE.get(0));
                    relation.setPostId(String.valueOf(info.getPostId()));
                    relation.setState(0);
                    relation.setJoinType(0);
                    relation.setJoinTime(new Date());
                    relation.setIsDel(0);
                    relation.setCreateTime(new Date());
                    relation.setUpdateTime(new Date());
                    circlePostRelationMysqlDao.replaceMany(Arrays.asList(relation));
                } else if (a.TYPE == 81 && !CollectionUtils.isEmpty(a.CODE)) {
                    int groupId = Integer.parseInt(a.CODE.get(0));
                    GroupSceneKnowledgePlanet knowledgePlanet = groupSceneKnowledgePlanetMysqlDao.getByGroupId(groupId);
                    if (knowledgePlanet == null) {
                        continue;
                    }

                    GroupPostRelation relation = new GroupPostRelation();
                    relation.setGroupId(groupId);
                    relation.setGroupType(knowledgePlanet.getGroupType());
                    relation.setPostId(info.getPostId());
                    relation.setState(0);
                    relation.setJoinType(0);
                    relation.setJoinTime(new Date());
                    groupPostRelationMysqlDao.insertManyWhenPost(Arrays.asList(relation));

                    if (StringUtils.hasLength(a.EXTEND)) {
                        GroupTagPostRelationDto relationDto = new GroupTagPostRelationDto();
                        relationDto.setTagId(Integer.parseInt(a.EXTEND));
                        relationDto.setPostId(info.getPostId());
                        groupTagPostRelationDao.insertManyWhenPost(Arrays.asList(relationDto));
                    }
                } else if (a.TYPE == 82 && !CollectionUtils.isEmpty(a.CODE)) {
                    String groupId = info.getExtraList().stream().filter(o -> o.TYPE == 81 && !CollectionUtils.isEmpty(o.CODE)).map(o -> o.CODE.get(0)).findFirst().orElse(null);
                    knowledgePlanetPdfDao.updateWhenPost(a.CODE.get(0), info.getPostId(), groupId);
                }
            }

        }
    }
}
