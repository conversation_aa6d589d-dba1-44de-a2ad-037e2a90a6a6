package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.ExcellentReplyIntoPostDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 优质评论发帖帖子同步job
 * 备注：    需求：#659633 【基金吧6.13.4】优质评论转发贴
 */
@JobHandler("ExcellentReplyIntoPostSyncJob")
@Component
public class ExcellentReplyIntoPostSyncJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(ExcellentReplyIntoPostSyncJob.class);

    private static List<String> includeFields = Arrays.asList("UID", "TYPE", "CODE", "CODELIST", "TIME", "TIMEPOINT", "DEL", "TTJJDEL", "UPDATETIME");

    private static String selectFields = "ID, UID, TYPE, CODE, CODELIST, TIME, TIMEPOINT, DEL, TTJJDEL, UPDATETIME";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private ExcellentReplyIntoPostDao excellentReplyIntoPostDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.EXCELLENTREPLYINTOPOSTSYNCJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            deal(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount) {

        String breakpointName = UserRedisConfig.EXCELLENTREPLYINTOPOSTSYNCJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("第一步，读取断点。断点:{}", breakpoint);


        List<Map<String, Object>> list = postInfoNewDao.getListByUpdateTime(selectFields, breakpointDate, batchReadCount);

        logger.info("第二步，读取数据。数量:{}，头部id列表：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = (Date) list.get(list.size() - 1).get("UPDATETIME");

            List<Long> ids = list.stream().map(a -> Long.parseLong(a.get("ID").toString())).collect(Collectors.toList());

            Set<Long> existIds = new HashSet<>();
            List<List<Long>> batchList = CommonUtils.toSmallList2(ids, 200);
            for (List<Long> batch : batchList) {
                List<Document> documents = excellentReplyIntoPostDao.getListByPostIds(Arrays.asList("POSTID"), batch);
                if (!CollectionUtils.isEmpty(documents)) {
                    existIds.addAll(documents.stream().map(a -> Long.parseLong(a.get("POSTID").toString())).collect(Collectors.toList()));
                }
            }

            list = list.stream().filter(a -> existIds.contains(Long.parseLong(a.get("ID").toString()))).collect(Collectors.toList());

        }

        logger.info("第三步，过滤数据。数量:{}，头部id列表：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(a -> {
                        a.put("POSTID", Long.parseLong(a.get("ID").toString()));
                        a.put("UPDATETIME", new Date());
                    }
            );

            List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(list, 200);
            for (List<Map<String, Object>> batch : batchList) {
                excellentReplyIntoPostDao.updateManyBySetWithIncludeFields(batch, includeFields, "POSTID");
            }

        }

        logger.info("第四步，写库。数量:{}，头部id列表：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        logger.info("第五步，更新断点。断点：{}", breakpoint);

    }

}
