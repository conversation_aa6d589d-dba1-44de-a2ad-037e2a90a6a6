package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.PostHtRelationModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostTopicModel;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.constant.DateConstant;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.msyql.PostTopicRetDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基金吧帖子所属话题
 */
@JobHandler("PostTopicJob")
@Component
public class PostTopicJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(PostTopicJob.class);

    private static final String LOG_PREFIX = "PostTopicJob[基金吧帖子所属话题]";

    private static final int BATCH_SIZE = 5000;

    private static final String BREAK_TIME_KEY = UserRedisConfig.POST_TOPIC_JOB_BREAKPOINT;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostTopicRetDao postTopicRetDao;

    @Autowired
    private PostDao postDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        logicDeal();
        return ReturnT.SUCCESS;
    }

    public void logicDeal() {
        try {
            //上次更新时间
            Date dateForBreakpoint = userRedisDao.getBreakTime(BREAK_TIME_KEY, DateUtil.calendarDateByYears(-3));

            List<PostHtRelationModel> list = postTopicRetDao.get(dateForBreakpoint, BATCH_SIZE);
            LOGGER.info("{}: 拉取帖子话题关系列表长度为: {}", LOG_PREFIX, list.size());
            if(CollectionUtils.isEmpty(list)) {
                return;
            }

            dateForBreakpoint = list.stream().max(Comparator.comparing(o -> o.updateTime)).get().updateTime;

            List<String> listPostIds = list.stream().map(a -> a.postid).distinct().collect(Collectors.toList());
            for (String postId : listPostIds) {
                //根据帖子ID 获取关联话题的数据
                List<PostTopicModel> topicList = postTopicRetDao.getRelationTopic(postId);
                if (topicList == null) {
                    topicList = new ArrayList<>();
                } else {
                    Map<String, PostTopicModel> distinctMap = new LinkedHashMap<>(topicList.size() * 4 / 3 + 1);
                    for (PostTopicModel item : topicList) {
                        if (!distinctMap.containsKey(item.HtId)) {
                            distinctMap.put(item.HtId, item);
                        }
                    }
                    topicList = new ArrayList<>(distinctMap.values());
                }
                postDao.setTopicList(postId, topicList);
            }
            userRedisDao.set(BREAK_TIME_KEY, DateUtil.dateToStr(dateForBreakpoint), 60 * DateConstant.ONE_DAY);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
        }
    }


}
