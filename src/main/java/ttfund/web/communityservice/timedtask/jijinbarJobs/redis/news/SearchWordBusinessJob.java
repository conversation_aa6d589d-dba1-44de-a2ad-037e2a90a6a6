package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.news;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ttfund.web.base.helper.CacheHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.NewsRedisConstantConfig;
import ttfund.web.communityservice.enums.EnumSearchWord;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新闻资讯搜索关键过滤
 * 官方词数据到缓存，屏蔽词数据到缓存
 */
@JobHandler(value = "searchWordBusinessJob")
@Component
public class SearchWordBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SearchWordBusinessJob.class);

    @Autowired
    private App app;

    @Autowired
    private AppConstant appConstant;

    @Override
    public ReturnT<String> execute(String param) throws JsonProcessingException {

        try {

            setDataToCahche();

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 初始化数据到缓存
     */
    private void setDataToCahche() {
        try {

            Map<EnumSearchWord, String> dic = new HashMap<>();
            dic.put(EnumSearchWord.BLACK, NewsRedisConstantConfig.ASP_NET_FUND_SERVICE_NEWSSEARCHWORD_BLACK);
            dic.put(EnumSearchWord.OFFICIAL, NewsRedisConstantConfig.ASP_NET_FUND_SERVICE_NEWSSEARCHWORD_OFFICIAL);

            for (EnumSearchWord key : dic.keySet()) {

                List<String> cacheData = getList(key);

                logger.info("1.读取数据。类型：{}，数量：{}",
                        key.getValue(),
                        cacheData == null ? 0 : cacheData.size());

                if (!CollectionUtils.isEmpty(cacheData)) {

                    Boolean flag = app.newsRedis.set(dic.get(key), JacksonUtil.obj2String(cacheData));
                    if (flag) {
                        logger.info("2.缓存{}插入【成功】数量为:{}", dic.get(key), cacheData.size());
                    } else {
                        logger.error("2.缓存{}插入【失败】数量为:{}", dic.get(key), cacheData.size());
                    }
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }

    /**
     * 根据类型获取列表
     *
     * @param type
     * @return
     */
    private List<String> getList(EnumSearchWord type) {

        List<String> list = new ArrayList<>();

        try {
            String apiUrl = MessageFormat.format(appConstant.newsSearchWordApiUrl, type.getValue());
            list = CacheHelper.get(apiUrl);
            if (CollectionUtils.isEmpty(list)) {
                String content = HttpHelper.requestGet(apiUrl);
                if (StringUtils.hasLength(content)) {
                    list = JacksonUtil.string2Obj(content, List.class, String.class);
                    if (!CollectionUtils.isEmpty(list)) {
                        CacheHelper.put(apiUrl, list, 5 * 60 * 1000L);
                    }
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return list;
    }
}
