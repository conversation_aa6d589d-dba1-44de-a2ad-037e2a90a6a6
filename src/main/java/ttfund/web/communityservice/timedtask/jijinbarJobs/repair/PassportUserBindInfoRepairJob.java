package ttfund.web.communityservice.timedtask.jijinbarJobs.repair;

import com.google.common.collect.Sets;
import com.mongodb.bulk.BulkWriteResult;
import com.ttfund.web.core.register.AppCore;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserBindInfo;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.config.dataconfig.UserAccMongoConstantConfig;
import ttfund.web.communityservice.utils.JacksonUtil;
import ttfund.web.communityservice.utils.StringUtil;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 通行证用户绑定信息修复任务
 * 基金吧MongoDB 《=》 用户MongoDB
 */
@JobHandler(value = "passportUserBindInfoRepairJob")
@Component
public class PassportUserBindInfoRepairJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PassportUserBindInfoRepairJob.class);

    private static final String  COLLECTION_NAME = "PassportUserBindInfo";

    /**
     * 用户MongoDB
     */
    @Autowired
    @Qualifier(UserAccMongoConstantConfig.USER_ACC_MONGO_BEAN_NAME)
    private MongoTemplate userMongoTemplate;

    /**
     * 基金吧MongoDB
     */
    @Autowired
    @Qualifier(BarMongodbConfig.bar_mongo_conn_beanname)
    protected MongoTemplate barMongoTemplate;

    @Autowired
    private AppCore appCore;

    @Override
    public ReturnT<String> execute(String param) {

        ReturnT resultStatus = ReturnT.SUCCESS;

        try {

            boolean reverse = !StringUtils.isEmpty(param);

            logger.info("通行证用户绑定信息修复任务开始，反向修复:{}", reverse);

            // 获取断点
            String breakName = "passportUserBindInfoRepairJob_break_" + reverse;
            String lastId = appCore.redisuserread.get(breakName);

            PassportUserBindInfo bindInfo = new PassportUserBindInfo();
            if (StringUtils.isEmpty(lastId)) {
                bindInfo._id = "";
            } else {
                bindInfo._id = lastId;
            }

            // 确认源目
            MongoTemplate source = barMongoTemplate;
            MongoTemplate target = userMongoTemplate;

            if (reverse) {
                source = userMongoTemplate;
                target = barMongoTemplate;
            }

            // 修复数据
            int sum = 0;
            int batchCount = 2000;

            for (int i = 0; i < 100; i++) {

                Thread.sleep(100L);

                // 分批查询源数据
                Query query = new Query(
                    Criteria.where("_id").gt(bindInfo._id)
                ).with(Sort.by(Sort.Order.asc("_id")))
                    .limit(batchCount);

                List<PassportUserBindInfo> bindInfos = source.find(query, PassportUserBindInfo.class, COLLECTION_NAME);

                if (CollectionUtils.isEmpty(bindInfos)) {
                    break;
                }

                Set<String> ids = bindInfos.stream().map(w -> w._id).collect(Collectors.toSet());

                // 查询目标库是否存在
                query = new Query(
                    Criteria.where("_id").in(ids)
                );
                query.fields().include("_id");

                List<String> existedIds = target.find(query, PassportUserBindInfo.class, COLLECTION_NAME)
                    .stream().map(w -> w._id).collect(Collectors.toList());

                Sets.SetView<String> diff = Sets.difference(ids, new HashSet<>(existedIds));

                // 保存
                if (!diff.isEmpty()) {
                    List<PassportUserBindInfo> toSaveData = bindInfos.stream()
                        .filter(w -> diff.contains(w._id))
                        .collect(Collectors.toList());

                    BulkOperations bulkOperations = target.bulkOps(BulkOperations.BulkMode.UNORDERED, COLLECTION_NAME);
                    bulkOperations.insert(toSaveData);
                    bulkOperations.execute();

                    logger.info("修复的数据：{}", JacksonUtil.obj2String(diff));
                }

                sum += bindInfos.size();
                bindInfo = bindInfos.get(bindInfos.size() - 1);

                logger.info("已处理{}条数据，当前数据：{}", sum, JacksonUtil.obj2String(bindInfo));

                if (bindInfos.size() < batchCount) {
                    break;
                }
            }

            appCore.redisuserwrite.set(breakName, bindInfo._id);

            logger.info("通行证用户绑定信息修复任务结束");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            resultStatus = ReturnT.FAIL;
        }
        return resultStatus;
    }
}
