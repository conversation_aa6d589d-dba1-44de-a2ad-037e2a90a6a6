package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.CirclePostRelation;
import ttfund.web.communityservice.bean.jijinBar.data.FindPageSourcePost;
import ttfund.web.communityservice.bean.jijinBar.post.config.HighQualityPostModel;
import ttfund.web.communityservice.bean.jijinBar.post.guba.FundBarModel;
import ttfund.web.communityservice.bean.news.CmsArticle;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.FindPageSourceNewsDao;
import ttfund.web.communityservice.dao.mongo.FindPageSourcePostDao;
import ttfund.web.communityservice.dao.mongo.HighQualityPostDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.mongo.WaterfallPostConfigDao;
import ttfund.web.communityservice.dao.msyql.CirclePostRelationMysqlDao;
import ttfund.web.communityservice.dao.msyql.FundBarDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.enums.FindPageSourcePostEnum;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 社区发现页来源帖子 -同步job
 * 备注：需求 #711535 【基金吧6.17】资讯改社区
 */
@Slf4j
@JobHandler("FindPageSourcePostSyncJob")
@Component
public class FindPageSourcePostSyncJob extends IJobHandler {

    private static final String SELECT_FIELDS = "a.ID,a.TITLE,a.TYPE,a.UID,a.CODE,a.TIME,a.TIMEPOINT,a.DEL,a.TTJJDEL,b.CONTENTCOUNT,b.CLICKNUM,b.LIKECOUNT,b.PINGLUNNUM ";

    private static final List<String> SET_ON_INSERT_FIELDS = Arrays.asList("createTime");

    private static final List<Integer> CACHE_POST_SOURCES = Arrays.asList(1, 2, 3, 5, 6, 7, 10);
    private static final List<String> CACHE_POST_FIELDS = Arrays.asList("_id", "postId", "time", "timePoint", "clickCount", "likeCount", "replyCount", "contentCount");
    private static final List<String> CACHE_NEWS_FIELDS = Arrays.asList("_id", "artCode", "artId", "artShowTime", "timePoint");


    @Autowired
    private App app;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private FindPageSourcePostDao findPageSourcePostDao;

    @Autowired
    private HighQualityPostDao highQualityPostDao;

    @Autowired
    private WaterfallPostConfigDao waterfallPostConfigDao;

    @Autowired
    private CirclePostRelationMysqlDao circlePostRelationMysqlDao;

    @Autowired
    private PostDao postDao;

    @Autowired
    private FundBarDao fundBarDao;

    @Autowired
    private PostInfoExtraDao postInfoExtraDao;

    @Autowired
    private FindPageSourceNewsDao findPageSourceNewsDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            Boolean dealMongodb = null;
            Boolean dealRedis = null;

            Integer startTime = null;  //单位秒  负值
            Integer endTime = null;//单位秒  负值
            Integer batchReadCount = null;
            Integer batchSize = null;
            String templateTopicIds = null;//模板贴对应话题id
            List<Integer> filterType = null;
            List<Integer> charCountLimit = null;
            List<Double> interactFactorConfig = null;
            List<Double> timeFactorConfig = null;
            Integer newsExpire = null;//单位秒  负值
            Long cacheExpire = null;//单位秒
            Integer cacheCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                dealMongodb = jsonObject.getBoolean("dealMongodb");
                dealRedis = jsonObject.getBoolean("dealRedis");

                startTime = jsonObject.getInteger("startTime");
                endTime = jsonObject.getInteger("endTime");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                batchSize = jsonObject.getInteger("batchSize");
                templateTopicIds = jsonObject.getString("templateTopicIds");
                JSONArray jsonArray = jsonObject.getJSONArray("filterType");
                if (!CollectionUtils.isEmpty(jsonArray)) {
                    filterType = jsonArray.stream().map(a -> Integer.parseInt(a.toString())).collect(Collectors.toList());
                }
                jsonArray = jsonObject.getJSONArray("charCountLimit");
                if (!CollectionUtils.isEmpty(jsonArray)) {
                    charCountLimit = jsonArray.stream().map(a -> Integer.parseInt(a.toString())).collect(Collectors.toList());
                }
                jsonArray = jsonObject.getJSONArray("interactFactorConfig");
                if (!CollectionUtils.isEmpty(jsonArray)) {
                    interactFactorConfig = jsonArray.stream().map(a -> Double.parseDouble(a.toString())).collect(Collectors.toList());
                }
                jsonArray = jsonObject.getJSONArray("timeFactorConfig");
                if (!CollectionUtils.isEmpty(jsonArray)) {
                    timeFactorConfig = jsonArray.stream().map(a -> Double.parseDouble(a.toString())).collect(Collectors.toList());
                }
                newsExpire = jsonObject.getInteger("newsExpire");

                cacheExpire = jsonObject.getLong("cacheExpire");
                cacheCount = jsonObject.getInteger("cacheCount");

            }

            if (dealMongodb == null) {
                dealMongodb = true;
            }
            if (dealRedis == null) {
                dealRedis = true;
            }
            if (startTime == null) {
                startTime = -3 * 24 * 3600;
            }
            if (endTime == null) {
                endTime = -5 * 60;
            }
            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (batchSize == null) {
                batchSize = 200;
            }
            if (templateTopicIds == null) {
                templateTopicIds = "9689,53843";
            }
            if (filterType == null) {
                filterType = Arrays.asList(49, 50);
            }
            if (charCountLimit == null) {
                charCountLimit = Arrays.asList(0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
            }
            if (interactFactorConfig == null) {
                interactFactorConfig = Arrays.asList(0.002, 1.0, 1.0);
            }
            if (timeFactorConfig == null) {
                timeFactorConfig = Arrays.asList(1.0, 1.0, 1.0);
            }
            if (newsExpire == null) {
                newsExpire = -3 * 24 * 3600;
            }
            if (cacheExpire == null) {
                cacheExpire = 1800L;
            }
            if (cacheCount == null) {
                cacheCount = 100;
            }


            log.info("0.打印参数。dealMongodb：{}，dealRedis：{}，startTime：{}，endTime：{}，batchReadCount：{}，batchSize：{}，templateTopicIds：{}，filterType：{}，charCountLimit：{}，interactFactorConfig：{}，timeFactorConfig：{}，newsExpire：{}，cacheExpire：{}，cacheCount：{}",
                dealMongodb,
                dealRedis,
                startTime,
                endTime,
                batchReadCount,
                batchSize,
                templateTopicIds,
                filterType,
                charCountLimit,
                interactFactorConfig,
                timeFactorConfig,
                newsExpire,
                cacheExpire,
                cacheCount
            );

            if (dealMongodb) {
                dealMongodb(startTime, endTime, batchReadCount, batchSize, templateTopicIds, filterType, charCountLimit, interactFactorConfig, timeFactorConfig, newsExpire);
            }

            if (dealRedis) {
                dealRedis(cacheExpire, cacheCount);
            }

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void dealMongodb(Integer startTime,
                             Integer endTime,
                             int batchReadCount,
                             int batchSize,
                             String templateTopicIds,
                             List<Integer> filterType,
                             List<Integer> charCountLimit,
                             List<Double> interactFactorConfig,
                             List<Double> timeFactorConfig,
                             int newsExpire
    ) throws Exception {
        Date now = new Date();
        Date deleteNewsTime = DateUtil.calendarDateBySecond(now, newsExpire);

        syncToMongodb(now, startTime, endTime, batchReadCount, batchSize, templateTopicIds, filterType, charCountLimit, interactFactorConfig, timeFactorConfig);

        deletePost(now);

        deleteNews(deleteNewsTime);

    }

    private void dealRedis(Long cacheExpire, Integer cacheCount) {
        for (Integer source : CACHE_POST_SOURCES) {
            String key = String.format(BarRedisKey.FIND_PAGE_SOURCE_POSTS, source);
            List<Document> list = findPageSourcePostDao.getListBySources(Document.class, CACHE_POST_FIELDS, cacheCount, Arrays.asList(source), null);
            app.barredis.set(key, JSON.toJSONString(list == null ? new ArrayList<>() : list), cacheExpire);

            log.info("上缓存-1帖子源。通道：{}，数量：{}，数据：{}",
                source,
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? 0 : JSON.toJSONString(list.get(0))
            );
        }

        String key = BarRedisKey.FIND_PAGE_SOURCE_NEWS;
        List<Document> list = findPageSourceNewsDao.getList(Document.class, CACHE_NEWS_FIELDS, cacheCount);
        app.barredis.set(key, JSON.toJSONString(list == null ? new ArrayList<>() : list), cacheExpire);

        log.info("上缓存-2资讯源。数量：{}，数据：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? 0 : JSON.toJSONString(list.get(0))
        );
    }

    private void syncToMongodb(Date now,
                               Integer startTime,
                               Integer endTime,
                               int batchReadCount,
                               int batchSize,
                               String templateTopicIds,
                               List<Integer> filterType,
                               List<Integer> charCountLimit,
                               List<Double> interactFactorConfig,
                               List<Double> timeFactorConfig
    ) throws Exception {

        Date start = DateUtil.calendarDateBySecond(now, startTime);
        Date end = DateUtil.calendarDateBySecond(now, endTime);

        int round = 0;
        boolean run = true;
        while (run) {
            round++;

            List<Map<String, Object>> postList = postInfoNewDao.getPostWithExtraInfoByTime(SELECT_FIELDS, start, end, batchReadCount);

            log.info("同步-第{}轮。1.读区间帖子集合。start：{}，end：{}，数量：{}，头部数据：{}",
                round,
                DateUtil.dateToStr(start),
                DateUtil.dateToStr(end),
                CollectionUtils.isEmpty(postList) ? 0 : postList.size(),
                CollectionUtils.isEmpty(postList) ? null : JSON.toJSONStringWithDateFormat(postList.get(0), DateUtil.datePattern)
            );

            if (CollectionUtils.isEmpty(postList) || postList.size() < batchReadCount) {
                run = false;
            }

            start = (Date)postList.get(postList.size() - 1).get("TIME");

            postList = postList.stream().
                filter(a -> CollectionUtils.isEmpty(filterType) || !filterType.contains((Integer)a.get("TYPE")))
                .collect(Collectors.toList());

            log.info("同步-第{}轮。2.过滤。数量：{}，头部数据：{}",
                round,
                CollectionUtils.isEmpty(postList) ? 0 : postList.size(),
                CollectionUtils.isEmpty(postList) ? null : JSON.toJSONStringWithDateFormat(postList.get(0), DateUtil.datePattern)
            );


            Map<String, Integer> postCharCountMap = new HashMap<>();
            postList.forEach(a -> {
                postCharCountMap.put(a.get("ID").toString(),
                    a.get("CONTENTCOUNT") == null ? 0 : Integer.parseInt(a.get("CONTENTCOUNT").toString())
                );
            });

            int batchRound = 0;
            List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(postList, batchSize);
            for (List<Map<String, Object>> batch : batchList) {
                batchRound++;

                Map<String, HighQualityPostModel> artificialQualityPostMap = new HashMap<>();
                Map<String, HighQualityPostModel> aiQualityPostMap = new HashMap<>();
                Map<String, FundBarModel> barHotPostMap = new HashMap<>();
                Map<String, Document> topicPostMap = new HashMap<>();
                Map<String, CirclePostRelation> circlePostMap = new HashMap<>();
                Map<String, Document> templatePostMap = new HashMap<>();
                Map<String, Document> configPostMap = new HashMap<>();


                List<String> postIds = batch.stream().map(a -> String.valueOf(a.get("ID"))).collect(Collectors.toList());
                List<String> codes = batch.stream().map(a -> String.valueOf(a.get("CODE"))).distinct().collect(Collectors.toList());

                /**
                 * 人工优质贴 通道1
                 * AI优质贴  通道2
                 */
                {
                    List<HighQualityPostModel> tempList = highQualityPostDao.getList(HighQualityPostModel.class, null, postIds);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        for (HighQualityPostModel a : tempList) {
                            if (postCharCountMap.getOrDefault(a.get_id(), 0) >= charCountLimit.get(1)) {
                                aiQualityPostMap.put(a.get_id(), a);
                            }
                            if (Objects.equals(a.getState(), 2)) {
                                if (postCharCountMap.getOrDefault(a.get_id(), 0) >= charCountLimit.get(0)) {
                                    artificialQualityPostMap.put(a.get_id(), a);
                                }
                            }
                        }
                    }
                }

                /**
                 * 单吧热帖 通道3
                 */
                {
                    List<FundBarModel> tempList = fundBarDao.getSingleBarByIds("*", codes);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        Map<String, FundBarModel> barMap = new HashMap<>();
                        tempList.forEach(a -> barMap.put(a.ID, a));
                        for (Map<String, Object> a : batch) {
                            if (barMap.containsKey(String.valueOf(a.get("CODE")))) {
                                if (postCharCountMap.getOrDefault(String.valueOf(a.get("ID")), 0) >= charCountLimit.get(2)) {
                                    barHotPostMap.put(String.valueOf(a.get("ID")), barMap.get(String.valueOf(a.get("CODE"))));
                                }
                            }
                        }
                    }
                }

                /**
                 * 话题贴   通道5
                 * 模板贴   通道7
                 */
                {
                    List<Document> tempList = postDao.getTopicPosts(Document.class, null, postIds);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        for (Document a : tempList) {
                            if (postCharCountMap.getOrDefault(a.getString("_id"), 0) >= charCountLimit.get(4)) {
                                topicPostMap.put(a.getString("_id"), a);
                            }
                        }
                    }

                    if (!CollectionUtils.isEmpty(tempList) && StringUtils.hasLength(templateTopicIds)) {
                        tempList.forEach(a -> {
                                List<Document> htlist = a.getList("HTLIST", Document.class);
                                if (htlist.stream().filter(f -> templateTopicIds.contains(f.getString("HtId"))).findFirst().orElse(null) != null) {
                                    if (postCharCountMap.getOrDefault(a.getString("_id"), 0) >= charCountLimit.get(6)) {
                                        templatePostMap.put(a.getString("_id"), a);
                                        topicPostMap.remove(a.getString("_id"));
                                    }
                                }
                            }
                        );
                    }
                }

                /**
                 * 圈子贴 通道6
                 */
                {
                    List<CirclePostRelation> tempList = circlePostRelationMysqlDao.getUsefulByPostIds(postIds);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        for (CirclePostRelation a : tempList) {
                            if (postCharCountMap.getOrDefault(a.getPostId(), 0) >= charCountLimit.get(5)) {
                                circlePostMap.put(a.getPostId(), a);
                            }
                        }
                    }
                }

                /**
                 * 运营贴 通道10
                 */
                {
                    List<Document> tempList = waterfallPostConfigDao.getList(null, Document.class, postIds);
                    if (!CollectionUtils.isEmpty(tempList)) {

                        for (Document a : tempList) {
                            if (postCharCountMap.getOrDefault(a.getString("_id"), 0) >= charCountLimit.get(9)) {
                                configPostMap.put(a.getString("_id"), a);
                            }
                        }
                    }
                }

                List<FindPageSourcePost> modelList = new ArrayList<>();
                FindPageSourcePost model = null;
                List<Integer> source = null;
                for (Map<String, Object> a : batch) {
                    model = generate(a, interactFactorConfig, timeFactorConfig);

                    source = new ArrayList<>();
                    if (artificialQualityPostMap.containsKey(model.get_id())) {
                        source.add(FindPageSourcePostEnum.ARTIFICIAL_QUALITY_POST.getValue());
                    }
                    if (aiQualityPostMap.containsKey(model.get_id())) {
                        source.add(FindPageSourcePostEnum.AI_QUALITY_POST.getValue());
                    }
                    if (barHotPostMap.containsKey(model.get_id())) {
                        source.add(FindPageSourcePostEnum.BAR_HOT_POST.getValue());
                    }
                    if (topicPostMap.containsKey(model.get_id())) {
                        source.add(FindPageSourcePostEnum.TOPIC_POST.getValue());
                    }
                    if (circlePostMap.containsKey(model.get_id())) {
                        source.add(FindPageSourcePostEnum.CIRCLE_POST.getValue());
                    }
                    if (templatePostMap.containsKey(model.get_id())) {
                        source.add(FindPageSourcePostEnum.TEMPLATE_POST.getValue());
                    }
                    if (configPostMap.containsKey(model.get_id())) {
                        source.add(FindPageSourcePostEnum.CONFIG_POST.getValue());
                    }

                    model.setSource(source);

                    modelList.add(model);
                }

                List<List<FindPageSourcePost>> batchListForModel = CommonUtils.toSmallList2(modelList, 100);
                for (List<FindPageSourcePost> batchForModel : batchListForModel) {
                    List<Map<String, Object>> mapList = new ArrayList<>();
                    for (FindPageSourcePost a : batchForModel) {
                        mapList.add(CommonUtils.beanToMap(a));
                    }

                    findPageSourcePostDao.upsertManyBySetWithSetOnInsertFields(mapList, SET_ON_INSERT_FIELDS, "_id");
                }

                log.info("同步-第{}轮-第{}批。3.处理并写库区间帖子。start：{}，end：{}，数量：{}，头部数据：{}",
                    round,
                    batchRound,
                    DateUtil.dateToStr(start),
                    DateUtil.dateToStr(end),
                    CollectionUtils.isEmpty(modelList) ? 0 : modelList.size(),
                    CollectionUtils.isEmpty(modelList) ? null : JSON.toJSONStringWithDateFormat(modelList.get(0), DateUtil.datePattern)
                );

            }

            log.info("同步-第{}轮。3.处理区间帖子完成。start：{}，end：{}，数量：{}，头部数据：{}",
                round,
                DateUtil.dateToStr(start),
                DateUtil.dateToStr(end),
                CollectionUtils.isEmpty(postList) ? 0 : postList.size(),
                CollectionUtils.isEmpty(postList) ? null : JSON.toJSONStringWithDateFormat(postList.get(0), DateUtil.datePattern)
            );

        }
    }

    private void deletePost(Date updateTime) {
        findPageSourcePostDao.delete(updateTime);

        log.info("清除完成-帖子。时间：{}", updateTime);
    }

    private void deleteNews(Date artShowTime) {

        findPageSourceNewsDao.deleteByTimePoint(CmsArticle.generateTimePoint(artShowTime));

        log.info("清除完成-资讯。时间：{}", artShowTime);
    }


    private FindPageSourcePost generate(Map<String, Object> a, List<Double> interactFactorConfig, List<Double> timeFactorConfig) {
        FindPageSourcePost model = new FindPageSourcePost();
        model.set_id(String.valueOf(a.get("ID")));
        model.setPostId(String.valueOf(a.get("ID")));
        model.setTitle((String)a.get("TITLE"));
        model.setTime((Date)a.get("TIME"));
        model.setTimePoint(a.get("TIMEPOINT") == null ? null : Long.parseLong(a.get("TIMEPOINT").toString()));
        model.setType(a.get("TYPE") == null ? null : Integer.parseInt(a.get("TYPE").toString()));
        model.setUid((String)a.get("UID"));
        model.setCode((String)a.get("CODE"));
        model.setDel(a.get("DEL") == null ? null : Integer.parseInt(a.get("DEL").toString()));
        model.setTtjjDel(a.get("TTJJDEL") == null ? null : Integer.parseInt(a.get("TTJJDEL").toString()));

        int clickCount = (a.get("CLICKNUM") == null ? 0 : Integer.parseInt(a.get("CLICKNUM").toString()));
        int likeCount = (a.get("LIKECOUNT") == null ? 0 : Integer.parseInt(a.get("LIKECOUNT").toString()));
        int pinglunNum = (a.get("PINGLUNNUM") == null ? 0 : Integer.parseInt(a.get("PINGLUNNUM").toString()));
        int contentCount = (a.get("CONTENTCOUNT") == null ? 0 : Integer.parseInt(a.get("CONTENTCOUNT").toString()));

        double days = (System.currentTimeMillis() - ((Date)a.get("TIME")).getTime()) / (24 * 3600 * 1000.0);
        double postTimeFactor = 0;
        if (days >= 2.0) {
            postTimeFactor = timeFactorConfig.get(0);
        } else if (days >= 1.0) {
            postTimeFactor = timeFactorConfig.get(1);
        } else {
            postTimeFactor = timeFactorConfig.get(2);
        }

        double score = (interactFactorConfig.get(0) * clickCount + interactFactorConfig.get(1) * likeCount + interactFactorConfig.get(2) * pinglunNum) * postTimeFactor;

        model.setClickCount(clickCount);
        model.setLikeCount(likeCount);
        model.setReplyCount(pinglunNum);
        model.setScore(score);
        model.setContentCount(contentCount);

        model.setCreateTime(new Date());
        model.setUpdateTime(new Date());

        return model;
    }


}
