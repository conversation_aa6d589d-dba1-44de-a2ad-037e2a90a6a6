package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.BigVDataUserModel;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.dao.mongo.CalVCountDataUserDao;
import ttfund.web.communityservice.dao.msyql.UserRelationCountDao;
import ttfund.web.communityservice.utils.CommonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户关注粉丝数初始化job
 */
@Slf4j
@JobHandler("UserRelationCountInitJob")
@Component
public class UserRelationCountInitJob extends IJobHandler {

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private CalVCountDataUserDao calVCountDataUserDao;

    @Autowired
    private UserRelationCountDao userRelationCountDao;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            log.info("第零步，打印参数。batchReadCount：{}", batchReadCount);

            deal(batchReadCount);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void deal(int batchReadCount) {

        try {

            List<BigVDataUserModel> userList = new ArrayList<>();
            String uid = null;
            while (true) {
                List<BigVDataUserModel> tempList = calVCountDataUserDao.getByUid(uid, batchReadCount);
                if (!CollectionUtils.isEmpty(tempList)) {
                    userList.addAll(tempList);
                }

                if (!CollectionUtils.isEmpty(tempList)) {
                    uid = tempList.get(tempList.size() - 1).getUID();
                }

                if (tempList == null || tempList.size() < batchReadCount) {
                    break;
                }
            }

            log.info("第一步，读取用户表。数量：{}，头部数据：{}",
                    userList == null ? 0 : userList.size(),
                    CollectionUtils.isEmpty(userList) ? null : JSON.toJSONString(userList.get(0))
            );

            int totalCount = 0;
            if (!CollectionUtils.isEmpty(userList)) {

                List<String> uids = userList.stream().map(a -> a.getUID()).collect(Collectors.toList());
                List<List<String>> batchList = CommonUtils.toSmallList2(uids, 50);

                int i = 0;
                for (List<String> batch : batchList) {
                    i++;

                    List<Map<String, Object>> mapList = new ArrayList<>(batch.size());
                    Map<String, Object> userMap = getFollowFansCounts(batch);

                    if (!CollectionUtils.isEmpty(userMap)) {
                        Set<String> keySet = userMap.keySet();
                        keySet.forEach(a -> ((Map<String, Object>) (userMap.get(a))).put("uid", a));

                        userMap.values().forEach(a -> mapList.add((Map<String, Object>) a));

                        userRelationCountDao.upsertMany(mapList);

                        totalCount += mapList.size();
                    }


                    log.info("第二步，调接口及写库详情。第{}/{}批，数量：{}，头部数据：{}",
                            i,
                            batchList.size(),
                            mapList == null ? 0 : mapList.size(),
                            CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONString(mapList.get(0))
                    );

                }

            }

            log.info("第二步，调接口及写库完成。数量：{}", totalCount);


        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    /**
     * 获取多个用户关注粉丝数
     * 股吧接口文档：https://pcmp.eastmoney.com/pcmp/knowledge/doc/detail?docId=g535zlqbclm1zw5ok8tigca41tennppz
     * 对接人：袁明勇
     */
    private Map<String, Object> getFollowFansCounts(List<String> uids) {
        Map<String, Object> result = null;
        if (CollectionUtils.isEmpty(uids)) {
            return result;
        }
        String url = appConstant.config_gubahostnew + "/follow/api/Follow/GetFollowFansCounts?uids=%s";
        url = String.format(url, String.join(",", uids));
        String html = HttpHelper.requestGet(url);
        JSONObject jsonObject = JSON.parseObject(html);
        if (jsonObject != null && jsonObject.getJSONObject("re") != null) {
            result = jsonObject.getJSONObject("re");
        }

        return result;
    }

}
