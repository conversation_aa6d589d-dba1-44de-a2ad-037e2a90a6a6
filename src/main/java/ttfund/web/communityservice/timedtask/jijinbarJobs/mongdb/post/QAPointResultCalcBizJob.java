package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.SendPiontType;
import ttfund.web.communityservice.bean.jijinBar.post.QA.FundAnswerInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.QA.PostRewardDetailMongodb;
import ttfund.web.communityservice.bean.jijinBar.post.QA.QuestionEntity;
import ttfund.web.communityservice.bean.messagepush.RewardPointResponse;
import ttfund.web.communityservice.bean.messagepush.SendPointRecord;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.dao.mongo.PostRewardDetailDao;
import ttfund.web.communityservice.dao.mongo.SendPointRecordDao;
import ttfund.web.communityservice.dao.msyql.AnswerDao;
import ttfund.web.communityservice.dao.msyql.QuestionExtraDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 问答财富币计算服务
 */
@JobHandler("QAPointResultCalcBizJob")
@Component
public class QAPointResultCalcBizJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(QAPointResultCalcBizJob.class);

    @Autowired
    private QuestionExtraDao questionExtraDao;

    @Autowired
    private SendPointRecordDao sendPointRecordDao;

    @Autowired
    private AnswerDao answerDao;

    @Autowired
    private PostRewardDetailDao postRewardDetailDao;

    @Autowired
    private AppConstant appConstant;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        Integer minute = null;
        Integer qaCount = null;
        Integer postRewardDetailCount = null;

        if (StringUtils.hasLength(s)) {
            JSONObject jsonObject = JSON.parseObject(s);
            minute = jsonObject.getInteger("minute");
            qaCount = jsonObject.getInteger("qaCount");
            postRewardDetailCount = jsonObject.getInteger("postRewardDetailCount");
        }

        if (minute == null) {
            minute = -10;
        }
        if (qaCount == null) {
            qaCount = 100;
        }
        if (postRewardDetailCount == null) {
            postRewardDetailCount = 100;
        }

        logger.info("0.获取传入参数。minute：{}，qaCount：{}，postRewardDetailCount：{}", minute, qaCount, postRewardDetailCount);

        qaPointResultCalcNew(minute, qaCount);
        businessPostRewardDetailMongodb(postRewardDetailCount);

        return ReturnT.SUCCESS;
    }

    /**
     * 基金吧问答 悬赏财富币计算
     */
    public void qaPointResultCalcNew(int minute, int qaCount) {

        String logPre = "财富币计算服务：";
        try {

            //延迟10分钟，获取已经结束的问答列表
            List<QuestionEntity> questions = questionExtraDao.getQAEndList(DateUtil.calendarDateByMinute(minute), qaCount);

            logger.info(logPre + "1.获取问答列表。数量：{}", CollectionUtils.isEmpty(questions) ? 0 : questions.size());

            if (!CollectionUtils.isEmpty(questions)) {

                List<SendPointRecord> refunds = new ArrayList<>();
                StringBuilder builder = null;
                int i = 0;
                for (QuestionEntity item : questions) {

                    ++i;

                    boolean hasRec = sendPointRecordDao.checkByQid(item.QID);

                    builder = new StringBuilder();
                    builder.append("1.问题是否计算过:" + hasRec + "\n");

                    if (hasRec) {
                        boolean updateResult = questionExtraDao.setCalculated(Arrays.asList(item.QID));

                        builder.append("1-1.该问题已经计算过,更新数据库结果:" + updateResult + "\n");

                        if (updateResult) {
                            logger.info(logPre + "该问题已经计算过,更新数据库成功。qid:{}", item.QID);
                        } else {
                            logger.error(logPre + "该问题已经计算过,更新数据库失败。qid:{}", item.QID);
                        }
                        continue;
                    }

                    builder.append("2.问题状态分类" + "\n");

                    if (item.AuditStatusType == 2 || item.IsEnable == 0) {

                        builder.append("2-1.问题未审核通过退还" + "\n");

                        SendPointRecord record = new SendPointRecord();
                        record._id = item.QID + "_" + item.UserId;
                        record.AID = "";
                        record.Amount = item.Amount.doubleValue();
                        record.CustomerNo = "";
                        record.EndTime = item.EndTime;
                        record.IsSend = false;
                        record.PayId = "";
                        record.QID = item.QID;
                        record.ResultMessage = "";
                        record.Type = SendPiontType.RefundForDel.getValue(); //问题未审核通过退还
                        record.UserId = item.UserId;
                        record.PostID = (int) item.ArticleId;
                        refunds.add(record);
                    } else if (item.HasBestAnswer == 0 && item.AuditStatusType == 1)//没有最佳答案，财富币退还
                    {
                        builder.append("2-2.没有最佳答案，财富币退还" + "\n");

                        SendPointRecord record = new SendPointRecord();
                        record._id = item.QID + "_" + item.UserId;
                        record.AID = "";
                        record.Amount = item.Amount.doubleValue();
                        record.CustomerNo = "";
                        record.EndTime = item.EndTime;
                        record.IsSend = false;
                        record.PayId = "";
                        record.QID = item.QID;
                        record.ResultMessage = "";
                        record.Type = SendPiontType.RefundNoAnswer.getValue(); //无最佳答案退还
                        record.UserId = item.UserId;
                        record.PostID = (int) item.ArticleId;
                        refunds.add(record);
                    } else {

                        builder.append("2-3.有最佳答案" + "\n");

                        List<FundAnswerInfoModel> answers = answerDao.getList(item.QID, 1, 1, 1);

                        // 剔除自己回答自己的逻辑
                        if (!CollectionUtils.isEmpty(answers)) {
                            answers = answers.stream().filter(a -> !a.CreatorID.equals(a.UserId)).collect(Collectors.toList());
                        }

                        builder.append("2-3-1.最佳答案集合：" + JSON.toJSONString(answers) + "\n");

                        if (!CollectionUtils.isEmpty(answers)) {
                            double point = item.Amount.intValue() / answers.size(); //取商
                            double remains = item.Amount.doubleValue() % answers.size(); //取余数

                            List<SendPointRecord> sends = answers.stream().map(l -> {
                                SendPointRecord model = new SendPointRecord();
                                model._id = l.QID + "_" + l.CreatorID + "_" + l.AID;
                                model.QID = l.QID;
                                model.AID = l.AID;
                                model.PostID = l.ArticleId;
                                model.Amount = point;
                                model.PayId = "";
                                model.IsSend = false;
                                model.Type = SendPiontType.Adopted.getValue();
                                model.UserId = l.CreatorID;
                                model.CustomerNo = "";
                                model.ResultMessage = "";
                                model.EndTime = item.EndTime;
                                return model;
                            }).collect(Collectors.toList());

                            sends.get(0).Amount += remains;

                            builder.append("2-3-2.财富币计算结果打印：" + JSON.toJSONString(sends) + "\n");

                            //更新成功的话更新数据库字段
                            List<Map<String, Object>> mapList = new ArrayList<>();
                            if (!CollectionUtils.isEmpty(sends)) {
                                for (SendPointRecord a : sends) {
                                    Map<String, Object> map = CommonUtils.beanToMap(a);
                                    if (!CollectionUtils.isEmpty(map)) {
                                        mapList.add(map);
                                    }
                                }

                            }
                            boolean isSuccess = sendPointRecordDao.upsertMany(mapList);

                            builder.append("2-3-3.财富币计算记录写库：" + isSuccess + "\n");

                            if (isSuccess) {
                                List<String> qids = sends.stream().map(a -> a.QID).distinct().collect(Collectors.toList());
                                boolean updateResult = questionExtraDao.setCalculated(qids);

                                builder.append("2-3-4.提问结算状态写库：" + updateResult + "\n");

                                if (updateResult) {

                                } else {
                                    logger.error(logPre + "提问结算状态写库失败：" + item.QID);
                                }

                            } else {
                                logger.error(logPre + "财富币计算记录写库失败：" + item.QID);

                            }
                        } else {//最佳答案可能已经被删除

                            builder.append("2-3-2.最佳答案可能已经被删除" + "\n");

                            SendPointRecord record = new SendPointRecord();
                            record._id = item.QID + "_" + item.UserId;
                            record.AID = "";
                            record.Amount = item.Amount.doubleValue();
                            record.CustomerNo = "";
                            record.EndTime = item.EndTime;
                            record.IsSend = false;
                            record.PayId = "";
                            record.QID = item.QID;
                            record.ResultMessage = "";
                            record.Type = SendPiontType.RefundNoAnswer.getValue(); //无最佳答案退还
                            record.UserId = item.UserId;
                            record.PostID = (int) item.ArticleId;
                            refunds.add(record);

                        }

                    }

                    builder.append("4.财富币退回记录打印：" + refunds.size() + "\n");

                    if (!CollectionUtils.isEmpty(refunds)) {
                        List<Map<String, Object>> mapList = new ArrayList<>();

                        for (SendPointRecord a : refunds) {
                            Map<String, Object> map = CommonUtils.beanToMap(a);
                            if (!CollectionUtils.isEmpty(map)) {
                                mapList.add(map);
                            }
                        }


                        boolean isSuccess = sendPointRecordDao.upsertMany(mapList);

                        builder.append("4-1.财富币退回记录写库：" + isSuccess + "\n");

                        if (isSuccess) {
                            List<String> qids = refunds.stream().map(a -> a.QID).distinct().collect(Collectors.toList());
                            boolean isupdate = questionExtraDao.setCalculated(qids);

                            builder.append("4-2.提问状态写库-财富币退回：" + isupdate + "\n");

                            if (isupdate) {

                            } else {
                                logger.error(logPre + "提问状态写库-财富币退回。失败。提问id：" + item.QID);
                            }
                        }
                    }

                    logger.info(logPre + "第{}/{}个-{}-2.处理详情：\n{}", i, questions.size(), item.QID, builder.toString());

                }

            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 帖子打赏财富币回退服务
     */
    public void businessPostRewardDetailMongodb(int batchReadCount) {

        try {
            String logPre = "财富币回退服务：";
            List<PostRewardDetailMongodb> rewardDetails = postRewardDetailDao.findPaygedList(1, batchReadCount, PostRewardDetailMongodb.class);
            logger.info(logPre + "1.读取财富币打赏失败记录。数量：{}", CollectionUtils.isEmpty(rewardDetails) ? 0 : rewardDetails.size());


            if (!CollectionUtils.isEmpty(rewardDetails)) {

                boolean isEnd;
                int returnCount;
                int i = 0;
                for (PostRewardDetailMongodb item : rewardDetails) {

                    ++i;

                    int point = item.POINTNUM.intValue();
                    String reference = "打赏失败退积分";
                    String taskId = appConstant.postRewardTaskId;
                    RewardPointResponse result =
                            sendPointRecordDao.acceptActiveChangeUserPointNew("", taskId, reference,
                                    point, item.PASSPORTIDSEND, "Web", item.PASSPORTIDSEND);

                    logger.info(logPre + "第{}/{}个-2.调财富币发放接口。内容：{}，结果：{}",
                            i, rewardDetails.size(), JSON.toJSONString(item), JSON.toJSONString(result));

                    PostRewardDetailMongodb obj = postRewardDetailDao.getById(PostRewardDetailMongodb.class, item._id);

                    logger.info(logPre + "第{}/{}个-3.查询财富币打赏记录。id：{}，结果：{}",
                            i, rewardDetails.size(), item._id, JSON.toJSONString(obj));

                    //如果数据存在，回退次数超过三次时 就不再回退
                    if (obj != null && (obj.ReturnCount == null || obj.ReturnCount < 3)) {

                        if (result != null && result.sendstatus == 0) {
                            isEnd = true;
                            returnCount = (obj.ReturnCount == null ? 0 : obj.ReturnCount + 1);
                        } else {
                            isEnd = false;
                            returnCount = (obj.ReturnCount == null ? 0 : obj.ReturnCount + 1);
                        }

                        boolean isSuccess = postRewardDetailDao.updateIsEndAndReturnCount(item._id, isEnd, returnCount);

                        logger.info(logPre + "第{}/{}个-4.更新财富币打赏记录。id：{}，isEnd：{}，returnCount：{}，结果：{}",
                                i, rewardDetails.size(), item._id, isEnd, returnCount, isSuccess);

                    }

                    Thread.sleep(3500);

                }
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }


}
