package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoKafkaForBarChangeModel;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.SimplePostModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 帖子的吧关系变更kafka消费者
 */

@Component
public class BarRelationChangeJob {

    private static Logger logger = LoggerFactory.getLogger(BarRelationChangeJob.class);

    public static final String KAFKA_LISTENER_ID = "BarRelationChangeJob";

    @Autowired
    private App app;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.GubaPostInfo4FundQueue}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_BARRELATIONCHANGEJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_zp)
    private void onListen(ConsumerRecord<String, String> record) {

        /**
         *         当前需求情况 ，修改主吧的（组合吧  策略吧 投顾吧） 不支持
         *         文章类型只涉及财富号文章类型
         */
        try {
            String value = record.value();
            if (StringUtils.hasLength(value)) {
                PostInfoKafkaForBarChangeModel post = JacksonUtil.string2Obj(value, PostInfoKafkaForBarChangeModel.class);

                Integer id = post.getId();
                String actionType = post.getActionType();
                String code = post.getCode();
                String codeList = post.getCodeList();
                String whoDel = post.getWhoDel();

                switch (actionType) {
                    case "update":
                        //whoDel为老的主吧， code为新的主吧 ， code!=whoDel 为  修改主吧
                        boolean isMainBarChange = !equalsForCode(whoDel, code);
                        String filtedCode = null;
                        if (isMainBarChange) {
                            filtedCode = filterCode(whoDel);
                            dealChangeCode(filtedCode, String.valueOf(id));
                        }

                        logger.info("帖子id:{},actionType:{},code:{},whoDel:{},是否主吧变更:{},旧的主吧:{}", id, actionType, code, whoDel, isMainBarChange, filtedCode);
                        break;
                    case "zmtdistribute":
                        //whoDel为老的分发吧， code_list为新的分发吧 ，code_list为空 且 code_list!=whoDel 为  修改分发吧
                        boolean isClear = StringUtils.hasLength(whoDel) && !StringUtils.hasLength(codeList);
                        List<String> filtedCodes = null;
                        if (isClear) {
                            filtedCodes = filterCodeList(whoDel);
                            if (!CollectionUtils.isEmpty(filtedCodes)) {
                                for (String item : filtedCodes) {
                                    dealChangeCode(item, String.valueOf(id));
                                }
                            }

                        }

                        logger.info("帖子id:{},actionType:{},codeList:{},whoDel:{},是否清空分发吧:{},旧的分发吧:{}", id, actionType, codeList, whoDel, isClear, filtedCodes);
                        break;
                }
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 提取codelist中的code
     */
    private List<String> filterCodeList(String codeList) {
        List<String> result = new ArrayList<>();
        if (StringUtils.hasLength(codeList)) {
            List<String> codes = CommonUtils.toList(codeList, ",");
            if (!CollectionUtils.isEmpty(codes)) {
                final String OF = "of";
                final String ZH = "43-";
                final String TG = "58-";
                String temp = null;
                for (String code : codes) {
                    if (code.startsWith(OF)) {
                        temp = code.substring(2);
                    } else if (code.startsWith(ZH)) {
                        temp = null;
                    } else if (code.startsWith(TG)) {
                        temp = null;
                    } else {
                        temp = null;
                    }


                    if (StringUtils.hasLength(temp)) {
                        result.add(temp);
                    }
                }
            }
        }
        return result;
    }


    /**
     * 处理吧关系变更
     */
    private void dealChangeCode(String code, String postId) throws Exception {
        if (StringUtils.hasLength(code) && StringUtils.hasLength(postId)) {
            //删除旧的吧帖子列表缓存中的帖子
            String key = String.format(BarRedisKey.FUND_GUBA_SERVICE_FUND_POST_INFO_POST_LIST_LIST_NEW, code);
            String value = app.barredis.get(key);
            if (StringUtils.hasLength(value)) {
                List<SimplePostModel> list = JacksonUtil.string2Obj(value, List.class, SimplePostModel.class);
                if (!CollectionUtils.isEmpty(list)) {
                    int beforeSize = list.size();
                    list = list.stream().filter(a -> !postId.equals(String.valueOf(a.Id))).collect(Collectors.toList());
                    int afterSize = list.size();
                    if (beforeSize != afterSize) {
                        Boolean result = app.barredis.set(key, JacksonUtil.obj2String(list), 90 * 24 * 3600L);
                        if (result != null && !result) {
                            logger.info("吧关系变更写缓存失败。key:{},帖子id:{}", key, postId);
                        }
                    }
                }
            }
            logger.info("吧关系变更写缓存成功。key:{},帖子id:{}", key, postId);
        }
    }


    /**
     * 处理特殊的Code
     */
    public String filterCode(String code) {
        String result = code;
        //处理gd开头的基金和下划线后面带数字的基金code
        if (StringUtils.hasLength(result)) {
            if (result.length() > 6) {
                if (result.startsWith("gd")) {
                    result = result.substring(2);
                }
                if (result.contains("_")) {
                    result = result.split("_")[0];
                }
            } else if (result.endsWith("_1")) {
                result = result.substring(0, result.length() - 2);
            }
        }
        return result;
    }

    /**
     * 判断code是否相等
     */
    private boolean equalsForCode(String code1, String code2) {
        boolean result;
        if (!StringUtils.hasLength(code1) && !StringUtils.hasLength(code2)) {
            result = true;
        } else if (StringUtils.hasLength(code1)) {
            result = code1.equals(code2);
        } else {
            result = code2.equals(code1);
        }
        return result;
    }


}

