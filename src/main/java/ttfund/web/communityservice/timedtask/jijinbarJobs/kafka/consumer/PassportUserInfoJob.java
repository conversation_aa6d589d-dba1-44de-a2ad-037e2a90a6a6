package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.msyql.PassportUserInfoMysqlDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.Date;
import java.util.List;

/**
 * 用户通行证信息（kafka消费）
 */
@Slf4j
@Component
public class PassportUserInfoJob {

    public static final String KAFKA_LISTENER_ID = "KAFKA_LISTENER_ID_PassportUserInfoJob";

    @Autowired
    private PassportUserInfoDao passportUserInfoDao;

    @Autowired
    private PassportUserInfoMysqlDao passportUserInfoMysqlDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.Fund_SyncUserInfo}
        , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_PASSPORTUSERINFOJOB + "}"
        , containerFactory = KafkaConfig.kafkaListenerContainerFactory_fundBarOld_earliest)
    private void onListen(ConsumerRecord<String, String> record) {
        hanlderPassportUserInfo(record);
    }

    private void hanlderPassportUserInfo(ConsumerRecord<String, String> record) {

        log.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
            record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

        StringBuilder builder = new StringBuilder();
        try {

            List<PassportUserInfoModelNew> list = JacksonUtil.string2Obj(record.value(), List.class, PassportUserInfoModelNew.class);

            if (!CollectionUtils.isEmpty(list)) {
                for (PassportUserInfoModelNew model : list) {
                    builder = new StringBuilder();
                    dealModel(model, builder);
                    log.info(builder.toString());
                }
            }

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);

            log.error(builder.toString());

            log.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                record.partition(), record.offset(), record.timestamp(), record.key(), record.value())
            );
        }

    }

    private String getYYYYMMddStr(String unixTimeSpan) {
        String result = "";
        if (StringUtils.hasLength(unixTimeSpan)) {
            long value = Long.parseLong(unixTimeSpan) * 1000;
            result = DateUtil.dateToStr(new Date(value), DateUtil.dateTimeDefaultPattern);
        }
        return result;
    }

    public void dealModel(PassportUserInfoModelNew model, StringBuilder builder) throws Exception {
        builder.append("1.打印id：" + model.PassportID).append("\n");

        model._id = model.PassportID;
        PassportUserInfoModelNew oldModel = passportUserInfoDao.getPassportUserInfoById(model.PassportID);
        if (oldModel != null) {
            model.CFHUpdateTime = oldModel.CFHUpdateTime;
            model.CFHVSstatus = oldModel.CFHVSstatus;
            model.accreditationType = oldModel.accreditationType;
            model.abandon = oldModel.abandon;
            model.CFHVList = oldModel.CFHVList;
            if (StringUtils.isEmpty(model.CaifuhaoID)) {
                model.CaifuhaoID = oldModel.CaifuhaoID;
            }

            model.CreatTime = oldModel.CreatTime;
        }
        if (model.CreatTime == null) {
            model.CreatTime = new Date();
        }

        model.UpdateTime = new Date();
        model.RegisterDateTime = getYYYYMMddStr(model.Registertime);

        builder.append("2.读库。oldModel：" + (oldModel != null)).append("\n");

        // 同步到mysql
        int res = passportUserInfoMysqlDao.syncPassportUserToMySql(model);

        builder.append("3.同步到mysql").append("\n");

        //同步到mongdb
        boolean mongdbRes = passportUserInfoDao.updatePassportUserToMongdb(model);

        builder.append("4.同步到mongo").append("\n");

        // 如果mysql写成功则直接写入redis
        String key = String.format(UserRedisConfig.Asp_Net_Fund_Service_Passport_Info_pid, model.PassportID);
        userRedisDao.set(key, JacksonUtil.obj2String(model), 720 * 24 * 3600L);

        builder.append("5.同步到Redis").append("\n");

    }

}

