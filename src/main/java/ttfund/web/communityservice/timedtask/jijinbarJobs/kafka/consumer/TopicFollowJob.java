package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.guba.TopicFollowKafka;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.mongo.TopicFollowDao;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 帖子动效数量同步job
 * 需求：#668693 【基金吧6.14.1】新增帖子特殊动效
 */
@Slf4j
@Component
public class TopicFollowJob {

    public static final String KAFKA_LISTENER_ID = "TopicFollowJob";

    @Autowired
    private TopicFollowDao topicFollowDao;


    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.USER_COLLECTION_TOPIC}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_TOPICFOLLOWJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_qa_new_zp)
    private void onListen(ConsumerRecord<String, String> record) {
        deal(record, log);
    }

    public void deal(ConsumerRecord<String, String> record, Logger logger) {
        try {
            logger.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            TopicFollowKafka model = JSON.parseObject(record.value(), TopicFollowKafka.class);

            Map<String, Object> map = null;
            switch (model.getActiontype()) {
                case "add"://添加关注
                    map = new HashMap<>();
                    map.put("_id", String.format("%s_%s", model.getHtid(), model.getUid()));
                    map.put("htId", model.getHtid());
                    map.put("uid", model.getUid());
                    map.put("state", 1);
                    map.put("operationTime", model.getOperationTime());
                    map.put("createTime", new Date());
                    map.put("updateTime", new Date());

                    topicFollowDao.upsertManyBySetWithSetOnInsertFields(Arrays.asList(map), Arrays.asList("createTime"), "_id");
                    break;
                case "remove"://取消关注
                    map = new HashMap<>();
                    map.put("_id", String.format("%s_%s", model.getHtid(), model.getUid()));
                    map.put("htId", model.getHtid());
                    map.put("uid", model.getUid());
                    map.put("state", 0);
                    map.put("operationTime", model.getOperationTime());
                    map.put("createTime", new Date());
                    map.put("updateTime", new Date());

                    topicFollowDao.upsertManyBySetWithSetOnInsertFields(Arrays.asList(map), Arrays.asList("createTime"), "_id");
                    break;
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }


}
