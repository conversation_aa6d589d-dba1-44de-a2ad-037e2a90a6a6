package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.ModelPortfolioKafkaModel;
import ttfund.web.communityservice.bean.jijinBar.data.ModelPortfolioModel;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.ModelPortfolioDao;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.Date;

/**
 * 模拟组合动态  --消费者
 */
@Component
public class ModelPortfolioInfoJob {

    private static Logger logger = LoggerFactory.getLogger(ModelPortfolioInfoJob.class);

    public static final String KAFKA_LISTENER_ID = "listener_id_ModelPortfolioInfoJob";

    @Autowired
    private ModelPortfolioDao modelPortfolioDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.FUNDBAR_KAFKA_TOPIC_MODELPORTFOLIO}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_MODELPORTFOLIOINFOKAFKACONSUMER + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_fundbar)
    private void onListen(ConsumerRecord<String, String> record) {

        try {

            logger.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，value：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            String value = record.value();
            if (StringUtils.hasLength(value)) {
                ModelPortfolioKafkaModel kafkaInfo = JacksonUtil.deserialize(value, ModelPortfolioKafkaModel.class);

                ModelPortfolioModel model = new ModelPortfolioModel();
                model.setCode(kafkaInfo.getSubAccountNo());
                model.setName(kafkaInfo.getSubAccountName());
                model.setUid(kafkaInfo.getPassportId());
                model.setState(kafkaInfo.getState());
                model.setOpenState(kafkaInfo.getOpenState());
                model.setCreateTime(new Date());
                model.setUpdateTime(new Date());

                modelPortfolioDao.insertOrUpdate(model);

            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }


}
