package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.data.PostDynamicPerformanceCountKafkaModel;
import ttfund.web.communityservice.config.kafka.FundKafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.mongo.PostDynamicPerformanceCountDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;


/**
 * 帖子动效数量同步job
 * 需求：#668693 【基金吧6.14.1】新增帖子特殊动效
 */
@Slf4j
@JobHandler("PostDynamicPerformanceCountJob")
@Component
public class PostDynamicPerformanceCountJob extends IJobHandler {

    private static List<String> setOnInsertFields = Arrays.asList("state", "createTime");

    private volatile Thread thread;

    private volatile long timestamp = 0L;

    private volatile Integer heartBeatInterval = 0;

    private volatile Integer batchCount = 200;

    @Autowired
    private FundKafkaConfig fundKafkaConfig;

    @Autowired
    private PostDynamicPerformanceCountDao postDynamicPerformanceCountDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            //kafka一次拉取数量
            Integer maxPollRecords = null;
            //kafka一次拉取等待时间
            Integer pollDuration = null;
            //本服务运行心跳时间  单位毫秒
            Integer heartBeatInterval = null;
            //一批积攒数量
            Integer batchCount = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                maxPollRecords = jsonObject.getInteger("maxPollRecords");
                pollDuration = jsonObject.getInteger("pollDuration");
                heartBeatInterval = jsonObject.getInteger("heartBeatInterval");
                batchCount = jsonObject.getInteger("batchCount");
            }

            if (maxPollRecords == null) {
                maxPollRecords = 500;
            }
            if (pollDuration == null) {
                pollDuration = 1000;
            }
            if (heartBeatInterval == null) {
                heartBeatInterval = 0;
            }
            if (batchCount == null) {
                batchCount = 100;
            }

            this.heartBeatInterval = heartBeatInterval;
            this.batchCount = batchCount;
            this.timestamp = System.currentTimeMillis();

            log.info("第零步，打印参数。maxPollRecords：{}，pollDuration：{}，heartBeatInterval：{}，batchCount：{}，timestamp：{}",
                maxPollRecords,
                pollDuration,
                heartBeatInterval,
                batchCount,
                timestamp
            );

            deal(maxPollRecords, pollDuration);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int maxPollRecords, int pollDuration) {
        if (thread == null) {
            thread = new Thread(() -> dealTask(maxPollRecords, pollDuration));
            thread.start();

            log.info("0.检测到线程为null，新建线程并运行。");
        }
    }


    private void dealTask(int maxPollRecords, int pollDuration) {

        KafkaConsumer<String, String> consumer = null;
        try {
            log.info("1.启动。");

            Properties properties = fundKafkaConfig.consumerConfigsForPostDynamicPerformanceCountJob();
            properties.put("max.poll.records", maxPollRecords);
            consumer = new KafkaConsumer<>(properties);
            consumer.subscribe(Arrays.asList(KafkaTopicName.POST_DYNAMIC_PERFORMANCE_COUNT_TOPIC));


            log.info("2.订阅kafka，消费组配置：\n{}", JSON.toJSONString(properties));

            PostDynamicPerformanceCountKafkaModel model = null;
            List<PostDynamicPerformanceCountKafkaModel> modelList = null;

            List<Map<String, Object>> mapList = null;
            Map<String, Object> map = null;

            ConsumerRecords<String, String> consumerRecords = null;
            ConsumerRecord<String, String> lastRecord = null;
            while (true) {
                if (heartBeatInterval > 0 && (System.currentTimeMillis() - timestamp > heartBeatInterval)) {

                    log.info("3.业务运行心跳间隔超出限制，停止运行。当前时间：{}，timestamp：{}", System.currentTimeMillis(), timestamp);
                    break;
                }

                consumerRecords = consumer.poll(Duration.ofMillis(pollDuration));

                log.info("3.消费kafka。数量：{}", consumerRecords.count());

                if (consumerRecords.count() > 0) {
                    modelList = new ArrayList<>();

                    for (ConsumerRecord<String, String> record : consumerRecords) {
                        lastRecord = record;

                        model = JSON.parseObject(record.value(), PostDynamicPerformanceCountKafkaModel.class);

                        modelList.add(model);
                    }

                    log.info("4.处理消息。数量：{}，partition：{}，key：{}，offset：{}，timestamp：{}，value：\n{}",
                        consumerRecords.count(),
                        lastRecord.partition(),
                        lastRecord.key(),
                        lastRecord.offset(),
                        lastRecord.timestamp(),
                        lastRecord.value()
                    );

                    if (!CollectionUtils.isEmpty(modelList)) {
                        Map<String, PostDynamicPerformanceCountKafkaModel> modelMap = new HashMap<>();
                        for (PostDynamicPerformanceCountKafkaModel a : modelList) {
                            model = modelMap.get(String.format("%s_%s", a.get_id(), a.getType()));
                            if (model == null || model.getTime().compareTo(a.getTime()) < 0) {
                                modelMap.put(String.format("%s_%s", a.get_id(), a.getType()), a);
                            }
                        }

                        mapList = new ArrayList<>();
                        for (PostDynamicPerformanceCountKafkaModel a : modelMap.values()) {
                            map = new HashMap<>();
                            map.put("_id", a.get_id());
                            map.put("state", "1");
                            map.put("createTime", new Date());
                            map.put("updateTime", new Date());

                            if ("1".equals(a.getType())) {
                                map.put("hugNum", a.getCount());
                                map.put("hugTime", a.getTime());
                            } else if ("2".equals(a.getType())) {
                                map.put("eatNoodlesNum", a.getCount());
                                map.put("eatNoodlesTime", a.getTime());
                            }
                            mapList.add(map);
                        }

                        List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, batchCount);
                        for (List<Map<String, Object>> batch : batchList) {
                            postDynamicPerformanceCountDao.upsertManyBySetWithSetOnInsertFields(batch, setOnInsertFields, "_id");
                        }

                    }

                    log.info("5.插入数据库。数量：{}，数据：\n{}",
                        Thread.currentThread().getName(),
                        mapList == null ? 0 : mapList.size(),
                        CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONStringWithDateFormat(mapList.get(mapList.size() - 1), DateUtil.datePattern)
                    );

                    consumer.commitSync();
                }
            }
            log.info("6.正常结束。");
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            log.error("6.异常结束。");
        } finally {
            if (consumer != null) {
                consumer.close();
            }

            thread = null;
        }


    }

}
