package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.QA.AnswerExtensionModel;
import ttfund.web.communityservice.bean.jijinBar.post.QA.QAScoreInfo;
import ttfund.web.communityservice.bean.messagepush.FundQuestionInfo;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.PostFindScoreDao;
import ttfund.web.communityservice.dao.mongo.QAScoreInfoDao;
import ttfund.web.communityservice.dao.msyql.AnswerDao;
import ttfund.web.communityservice.dao.msyql.QuestionDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 问答打分服务job
 */
@JobHandler("QAScoreCalcJob")
@Component
public class QAScoreCalcJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(QAScoreCalcJob.class);

    private String[] configs;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private QuestionDao questionDao;

    @Autowired
    private QAScoreInfoDao qaScoreInfoDao;

    @Autowired
    private AnswerDao answerDao;

    @Autowired
    private PostFindScoreDao postFindScoreDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        Integer batchReadCount = null;
        String initBreakpoint1 = null;
        String initBreakpoint2 = null;
        String initBreakpoint3 = null;
        if (StringUtils.hasLength(s)) {
            JSONObject jsonObject = JSON.parseObject(s);
            batchReadCount = jsonObject.getInteger("batchReadCount");
            initBreakpoint1 = jsonObject.getString("initBreakpoint1");
            initBreakpoint2 = jsonObject.getString("initBreakpoint2");
            initBreakpoint3 = jsonObject.getString("initBreakpoint3");
        }

        if (batchReadCount == null) {
            batchReadCount = 5000;
        }

        logger.info("第零步，打印参数。batchReadCount：{}，initBreakpoint1：{}，initBreakpoint2：{}，initBreakpoint3：{}",
                batchReadCount,
                initBreakpoint1,
                initBreakpoint2,
                initBreakpoint3);

        if (StringUtils.hasLength(initBreakpoint1) || StringUtils.hasLength(initBreakpoint2) || StringUtils.hasLength(initBreakpoint3)) {
            if (StringUtils.hasLength(initBreakpoint1)) {
                userRedisDao.set(UserRedisConfig.QASCORECALCJOB_QUESTIONSCORECALC_BREAKPOINT, initBreakpoint1, 30 * 24 * 3600L);
            }
            if (StringUtils.hasLength(initBreakpoint2)) {
                userRedisDao.set(UserRedisConfig.QASCORECALCJOB_ANSWERSCORECALC_BREAKPOINT, initBreakpoint2, 30 * 24 * 3600L);
            }
            if (StringUtils.hasLength(initBreakpoint3)) {
                userRedisDao.set(UserRedisConfig.QASCORECALCJOB_CLEARDELQUETIONS_BREAKPOINT, initBreakpoint3, 30 * 24 * 3600L);
            }

            logger.info("第零步，初始化断点。initBreakpoint1：{}，initBreakpoint2：{}，initBreakpoint3：{}",
                    initBreakpoint1,
                    initBreakpoint2,
                    initBreakpoint3);

            return ReturnT.SUCCESS;
        }

        configs = appConstant.qaScoreConfig.split(",");

        questionScoreCalc(batchReadCount);

        answerScoreCalc(batchReadCount);

        //清除基金已经删除问答的所有回答数
        clearDelQuetions(batchReadCount);

        return ReturnT.SUCCESS;
    }


    /**
     * 问题打分计算
     * 问题分数= 悬赏分数+回答数*1.5+提问用户分数+基金吧分数
     * >8分则展示(上线一段时间后，需要调整)
     * <p>
     * 悬赏分数：
     * 无悬赏时，悬赏分=0；
     * 有悬赏时，悬赏分=5+(悬赏财富币数/100)*0.5
     * <p>
     * 用户分数：
     * 普通用户帖子分数=0；关注用户或自己=3；大V用户=5；基金经理=10
     * <p>
     * 基金吧分数：已关注+3
     */
    private boolean questionScoreCalc(int batchReadCount) {
        try {
            String breakpointName = UserRedisConfig.QASCORECALCJOB_QUESTIONSCORECALC_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("questionScoreCalc-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);

            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("questionScoreCalc-第一步，读取断点。断点:{}", breakpoint);

            Date maxUpdate = null;
            int round = 0;
            boolean isRunning = true;
            while (isRunning) {
                round++;

                List<FundQuestionInfo> dataList = questionDao.getFundQuestionList(breakpointDate, batchReadCount, 1);
                if (!CollectionUtils.isEmpty(dataList)) {
                    dataList.forEach(item -> item.set_id(item.getQID()));
                }

                logger.info("questionScoreCalc-第二步-读取mysql-轮次{}。数量：{}，头部id列表：{}",
                        round,
                        dataList == null ? 0 : dataList.size(),
                        dataList == null ? null : dataList.stream().map(a -> a.QID).limit(20).collect(Collectors.toList()));

                if (!CollectionUtils.isEmpty(dataList)) {
                    maxUpdate = dataList.stream().max(Comparator.comparing(o -> o.getUpdateTime())).get().getUpdateTime();
                    List<QAScoreInfo> questionScores = dataList.stream().map(item -> {
                        QAScoreInfo temp = new QAScoreInfo();
                        temp._id = item.getQID();
                        temp.QID = item.getQID();
                        temp.POSTID = item.getArticleId() == null ? 0 : item.getArticleId();
                        temp.TYPE = 49;
                        temp.UID = item.getUserId();
                        temp.CODE = item.getStockBarCode().replace("of", "");
                        temp.TIME = item.getCreatedTime();
                        temp.TIMEPOINT = item.getTIMEPOINT() == null ? DateUtil.getTimePoint(item.getCreatedTime()) : item.getTIMEPOINT();
                        temp.SCORE = calculateQuestionScore(item);
                        return temp;
                    }).collect(Collectors.toList());

                    Map<String, Object> map = null;
                    List<Map<String, Object>> mapList = null;
                    List<List<QAScoreInfo>> batchList = CommonUtils.toSmallList2(questionScores, 200);
                    for (List<QAScoreInfo> batch : batchList) {
                        mapList = new ArrayList<>(batch.size());
                        for (QAScoreInfo a : batch) {
                            map = CommonUtils.beanToMap(a);
                            mapList.add(map);
                        }
                        qaScoreInfoDao.upsertMany(mapList);
                    }

                    logger.info("questionScoreCalc-第三步-写mongo-轮次{}。数量：{}，头部id列表：{}",
                            round,
                            dataList == null ? 0 : dataList.size(),
                            dataList == null ? null : dataList.stream().map(a -> a.QID).limit(20).collect(Collectors.toList()));

                    if (breakpointDate.compareTo(maxUpdate) == 0 && dataList.size() == batchReadCount) {

                        logger.error("questionScoreCalc-同一秒有大量数据，超过阈值！");

                        break;
                    }

                    if (dataList.size() < batchReadCount && breakpointDate.compareTo(maxUpdate) == 0) {
                        maxUpdate = DateUtil.calendarDateBySecond(maxUpdate, 1);
                    }

                    breakpointDate = maxUpdate;
                    breakpoint = DateUtil.dateToStr(breakpointDate);
                    userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

                    logger.info("questionScoreCalc-第四步-更新断点-轮次{}。断点：{}", round, breakpoint);

                    if (dataList.size() < batchReadCount) {
                        isRunning = false;
                    }
                } else {
                    isRunning = false;
                }
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return false;
        }
        return true;
    }

    /**
     * 回答打分计算
     * 回答分数= 采纳分数+评论数*2+点赞数*1.5+用户分数+基金吧分数>8分则展示(上线一段时间后，需要调整)
     * 采纳分数：
     * 未被采纳=0
     * 被采纳=5+(悬赏金额/100)*0.5，最高可取到10分
     * <p>
     * 用户分数：
     * 普通用户帖子分数=0；关注用户或自己=3；大V用户=5；基金经理=10
     * <p>
     * 基金吧分数：已关注+3
     */
    private boolean answerScoreCalc(int batchReadCount) {
        try {

            String breakpointName = UserRedisConfig.QASCORECALCJOB_ANSWERSCORECALC_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("answerScoreCalc-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("answerScoreCalc-第一步，读取断点。断点:{}", breakpoint);

            Date maxUpdate = null;
            int pageIndex = 0;
            int round = 0;
            boolean isRunning = true;
            while (isRunning) {
                round++;

                List<AnswerExtensionModel> dataList = answerDao.getFundAnswerExtensionList(pageIndex, breakpointDate, batchReadCount);

                logger.info("answerScoreCalc-第二步-读取mysql-轮次{}。数量：{}，头部id列表：{}",
                        round,
                        dataList == null ? 0 : dataList.size(),
                        dataList == null ? null : dataList.stream().map(a -> a.AID).limit(20).collect(Collectors.toList()));

                if (!CollectionUtils.isEmpty(dataList)) {
                    maxUpdate = dataList.stream().max(Comparator.comparing(o -> o.getUpdateTime())).get().getUpdateTime();
                    List<QAScoreInfo> questionScores = dataList.stream().map(item -> {
                        QAScoreInfo temp = new QAScoreInfo();
                        temp._id = item.getAID();
                        temp.QID = item.getQID();
                        temp.POSTID = item.getArticleId() == null ? 0 : item.getArticleId();
                        temp.TYPE = 50;
                        temp.UID = item.getCreatorID();
                        temp.CODE = item.getStockBarCode().replace("of", "");
                        temp.TIME = item.getCreated();
                        temp.TIMEPOINT = item.TIMEPOINT == null ? DateUtil.getTimePoint(item.getCreated()) : item.TIMEPOINT;
                        temp.SCORE = calculateAnswerScore(item);
                        return temp;
                    }).collect(Collectors.toList());

                    Map<String, Object> map = null;
                    List<Map<String, Object>> mapList = null;
                    List<List<QAScoreInfo>> batchList = CommonUtils.toSmallList2(questionScores, 200);
                    for (List<QAScoreInfo> batch : batchList) {
                        mapList = new ArrayList<>(batch.size());
                        for (QAScoreInfo a : batch) {
                            map = CommonUtils.beanToMap(a);
                            mapList.add(map);
                        }
                        qaScoreInfoDao.upsertMany(mapList);
                    }

                    logger.info("answerScoreCalc-第三步-写mongo-轮次{}。数量：{}，头部id列表：{}",
                            round,
                            dataList == null ? 0 : dataList.size(),
                            dataList == null ? null : dataList.stream().map(a -> a.AID).limit(20).collect(Collectors.toList()));

                    if (dataList.size() < batchReadCount) {
                        isRunning = false;
                    }
                    if (breakpointDate.compareTo(maxUpdate) == 0 && dataList.size() == batchReadCount) {
                        batchReadCount += 5000;

                        logger.info("answerScoreCalc-同一秒有大量数据，超过阈值！");
                    }
                    breakpointDate = maxUpdate;
                    breakpoint = DateUtil.dateToStr(breakpointDate);
                    userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

                    logger.info("answerScoreCalc-第四步-更新断点-轮次{}。断点：{}", round, breakpoint);

                } else {
                    isRunning = false;
                }
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return false;
        }
        return true;
    }


    /**
     * 清除问答页，发现页 删除问题的回答数据
     */
    public boolean clearDelQuetions(int batchReadCount) {
        try {

            String breakpointName = UserRedisConfig.QASCORECALCJOB_CLEARDELQUETIONS_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("clearDelQuetions-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            logger.info("clearDelQuetions-第一步，读取断点。断点:{}", breakpoint);

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            int round = 0;
            boolean isRunning = true;
            Date maxUpdate = null;
            List<String> qIds = null;
            while (isRunning) {
                round++;

                Date tempDate = DateUtil.calendarDateByHour(breakpointDate, -1);
                List<FundQuestionInfo> dataList = questionDao.getFundQuestionDelList(tempDate, batchReadCount);
                if (!CollectionUtils.isEmpty(dataList)) {
                    for (FundQuestionInfo item : dataList) {
                        item.set_id(item.getQID());
                    }
                }

                logger.info("clearDelQuetions-第二步-读取mysql-轮次{}。数量：{}，头部id列表：{}",
                        round,
                        dataList == null ? 0 : dataList.size(),
                        dataList == null ? null : dataList.stream().map(a -> a.QID).limit(20).collect(Collectors.toList()));

                if (!CollectionUtils.isEmpty(dataList)) {

                    maxUpdate = dataList.stream().max(Comparator.comparing(o -> o.getUpdateTime())).get().getUpdateTime();

                    //问题列表
                    List<String> qids = dataList.stream().map(a -> a.QID).collect(Collectors.toList());

                    List<List<String>> batchList = CommonUtils.toSmallList2(qids, 200);
                    for (List<String> batch : batchList) {
                        //问答页删除
                        qaScoreInfoDao.removeByQidList(batch);
                    }

                    logger.info("clearDelQuetions-第三步-写mongo-{}-轮次{}。数量：{}，头部id列表：{}",
                            BarMongodbConfig.TABLE_QASCOREINFO,
                            round,
                            dataList == null ? 0 : dataList.size(),
                            dataList == null ? null : dataList.stream().map(a -> a.QID).limit(20).collect(Collectors.toList()));

                    for (List<String> batch : batchList) {
                        //发现页删除
                        postFindScoreDao.removeByQidList(batch);
                    }

                    logger.info("clearDelQuetions-第四步-写mongo-{}-轮次{}。数量：{}，头部id列表：{}",
                            BarMongodbConfig.TABLE_POSTFINDSCORE,
                            round,
                            dataList == null ? 0 : dataList.size(),
                            dataList == null ? null : dataList.stream().map(a -> a.QID).limit(20).collect(Collectors.toList()));

                    if (dataList.size() < batchReadCount) {
                        isRunning = false;
                    }

                    breakpointDate = maxUpdate;
                    breakpoint = DateUtil.dateToStr(breakpointDate);
                    userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

                    logger.info("clearDelQuetions-第四步-更新断点-轮次{}。断点：{}", round, breakpoint);

                } else {
                    isRunning = false;
                }
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return false;
        }
        return true;


    }


    /**
     * 具体算式来计算提问分数
     */
    private double calculateQuestionScore(FundQuestionInfo item) {
        item.AnswerCount = item.AnswerCount == null ? 0 : item.AnswerCount;
        return item.AnswerCount * Double.parseDouble(configs[0]) +
                (Math.abs(item.Amount) < 0.0000000001 ? 0 : Math.min(Double.parseDouble(configs[5]), Double.parseDouble(configs[4]) + item.Amount * Double.parseDouble(configs[3])));
    }

    /**
     * 具体算式来计算回答分数
     */
    private double calculateAnswerScore(AnswerExtensionModel item) {
        item.IsAdopted = item.IsAdopted == null ? 0 : item.IsAdopted;
        return item.CommentCount * Double.parseDouble(configs[1]) + item.LikeCount * Double.parseDouble(configs[2])
                + (item.IsAdopted == 1 ? Math.min(Double.parseDouble(configs[5]), Double.parseDouble(configs[4]) + item.Amount * Double.parseDouble(configs[3])) : 0);
    }

}
