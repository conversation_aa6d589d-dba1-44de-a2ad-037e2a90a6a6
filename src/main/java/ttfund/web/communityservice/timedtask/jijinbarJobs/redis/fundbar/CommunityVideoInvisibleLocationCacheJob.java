package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.CommunityVideoDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 社区视频隐藏数据写缓存job
 */
@Slf4j
@JobHandler("CommunityVideoInvisibleLocationCacheJob")
@Component
public class CommunityVideoInvisibleLocationCacheJob extends IJobHandler {

    private static final List<String> FIELDS = Arrays.asList("postId", "invisibleLocation", "authorInvisible");

    @Autowired
    private CommunityVideoDao communityVideoDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            String invisibleLocations = null;
            Long expire = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                invisibleLocations = jsonObject.getString("invisibleLocations");
                expire = jsonObject.getLong("expire");
            }

            if (invisibleLocations == null) {
                invisibleLocations = "1,2,3,4,5";
            }
            if (expire == null) {
                expire = 7 * 24 * 3600L;
            }

            log.info("0.打印参数。invisibleLocations：{}，expire：{}", invisibleLocations, expire);

            deal(CommonUtils.toList(invisibleLocations, ","), expire);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(List<String> invisibleLocations, Long expire) {

        Date now = new Date();
        Date lastYear = DateUtil.calendarDateByYears(now, -1);

        Date start = lastYear;
        Date end = DateUtil.calendarDateByMonth(lastYear, 1);
        Long startTimePoint = DateUtil.getTimePoint(start);
        Long endTimePoint = DateUtil.getTimePoint(end);
        int round = 0;

        List<Document> totalList = new ArrayList<>();

        while (true) {
            round++;

            List<Document> list = communityVideoDao.getHiddenVideoPostByTimePoint(FIELDS, Document.class, startTimePoint, endTimePoint);
            if (!CollectionUtils.isEmpty(list)) {
                totalList.addAll(list);
            }

            log.info("1.读取数据详情-第{}轮。start：{}，end：{}，数量：{}，数据：{}",
                round,
                startTimePoint,
                endTimePoint,
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

            start = end;
            end = DateUtil.calendarDateByMonth(end, 1);
            startTimePoint = DateUtil.getTimePoint(start);
            endTimePoint = DateUtil.getTimePoint(end);

            if (start.compareTo(now) >= 0) {
                break;
            }

        }

        log.info("1.读取数据完成。start：{}，end：{}，数量：{}，数据：{}",
            startTimePoint,
            endTimePoint,
            CollectionUtils.isEmpty(totalList) ? 0 : totalList.size(),
            CollectionUtils.isEmpty(totalList) ? null : JSON.toJSONStringWithDateFormat(totalList.get(0), DateUtil.datePattern)
        );

        Map<String, Set<String>> locationToPostsMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(totalList)) {
            for (Document document : totalList) {
                String postId = document.getString("postId");
                List<String> invisibleLocation = document.getList("invisibleLocation", String.class);

                if (!CollectionUtils.isEmpty(invisibleLocation)) {
                    for (String a : invisibleLocation) {
                        if (!"0".equals(a)) {
                            if (locationToPostsMap.get(a) == null) {
                                locationToPostsMap.put(a, new LinkedHashSet<>());
                            }
                            locationToPostsMap.get(a).add(postId);
                        }
                    }
                }

                List<String> authorInvisible = document.getList("authorInvisible", String.class);
                if (!CollectionUtils.isEmpty(authorInvisible)) {
                    for (String a : authorInvisible) {
                        if (!"0".equals(a)) {
                            if (locationToPostsMap.get(a) == null) {
                                locationToPostsMap.put(a, new LinkedHashSet<>());
                            }
                            locationToPostsMap.get(a).add(postId);
                        }
                    }
                }
            }
        }

        log.info("2.处理。真实隐藏位置数据数量：{}", CollectionUtils.isEmpty(locationToPostsMap) ? 0 : locationToPostsMap.size());

        if (!CollectionUtils.isEmpty(invisibleLocations)) {
            int i = 0;
            for (String a : invisibleLocations) {
                i++;
                Set<String> tempSet = locationToPostsMap.getOrDefault(a, new HashSet<>());
                app.barredis.set(String.format(BarRedisKey.VIDEO_POST_HIDE_LOCATION, a), JSON.toJSONString(tempSet), expire);

                log.info("3.写缓存详情-第{}/{}个。隐藏位置：{}，数量：{}，数据：{}",
                    i,
                    invisibleLocations.size(),
                    a,
                    tempSet.size(),
                    tempSet.stream().findFirst().orElse(null)
                );
            }
        }

        log.info("3.写缓存完成。预设隐藏位置数量：{}", CollectionUtils.isEmpty(invisibleLocations) ? 0 : invisibleLocations.size());

    }

}
