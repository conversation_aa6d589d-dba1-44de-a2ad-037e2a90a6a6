package ttfund.web.communityservice.timedtask.jijinbarJobs.robot;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.robot.RobotConfigModel;
import ttfund.web.communityservice.bean.jijinBar.robot.RobotGlobal;
import ttfund.web.communityservice.bean.jijinBar.robot.RobotPraiseModel;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.RobotConfigDao;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR> Zhuyuang
 * @date : 2024-11-28 10:09
 * @description :
 */
@Component
public class PostInfoCollectJob {

    private static Logger logger = LoggerFactory.getLogger(PostInfoCollectJob.class);

    private static final String KAFKA_LISTENER_ID = "listener_id_PostInfoCollectJob";

    @Autowired
    private RobotConfigDao robotConfigDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.EM_FUND_POST_LIKE_ROBOT}
        , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_ROBOT_POST_COLLECT_KAFKA_JOB + "}"
        , containerFactory = KafkaConfig.kafkaListenerContainerFactory_fundproduct)
    public void onListen(ConsumerRecord<String, String> msg) {
        try {
            if (msg != null && StringUtils.hasLength(msg.value())) {
                List<RobotPraiseModel> model = JacksonUtil.string2Obj(msg.value(), List.class, RobotPraiseModel.class);
                if (model != null && model.size() > 0) {
                    logger.info("Kafka消费信息 ==> {}", msg.value());
                    RobotConfigModel configModel = robotConfigDao.getRobotConfigModel();
                    if (configModel != null && configModel.getIsOpen() == 1) {
                        for (RobotPraiseModel item : model) {
                            if (item.PUSHTYPE == 0) {
                                ///实时的数据存储容器
                                RobotPraiseModel temp = RobotGlobal.list.stream().
                                    filter(obj -> obj.ID.equals(item.ID)).
                                    findFirst().orElse(null);
                                if (temp == null && RobotGlobal.list.size() < 10000) {
                                    RobotGlobal.list.add(item);
                                }
                            }
                            else if (item.PUSHTYPE == 1) {
                                ///三天的数据存储容器
                                RobotPraiseModel temp = RobotGlobal.ThreadList.stream().
                                    filter(obj -> obj.ID.equals(item.ID)).
                                    findFirst().orElse(null);
                                if (temp == null) {
                                    RobotPraiseModel oldData = RobotGlobal.OldList.stream().
                                        filter(obj -> obj.ID.equals(item.ID)).
                                        findFirst().orElse(null);
                                    if (oldData != null) {
                                        item.UserList = oldData.UserList;
                                        item.ClickNum = (item.LIKECOUNT - oldData.ClickNum) + item.REPLYCOUNT / 10;
                                        RobotGlobal.OldList.remove(oldData);
                                    }
                                    else {
                                        item.ClickNum = item.LIKECOUNT + item.REPLYCOUNT / 10;
                                    }
                                    if (item.ClickNum > 10) {
                                        //获取6，10之前的随机数
                                        item.ClickNum = getRandom(6, 11);
                                    }
                                    if (item.ClickNum > 0 && RobotGlobal.ThreadList.size() < 10000) {
                                        RobotGlobal.ThreadList.add(item);
                                    }
                                }
                            }
                        }
                    } else {
                        RobotGlobal.list = new ArrayList<>();
                        RobotGlobal.ThreadList = new ArrayList<>();
                        RobotGlobal.OldList = new ArrayList<>();
                    }
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    private int getRandom(int minNumber, int maxNumber) {
        Random random = new Random();
        return random.nextInt(maxNumber - minNumber) + minNumber;
    }
}
