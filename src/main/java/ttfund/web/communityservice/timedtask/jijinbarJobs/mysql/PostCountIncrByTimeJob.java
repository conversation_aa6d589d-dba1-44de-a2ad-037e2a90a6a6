package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.data.PostCountHisModel;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.msyql.PostCountHisDao;
import ttfund.web.communityservice.dao.msyql.PostCountIncrByTimeDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;

/**
 * 帖子 点赞、评论、阅读增量统计
 */
@Slf4j
@JobHandler("PostCountIncrByTimeJob")
@Component
public class PostCountIncrByTimeJob extends IJobHandler {

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostCountHisDao postCountHisDao;

    @Autowired
    private PostCountIncrByTimeDao postCountIncrByTimeDao;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            log.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.POSTCOUNTINCRBYTIMEJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            logicDeal(batchReadCount);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void logicDeal(int batchReadCount) {
        /*
         * 1.增量获取拍照数据
         * 2.计算增量数据
         */

        String breakpointName = UserRedisConfig.POSTCOUNTINCRBYTIMEJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            log.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("第一步，读取断点。断点:{}", breakpoint);

        List<PostCountHisModel> list = postCountHisDao.getList(breakpointDate, batchReadCount);

        log.info("第二步，读取数据。数量:{}，头部列表：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = list.get(list.size() - 1).UPDATETIME;

            List<Map<String, Object>> incrPostList = new ArrayList<>();
            for (PostCountHisModel item : list) {
                List<PostCountHisModel> postCountList = postCountHisDao.get(item.PostId);
                if (!CollectionUtils.isEmpty(postCountList)) {
                    Map<String, Object> model = new HashMap<>();
                    model.put("CLICKNUM", Math.max(postCountList.get(postCountList.size() - 1).CLICKNUM - postCountList.get(0).CLICKNUM, 0));
                    model.put("LIKECOUNT", Math.max(postCountList.get(postCountList.size() - 1).LIKECOUNT - postCountList.get(0).LIKECOUNT, 0));
                    model.put("PINGLUNNUM", Math.max(postCountList.get(postCountList.size() - 1).PINGLUNNUM - postCountList.get(0).PINGLUNNUM, 0));
                    model.put("ID", item.PostId);
                    model.put("UPDATETIME", new Date());

                    incrPostList.add(model);
                }

            }

            log.info("第三步，处理数据。数量:{}，头部列表：{}",
                    CollectionUtils.isEmpty(incrPostList) ? 0 : incrPostList.size(),
                    CollectionUtils.isEmpty(incrPostList) ? null : JSON.toJSONStringWithDateFormat(incrPostList.get(0), DateUtil.datePattern)
            );

            if (!CollectionUtils.isEmpty(incrPostList)) {
                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(incrPostList, 50);
                for (List<Map<String, Object>> batch : batchList) {
                    postCountIncrByTimeDao.insertOrUpdate(batch);
                }
            }

            log.info("第四步，写库。数量:{}，头部列表：{}",
                    CollectionUtils.isEmpty(incrPostList) ? 0 : incrPostList.size(),
                    CollectionUtils.isEmpty(incrPostList) ? null : JSON.toJSONStringWithDateFormat(incrPostList.get(0), DateUtil.datePattern)
            );

        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

        log.info("第五步，更新断点。断点：{}", breakpoint);
    }

}
