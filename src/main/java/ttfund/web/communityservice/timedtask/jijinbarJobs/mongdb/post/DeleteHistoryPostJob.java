package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.HotPostModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfo;
import ttfund.web.communityservice.bean.jijinBar.post.data.CommunityVideo;
import ttfund.web.communityservice.bean.jijinBar.post.elitepost.ElitePostModel;
import ttfund.web.communityservice.bean.jijinBar.user.UserRankMModel;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.dao.mongo.*;
import ttfund.web.communityservice.utils.DateUtil;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 删除一年前的历史帖子
 *
 * @author：yj
 * @date：2024/02/29 17:30
 */
@JobHandler(value = "deleteHistoryPostJob")
@Component
public class DeleteHistoryPostJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(DeleteHistoryPostJob.class);

    @Autowired
    private PostDao postDao;

    @Autowired
    private PopularityRankWDao popularityRankWDao;

    @Autowired
    private PopularityRankMDao popularityRankMDao;

    @Autowired
    private ElitePostDao elitePostDao;

    @Autowired
    private HotPostDao hotPostDao;

    @Autowired
    private CommunityVideoDao communityVideoDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        ReturnT result = ReturnT.SUCCESS;

        // 9点至16点不处理
        if (LocalDateTime.now().getHour() > 8
                && LocalDateTime.now().getHour() < 16) {
            logger.info("高峰时间段，不处理");
            return result;
        }

        // 清理 PostInfo
        result = deletePostInfo(result);
        // 清理 PopularityRankW
        result = deletePopularityRankW(result);
        // 清理 PopularityRankM
        result = deletePopularityRankM(result);
        // 清理 ElitePost
        result = deleteElitePost(result);
        // 清理 HotPost
        result = deleteHotPost(result);
        // 清理 VideoPost
        result = deleteVideoPost(result);

        return result;

    }

    /**
     * 清理 PostInfo
     *
     * @param result
     * @return
     */
    private ReturnT deletePostInfo(ReturnT result) {

        try {
            int count = 0;

            logger.info("删除一年前的历史帖子开始");

            List<PostInfo> postList = postDao.getPostListBeforeOneYear();

            if (!CollectionUtils.isEmpty(postList)) {

                for (PostInfo postInfo : postList) {
                    boolean success = postDao.removeById(postInfo._id, BarMongodbConfig.TABLE_POST_INFO);
                    if (success) {
                        count++;
                        logger.info("已删除帖子：{}，更新时间：{}", postInfo._id, postInfo.UPDATETIME);
                    } else {
                        logger.error("删除帖子：{}失败", postInfo._id);
                    }
                }
            }

            logger.info("删除一年前的历史帖子结束，本次删除帖子数：{}", count);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }
        return result;
    }

    /**
     * 清理 PopularityRankW
     *
     * @param result
     * @return
     */
    private ReturnT deletePopularityRankW(ReturnT result) {

        try {
            int count = 0;

            logger.info("删除一年前的PopularityRankW数据开始");

            List<UserRankMModel> list = popularityRankWDao.getDataBeforeOneYear();

            if (!CollectionUtils.isEmpty(list)) {

                for (UserRankMModel item : list) {
                    boolean success = popularityRankWDao.removeById(item._id, BarMongodbConfig.TABLE_POPULARITYRANKW);
                    if (success) {
                        count++;
                        logger.info("已删除数据：{}，期数：{}", item._id, item.PID);
                    } else {
                        logger.error("删除数据：{}失败", item._id);
                    }
                }
            }

            logger.info("删除一年前的PopularityRankW数据结束，本次数量：{}", count);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }
        return result;
    }

    /**
     * 清理 PopularityRankM
     *
     * @param result
     * @return
     */
    private ReturnT deletePopularityRankM(ReturnT result) {

        try {
            int count = 0;

            logger.info("删除一年前的PopularityRankM数据开始");

            List<UserRankMModel> list = popularityRankMDao.getDataBeforeOneYear();

            if (!CollectionUtils.isEmpty(list)) {

                for (UserRankMModel item : list) {
                    boolean success = popularityRankMDao.removeById(item._id, BarMongodbConfig.TABLE_POPULARITYRANKM);
                    if (success) {
                        count++;
                        logger.info("已删除数据：{}，期数：{}", item._id, item.PID);
                    } else {
                        logger.error("删除数据：{}失败", item._id);
                    }
                }
            }

            logger.info("删除一年前的PopularityRankM数据结束，本次数量：{}", count);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }
        return result;
    }

    /**
     * 清理 ElitePost
     *
     * @param result
     * @return
     */
    private ReturnT deleteElitePost(ReturnT result) {

        try {
            int count = 0;

            logger.info("删除一年前的ElitePost数据开始");

            List<ElitePostModel> list = elitePostDao.getDataBeforeOneYear();

            if (!CollectionUtils.isEmpty(list)) {

                for (ElitePostModel item : list) {
                    boolean success = elitePostDao.removeById(item._id, BarMongodbConfig.TABLE_ELITEPOST);
                    if (success) {
                        count++;
                        logger.info("已删除数据：{}，更新时间：{}", item._id, item.UPDATETIME);
                    } else {
                        logger.error("删除数据：{} 失败", item._id);
                    }
                }
            }

            logger.info("删除一年前的ElitePost数据结束，本次数量：{}", count);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }
        return result;
    }

    /**
     * 清理 HotPost
     *
     * @param result
     * @return
     */
    private ReturnT deleteHotPost(ReturnT result) {

        try {
            int count = 0;

            logger.info("清理【HotPost】数据开始");

            // 一周前的数据
            List<HotPostModel> list = hotPostDao.getDataBeforeOneWeek();

            if (CollectionUtils.isEmpty(list) || list.size() < 1000) {
                // 一周内排名10000之后的数据
                list.addAll(hotPostDao.getLowScoreData());
            }

            // 移除
            count = removeHotPostData(list, count);

            logger.info("清理【HotPost】数据结束，本次数量：{}", count);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }
        return result;
    }

    /**
     * 移除 HotPost 表数据
     *
     * @param list
     * @param count
     * @return
     */
    private int removeHotPostData(List<HotPostModel> list, int count) {

        if (!CollectionUtils.isEmpty(list)) {
            for (HotPostModel item : list) {
                boolean success = hotPostDao.removeById(item._id, BarMongodbConfig.TABLE_HOTPOST);
                if (success) {
                    count++;
                    logger.info("已删除数据：{}，更新时间：{}", item._id, item.TIMEPOINT);
                } else {
                    logger.error("删除数据：{} 失败", item._id);
                }
            }
        }
        return count;
    }


    /**
     * 清理 VideoPost
     *
     * @param result
     * @return
     */
    private ReturnT deleteVideoPost(ReturnT result) {

        try {
            int count = 0;

            logger.info("清理【VideoPost】数据开始");

            List<CommunityVideo> list = communityVideoDao.getDataBeforeOneYear(CommunityVideo.class, 1000);
            if (!CollectionUtils.isEmpty(list)) {

                for (CommunityVideo a : list) {
                    boolean success = communityVideoDao.removeById(a.get_id(), BarMongodbConfig.TABLE_COMMUNITYVIDEO);
                    if (success) {
                        count++;
                        logger.info("已删除数据：{}，更新时间：{}", a.get_id(), DateUtil.dateToStr(a.getUpdateTime()));
                    } else {
                        logger.error("删除数据：{} 失败", a.get_id());
                    }
                }
            }

            logger.info("清理【VideoPost】数据结束，本次数量：{}", count);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }
        return result;
    }

}
