package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.BigVCalDataModel;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.BigVDataUserModel;
import ttfund.web.communityservice.dao.mongo.BigVCalDataNewDao;
import ttfund.web.communityservice.dao.mongo.CalVCountDataUserDao;
import ttfund.web.communityservice.dao.mongo.VUserSummaryPostDetailInfosDao;
import ttfund.web.communityservice.dao.msyql.*;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 大V相关数据计算保存MySql mongo     新版
 * 需求：#653737 【基金吧6.12】新增创作中心
 */
@JobHandler("CalBigVDataJobNewJob")
@Component
public class CalBigVDataJobNewJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(CalBigVDataJobNewJob.class);

    private ThreadPoolExecutor executor = new ThreadPoolExecutor(0, 16, 5, TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(50), new ThreadPoolExecutor.CallerRunsPolicy());

    private static List<String> MONGO_FIELDS = Arrays.asList("TotalClickNum", "TotalLikeNum", "TotalReceiveCommentNum",
            "TotalPostNum", "TotalSendCommentNum", "TotalFansNum", "TotalFollowNum",
            "ClickNum", "LikeNum", "ReceiveCommentNum", "PostNum", "SendCommentNum",
            "FansNum", "FollowNum",
            "FansAddNum", "FansOffNum", "FollowAddNum", "FollowOffNum"
    );

    @Autowired
    private CalVCountDataUserDao calVCountDataUserDao;

    @Autowired
    private CalBigVDataDao calBigVDataDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private ReplyInfoNewDao replyInfoNewDao;

    @Autowired
    private UserRelationDao userRelationDao;

    @Autowired
    private BigVCalDataNewDao bigVCalDataNewDao;

    @Autowired
    private VUserSummaryPostDetailInfosDao vUserSummaryPostDetailInfosDao;

    @Autowired
    private UserRelationCountDao userRelationCountDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            Integer batchReadCount = null;
            Integer batchSize = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                batchReadCount = jsonObject.getInteger("batchReadCount");
                batchSize = jsonObject.getInteger("batchSize");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            if (batchSize == null) {
                batchSize = 100;
            }

            logger.info("第零步，打印参数。batchReadCount：{}，batchSize：{}", batchReadCount, batchSize);

            deal(batchReadCount, batchSize);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount, int batchSize) {


        try {

            List<BigVDataUserModel> userList = new ArrayList<>();
            String uid = null;
            while (true) {
                List<BigVDataUserModel> tempList = calVCountDataUserDao.getByUid(uid, batchReadCount);
                if (!CollectionUtils.isEmpty(tempList)) {
                    userList.addAll(tempList);
                }

                if (!CollectionUtils.isEmpty(tempList)) {
                    uid = tempList.get(tempList.size() - 1).getUID();
                }

                if (tempList == null || tempList.size() < batchReadCount) {
                    break;
                }
            }

            logger.info("第一步，读取用户表。数量：{}，头部数据：{}",
                    userList == null ? 0 : userList.size(),
                    CollectionUtils.isEmpty(userList) ? null : JSON.toJSONString(userList.get(0))
            );


            if (!CollectionUtils.isEmpty(userList)) {

                Date calDate = DateUtil.strToDate(DateUtil.dateToStr(DateUtil.calendarDateByDays(-1), "yyyy-MM-dd"), "yyyy-MM-dd");

                List<String> uidList = userList.stream().map(BigVDataUserModel::getUID).distinct().collect(Collectors.toList());
                List<List<String>> batchList = CommonUtils.toSmallList2(uidList, batchSize);

                logger.info("第二步，对用户分批。数量：{}", batchList == null ? 0 : batchList.size());

                CountDownLatch countDownLatch = new CountDownLatch(batchList.size());

                int i = 0;
                for (List<String> batch : batchList) {
                    i++;
                    final int finalI = i;

                    executor.execute(() -> {
                        dealByUids(batch, calDate, finalI, batchList.size(), countDownLatch);
                    });
                }

                countDownLatch.await();

                logger.info("第四步，计算完成。总批量：{}", batchList == null ? 0 : batchList.size());

                bigVCalDataNewDao.delExpiredData();

                logger.info("第五步，删除历史数据。");
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);

        }

    }


    private void dealByUids(List<String> batch, Date calDate, int i, int size, CountDownLatch countDownLatch) {

        StringBuilder logBuilder = new StringBuilder();
        logBuilder.append("\n");

        try {

            //获取前一天大V数据
            List<Map<String, Object>> lastDayResult = bigVCalDataNewDao.getLastDayBigVDataByUids(batch, calDate);

            //获取用户最新收到阅读总数、收到点赞总数、收到评论总数、帖子总数
            List<Map<String, Object>> totalPostThingCountList = postInfoNewDao.getPostThingCountByUids(batch);

            //获取用户最新发送评论总数
            List<Map<String, Object>> totalReplyCountList = replyInfoNewDao.getTotalReplyCountByUids(batch);

/*            //获取用户最新粉丝总数
            List<Map<String, Object>> totalFansCountList = userRelationDao.getTotalFansCountByUids(batch);

            //获取用户最新关注总数
            List<Map<String, Object>> totalFollowCountList = userRelationDao.getTotalFollowCountByUids(batch);*/

            //获取用户最新关注总数、粉丝总数
            List<Map<String, Object>> relationCountList = userRelationCountDao.getByUids(batch);

            //获取用户昨日发送评论数
            List<Map<String, Object>> lastDaySendReplyCountList = replyInfoNewDao.getPeriodReplyCountByUids(batch, calDate, DateUtil.calendarDateByDays(calDate, 1));

            //获取用户昨日发送帖子数
            List<Map<String, Object>> lastDaySendPostCountList = postInfoNewDao.getPeriodPostCountByUids(batch, calDate, DateUtil.calendarDateByDays(calDate, 1));

            //获取用户昨日新增收到关注数
            List<Map<String, Object>> lastDayBeFollowedCountList = userRelationDao.getPeriodBeFollowedCountByUids(batch, calDate, DateUtil.calendarDateByDays(calDate, 1));

            //获取用户昨日新增收到取关数
            List<Map<String, Object>> lastDayBeUnFollowedCountList = userRelationDao.getPeriodBeUnFollowedCountByUids(batch, calDate, DateUtil.calendarDateByDays(calDate, 1));

            //获取用户昨日新增发起关注数
            List<Map<String, Object>> lastDayFollowCountList = userRelationDao.getPeriodFollowCountByUids(batch, calDate, DateUtil.calendarDateByDays(calDate, 1));

            //获取用户昨日新增取消取关数
            List<Map<String, Object>> lastDayUnFollowCountList = userRelationDao.getPeriodUnFollowCountByUids(batch, calDate, DateUtil.calendarDateByDays(calDate, 1));

            Map<String, Map<String, Object>> allUserMap = new HashMap<>();
            Map<String, Object> tempMap = null;

            for (String a : batch) {
                tempMap = new HashMap<>();
                tempMap.put("UID", a);
                allUserMap.put(a, tempMap);
            }

            int totalClickNum = 0;
            int totalLikeNum = 0;
            int totalReceiveCommentNum = 0;
            int totalPostNum = 0;
            int totalSendCommentNum = 0;
            int totalFansNum = 0;
            int totalFollowNum = 0;
            int sendCommentNum = 0;
            int postNum = 0;
            int fansNum = 0;
            int fansAddNum = 0;
            int fansOffNum = 0;
            int followNum = 0;
            int followAddNum = 0;
            int followOffNum = 0;
            int clickNum = 0;
            int likeNum = 0;
            int receiveCommentNum = 0;

            if (!CollectionUtils.isEmpty(totalPostThingCountList)) {
                for (Map<String, Object> a : totalPostThingCountList) {
                    tempMap = allUserMap.get(a.get("UID"));

                    totalClickNum = a.get("TotalClickNum") == null ? 0 : Integer.parseInt(a.get("TotalClickNum").toString());
                    totalLikeNum = a.get("TotalLikeNum") == null ? 0 : Integer.parseInt(a.get("TotalLikeNum").toString());
                    totalReceiveCommentNum = a.get("TotalReceiveCommentNum") == null ? 0 : Integer.parseInt(a.get("TotalReceiveCommentNum").toString());
                    totalPostNum = a.get("TotalPostNum") == null ? 0 : Integer.parseInt(a.get("TotalPostNum").toString());

                    tempMap.put("TotalClickNum", totalClickNum);
                    tempMap.put("TotalLikeNum", totalLikeNum);
                    tempMap.put("TotalReceiveCommentNum", totalReceiveCommentNum);
                    tempMap.put("TotalPostNum", totalPostNum);

                }
            }

            if (!CollectionUtils.isEmpty(totalReplyCountList)) {
                for (Map<String, Object> a : totalReplyCountList) {
                    tempMap = allUserMap.get(a.get("UID"));

                    totalSendCommentNum = a.get("TotalSendCommentNum") == null ? 0 : Integer.parseInt(a.get("TotalSendCommentNum").toString());

                    tempMap.put("TotalSendCommentNum", totalSendCommentNum);
                }
            }

            if (!CollectionUtils.isEmpty(relationCountList)) {
                for (Map<String, Object> a : relationCountList) {
                    tempMap = allUserMap.get(a.get("uid"));

                    totalFansNum = a.get("TotalFansNum") == null ? 0 : Integer.parseInt(a.get("TotalFansNum").toString());

                    tempMap.put("TotalFansNum", totalFansNum);

                }
            }

            if (!CollectionUtils.isEmpty(relationCountList)) {
                for (Map<String, Object> a : relationCountList) {
                    tempMap = allUserMap.get(a.get("uid"));

                    totalFollowNum = a.get("TotalFollowNum") == null ? 0 : Integer.parseInt(a.get("TotalFollowNum").toString());

                    tempMap.put("TotalFollowNum", totalFollowNum);

                }
            }

            if (!CollectionUtils.isEmpty(lastDaySendReplyCountList)) {
                for (Map<String, Object> a : lastDaySendReplyCountList) {
                    tempMap = allUserMap.get(a.get("UID"));

                    sendCommentNum = a.get("SendCommentNum") == null ? 0 : Integer.parseInt(a.get("SendCommentNum").toString());

                    tempMap.put("SendCommentNum", sendCommentNum);
                }
            }


            if (!CollectionUtils.isEmpty(lastDaySendPostCountList)) {
                for (Map<String, Object> a : lastDaySendPostCountList) {
                    tempMap = allUserMap.get(a.get("UID"));

                    postNum = a.get("PostNum") == null ? 0 : Integer.parseInt(a.get("PostNum").toString());

                    tempMap.put("PostNum", postNum);
                }
            }


            if (!CollectionUtils.isEmpty(lastDayBeFollowedCountList)) {
                for (Map<String, Object> a : lastDayBeFollowedCountList) {
                    tempMap = allUserMap.get(a.get("OBJID"));

                    fansAddNum = a.get("FansAddNum") == null ? 0 : Integer.parseInt(a.get("FansAddNum").toString());

                    tempMap.put("FansAddNum", fansAddNum);
                }
            }

            if (!CollectionUtils.isEmpty(lastDayBeUnFollowedCountList)) {
                for (Map<String, Object> a : lastDayBeUnFollowedCountList) {
                    tempMap = allUserMap.get(a.get("OBJID"));

                    fansOffNum = a.get("FansOffNum") == null ? 0 : Integer.parseInt(a.get("FansOffNum").toString());

                    tempMap.put("FansOffNum", fansOffNum);
                }
            }

            if (!CollectionUtils.isEmpty(lastDayFollowCountList)) {
                for (Map<String, Object> a : lastDayFollowCountList) {
                    tempMap = allUserMap.get(a.get("USERID"));

                    followAddNum = a.get("FollowAddNum") == null ? 0 : Integer.parseInt(a.get("FollowAddNum").toString());

                    tempMap.put("FollowAddNum", followAddNum);
                }
            }

            if (!CollectionUtils.isEmpty(lastDayUnFollowCountList)) {
                for (Map<String, Object> a : lastDayUnFollowCountList) {
                    tempMap = allUserMap.get(a.get("USERID"));

                    followOffNum = a.get("FollowOffNum") == null ? 0 : Integer.parseInt(a.get("FollowOffNum").toString());

                    tempMap.put("FollowOffNum", followOffNum);
                }
            }

            int temp1 = 0;
            int temp2 = 0;
            if (!CollectionUtils.isEmpty(lastDayResult)) {
                for (Map<String, Object> a : lastDayResult) {
                    tempMap = allUserMap.get(a.get("UID"));

                    temp1 = tempMap.get("TotalClickNum") == null ? 0 : Integer.parseInt(tempMap.get("TotalClickNum").toString());
                    temp2 = a.get("TotalClickNum") == null ? 0 : Integer.parseInt(a.get("TotalClickNum").toString());
                    clickNum = temp1 - temp2;
                    tempMap.put("ClickNum", clickNum);

                    temp1 = tempMap.get("TotalLikeNum") == null ? 0 : Integer.parseInt(tempMap.get("TotalLikeNum").toString());
                    temp2 = a.get("TotalLikeNum") == null ? 0 : Integer.parseInt(a.get("TotalLikeNum").toString());
                    likeNum = temp1 - temp2;
                    tempMap.put("LikeNum", likeNum);

                    temp1 = tempMap.get("TotalReceiveCommentNum") == null ? 0 : Integer.parseInt(tempMap.get("TotalReceiveCommentNum").toString());
                    temp2 = a.get("TotalReceiveCommentNum") == null ? 0 : Integer.parseInt(a.get("TotalReceiveCommentNum").toString());
                    receiveCommentNum = temp1 - temp2;
                    tempMap.put("ReceiveCommentNum", receiveCommentNum);

                    temp1 = tempMap.get("TotalFansNum") == null ? 0 : Integer.parseInt(tempMap.get("TotalFansNum").toString());
                    temp2 = a.get("TotalFansNum") == null ? 0 : Integer.parseInt(a.get("TotalFansNum").toString());
                    fansNum = temp1 - temp2;
                    tempMap.put("FansNum", fansNum);

                    temp1 = tempMap.get("TotalFollowNum") == null ? 0 : Integer.parseInt(tempMap.get("TotalFollowNum").toString());
                    temp2 = a.get("TotalFollowNum") == null ? 0 : Integer.parseInt(a.get("TotalFollowNum").toString());
                    followNum = temp1 - temp2;
                    tempMap.put("FollowNum", followNum);

                }
            }

            logBuilder.append(
                    String.format("1.读取用户指标数据详情。数量：%s，头部数据：%s\n",
                            allUserMap == null ? 0 : allUserMap.size(),
                            CollectionUtils.isEmpty(allUserMap) ? null : allUserMap.get(batch.get(0))
                    )
            );

            List<BigVCalDataModel> modelList = new ArrayList<>();
            BigVCalDataModel model = null;

            for (String a : batch) {
                tempMap = allUserMap.get(a);
                model = new BigVCalDataModel();
                model.setUID(a);
                model.setCDate(calDate);
                model.setTotalClickNum((Integer) tempMap.getOrDefault("TotalClickNum", 0));
                model.setClickNum((Integer) tempMap.getOrDefault("ClickNum", 0));
                model.setTotalLikeNum((Integer) tempMap.getOrDefault("TotalLikeNum", 0));
                model.setLikeNum((Integer) tempMap.getOrDefault("LikeNum", 0));
                model.setTotalSendCommentNum((Integer) tempMap.getOrDefault("TotalSendCommentNum", 0));
                model.setSendCommentNum((Integer) tempMap.getOrDefault("SendCommentNum", 0));
                model.setTotalReceiveCommentNum((Integer) tempMap.getOrDefault("TotalReceiveCommentNum", 0));
                model.setReceiveCommentNum((Integer) tempMap.getOrDefault("ReceiveCommentNum", 0));
                model.setTotalPostNum((Integer) tempMap.getOrDefault("TotalPostNum", 0));
                model.setPostNum((Integer) tempMap.getOrDefault("PostNum", 0));
                model.setTotalFansNum((Integer) tempMap.getOrDefault("TotalFansNum", 0));
                model.setFansAddNum((Integer) tempMap.getOrDefault("FansAddNum", 0));
                model.setFansOffNum((Integer) tempMap.getOrDefault("FansOffNum", 0));
                model.setCreateTime(new Date());
                model.setUpdateTime(new Date());
                model.setIsDel(0);

                modelList.add(model);

            }


            logBuilder.append(
                    String.format("2.构建实体。数量：%s，头部数据：%s\n",
                            modelList == null ? 0 : modelList.size(),
                            CollectionUtils.isEmpty(modelList) ? null : JSON.toJSONString(modelList.get(0))
                    )
            );

            if (!CollectionUtils.isEmpty(modelList)) {
                calBigVDataDao.saveMany(modelList);

                logBuilder.append(
                        String.format("3.写mysql。\n")
                );

            }

            if (!CollectionUtils.isEmpty(allUserMap)) {
                String dateStr = DateUtil.dateToStr(calDate, DateHelper.FORMAT_YYYY_MM_DD);

                List<Map<String, Object>> mapList = new ArrayList<>(allUserMap.size());
                Map<String, Object> map = null;

                for (Map.Entry<String, Map<String, Object>> entry : allUserMap.entrySet()) {
                    tempMap = entry.getValue();

                    map = new HashMap<>();
                    map.put("_id", entry.getKey() + "_" + dateStr);
                    map.put("UID", entry.getKey());
                    map.put("CDate", calDate);
                    map.put("CreateTime", new Date());
                    map.put("UpdateTime", new Date());

                    for (String mongoField : MONGO_FIELDS) {
                        map.put(mongoField, (Integer) tempMap.getOrDefault(mongoField, 0));
                    }

                    mapList.add(map);
                }

                bigVCalDataNewDao.upsertManyBySetWithSetOnInsertFields(mapList, Arrays.asList("CreateTime"), "_id");

                logBuilder.append(
                        String.format("4.写mongo用户每日结果表。\n")
                );
            }

            if (!CollectionUtils.isEmpty(modelList)) {
                List<Map<String, Object>> mapList = new ArrayList<>(modelList.size());
                Map<String, Object> map = null;
                for (BigVCalDataModel a : modelList) {
                    map = new HashMap<>();
                    map.put("UserId", a.getUID());
                    map.put("ClickNums", a.getTotalClickNum());
                    map.put("LikeNums", a.getTotalLikeNum());
                    map.put("CommentNums", a.getTotalReceiveCommentNum());
                    map.put("PostNums", a.getPostNum());
                    map.put("FansNums", a.getTotalFansNum());
                    map.put("CreateTime", new Date());
                    map.put("UpdateTime", new Date());

                    mapList.add(map);
                }

                vUserSummaryPostDetailInfosDao.upsertManyBySetWithSetOnInsertFields(mapList, Arrays.asList("CreateTime"), "UserId");

                logBuilder.append(
                        String.format("5.写mongo用户最新结果表。\n")
                );

            }

            logger.info("第三步，处理数据详情。第{}/{}批。数量：{}，头部数据：{}，详情：{}",
                    i,
                    size,
                    batch == null ? 0 : batch.size(),
                    CollectionUtils.isEmpty(batch) ? null : batch.get(0),
                    logBuilder.toString()
            );

        } catch (Exception ex) {

            logger.error("第三步，处理数据详情出错。第{}/{}批。数量：{}，头部数据：{}，详情：{}",
                    i,
                    size,
                    batch == null ? 0 : batch.size(),
                    CollectionUtils.isEmpty(batch) ? null : batch.get(0),
                    logBuilder.toString()
            );

            logger.error(ex.getMessage(), ex);

        } finally {
            countDownLatch.countDown();
        }
    }

}
