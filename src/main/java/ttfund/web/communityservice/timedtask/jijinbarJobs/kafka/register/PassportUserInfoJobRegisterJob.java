package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.register;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer.PassportUserInfoJob;

import javax.annotation.Resource;
import java.time.LocalTime;

/**
 * 用户通行证信息（kafka消费）启停控制
 */
@JobHandler(value = "PassportUserInfoJobRegisterJob")
@Component
public class PassportUserInfoJobRegisterJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(PassportUserInfoJobRegisterJob.class);

    @Resource
    private KafkaListenerEndpointRegistry registry;

    /**
     * kafka消费启停控制逻辑
     */
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            Boolean run = null;
            Boolean tradeTimeRun = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                run = jsonObject.getBoolean("run");
                tradeTimeRun = jsonObject.getBoolean("tradeTimeRun");
            }

            if (run == null) {
                run = true;
            }
            if (tradeTimeRun == null) {
                tradeTimeRun = false;
            }

            logger.info("第零步，打印参数。run：{}，tradeTimeRun：{}", run, tradeTimeRun);


            boolean shouldRun = run;
            if (run && !tradeTimeRun) {
                if ((LocalTime.now().compareTo(LocalTime.of(12, 50)) > 0 &&
                        LocalTime.now().compareTo(LocalTime.of(15, 10)) < 0)
                        ||
                        (LocalTime.now().compareTo(LocalTime.of(9, 20)) > 0 &&
                                LocalTime.now().compareTo(LocalTime.of(11, 30)) < 0)
                ) {
                    shouldRun = false;
                }
            }


            MessageListenerContainer listenerContainer = registry.getListenerContainer(PassportUserInfoJob.KAFKA_LISTENER_ID);
            if (listenerContainer != null) {
                boolean running = listenerContainer.isRunning();
                if (shouldRun && !running) {
                    listenerContainer.start();
                } else if (!shouldRun && running) {
                    listenerContainer.stop();
                }
            }

            logger.info("第一步，kafka消费启停控制。shouldRun：{}", shouldRun);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }


        return ReturnT.SUCCESS;
    }


}
