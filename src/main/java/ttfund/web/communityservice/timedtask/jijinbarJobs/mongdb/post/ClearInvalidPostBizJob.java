package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.*;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 清理无效帖子
 */
@JobHandler("ClearInvalidPostBizJob")
@Component
public class ClearInvalidPostBizJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(ClearInvalidPostBizJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private PostFindScoreDao postFindScoreDao;

    @Autowired
    private ElitePostDao elitePostDao;

    @Autowired
    private HotPostDao hotPostDao;

    @Autowired
    private FundUserProfitDao fundUserProfitDao;

    @Autowired
    private FundManagerPostDao fundManagerPostDao;

    @Autowired
    private FindRecommendPostDao recommendPostDao;

    @Autowired
    private PostRankNewDao postRankNewDao;

    @Autowired
    private SubAccountDtoDao subAccountDtoDao;

    @Autowired
    private QualityPostTopicRelationDao qualityPostTopicRelationDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint1 = null;
            String initBreakpoint2 = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint1 = jsonObject.getString("initBreakpoint1");
                initBreakpoint2 = jsonObject.getString("initBreakpoint2");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint1：{}，initBreakpoint2：{}，batchReadCount：{}",
                    initBreakpoint1,
                    initBreakpoint2,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint1) || StringUtils.hasLength(initBreakpoint2)) {

                if (StringUtils.hasLength(initBreakpoint1)) {
                    userRedisDao.set(UserRedisConfig.CLEARINVALIDPOSTBIZJOB_SYNCCLEARINVALIDPOST_BREAKPOINT, initBreakpoint1, 30 * 24 * 3600L);
                    logger.info("第零步，初始化断点。initBreakpoint1：{}", initBreakpoint1);
                }

                if (StringUtils.hasLength(initBreakpoint2)) {
                    userRedisDao.set(UserRedisConfig.CLEARINVALIDPOSTBIZJOB_SYNCCLEARINVALIDSUBACCOUNTDTO_BREAKPOINT, initBreakpoint2, 30 * 24 * 3600L);
                    logger.info("第零步，初始化断点。initBreakpoint2：{}", initBreakpoint2);
                }

                return ReturnT.SUCCESS;
            }

            syncClearInvalidPost(batchReadCount);
            syncClearInvalidSubAccountDto(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 定时清理无效的帖子
     */
    private void syncClearInvalidPost(int batchReadCount) {
        try {
            String breakpointName = UserRedisConfig.CLEARINVALIDPOSTBIZJOB_SYNCCLEARINVALIDPOST_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("syncClearInvalidPost-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("syncClearInvalidPost-第一步，读取断点。断点:{}", breakpoint);


            List<PostInfoNewModel> postList = postInfoNewDao.getTTJJDELPostInfoNewList(breakpointDate, batchReadCount, false);

            logger.info("syncClearInvalidPost-第二步，读取数据。数量:{}，头部id列表：{}",
                    postList == null ? 0 : postList.size(),
                    postList == null ? null : postList.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

            Date maxUpdateTime = breakpointDate;
            if (!CollectionUtils.isEmpty(postList)) {

                maxUpdateTime = postList.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;

                List<String> ids = postList.stream().map(a -> String.valueOf(a.ID)).collect(Collectors.toList());
                List<List<String>> batchList = CommonUtils.toSmallList2(ids, 20);
                for (List<String> batch : batchList) {
                    postFindScoreDao.removeByIds(batch, BarMongodbConfig.TABLE_POSTFINDSCORE);
                    elitePostDao.removeByIds(batch, BarMongodbConfig.TABLE_ELITEPOST);//精化帖
                    hotPostDao.removeByIds(batch, BarMongodbConfig.TABLE_HOTPOST);
                    fundUserProfitDao.deleteByPostIds(batch.stream().map(a -> Integer.valueOf(a)).collect(Collectors.toList()));//持有人帖
                    fundManagerPostDao.removeByIds(batch, BarMongodbConfig.TABLE_FUNDMANAGERPOST);//基金经理帖
                    recommendPostDao.removeByIds(batch, BarMongodbConfig.TABLE_RECOMMENDPOST);//发现页B侧
                    postRankNewDao.removeByIds(batch, BarMongodbConfig.TABLE_POSTRANKNEW);//智能排序支持一贴多发
                    qualityPostTopicRelationDao.removeByPostIds(batch);// 优质帖录入话题
                }

                logger.info("syncClearInvalidPost-第三步，数据写库。数量:{}，头部id列表：{}",
                        postList == null ? 0 : postList.size(),
                        postList == null ? null : postList.stream().map(a -> a.ID).limit(20).collect(Collectors.toList())
                );

                if (postList.size() < batchReadCount) {
                    //如果不满足最大数量，把时间往前推一分钟
                    maxUpdateTime = DateUtil.calendarDateByMinute(maxUpdateTime, -1);
                }

                breakpointDate = maxUpdateTime;
                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("syncClearInvalidPost-第四步，更新断点。断点：{}", breakpoint);

            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }

    /**
     * 清除不可用子账户帖子
     */
    private void syncClearInvalidSubAccountDto(int batchReadCount) {
        try {
            String breakpointName = UserRedisConfig.CLEARINVALIDPOSTBIZJOB_SYNCCLEARINVALIDSUBACCOUNTDTO_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("syncClearInvalidSubAccountDto-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("syncClearInvalidSubAccountDto-第一步，读取断点。断点:{}", breakpoint);


            List<PostInfoNewModel> postList = postInfoNewDao.getTTJJDELPostInfoNewList(breakpointDate, batchReadCount, false);

            logger.info("syncClearInvalidSubAccountDto-第二步，读取数据。数量:{}，头部id列表：{}",
                    postList == null ? 0 : postList.size(),
                    postList == null ? null : postList.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

            Date maxUpdateTime = breakpointDate;
            if (!CollectionUtils.isEmpty(postList)) {

                maxUpdateTime = postList.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;

                List<Map<String, Object>> mapList = new ArrayList<>();
                Map<String, Object> map = new HashMap<>();
                for (PostInfoNewModel item : postList) {
                    //如果是子账户帖子，且是删除的帖子
                    if (item.CODE.startsWith("43-") && (item.DEL == 1 || item.TTJJDEL == 1 || item.ISENABLED == 0)) {
                        //先判断子账户中是否有该帖子，如果存在则删除
                        map = new HashMap<>();
                        map.put("_id", "POST_" + item.ID);
                        map.put("DEL", item.DEL);
                        map.put("TTJJDEL", item.TTJJDEL);
                        map.put("ISENABLED", item.ISENABLED);
                        mapList.add(map);
                    }
                }

                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 50);
                for (List<Map<String, Object>> batch : batchList) {
                    subAccountDtoDao.updateMany(batch);
                }

                logger.info("syncClearInvalidSubAccountDto-第三步，数据写库。数量:{}，头部id列表：{}",
                        postList == null ? 0 : postList.size(),
                        postList == null ? null : postList.stream().map(a -> a.ID).limit(20).collect(Collectors.toList())
                );

                //如果不满足最大数量，把时间往前推一分钟
                maxUpdateTime = DateUtil.calendarDateByMinute(maxUpdateTime, -1);
                breakpointDate = maxUpdateTime;
                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("syncClearInvalidSubAccountDto-第四步，更新断点。断点：{}", breakpoint);

            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }
}
