package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoNewModel;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 帖子子评论数量统计-增量
 */
@Slf4j
@JobHandler("PostSubReplyCountJob")
@Component
public class PostSubReplyCountJob extends IJobHandler {

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private ReplyInfoNewDao replyInfoNewDao;

    @Autowired
    private PostInfoExtraDao postInfoExtraDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            log.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.POSTSUBREPLYCOUNTJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            logicDeal(batchReadCount);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void logicDeal(int batchReadCount) {

        String breakpointName = UserRedisConfig.POSTSUBREPLYCOUNTJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            log.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("第一步，读取断点。断点:{}", breakpoint);

        /*获取基金吧帖子评论数据
         */
        List<ReplyInfoNewModel> list = replyInfoNewDao.getList("*", breakpointDate, batchReadCount);

        log.info("第二步，读取数据。数量:{}，头部列表：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = list.get(list.size() - 1).UPDATETIME;

            List<Integer> postIds = list.stream().map(a -> a.TOPICID).distinct().collect(Collectors.toList());

            log.info("第三步，提取帖子id。数量:{}，头部id列表：{}",
                    CollectionUtils.isEmpty(postIds) ? 0 : postIds.size(),
                    CollectionUtils.isEmpty(postIds) ? null : postIds.get(0)
            );

            if (!CollectionUtils.isEmpty(postIds)) {
                for (Integer a : postIds) {
                    Integer subCount = replyInfoNewDao.getSubReplyCount(String.valueOf(a));
                    if (subCount != null && subCount > 0) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("ID", a);
                        map.put("SUBPINGLUNNUM", subCount);
                        postInfoExtraDao.insertOrUpdateSubReplyCount(map);
                    }
                }
            }

            log.info("第四步，写库。数量:{}，头部id列表：{}",
                    CollectionUtils.isEmpty(postIds) ? 0 : postIds.size(),
                    CollectionUtils.isEmpty(postIds) ? null : postIds.get(0)
            );

        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        log.info("第五步，更新断点。断点：{}", breakpoint);
    }

}
