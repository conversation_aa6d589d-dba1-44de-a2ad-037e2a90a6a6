package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.config.CfbRewardValueModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.CfbRewardConfigDao;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基金吧问答奖励cfb配置
 */
@JobHandler(value = "rewardCfbConfigBusinessJob")
@Component
public class RewardCfbConfigBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(RewardCfbConfigBusinessJob.class);

    @Autowired
    private CfbRewardConfigDao cfbRewardConfigDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String param) {

        try {

            setToCache();

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void setToCache() {
        try {
            List<CfbRewardValueModel> list = cfbRewardConfigDao.getConfigValues();

            if (!CollectionUtils.isEmpty(list)) {

                //老版悬赏
                CfbRewardValueModel obj = list.stream()
                        .filter(a -> a.State == 0 && a.Type == 0)
                        .findFirst()
                        .orElse(null);

                if (obj != null) {
                    List<String> arr = Arrays.asList(obj.getRewardValuesArr());
                    Boolean resultFlag = app.barredis.set(BarRedisKey.REWARD_CFB_CONFIG, JacksonUtil.obj2String(arr));

                    if (resultFlag) {
                        logger.info("缓存{}插入【成功】数量为:{}", BarRedisKey.REWARD_CFB_CONFIG, arr.size());
                    } else {
                        logger.error("缓存{}插入【失败】数量为:{}", BarRedisKey.REWARD_CFB_CONFIG, arr.size());
                    }
                }

                //悬赏，打赏 【新版】
                List<CfbRewardValueModel> tempList = list.stream()
                        .filter(a -> a.Type > 0 && a.State >= 0)
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(tempList)) {
                    Boolean resultFlag = app.barredis.set(BarRedisKey.REWARD_DS_CFB_CONFIG, JacksonUtil.obj2String(tempList));

                    if (resultFlag) {
                        logger.info("缓存{}插入【成功】数量为:{}", BarRedisKey.REWARD_DS_CFB_CONFIG, tempList.size());
                    } else {
                        logger.error("缓存{}插入【失败】数量为:{}", BarRedisKey.REWARD_DS_CFB_CONFIG, tempList.size());
                    }
                } else {
                    logger.info("数据库中无数据，缓存{}删除", BarRedisKey.REWARD_DS_CFB_CONFIG);

                    app.barredis.del(BarRedisKey.REWARD_DS_CFB_CONFIG);
                }

            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }
}
