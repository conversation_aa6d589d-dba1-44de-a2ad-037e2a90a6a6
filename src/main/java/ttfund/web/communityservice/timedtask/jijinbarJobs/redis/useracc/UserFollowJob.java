package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.useracc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.user.FollowInfo;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.UserAccRedisKey;
import ttfund.web.communityservice.dao.mongo.PassportUserFollowNewDao;
import ttfund.web.communityservice.dao.msyql.UserRelationDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;

/**
 * 用户关注和粉丝列表
 */
@JobHandler(value = "userFollowJob")
@Component
public class UserFollowJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(UserFollowJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private UserRelationDao userRelationDao;

    @Autowired
    private PassportUserFollowNewDao passportUserFollowNewDao;

    @Override
    public ReturnT<String> execute(String s) {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.USERFOLLOWJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            logicDeal(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void logicDeal(int batchReadCount) {
        try {


            String breakpointName = UserRedisConfig.USERFOLLOWJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("第一步，读取断点。断点:{}", breakpoint);

            int round = 0;
            while (true) {
                round++;

                //这里向前推9秒钟
                List<Map<String, Object>> followsList = userRelationDao.getUserRelationList(breakpointDate, batchReadCount);


                logger.info("第二步，读取数据-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        CollectionUtils.isEmpty(followsList) ? 0 : followsList.size(),
                        CollectionUtils.isEmpty(followsList) ? null : JSON.toJSONStringWithDateFormat(followsList.get(0), DateUtil.datePattern));

                if (CollectionUtils.isEmpty(followsList)) {
                    break;
                }

                int i = 0;
                for (Map<String, Object> item : followsList) {
                    i++;

                    breakpointDate = (Date) followsList.get(followsList.size() - 1).get("UPDATETIME");

                    try {
                        //用户关注信息
                        List<String> followInfoList = userRelationDao.getUserFollowListByUserId(item.get("USERID").toString());
                        //被关注用户的关注信息
                        List<String> targetFollowInfoList = userRelationDao.getUserFollowListByUserId(item.get("OBJID").toString());
                        //关注用户的关注信息
                        Map<String, Object> followInfo = initUserFollowListNew(item.get("USERID").toString(), followInfoList);
                        //被关注用户的关注信息
                        Map<String, Object> targetFollowInfo = initUserFollowListNew(item.get("OBJID").toString(), targetFollowInfoList);

                        //用户关注保存到mongdb
                        boolean followUserResult = syncFollowToMongdb(followInfo);
                        if (!followUserResult) {
                            logger.error("followInfo同步到Mongdb失败。第{}轮，第{}/{}个，USERID：{}，OBJID：{}",
                                    round,
                                    i,
                                    followsList.size(),
                                    item.get("USERID").toString(),
                                    item.get("OBJID").toString()
                            );

                        } else {
                            //被关注用户数据同步保存到Mongdb
                            boolean targerSyncResult = syncFollowToMongdb(targetFollowInfo);
                            if (!targerSyncResult) {
                                logger.error("targetFollowInfo同步到Mongdb失败。第{}轮，第{}/{}个，USERID：{}，OBJID：{}",
                                        round,
                                        i,
                                        followsList.size(),
                                        item.get("USERID").toString(),
                                        item.get("OBJID").toString()
                                );
                            }
                        }

                        // redis操作
                        syncToCache(item, followInfo, targetFollowInfo);

                        logger.info("第三步，处理详情-第{}轮。第{}/{}个，USERID：{}，OBJID：{}，followInfo：{}，targetFollowInfo：{}",
                                round,
                                i,
                                followsList.size(),
                                item.get("USERID").toString(),
                                item.get("OBJID").toString(),
                                followInfo.get("Count"),
                                targetFollowInfo.get("Count")
                        );

                    } catch (Exception ex) {

                        logger.error(ex.getMessage(), ex);

                        logger.error("第三步，处理详情出错-第{}轮。第{}/{}个，USERID：{}，OBJID：{}",
                                round,
                                i,
                                followsList.size(),
                                item.get("USERID").toString(),
                                item.get("OBJID").toString()
                        );
                    }
                }

                logger.info("第三步，处理完成-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        CollectionUtils.isEmpty(followsList) ? 0 : followsList.size(),
                        CollectionUtils.isEmpty(followsList) ? null : JSON.toJSONStringWithDateFormat(followsList.get(0), DateUtil.datePattern));

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("第四步，更新断点-第{}轮。断点：{}", round, breakpoint);

                if (followsList == null || followsList.size() < batchReadCount) {
                    break;
                }
            }

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 同步数据到缓存
     */
    private void syncToCache(Map<String, Object> item, Map<String, Object> followInfo,
                             Map<String, Object> targetFollowInfo) {

        if (item == null) {
            return;
        }

        try {

            //用户关注列表
            String userFollowCache = UserAccRedisKey.FUND_GUBA_SERVICE_USERFOLLOWLISTNEW_PASSPORT + item.get("USERID").toString();
            //用户关注数
            String userFollowCountCache = UserAccRedisKey.FUND_GUBA_SERVICE_USERFOLLOW_FOLLOWNEW_PASSPORT + item.get("USERID").toString();
            //被关注用户的关注列表
            String targerUserFollowCache = UserAccRedisKey.FUND_GUBA_SERVICE_USERFOLLOWLISTNEW_PASSPORT + item.get("OBJID").toString();

            long expire = 3 * 24 * 3600L;

            //关注用户
            if (followInfo != null) {
                //用户关注缓存数
                userRedisDao.set(userFollowCountCache, followInfo.get("Count").toString(), expire);
                //用户关注列表
                userRedisDao.set(userFollowCache, JSON.toJSONString(followInfo.get("List")), expire);
            }

            //被关注用户
            if (targetFollowInfo != null) {
                //用户关注列表
                userRedisDao.set(targerUserFollowCache, JSON.toJSONString(targetFollowInfo.get("List")), expire);
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 同步到mongdb
     */
    private boolean syncFollowToMongdb(Map<String, Object> followInfo) {

        try {
            passportUserFollowNewDao.save(followInfo);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return false;
        }
        return true;
    }

    /**
     * 初始化用户关注信息
     *
     * @param userId     用户ID
     * @param followList 关注列表
     */
    public Map<String, Object> initUserFollowListNew(String userId, List<String> followList) {

        if (!StringUtils.isEmpty(userId)) {

            Map<String, Object> followInfo = new HashMap<>();
            followInfo.put("_id", userId);
            followInfo.put("List", new ArrayList<>());
            followInfo.put("Count", 0);
            followInfo.put("UpdateTime", new Date());

            if (!CollectionUtils.isEmpty(followList)) {

                //被关注用户的用户ID
                List<Map<String, Object>> followUserRetaion = userRelationDao.getUserFollowRelation(followList, userId);

                Set<String> set = new HashSet<>();
                if (!CollectionUtils.isEmpty(followUserRetaion)) {
                    followUserRetaion.forEach(a -> set.add(a.get("USERID").toString()));
                }

                List<FollowInfo> list = new ArrayList<>();

                if (!CollectionUtils.isEmpty(followUserRetaion)) {

                    for (String item : followList) {
                        FollowInfo follow = new FollowInfo();
                        follow.PId = item;
                        follow.IsMutualFollow = set.contains(item);
                        list.add(follow);
                    }
                } else {
                    for (String item : followList) {
                        FollowInfo follow = new FollowInfo();
                        follow.PId = item;
                        follow.IsMutualFollow = false;
                        list.add(follow);
                    }
                }

                //用户关注列表
                followInfo.put("List", list);
                //用户关注数量
                followInfo.put("Count", list.size());
            }
            return followInfo;
        }
        return null;
    }
}
