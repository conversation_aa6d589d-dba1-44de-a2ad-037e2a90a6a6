package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import javafx.util.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumPostType;
import ttfund.web.communityservice.bean.jijinBar.post.data.ZHBAdminReplyModel;
import ttfund.web.communityservice.bean.jijinBar.post.data.ZHBPostInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.guba.FundBarModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.FundBarDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 组合宝相关帖子统计
 * 1.吧下总帖子数、楼主总帖子数、楼主评论数据、楼主回答数
 * 缓存名：{code}：基金吧代码，如：43-10207797
 * fund_service_zhbpostcount_{code}
 * 缓存值为hash ：有效期三个月
 */
@JobHandler(value = "zhbPostCountRedisBusinessJob")
@Component
public class ZHBPostCountRedisBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(ZHBPostCountRedisBusinessJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private FundBarDao fundBarDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            String initBreakpoint1 = null;
            String initBreakpoint2 = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint1 = jsonObject.getString("initBreakpoint1");
                initBreakpoint2 = jsonObject.getString("initBreakpoint2");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint1：{}，initBreakpoint2：{}，batchReadCount：{}",
                    initBreakpoint1,
                    initBreakpoint2,
                    batchReadCount
            );

            if (StringUtils.hasLength(initBreakpoint1) || StringUtils.hasLength(initBreakpoint2)) {

                if (StringUtils.hasLength(initBreakpoint1)) {
                    userRedisDao.set(UserRedisConfig.ZHBPOSTCOUNTREDISBUSINESSJOB_ZHBPOSTCOUNT_BREAKPOINT, initBreakpoint1, 30 * 24 * 3600L);

                    logger.info("第零步，初始化断点。initBreakpoint1：{}", initBreakpoint1);
                }

                if (StringUtils.hasLength(initBreakpoint2)) {
                    userRedisDao.set(UserRedisConfig.ZHBPOSTCOUNTREDISBUSINESSJOB_ZHBREPLYCOUNT_BREAKPOINT, initBreakpoint2, 30 * 24 * 3600L);

                    logger.info("第零步，初始化断点。initBreakpoint2：{}", initBreakpoint2);
                }

                return ReturnT.SUCCESS;
            }

            zhbPostCount(batchReadCount);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;

    }

    private void zhbPostCount(int batchReadCount) {

        /*
         * 1.基金吧帖子相关（吧下总帖子数、楼主总帖子数、楼主回答数）
         * 2.基金吧帖子评论相关统计（楼主评论数据）
         */
        try {

            //帖子数量
            logger.info("设置帖子数量统计hash开始");

            // 数据时间取值范围
            Date dateRange = DateUtil.calendarDateByMonth(-3);

            // 组合宝帖子统计断点
            String breakpointName = UserRedisConfig.ZHBPOSTCOUNTREDISBUSINESSJOB_ZHBPOSTCOUNT_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                // 获取上次断点时间,默认取近三个月数据
                breakpoint = DateUtil.dateToStr(dateRange);

                logger.error("zhbPostCount-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("zhbPostCount-第一步，读取断点。断点:{}", breakpoint);

            // 获取组合宝
            List<ZHBPostInfoModel> list = postInfoNewDao.getZHBPostList(breakpointDate, dateRange, batchReadCount);

            logger.info("zhbPostCount-第二步，读取帖子。数量:{}，头部列表：{}",
                    list == null ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

            if (!CollectionUtils.isEmpty(list)) {

                //最后更新时间
                breakpointDate = list.stream().map(a -> a.UPDATETIME).max(Comparator.naturalOrder()).get();

                List<String> codes = list.stream().map(a -> a.CODE).distinct().collect(Collectors.toList());
                List<FundBarModel> codeInfos = fundBarDao.getByCodes(codes);

                logger.info("zhbPostCount-第三步，读取吧。数量:{}，头部列表：{}",
                        codeInfos == null ? 0 : codeInfos.size(),
                        CollectionUtils.isEmpty(codeInfos) ? null : JSON.toJSONStringWithDateFormat(codeInfos.get(0), DateUtil.datePattern)
                );

                Map<String, FundBarModel> codeInfoMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(codeInfos)) {
                    codeInfos.forEach(a -> codeInfoMap.put(a.BarCode, a));
                }

                FundBarModel barInfo = null;
                for (ZHBPostInfoModel a : list) {
                    barInfo = codeInfoMap.get(a.CODE);
                    if (barInfo != null) {
                        a.AdminPassportId = barInfo.AdminPassportId;
                    }
                }

                //所在吧列表
                List<String> codeList = list.stream()
                        .map(a -> a.CODE)
                        .distinct()
                        .collect(Collectors.toList());

                logger.info("zhbPostCount-第四步，按吧分组。数量:{}，头部列表：{}",
                        codeList == null ? 0 : codeList.size(),
                        CollectionUtils.isEmpty(codeList) ? null : codeList.get(0)
                );

                if (!CollectionUtils.isEmpty(codeList)) {

                    int i = 0;
                    for (String code : codeList) {

                        i++;

                        //设置吧的数量缓存
                        try {

                            int postCount = postInfoNewDao.getPostCountByCode(code, dateRange);
                            String cacheKey = BarRedisKey.ZHB_POST_COUNT + code;
                            Map<String, String> map = new HashMap<>();
                            map.put("PostCount", String.valueOf(postCount));
                            app.barredis.hmset(cacheKey, map);
                            app.barredis.expire(cacheKey, 90 * 24 * 3600L);

                            logger.info("zhbPostCount-第五步，设置帖子数量缓存-详情。第{}/{}个，code：{}，数据：{}",
                                    i,
                                    codeList.size(),
                                    code,
                                    JSON.toJSONString(map)
                            );

                        } catch (Exception ex) {
                            logger.error(ex.getMessage(), ex);
                        }
                    }

                }

                logger.info("zhbPostCount-第五步，设置帖子数量缓存完成。数量:{}，头部列表：{}",
                        codeList == null ? 0 : codeList.size(),
                        CollectionUtils.isEmpty(codeList) ? null : codeList.get(0)
                );

                // 管理人所在吧
                List<Pair<String, String>> adminAndCodeList = list.stream()
                        .filter(a -> StringUtils.hasLength(a.AdminPassportId))
                        .map(a -> new Pair<String, String>(a.AdminPassportId, a.CODE))
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(adminAndCodeList)) {

                    int i = 0;
                    for (Pair<String, String> item : adminAndCodeList) {

                        i++;

                        String cacheKey = BarRedisKey.ZHB_POST_COUNT + item.getValue();
                        Map<String, String> map = new HashMap<>();
                        map.put("AdminPassportId", item.getKey());
                        app.barredis.hmset(cacheKey, map);

                        Map<String, String> dicHash = new HashMap<>();

                        // 获取用户所在吧的发帖数量
                        int postCount = postInfoNewDao.getPostCountByUidAndCode(item.getKey(), item.getValue(), dateRange);
                        dicHash.put("AdminPostCount", String.valueOf(postCount));

                        // 回答数量
                        int answerCount = postInfoNewDao.getAnswerPostCountByUidAndCode(
                                item.getKey(),
                                item.getValue(),
                                EnumPostType.QAAnswer.getValue(),
                                dateRange);
                        dicHash.put("AdminAnswerCount", String.valueOf(answerCount));

                        //设置多个hash值
                        app.barredis.hmset(cacheKey, dicHash);
                        app.barredis.expire(cacheKey, 90 * 24 * 3600L);

                        logger.info("zhbPostCount-第六步，设置管理人所在吧缓存-详情。第{}/{}个，code：{}, uid：{}，数据：{}",
                                i,
                                adminAndCodeList.size(),
                                item.getValue(),
                                item.getKey(),
                                JSON.toJSONString(dicHash)
                        );
                    }
                }

                logger.info("zhbPostCount-第六步，设置管理人所在吧缓存完成。数量:{}，头部列表：{}",
                        adminAndCodeList == null ? 0 : adminAndCodeList.size(),
                        CollectionUtils.isEmpty(adminAndCodeList) ? null : JSON.toJSONStringWithDateFormat(adminAndCodeList.get(0), DateUtil.datePattern)
                );

                // 保存断点
                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("zhbPostCount-第七步，更新断点。断点：{}", breakpoint);

            }

            logger.info("设置帖子数量统计hash结束");


            // 用户评论相关
            logger.info("设置评论数量统计hash开始");

            // 获取组合宝评论信息
            breakpointName = UserRedisConfig.ZHBPOSTCOUNTREDISBUSINESSJOB_ZHBREPLYCOUNT_BREAKPOINT;
            breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                // 获取上次断点时间,默认取近三个月数据
                breakpoint = DateUtil.dateToStr(dateRange);

                logger.error("zhbReplyCount-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("zhbReplyCount-第一步，读取断点。断点:{}", breakpoint);

            // 管理人回复的最新时间
            List<ZHBAdminReplyModel> replyList = postInfoNewDao.getZHAdminReplys(dateRange, breakpointDate, 1000);

            logger.info("zhbReplyCount-第二步，读取评论。数量:{}，头部列表：{}",
                    replyList == null ? 0 : replyList.size(),
                    CollectionUtils.isEmpty(replyList) ? null : JSON.toJSONStringWithDateFormat(replyList.get(0), DateUtil.datePattern)
            );

            if (!CollectionUtils.isEmpty(replyList)) {

                breakpointDate = replyList.stream()
                        .map(a -> a.UPDATETIME)
                        .max(Comparator.naturalOrder())
                        .get();

                List<Pair<String, String>> tempRelyList = replyList.stream()
                        .map(a -> new Pair<String, String>(a.CODE, a.UID))
                        .collect(Collectors.toList());

                int i = 0;
                for (Pair<String, String> reply : tempRelyList) {
                    i++;

                    int repelyCount = postInfoNewDao.getZHAdminReplyCount(dateRange, reply.getValue(), reply.getKey());
                    String cacheKey = BarRedisKey.ZHB_POST_COUNT + reply.getKey();
                    Map<String, String> map = new HashMap<>();
                    map.put("AdminReplyCount", String.valueOf(repelyCount));
                    app.barredis.hmset(cacheKey, map);
                    app.barredis.expire(cacheKey, 90 * 24 * 3600L);

                    logger.info("zhbReplyCount-第三步，设置管理人回复缓存-详情。第{}/{}个，code：{}, uid：{}，数据：{}",
                            i,
                            tempRelyList.size(),
                            reply.getKey(),
                            reply.getValue(),
                            JSON.toJSONString(map)
                    );
                }

                logger.info("zhbReplyCount-第三步，设置管理人回复缓存完成。数量:{}，头部列表：{}",
                        tempRelyList == null ? 0 : tempRelyList.size(),
                        CollectionUtils.isEmpty(tempRelyList) ? null : JSON.toJSONString(tempRelyList.get(0))
                );

                // 保存断点
                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("zhbReplyCount-第四步，更新断点。断点：{}", breakpoint);

            }

            logger.info("设置评论数量统计hash结束");

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }
}
