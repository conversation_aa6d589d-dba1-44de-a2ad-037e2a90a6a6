package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;

/**
 * 推荐系统删除帖子
 */
@Component
public class RecommendBusinessJob {
    private static Logger logger = LoggerFactory.getLogger(RecommendBusinessJob.class);

    public static final String KAFKA_LISTENER_ID = "RecommendBusinessJob";

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private PostInfoExtraDao postInfoExtraDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.FUNDBAR_DEL_INFO}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_RECOMMENDBUSINESSJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_fundproduct)
    private void onListen(ConsumerRecord<String, String> record) {
        handleFundBarDelInfo((record));
    }


    /**
     * 推荐系统删除帖子
     */
    public void handleFundBarDelInfo(ConsumerRecord<String, String> record) {

        try {
            logger.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            JSONObject model = JSON.parseObject(record.value());
            Object o = model.get("FundBarId");
            if (o != null && !"".equals(o)) {
                postInfoNewDao.updateRecommandDelStatus(Integer.parseInt(o.toString()));
                postInfoExtraDao.updateRecommandDelStatus(Integer.parseInt(o.toString()));
            }

        } catch (Exception ex) {

            logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));
            logger.error(ex.getMessage(), ex);
        }

    }
}

