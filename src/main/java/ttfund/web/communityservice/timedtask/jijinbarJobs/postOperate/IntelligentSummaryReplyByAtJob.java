package ttfund.web.communityservice.timedtask.jijinbarJobs.postOperate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.HtmlUtils;
import ttfund.web.communityservice.bean.barrage.PassportUserInfoModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.IntelligentSummaryReplyByAtDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.dao.vertica.PostDigestBasicDrctAllDao;
import ttfund.web.communityservice.service.ReplyServiceImpl;
import ttfund.web.communityservice.service.TtAgentApiServiceImpl;
import ttfund.web.communityservice.service.entity.AddReplyRequest;
import ttfund.web.communityservice.service.entity.AddReplyResponse;
import ttfund.web.communityservice.service.redis.UserRedisService;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 智能摘要发评论job  --通过艾特召唤
 */
@JobHandler("IntelligentSummaryReplyByAtJob")
@Component
public class IntelligentSummaryReplyByAtJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(IntelligentSummaryReplyByAtJob.class);

    private static String FIELDS = "a.ID, a.TOPICID, a.TEXT, a.UID, a.TIME, b.TYPE, b.CONTENT";

    private static final String USER_REGEX = "\\[at=(.*?)\\]([\\s\\S]*?)\\[/at\\]";
    private static final Pattern USER_REGEX_PATTERN = Pattern.compile(USER_REGEX);

    private static final String TAB_REGEX = "<[^>]*?>";
    private static final Pattern TAB_REGEX_PATTERN = Pattern.compile(TAB_REGEX);

    private static String TEXT_FORMAT_AI_SUMMARY_1 = "%s\n本内容由小助理生成，点击头像查看更多精彩内容\n感谢 @%s 的召唤";
    private static String TEXT_FORMAT_AI_SUMMARY_2 = "%s\n本内容由小助理生成，点击头像查看更多精彩内容\n感谢召唤";

    @Autowired
    private TtAgentApiServiceImpl ttAgentService;

    @Autowired
    private ReplyServiceImpl replyService;

    @Autowired
    private IntelligentSummaryReplyByAtDao intelligentSummaryReplyByAtDao;

    @Autowired
    private PostDigestBasicDrctAllDao postDigestBasicDrctAllDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private UserRedisService userRedisService;

    @Autowired
    private ReplyInfoNewDao replyInfoNewDao;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            Integer forwardTime = null;
            String pic = null;
            Integer filterCharLimit = null;
            String filterBarType = null;
            Long expire = null;
            String callUids = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                forwardTime = jsonObject.getInteger("forwardTime");
                pic = jsonObject.getString("pic");

                filterCharLimit = jsonObject.getInteger("filterCharLimit");
                filterBarType = jsonObject.getString("filterBarType");

                expire = jsonObject.getLong("expire");
                callUids = jsonObject.getString("callUids");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (forwardTime == null) {
                forwardTime = -3 * 60;
            }
            if (pic == null) {
                pic = "https://gbres.dfcfw.com/Files/picture/20240912/79C6F00877B08176DB1E7EBFCD4AC475_w588h192.jpg";
            }
            if (filterCharLimit == null) {
                filterCharLimit = 50;
            }
            if (filterBarType == null) {
                filterBarType = "49";
            }
            if (expire == null) {
                expire = 24 * 3600L;
            }

            logger.info("0.打印参数。initBreakpoint：{}，batchReadCount：{}，forwardTime：{}，pic：{}，filterCharLimit：{}，filterBarType：{}，expire：{}，callUids：{}",
                initBreakpoint,
                batchReadCount,
                forwardTime,
                pic,
                filterCharLimit,
                filterBarType,
                expire,
                callUids
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.INTELLIGENTSUMMARYREPLYBYATJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("0.初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            List<Integer> filterBarTypeList = CommonUtils.toList(filterBarType, ",").stream().map(a -> Integer.parseInt(a)).collect(Collectors.toList());
            deal(batchReadCount, forwardTime, pic, filterCharLimit, filterBarTypeList, expire, CommonUtils.toList(callUids, ","));

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount, int forwardTime, String pic, int filterCharLimit, List<Integer> filterBarType, Long expire, List<String> callUids) throws Exception {

        String breakpointName = UserRedisConfig.INTELLIGENTSUMMARYREPLYBYATJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-10));

            logger.error("0.读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("1.读取断点。断点:{}", breakpoint);


        Date end = DateUtil.calendarDateBySecond(forwardTime);
        List<Map<String, Object>> dataList = replyInfoNewDao.getByTime(FIELDS, breakpointDate, end, batchReadCount);

        logger.info("2.读取数据。start：{}，end：{}，数量:{}，头部列表：{}",
            DateUtil.dateToStr(breakpointDate),
            DateUtil.dateToStr(end),
            dataList == null ? 0 : dataList.size(),
            CollectionUtils.isEmpty(dataList) ? null : JSON.toJSONStringWithDateFormat(dataList.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(dataList)) {

            breakpointDate = (Date)dataList.get(dataList.size() - 1).get("TIME");

            Long postId = null;
            //0：待处理
            //1：生成摘要成功
            //2：不满足生成摘要条件
            //3：生成摘要失败
            //4：已经处理过
            //5：已经成功生成过
            Integer aiSummaryStatus = null;
            Long aiReplyId = null;
            Date aiReplyTime = null;
            String aiReplyMessage = null;
            Long callReplyId = null;

            String callUid = null;
            String callNickName = null;

            String text = null;
            String content = null;
            String uid = null;
            Integer type = null;
            String aiSummary = null;
            boolean call = false;//是否触发召唤
            boolean alreadyDealByBackground = false;//是否已经处理过 通过被动后台服务
            boolean alreadyDealByThis = false;//是否已经处理过 通过本服务
            boolean meet = false;//是否满足摘要条件

            String key = null;
            String value = null;

            int i = 0;
            for (Map<String, Object> a : dataList) {
                i++;

                postId = Long.parseLong(String.valueOf(a.get("TOPICID")));
                aiSummaryStatus = 0;
                aiReplyId = null;
                aiReplyTime = null;
                aiReplyMessage = null;
                callReplyId = Long.parseLong(a.get("ID").toString());

                callUid = a.get("UID") == null ? "" : a.get("UID").toString();
                callNickName = null;

                text = a.get("TEXT") == null ? "" : a.get("TEXT").toString();
                content = a.get("CONTENT") == null ? "" : a.get("CONTENT").toString();
                uid = null;
                type = a.get("TYPE") == null ? null : Integer.parseInt(a.get("TYPE").toString());
                aiSummary = null;

                call = false;
                alreadyDealByBackground = false;
                alreadyDealByThis = false;
                meet = false;

                if (StringUtils.hasLength(text)) {
                    text = HtmlUtils.htmlUnescape(text);

                    Matcher tabMatcher = TAB_REGEX_PATTERN.matcher(text);
                    while (tabMatcher.find()) {
                        text = text.replace(tabMatcher.group(0), "");
                    }

                    Matcher atMatcher = USER_REGEX_PATTERN.matcher(text);
                    while (atMatcher.find()) {
                        text = text.replace(atMatcher.group(1), atMatcher.group(2));
                        uid = atMatcher.group(1);

                        if (appConstant.intelligentSummaryReplyUid.equals(uid)) {
                            call = true;
                            break;
                        }
                    }
                }

                if (!call) {

                    logger.info("3.处理详情。第{}/{}个，帖子id：{}，评论id：{}，结果：{}，aiSummaryStatus：{}",
                        i,
                        dataList.size(),
                        postId,
                        callReplyId,
                        "非召唤",
                        aiSummaryStatus
                    );

                    continue;
                }

                if (!CollectionUtils.isEmpty(callUids) && !callUids.contains(callUid)) {

                    logger.info("3.处理详情。第{}/{}个，帖子id：{}，评论id：{}，结果：{}，aiSummaryStatus：{}",
                        i,
                        dataList.size(),
                        postId,
                        callReplyId,
                        "非指定用户",
                        aiSummaryStatus
                    );

                    continue;
                }

                if (!alreadyDealByThis) {
                    Document document = intelligentSummaryReplyByAtDao.getOneByKeyIsValue("_id", postId.toString(), Arrays.asList("_id", "aiReplyId", "aiSummary"), Document.class);
                    if (document != null) {
                        alreadyDealByThis = true;
                        aiSummaryStatus = 4;
                        if (document.get("aiReplyId") != null && !callReplyId.equals(document.get("aiReplyId").toString())) {
                            aiSummaryStatus = 5;
                            aiSummary = document.getString("aiSummary");
                        }
                    }
                }

                if (!alreadyDealByThis) {
                    List<Map<String, Object>> tempList = postDigestBasicDrctAllDao.getByPostId("POSTID,REPLYID,DIGEST", postId);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        alreadyDealByBackground = true;
                        aiSummaryStatus = 4;
                        if (tempList.get(0).get("REPLYID") != null && !callReplyId.equals(tempList.get(0).get("REPLYID").toString())) {
                            aiSummaryStatus = 5;
                            aiSummary = (String)tempList.get(0).get("DIGEST");
                        }
                    }
                }

                key = String.format(BarRedisKey.AI_SUMMARY_BY_AT, callReplyId);
                if (aiSummaryStatus == 5) {
                    value = app.barredis.get(key);
                    if (StringUtils.hasLength(value)) {

                        logger.info("3.处理详情。第{}/{}个，帖子id：{}，评论id：{}，结果：{}，aiSummaryStatus：{}",
                            i,
                            dataList.size(),
                            postId,
                            callReplyId,
                            "已经成功生成过-已回复",
                            aiSummaryStatus
                        );

                        continue;
                    }
                }

                if (aiSummaryStatus == 4 && alreadyDealByThis) {
                    logger.info("3.处理详情。第{}/{}个，帖子id：{}，评论id：{}，结果：{}，aiSummaryStatus：{}",
                        i,
                        dataList.size(),
                        postId,
                        callReplyId,
                        "已处理过-本服务",
                        aiSummaryStatus
                    );

                    continue;
                }

                if (alreadyDealByBackground) {
                    logger.info("3.处理详情。第{}/{}个，帖子id：{}，评论id：{}，结果：{}，aiSummaryStatus：{}",
                        i,
                        dataList.size(),
                        postId,
                        callReplyId,
                        "已处理过-被动后台服务",
                        aiSummaryStatus
                    );
                }

                List<PassportUserInfoModel> tempList = userRedisService.passportUserInfoCache(Arrays.asList(callUid));
                if (!CollectionUtils.isEmpty(tempList)) {
                    callNickName = tempList.get(0).NickName;
                }

                if (aiSummaryStatus == 0) {
                    if (StringUtils.hasLength(content)) {
                        content = HtmlUtils.htmlUnescape(content);

                        Matcher tabMatcher = TAB_REGEX_PATTERN.matcher(content);
                        while (tabMatcher.find()) {
                            content = content.replace(tabMatcher.group(0), "");
                        }

                        Matcher atMatcher = USER_REGEX_PATTERN.matcher(content);
                        while (atMatcher.find()) {
                            content = content.replace(atMatcher.group(1), atMatcher.group(2));
                        }
                    }

                    meet = filter(content, type, filterCharLimit, filterBarType);
                    if (!meet) {
                        aiSummaryStatus = 2;
                    } else {
                        aiSummary = commentPost(content);
                        if (aiSummary == null) {
                            aiSummaryStatus = 3;
                        } else {
                            aiSummaryStatus = 1;
                        }
                    }
                }


                if (aiSummaryStatus == 5) {
                    AddReplyRequest request = new AddReplyRequest();
                    request.setId(postId.toString());
                    request.setHuifuid(callReplyId.toString());
                    request.setText(StringUtils.hasLength(callNickName) ? String.format(TEXT_FORMAT_AI_SUMMARY_1, aiSummary, callNickName) : String.format(TEXT_FORMAT_AI_SUMMARY_2, aiSummary));
                    request.setPic(pic);
                    request.setT_type(0);
                    request.setUid(appConstant.intelligentSummaryReplyUid);
                    request.setBusiness_type(3);
                    AddReplyResponse addReplyResponse = replyService.addReply(request);
                    if (addReplyResponse != null && addReplyResponse.getReplyId() > 0) {
                        aiReplyId = addReplyResponse.getReplyId();
                    } else {
                        aiReplyId = 0L;
                    }

                    app.barredis.set(key, String.valueOf(aiReplyId), expire);

                    logger.info("3.处理详情。第{}/{}个，帖子id：{}，评论id：{}，结果：{}，aiSummaryStatus：{}",
                        i,
                        dataList.size(),
                        postId,
                        callReplyId,
                        "已经成功生成过-回复-" + aiReplyId,
                        aiSummaryStatus
                    );

                    continue;
                } else if (aiSummaryStatus == 1) {
                    AddReplyRequest request = new AddReplyRequest();
                    request.setId(postId.toString());
                    request.setText(StringUtils.hasLength(callNickName) ? String.format(TEXT_FORMAT_AI_SUMMARY_1, aiSummary, callNickName) : String.format(TEXT_FORMAT_AI_SUMMARY_2, aiSummary));
                    request.setPic(pic);
                    request.setT_type(0);
                    request.setUid(appConstant.intelligentSummaryReplyUid);
                    request.setBusiness_type(3);
                    AddReplyResponse addReplyResponse = replyService.addReply(request);
                    if (addReplyResponse != null && addReplyResponse.getReplyId() > 0) {
                        aiReplyId = addReplyResponse.getReplyId();
                    } else if (addReplyResponse != null && StringUtils.hasLength(addReplyResponse.getMe())) {
                        aiReplyMessage = addReplyResponse.getMe().substring(
                            0, addReplyResponse.getMe().length() > 15 ? 15 : addReplyResponse.getMe().length()
                        );
                    } else {
                        aiReplyMessage = "默认信息：调股吧发评论接口失败";
                    }

                    aiReplyTime = new Date();

                    logger.info("3.处理详情。第{}/{}个，帖子id：{}，评论id：{}，结果：{}，aiSummaryStatus：{}",
                        i,
                        dataList.size(),
                        postId,
                        callReplyId,
                        "生成摘要成功-回复-" + aiReplyId,
                        aiSummaryStatus
                    );

                }

                Map<String, Object> insertMap = new HashMap<>();
                insertMap.put("_id", postId.toString());
                insertMap.put("postId", postId);
                insertMap.put("callReplyId", callReplyId);
                insertMap.put("aiSummaryStatus", aiSummaryStatus);
                insertMap.put("aiSummary", aiSummary);
                insertMap.put("aiReplyId", aiReplyId);
                insertMap.put("aiReplyTime", aiReplyTime);
                insertMap.put("aiReplyMessage", aiReplyMessage);
                insertMap.put("createTime", new Date());
                insertMap.put("updateTime", new Date());

                intelligentSummaryReplyByAtDao.upsertManyBySetWithSetOnInsertFields(
                    Arrays.asList(insertMap),
                    Arrays.asList("createTime"),
                    "_id"
                );

                logger.info("3.处理详情。第{}/{}个，帖子id：{}，评论id：{}，写库，数据：{}",
                    i,
                    dataList.size(),
                    postId,
                    callReplyId,
                    JSON.toJSONStringWithDateFormat(insertMap, DateUtil.datePattern)
                );

            }
        }

        logger.info("3.处理完成。数量:{}，头部列表：{}",
            dataList == null ? 0 : dataList.size(),
            CollectionUtils.isEmpty(dataList) ? null : JSON.toJSONStringWithDateFormat(dataList.get(0), DateUtil.datePattern)
        );

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        logger.info("4.更新断点。断点：{}", breakpoint);

    }


    private boolean filter(String text, Integer type, int filterCharLimit, List<Integer> filterBarType) {
        boolean result = false;

        if (!StringUtils.hasLength(text)) {
            return result;
        }

        if (text.length() < filterCharLimit) {
            return result;
        }

        if (!CollectionUtils.isEmpty(filterBarType) && filterBarType.contains(type)) {
            return result;
        }

        result = true;
        return result;
    }

    private String commentPost(String text) {
        String result = null;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("customerNo", appConstant.intelligentSummaryReplyUid);
        paramMap.put("userInput", text);
        String html = ttAgentService.commentPost(paramMap);
        if (!StringUtils.hasLength(html)) {
            return result;
        }

        JSONObject jsonObject = JSON.parseObject(html);
        if (jsonObject != null
            && jsonObject.getBoolean("succeed") != null
            && jsonObject.getBoolean("succeed") == true
            && jsonObject.getJSONObject("result") != null
            && StringUtils.hasLength(jsonObject.getJSONObject("result").getString("data"))) {
            result = jsonObject.getJSONObject("result").getString("data");
        }

        return result;
    }

}
