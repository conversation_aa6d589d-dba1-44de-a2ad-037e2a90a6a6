package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.ScenePostRelationDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 场景帖子关系 帖子同步job
 * 备注：需求 #697822 【运营专区1.2.15】基金经理答疑团
 */
@Slf4j
@JobHandler("ScenePostRelationSyncJob")
@Component
public class ScenePostRelationSyncJob extends IJobHandler {

    private static String selectFields = "ID, TITLE, UID, TYPE, CODE, CODELIST, TIME, TIMEPOINT, DEL, TTJJDEL, UPDATETIME";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private ScenePostRelationDao scenePostRelationDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }


            log.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.SCENEPOSTRELATIONSYNCJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            deal(batchReadCount);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount) {
        String breakpointName = UserRedisConfig.SCENEPOSTRELATIONSYNCJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            log.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("第一步，读取断点。断点:{}", breakpoint);

        List<Map<String, Object>> list = postInfoNewDao.getListByUpdateTime(selectFields, breakpointDate, batchReadCount);

        log.info("第二步，读取帖子。数量:{}，头部列表：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        Set<String> existPostIds = new HashSet<>();
        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = (Date) list.get(list.size() - 1).get("UPDATETIME");

            List<String> ids = list.stream().map(a -> a.get("ID").toString()).collect(Collectors.toList());
            List<List<String>> batchList = CommonUtils.toSmallList2(ids, 200);
            for (List<String> batch : batchList) {
                List<Document> tempList = scenePostRelationDao.getListByPostIds(null, Document.class, batch);
                if (!CollectionUtils.isEmpty(tempList)) {
                    existPostIds.addAll(tempList.stream().map(a -> a.getString("postId")).collect(Collectors.toList()));
                }
            }

            list = list.stream().filter(a -> existPostIds.contains(a.get("ID").toString())).collect(Collectors.toList());

        }

        log.info("第三步，过滤帖子。数量:{}，头部列表：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        List<Map<String, Object>> updateList = null;
        Map<String, Object> map = null;
        if (!CollectionUtils.isEmpty(list)) {
            updateList = new ArrayList<>(list.size());

            list.forEach(a -> {
                        a.put("POSTID", Long.parseLong(a.get("ID").toString()));
                        a.put("UPDATETIME", new Date());
                    }
            );

            for (Map<String, Object> a : list) {
                map = new HashMap<>();
                map.put("postId", a.get("ID").toString());
                map.put("postTitle", a.get("TITLE"));
                map.put("postTimepoint", a.get("TIMEPOINT"));
                map.put("postTime", a.get("TIME"));
                map.put("postUid", a.get("UID"));
                map.put("postDel", a.get("DEL"));
                map.put("postTtjjdel", a.get("TTJJDEL"));

                map.put("updateTime", new Date());

                updateList.add(map);
            }

            List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(updateList, 200);
            for (List<Map<String, Object>> batch : batchList) {
                scenePostRelationDao.updateMany(batch, "postId");
            }

        }

        log.info("第四步，写库帖子。数量:{}，头部列表：{}",

                CollectionUtils.isEmpty(updateList) ? 0 : updateList.size(),
                CollectionUtils.isEmpty(updateList) ? null : JSON.toJSONStringWithDateFormat(updateList.get(0), DateUtil.datePattern)
        );

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        log.info("第五步，更新断点。断点：{}", breakpoint);

    }

}
