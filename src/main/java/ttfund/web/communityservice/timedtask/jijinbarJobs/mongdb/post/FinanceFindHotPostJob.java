package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.mongodb.client.result.DeleteResult;
import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.LcLabelAuthorRelationDo;
import ttfund.web.communityservice.bean.jijinBar.data.LcLabelInfoDo;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoExt;
import ttfund.web.communityservice.bean.jijinBar.post.PostVideoInfo;
import ttfund.web.communityservice.bean.jijinBar.post.finance.LCAuthorInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.finance.LCPostInfoModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.FinanceFindDao;
import ttfund.web.communityservice.dao.mongo.FindRecommendPostDao;
import ttfund.web.communityservice.dao.mongo.LCPostInfoDao;
import ttfund.web.communityservice.dao.mongo.LcLabelAuthorRelationDao;
import ttfund.web.communityservice.dao.mongo.LcLabelInfoDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 理财发现页 热门帖子
 */
@JobHandler(value = "financeFindHotPostJob")
@Component
public class FinanceFindHotPostJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(FinanceFindHotPostJob.class);

    @Autowired
    private FinanceFindDao financeFindDao;

    @Autowired
    private LCPostInfoDao lcPostInfoDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostDao postDao;

    @Autowired
    private FindRecommendPostDao findRecommendPostDao;

    @Autowired
    private App app;

    @Value("${finance.find.group}")
    private String groupName;

    @Value("${finance.find.range.days:60}")
    private int rangeDays;

    @Value("${finance.find.score.default:2000}")
    private long defaultScore;

    @Autowired
    private LcLabelInfoDao lcLabelInfoDao;

    @Autowired
    private LcLabelAuthorRelationDao lcLabelAuthorRelationDao;

    private static final Set<Integer> typeSet = new HashSet<>(Arrays.asList(0, 20, 43, 48, 48, 50, 58));

    @Override
    public ReturnT<String> execute(String param) {

        MDC.put(CoreConstant.logtraceid, UUID.randomUUID().toString().replaceAll("-", ""));
        logger.info("理财发现页-热门帖子开始");

        ReturnT result = ReturnT.SUCCESS;

        try {
            Date now = DateUtil.getNowDate();
            String breakTimeKey = "financeFindBreakTime";
            Date lastUpdatetime = userRedisDao.getBreakTime(breakTimeKey);

            // 获取有效作者，已删除作者的帖子从帖子底池表移除
            List<LCAuthorInfoModel> authorInfoList = getValidUserList(lastUpdatetime);
            logger.info("需要处理的作者数量：" + authorInfoList.size());

            // 获取帖子底池中单独添加的帖子
            List<LCPostInfoModel> addedPost = lcPostInfoDao.getSingleAddedPost();
            logger.info("帖子底池中单独添加的帖子数量：" + addedPost.size());

            if (CollectionUtils.isEmpty(authorInfoList) && CollectionUtils.isEmpty(addedPost)) {
                logger.info("没有需要处理的帖子");
            } else {
                int size = 2000;

                // 全量更新
                List<String> userList = authorInfoList.stream()
                    .map(w -> w.getUid())
                    .collect(Collectors.toList());
                fullUpdate(userList, addedPost, size);
            }

            // N天前的帖子删除
            lcPostInfoDao.deleteOldPost();

            // 设置时间断点
            userRedisDao.setBreakTime(breakTimeKey, now);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }

        // 塞缓存
        try {
            saveDataToRedis();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }

        // 处理分签
        try {
            List<Integer> stateList = null;
            if (StringUtils.hasLength(param)) {
                JSONObject jsonObject = JSON.parseObject(param);
                stateList = CommonUtils.toList(jsonObject.getString("labelState"), ",").stream().map(a -> Integer.parseInt(a)).collect(Collectors.toList());
            }
            if (stateList == null) {
                stateList = Arrays.asList(2);
            }
            dealLabels(stateList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }

        logger.info("理财发现页-热门帖子结束");

        MDC.remove(CoreConstant.logtraceid);
        return result;

    }

    /**
     * 往Redis保存数据
     */
    private void saveDataToRedis() throws JsonProcessingException {

        logger.info("往Redis保存数据开始");
        int pageSize = 5000;
        int pageIndex = 1;

        while (true) {

            List<LCPostInfoModel> postList = lcPostInfoDao.findAllPost(pageIndex, pageSize);

            if (CollectionUtils.isEmpty(postList)) {
                break;
            }

            app.barredis.set(BarRedisKey.FINANCE_FIND_HOT_POST + pageIndex, JacksonUtil.obj2String(postList), 7 * 24 * 60 * 60L);

            logger.info("往Redis保存数量：" + postList.size());

            if (postList.size() < pageSize) {
                break;
            }

            pageIndex++;
        }

        logger.info("往Redis保存数据结束");
    }

    /**
     * 数据转换
     *
     * @param scoreMap
     * @param toSaveModelList
     * @param item
     */
    private void mergeData(Map<Long, Long> scoreMap, List<LCPostInfoModel> toSaveModelList, PostInfoExt item) {
        LCPostInfoModel model = new LCPostInfoModel();
        model.setId(String.valueOf(item.ID));
        model.setTid(item.ID);
        model.setUid(item.UID);
        model.setTime(item.TIME);
        model.setTimePoint(item.TIMEPOINT);
        model.setQid(item.QID);
        model.setCode(item.CODE);
        model.setCreateTime(DateUtil.getNowDate());
        model.setUpdateTime(DateUtil.getNowDate());
        model.setIsDel(0);
        if (scoreMap.containsKey(item.ID)) {
            model.setScore(scoreMap.get(item.ID));
        } else {
            model.setScore(defaultScore);
        }
        model.setSort(0L);
        model.setSource(1);
        model.setLabel(0);
        toSaveModelList.add(model);
    }

    /**
     * 全量更新
     *
     * @param userList
     * @param addedPost
     * @param size
     * @throws JsonProcessingException
     */
    private void fullUpdate(List<String> userList, List<LCPostInfoModel> addedPost, int size) throws JsonProcessingException {

        logger.info("全量处理开始");

        // 近N天
        Date beginDate = DateUtil.calendarDateByDays(-rangeDays);

        List<PostInfoExt> validPostList = new ArrayList<>();
        Map<Long, Long> postScoreMap = new HashMap<>();
        List<LCPostInfoModel> toSaveModelList = new ArrayList<>();

        // 获取手动添加的帖子得分
        Set<Long> addedSet = addedPost.stream().map(w -> w.getTid()).collect(Collectors.toSet());
        findRecommendPostDao.findByID(addedSet, groupName, postScoreMap);

        Map<String, PostInfoExt> addedPostExtMap = new HashMap<>();
        List<String> addedList = addedSet.stream().map(a -> String.valueOf(a)).collect(Collectors.toList());
        List<List<String>> batchList = CommonUtils.toSmallList2(addedList, 200);
        for (List<String> batch : batchList) {
            List<PostInfoExt> tempList = postDao.getFromMong(batch, Arrays.asList("_id", "TIMEPOINT"), PostInfoExt.class);
            if (!CollectionUtils.isEmpty(tempList)) {
                tempList.forEach(a -> addedPostExtMap.put(a._id, a));
            }
        }

        // 手动添加的帖子数据转换
        for (LCPostInfoModel item : addedPost) {
            PostInfoExt postInfoExt = addedPostExtMap.get(item.getId());
            if (postInfoExt != null) {
                item.setTimePoint(postInfoExt.TIMEPOINT);
            }
            mergeAddedData(postScoreMap, toSaveModelList, item);
        }

        while (true) {

            // 从PostInfo表获取符合条件的帖子
            List<PostInfoExt> postList = postDao.getFinanceFindPost(beginDate, userList, size);

            logger.info("PostInfo中的帖子数量：" + postList.size());

            if (CollectionUtils.isEmpty(postList)) {
                break;
            }

            beginDate = postList.get(postList.size() - 1).TIME;

            // PostInfo表中标记删除或者没有图和视频的帖子，从帖子底池表删除
            List<Long> toDeleteList = postList.stream()
                .filter(w -> !isValid(w) && !addedSet.contains(w.ID))
                .map(w -> w.ID)
                .collect(Collectors.toList());

            logger.info("PostInfo表中标记删除或者没有图和视频的数量：" + toDeleteList.size());

            removeDeletedPost(toDeleteList);

            // 获取得分
            List<PostInfoExt> queryScoreList = postList.stream()
                .filter(w -> isValid(w) && !addedSet.contains(w.ID))
                .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(queryScoreList)) {

                Set<Long> idSet = queryScoreList.stream()
                    .map(w -> w.ID)
                    .collect(Collectors.toSet());

                findRecommendPostDao.findByID(idSet, groupName, postScoreMap);

                validPostList.addAll(queryScoreList);
            }

            if (postList.size() < size) {
                break;
            }
        }

        Set<Long> toDeleteSet = new HashSet<>();

        // 判断重复内容
        checkDuplicate(validPostList, toDeleteSet);

        // 内容重复的从帖子底池删除
        removeDeletedPost(new ArrayList<>(toDeleteSet));

        // 数据封装
        for (PostInfoExt item : validPostList) {
            if (toDeleteSet.contains(item.ID)) {
                continue;
            }
            mergeData(postScoreMap, toSaveModelList, item);
        }

        // 保存
        HashMap<Integer, List<LCPostInfoModel>> subMap = CommonUtils.toSmallList(toSaveModelList, size);

        for (int i = 0; i < subMap.size(); i++) {

            List<LCPostInfoModel> subList = subMap.get(i);

            boolean flag = lcPostInfoDao.upsertBulk(subList);

            if (!flag) {
                logger.error("帖子底池表保存失败，数据：" + JacksonUtil.obj2String(toSaveModelList));
            } else {
                logger.info("全量更新-保存的数量：{}", toSaveModelList.size());
            }
        }

        logger.info("全量处理结束");
    }

    /**
     * 数据转换
     *
     * @param scoreMap
     * @param toSaveModelList
     * @param item
     */
    private void mergeAddedData(Map<Long, Long> scoreMap, List<LCPostInfoModel> toSaveModelList, LCPostInfoModel item) {

        if (item.getCreateTime() == null) {
            item.setCreateTime(item.getUpdateTime());
        }
        item.setUpdateTime(DateUtil.getNowDate());

        if (scoreMap.containsKey(item.getTid())) {
            item.setScore(scoreMap.get(item.getTid()));
        } else {
            item.setScore(defaultScore);
        }
        if (item.getSort() == null) {
            item.setSort(0L);
        }
        toSaveModelList.add(item);
    }

    /**
     * 判断重复内容
     *
     * @param validPostList
     * @param toDeleteSet
     */
    public static void checkDuplicate(List<PostInfoExt> validPostList, Set<Long> toDeleteSet) {

        Map<String, Map<String, PostInfoExt>> summaryMap = new HashMap<>();
        for (PostInfoExt item : validPostList) {
            if (StringUtils.isEmpty(item.SUMMARY)) {
                continue;
            }
            String summary = item.SUMMARY.replaceAll("[^\u4e00-\u9fa5]", "");
            if (summary.length() >= 50) {
                summary = summary.substring(0, 50);
            } else {
                continue;
            }
            Map<String, PostInfoExt> userSummaryMap = summaryMap.getOrDefault(item.UID, new HashMap<>());
            summaryMap.put(item.UID, userSummaryMap);
            if (userSummaryMap.containsKey(summary)) {
                PostInfoExt duplicateItem = userSummaryMap.get(summary);
                if (duplicateItem.TIME != null && duplicateItem.TIME.after(item.TIME)) {
                    toDeleteSet.add(item.ID);
                } else {
                    toDeleteSet.add(duplicateItem.ID);
                    userSummaryMap.put(summary, item);
                }
            } else {
                userSummaryMap.put(summary, item);
            }
        }
    }

    /**
     * 判断是否符合条件的帖子
     * 未删除、有图片或者视频、直播贴
     *
     * @param post
     * @return
     */
    public static boolean isValid(PostInfoExt post) {

        //新增知识星球内帖子
        if (post.DEL != 0 || (post.TTJJDEL != 0 && post.TTJJDEL != 4)) {
            return false;
        }

        // 直播贴过滤
        if (post.ZMTLKType != null && post.ZMTLKType == 2) {
            return false;
        }

        if (!typeSet.contains(post.TYPE)) {
            return false;
        }

        if (!StringUtils.isEmpty(post.ALLPIC)) {
            if (isValidPic(post)) {
                return true;
            }
        }

        if (!CollectionUtils.isEmpty(post.Videos)) {
            for (PostVideoInfo video : post.Videos) {
                if (!StringUtils.isEmpty(video.ZMTLKVideoID) && !StringUtils.isEmpty(video.ZMTLKCover)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 校验图片是否合法
     *
     * @param post
     * @return
     */
    private static boolean isValidPic(PostInfoExt post) {
        String[] pics = post.ALLPIC.split(",");
        if (pics.length >= 1) {
            String pic = pics[0];
            if (pic.contains("empic.dfcfw.com")) {
                // 财富号
                String[] arrs = pic.split("/");
                if (arrs.length >= 3 && arrs[arrs.length - 2].contains("w")
                    && arrs[arrs.length - 2].contains("h")) {
                    return true;
                }
            } else if (pic.contains("@!gbpic") || pic.contains("gbres.dfcfw.com") || pic.contains("gbrestest.dfcfw.com")) {
                // 股吧
                return true;
            } else if (pic.contains("webquotepic.eastmoney.com/GetPic.aspx") && pic.contains("&imageType=r&")) {
                // 分时图
                return true;
            } else if (pic.contains("webquoteklinepic.eastmoney.com/GetPic.aspx")
                && (pic.contains("&imageType=knews&") || pic.contains("&imageType=k&"))) {
                // k线图
                return true;
            }
        }
        return false;
    }

    /**
     * PostInfo表中标记删除的帖子，从帖子底池表删除
     *
     * @param toDeleteList
     * @throws JsonProcessingException
     */
    private void removeDeletedPost(List<Long> toDeleteList) throws JsonProcessingException {

        if (CollectionUtils.isEmpty(toDeleteList)) {
            return;
        }

        DeleteResult deleteResult = lcPostInfoDao.deleteByPostIds(toDeleteList);

        if (deleteResult != null && deleteResult.getDeletedCount() >= 0) {
            logger.info("帖子底池表删除的数量：{}", deleteResult.getDeletedCount());
        } else {
            logger.error("帖子底池根据帖子ID删除失败，帖子ID：" + JacksonUtil.obj2String(toDeleteList));
        }
    }

    /**
     * 获取作者列表
     *
     * @param lastUpdatetime
     * @return
     */
    private List<LCAuthorInfoModel> getValidUserList(Date lastUpdatetime) {

        List<LCAuthorInfoModel> result = new ArrayList<>();

        try {
            // 查询所有用户信息
            List<LCAuthorInfoModel> authorList = financeFindDao.findAll();

            logger.info("查询到的作者数量：" + authorList.size());

            // 已标记删除的作者帖子直接从帖子底池删除
            List<String> deletedUserList = authorList.stream()
                .filter(w -> w.getIsDel() == 1
                    && (lastUpdatetime == null || w.getUpdateTime().after(lastUpdatetime)))
                .map(w -> w.getUid())
                .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(deletedUserList)) {

                DeleteResult deleteResult = lcPostInfoDao.deleteByUsers(deletedUserList);

                if (deleteResult != null && deleteResult.getDeletedCount() >= 0) {
                    logger.info("根据通行证ID删除帖子成功，删除数量：" + deleteResult.getDeletedCount());
                } else {
                    logger.error("根据通行证ID删除帖子失败，通行证ID：" + JacksonUtil.obj2String(deletedUserList));
                }
            }

            return authorList.stream()
                .filter(w -> w.getIsDel() == 0)
                .collect(Collectors.toList());

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        return result;
    }

    private void dealLabels(List<Integer> stateList) throws Exception {
        List<LcLabelInfoDo> list = lcLabelInfoDao.listByState(LcLabelInfoDo.class, null, stateList);

        List<Map<String, Object>> toCacheList = new ArrayList<>();

        logger.info("分签处理-1，读取标签列表。数量:{}，头部列表：{}",
            list == null ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {
            toCacheList.addAll(list.stream()
                .map(a -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("labelId", a.getLabelId());
                    map.put("name", a.getName());
                    map.put("state", a.getState());
                    return map;
                })
                .collect(Collectors.toList())
            );

        }

        app.barredis.set(BarRedisKey.FINANCING_PAGE_LABEL_LIST, JSON.toJSONString(toCacheList), 7 * 24 * 3600L);

        logger.info("分签处理-2，写缓存-标签列表。数量:{}，头部列表：{}",
            toCacheList == null ? 0 : toCacheList.size(),
            CollectionUtils.isEmpty(toCacheList) ? null : JSON.toJSONStringWithDateFormat(toCacheList.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {
            List<String> labelIds = list.stream().map(a -> a.getLabelId()).collect(Collectors.toList());
            List<LcLabelAuthorRelationDo> relationList = lcLabelAuthorRelationDao.listByLabelIds(LcLabelAuthorRelationDo.class, null, labelIds);

            logger.info("分签处理-3，读取标签下作者列表。数量:{}，头部列表：{}",
                relationList == null ? 0 : relationList.size(),
                CollectionUtils.isEmpty(relationList) ? null : JSON.toJSONStringWithDateFormat(relationList.get(0), DateUtil.datePattern)
            );

            LinkedMultiValueMap<String, String> labelUidMap = new LinkedMultiValueMap<>();
            if (!CollectionUtils.isEmpty(relationList)) {
                relationList.forEach(a -> labelUidMap.add(a.getLabelId(), a.getUid()));
            }

            int i = 0;
            for (Map.Entry<String, List<String>> entry : labelUidMap.entrySet()) {
                i++;
                saveDataToRedisOfLabels(i, entry.getKey(), entry.getValue());
            }

            logger.info("分签处理-4，写缓存-标签下帖子列表-完成。数量:{}", labelUidMap.entrySet() == null ? 0 : labelUidMap.entrySet().size());
        }

    }

    /**
     * 往Redis保存数据
     */
    private void saveDataToRedisOfLabels(int i, String labelId, List<String> uids) throws Exception {

        int pageSize = 5000;
        int pageIndex = 1;

        int round = 0;
        while (true) {
            round++;

            List<LCPostInfoModel> postList = lcPostInfoDao.findAllPostByUids(pageIndex, pageSize, uids);
            if (postList == null) {
                postList = new ArrayList<>();
            }

            app.barredis.set(
                String.format(BarRedisKey.FINANCING_PAGE_POST_OF_LABEL, labelId) + pageIndex,
                JacksonUtil.obj2String(postList),
                7 * 24 * 60 * 60L
            );

            logger.info("分签处理-4，写缓存-标签下帖子列表-详情-第{}个-第{}轮。标签：{}，数量：{}，头部数据：{}",
                i,
                round,
                labelId,
                CollectionUtils.isEmpty(postList) ? 0 : postList.size(),
                CollectionUtils.isEmpty(postList) ? null : JSON.toJSONStringWithDateFormat(postList.get(0), DateUtil.datePattern)
            );

            if (CollectionUtils.isEmpty(postList) || postList.size() < pageSize) {
                break;
            }

            pageIndex++;
        }

    }

}
