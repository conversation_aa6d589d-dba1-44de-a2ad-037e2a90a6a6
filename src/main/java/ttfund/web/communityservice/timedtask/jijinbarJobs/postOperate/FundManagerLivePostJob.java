package ttfund.web.communityservice.timedtask.jijinbarJobs.postOperate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.*;
import ttfund.web.communityservice.bean.jijinBar.post.caifuhao.BigShotsShowUserModel;
import ttfund.web.communityservice.bean.jijinBar.post.caifuhao.CFHLiveBroadcastForPostModel;
import ttfund.web.communityservice.bean.jijinBar.post.caifuhao.FundManagerLivePostResultModel;
import ttfund.web.communityservice.bean.jijinBar.post.fundManager.ManagerIdAndPassportIdModel;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.GetChannelInfoApiResult;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.BigShotsShowUserDao;
import ttfund.web.communityservice.dao.mongo.CFHLiveBroadcastDao;
import ttfund.web.communityservice.dao.mongo.PassportFundMrgDao;
import ttfund.web.communityservice.dao.msyql.FundManagerLivePostResultDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.service.LangKeLvbCooperationApiServiceImpl;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;
import ttfund.web.communityservice.utils.StringUtil;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基金经理直播自动发帖job
 */
@JobHandler("FundManagerLivePostJob")
@Component
public class FundManagerLivePostJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(FundManagerLivePostJob.class);

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private CFHLiveBroadcastDao cfhLiveBroadcastDao;

    @Autowired
    private FundManagerLivePostResultDao fundManagerLivePostResultDao;

    @Autowired
    private BigShotsShowUserDao bigShotsShowUserDao;

    @Autowired
    private PassportFundMrgDao passportFundMrgDao;

    @Autowired
    private LangKeLvbCooperationApiServiceImpl langKeLvbCooperationApiService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        String logPre = DateUtil.dateToStr(new Date(), "yyyy-MM-dd_HH:mm:ss") + ":";
        try {
            //第零步，初始化断点
            if (StringUtils.hasLength(s)) {
                DateUtil.strToDate(s, "yyyy-MM-dd HH:mm:ss.SSS");
                userRedisDao.set(UserRedisConfig.FUND_MANAGER_LIVE_POST_JOB_BREAKPOINT, s, 3600 * 24 * 90L);
                logger.info(logPre + "第零步，初始化断点。breakpoint:" + s);
                return ReturnT.SUCCESS;
            }

            //第一步，读取断点
            String breakpoint = userRedisDao.get(UserRedisConfig.FUND_MANAGER_LIVE_POST_JOB_BREAKPOINT);
            logger.info(logPre + "第一步，读取断点。breakpoint:" + breakpoint);
            if (!StringUtils.hasLength(breakpoint)) {
                logger.error(logPre + "读取断点为空。breakpoint:" + breakpoint);
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-24));
                logger.error(logPre + "采用默认断点。breakpoint:" + breakpoint);
            }

            //第二步，增量读取直播配置
            List<CFHLiveBroadcastForPostModel> list = cfhLiveBroadcastDao.getByUpdateTime(DateUtil.strToDate(breakpoint, "yyyy-MM-dd HH:mm:ss.SSS"));
            logger.info(logPre + "第二步，增量读取直播配置。数量:" + list.size() + ",breakpoint:" + breakpoint);

            //第三步，对配置进行去重
            if (!CollectionUtils.isEmpty(list)) {
                list = distinctLiveBroadcast(list);
            }
            logger.info(logPre + "第三步，对配置进行去重。去重后数量:" + list.size());

            //第四步，读取直播配置的配置信息是否满足发帖条件。基金产品、基金经理、直播配置及状态、直播是否已结束及回放房间号
            //a.该场直播已授权发帖：TB_CFHLiveBroadcast表里IsCreatePlaybackPost为1
            //b.直播间关联到该基金经理的通行证id（即主播=关联自己的通行证id的基金经理）：
            //  UserId在TB_BigShotsShowUser表里的类型是基金经理，且在TB_BigShotsShowUser表里绑定的通行证ID（UID）和基金经理本身绑定的通行证id是一致的
            //c.该场直播已结束且未被隐藏：IsHide为0且该场直播生成回放完成(TB_CFHLiveBroadcast表里RoadShowDetail.status =3且调接口验证生成回放完成)
            Map<String, BigShotsShowUserModel> fundManagersMap = new HashMap<>();
            Map<String, String> managerBindInfoMap = new HashMap<>();
            List<String> userIdList = list.stream().map(item -> item.getUserId()).collect(Collectors.toList());
            List<BigShotsShowUserModel> fundManagers = bigShotsShowUserDao.getByUserIdList(userIdList);
            for (BigShotsShowUserModel item : fundManagers) {
                fundManagersMap.put(item.getUserId(), item);
            }
            List<String> fundManagerIdList = fundManagers.stream().map(item -> item.getManagerId())
                    .filter(item -> StringUtils.hasLength(item)).collect(Collectors.toList());
            List<ManagerIdAndPassportIdModel> managerBindInfoList = passportFundMrgDao.getBindRelationByManagerIdList(fundManagerIdList);
            for (ManagerIdAndPassportIdModel item : managerBindInfoList) {
                if (StringUtils.hasLength(item.getPassportId())) {
                    managerBindInfoMap.put(item.getManagerId(), item.getPassportId());
                }
            }
            logger.info(logPre + "第四步，读取直播配置的配置信息是否满足发帖条件。");

            //第五步，进行发帖
            boolean isPause = false;//标识在遍历过程中是否需要将断点暂停
            List<FundManagerLivePostResultModel> insertToDbList = new ArrayList<>();
            boolean preBool = false;
            boolean aBool = false;
            boolean bBool = false;
            boolean cBool = false;
            if (!CollectionUtils.isEmpty(list)) {
                int num = 0;//仅记录日志用
                for (CFHLiveBroadcastForPostModel item : list) {
                    preBool = (item.getStatus() != null && item.getStatus() == 3)//前置条件
                            && (item.getIsDelete() != null && item.getIsDelete() == 0);//前置条件
                    aBool = (item.getIsCreatePlaybackPost() != null && item.getIsCreatePlaybackPost() == 1);
                    bBool = (fundManagersMap.get(item.getUserId()) != null
                            && StringUtils.hasLength(fundManagersMap.get(item.getUserId()).getUid())
                            && Objects.equals(fundManagersMap.get(item.getUserId()).getUid()
                            , managerBindInfoMap.get(fundManagersMap.get(item.getUserId()).getManagerId())));
                    cBool = (item.getIsHide() != null && item.getIsHide() == 0
                            && item.getRoadShowDetail() != null && item.getRoadShowDetail().getStatus() != null
                            && item.getRoadShowDetail().getStatus() == 3 && isFinishPlayback(item.getRoomNumber()));
                    if (preBool && aBool && bBool && cBool) {
                        FundManagerLivePostResultModel model = uploadPost(item.getUserId(), fundManagersMap.get(item.getUserId()).getManagerId()
                                , fundManagersMap.get(item.getUserId()).getUid(), fundManagersMap.get(item.getUserId()).getUserName(), item);
                        insertToDbList.add(model);
                        if (!isPause) {
                            breakpoint = DateUtil.dateToStr(item.getUpdateTime(), "yyyy-MM-dd HH:mm:ss.SSS");
                        }
                    } else {
                        if (item.getEndTime() != null) {
                            if (new Date().compareTo(DateUtil.calendarDateByHour(item.getEndTime(), 4)) < 0) {
                                if (!isPause) {
                                    isPause = true;
                                }
                            } else {
                                FundManagerLivePostResultModel model = new FundManagerLivePostResultModel();
                                model.setId(item.get_id());
                                model.setState("3");
                                model.setUtime(new Date());
                                insertToDbList.add(model);
                            }
                        }
                    }
                    logger.info(logPre + "第五步详情-num：{}，配置id：{}，preBool：{}，aBool：{}，bBool：{}，cBool：{}"
                            , ++num, item.get_id(), preBool, aBool, bBool, cBool);
                }
            }
            logger.info(logPre + "第五步，进行发帖。处理总数量：" + list.size());

            //第六步，处理发帖结果及落库
            fundManagerLivePostResultDao.insertMany(insertToDbList);
            logger.info(logPre + "第六步，处理发帖结果及落库。数量：" + insertToDbList.size());

            //第七步，更新断点
            userRedisDao.set(UserRedisConfig.FUND_MANAGER_LIVE_POST_JOB_BREAKPOINT, breakpoint, 3600 * 24 * 90L);
            logger.info(logPre + "第七步，更新断点。断点：" + breakpoint + ",isPause：" + isPause);

        } catch (Exception ex) {
            logger.error("服务执行报错：" + ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 判断直播间是否生成回放完成
     *
     * <AUTHOR>
     */
    private boolean isFinishPlayback(String channelId) {
        boolean result = false;
        if (!StringUtil.isNull(channelId)) {
            GetChannelInfoApiResult res = langKeLvbCooperationApiService.getChannelDetailReturnResponse(channelId);
            if (res != null && res.getResult() == 1 && res.getData() != null
                    && res.getData().getId() > 0 && res.getData().getRecord_hls() != null) {
                //先判断是否有回放视频，如果有回放视频则设置回放视频地址
                if (res.getData().getRecord_hls().getVideo_files() != null &&
                        res.getData().getRecord_hls().getVideo_files().size() > 0) {
                    /*回放*/
                    result = true;
                }
            }
        }
        return result;
    }


    /**
     * 直播配置进行去重
     *
     * <AUTHOR>
     */
    private List<CFHLiveBroadcastForPostModel> distinctLiveBroadcast(List<CFHLiveBroadcastForPostModel> dataList) {
        List<CFHLiveBroadcastForPostModel> result = dataList;
        if (!CollectionUtils.isEmpty(dataList)) {
            List<String> idList = dataList.stream().map(item -> item.get_id()).collect(Collectors.toList());
            List<String> idListInDb = fundManagerLivePostResultDao.getByIdList(idList);
            result = dataList.stream().filter(item -> !idListInDb.contains(item.get_id())).collect(Collectors.toList());
        }
        return result;
    }

    /**
     * 调财富号接口发帖
     *
     * <AUTHOR>
     */
    public FundManagerLivePostResultModel uploadPost(String userid, String managerid, String passportid, String userName, CFHLiveBroadcastForPostModel model) throws Exception {

        FundManagerLivePostResultModel result = new FundManagerLivePostResultModel();
        String url = appConstant.caiFuHaoApiAddress + "/api/v1/audit/article/PublishArticlebygb";
        AddArticleRequest request = new AddArticleRequest();
        request.Title = "【直播回放】" + model.getTitle();
        //request.ListImage = "https://emav-test-1252033264.cos.ap-chongqing.myqcloud.com/roadshow_cover/channel/198191/198191_w960h432.jpg";

        //跟前端和产品约定了，在视频标签里设置标识值来区分跳直播间。标识值:LKFUND_fund_manager_live_playback_post_e5237a9c44ca82abe8fc3512ade5203_直播配置id
        String pattern = "<div class='cfh_lkvideo_tag' data-channelid='{0}' data-name='LKFUND_fund_manager_live_playback_post_e5237a9c44ca82abe8fc3512ade52036_{1}' data-cover='{2}' data-time='{3}' data-watch='0' data-zan='0' data-islive='0' data-duration='' data-size='' alt='{4}' ></div>" +
                "<i class='cfhlink_url langkezhibo' artcode='' roomtype='2' channelid='{5}' livetheme='' data-type='zbl'></i>";
        StringBuilder builder = new StringBuilder();
        builder.append("直播内容介绍：\n");
        builder.append(model.getPostContent() + "\n");
        builder.append("直播时间：" + DateUtil.dateToStr(model.getStartTime(), "yyyy-MM-dd HH:mm"));
        builder.append("到" + DateUtil.dateToStr(model.getEndTime(), "yyyy-MM-dd HH:mm") + "\n");
        builder.append("本场直播主讲人：" + userName + "\n");
        builder.append("直播内容仅供参考，不构成投资建议。");
        builder.append(MessageFormat.format(pattern.replace("'", "''"), model.getRoomNumber(),
                model.get_id(), model.getGubaShareImg(), "", "", model.getRoomNumber()));
        request.Content = builder.toString();
        request.Ordertime = new Date();
        request.Showtime = new Date();
        request.ColumnIds = "2";
        //request.Ip = "*************";
        request.IsSimpleVideo = 0;
        ArticleVideo video = new ArticleVideo();
        video.VideoSize = 0;
        video.VideoWith = 0;
        video.VideoImageUrl = model.getGubaShareImg();
        //video.VideoSrc = "http://1500000597.vod2.myqcloud.com/a274ebacvodtranssh1500000597/c3619a93243791578609401570/v.f230.m3u8";
        video.VideoHigh = 0;
        //video.VideoTitle = "客户端小视频-230114-02";
        video.VideoTime = "";
        video.ZhiboId = Integer.parseInt(model.getRoomNumber());
        video.VideoType = 1;
        request.Videos = Arrays.asList(video);
        //request.IsOriginal = 0;
        //request.GubaTalkId = null;
        //request.IsVistor = 0;
        request.PassportUid = passportid;
        //request.ImportType = 0;
        request.Port = "80";
        request.ArticleType = 1;
        request.DeviceId = "8C67557BE560F5454573A4F9CDF472F6";
        request.Version = "200";
        request.Product = "CFH";
        request.Plat = "web";
        request.BusinessId = "2fb6a93b-cf6f-4d49-a0ab-44006b04c4da";
        //源头标识，这个必传  LKFUND:表示基金端
        request.PlatFrom = "LKFUND";
        //request.CatalogPattern = 0;
        request.IsComment = 1;
        request.IsReCommendRead = 0;
        //request.ArticleTag = 0;
        request.Categories = "1,2";
        request.Tabs = "1,2";
        //request.DomainLab = null;
        //request.Columns = new ArrayList<>();
        request.ZMTLKVideoID = model.getRoomNumber();
        request.RoomType = 2;
        request.IsSetTop = 0;
        request.OperaterName = "";
        request.Stocks = new ArrayList<>();
        List<String> fundCodes = null;
        if (!CollectionUtils.isEmpty(model.getLiveFundModel())) {
            fundCodes = model.getLiveFundModel().stream().filter(o -> o.getProductType() != null && o.getProductType() == 0)
                    .map(o -> o.getfCode()).limit(3).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(fundCodes)) {
            String path = appConstant.quotationCodeTableUrl;
            for (String fundCode : fundCodes) {
                String apiRes = HttpHelper.requestGet(String.format(path, fundCode));
                if (StringUtils.hasLength(apiRes)) {
                    QuotationCodeTableResponse response = JacksonUtil.string2Obj(apiRes, QuotationCodeTableResponse.class);
                    if (response != null && response.getQuotationCodeTable() != null && !CollectionUtils.isEmpty(response.getQuotationCodeTable().getData())) {
                        QuotationCodeTableItemModel fund = response.getQuotationCodeTable().getData().get(0);
                        ArticleStock stock = new ArticleStock();
                        stock.StockCode = fund.getCode();
                        stock.StockName = fund.getName();
                        stock.Type = 0;
                        stock.MarketType = fund.getMktNum();
                        stock.SecurityType = fund.getSecurityType();
                        stock.MarketCode = fund.getMktNum();
                        stock.InnerCode = fund.getInnerCode();
                        request.Stocks.add(stock);
                    }
                }
            }
        }

        String html = HttpHelper.requestPostJson(url, JacksonUtil.obj2String(request), 15000);
        if (StringUtils.hasLength(html)) {
            JSONObject res = JSON.parseObject(html);
            if (res != null && res.getInteger("rc") == 1 && res.getJSONObject("main_post") != null
                    && res.getJSONObject("main_post").getInteger("post_id") != null) {
                result.setState("1");
                result.setPostid(String.valueOf(res.getJSONObject("main_post").getInteger("post_id")));
            } else {
                result.setState("2");
                if (res != null && StringUtils.hasLength(res.getString("me"))) {
                    result.setRemark(res.getString("me").length() > 40 ? res.getString("me").substring(0, 40) : res.getString("me"));
                } else {
                    result.setRemark("返回发帖失败");
                }
            }
        } else {
            result.setState("2");
            result.setRemark("返回null");
        }
        if (!CollectionUtils.isEmpty(fundCodes)) {
            result.setCodelist(String.join(",", fundCodes));
        }
        result.setId(model.get_id());
        result.setUserid(userid);
        result.setManagerid(managerid);
        result.setPassportid(passportid);
        result.setUtime(new Date());
        return result;
    }


    /**
     * 调财富号接口发帖-测试用
     *
     * <AUTHOR>
     */
    public void uploadPost2() throws Exception {

        String url = appConstant.caiFuHaoApiAddress + "/api/v1/audit/article/PublishArticlebygb";
        AddArticleRequest request = new AddArticleRequest();
        request.Title = "zhangshunxi测试基金经理发直播贴-uploadPost2()，时间:" + DateUtil.dateToStr(new Date());
        //request.ListImage = "https://emav-test-1252033264.cos.ap-chongqing.myqcloud.com/roadshow_cover/channel/198191/198191_w960h432.jpg";
        String pattern = "<div class='cfh_lkvideo_tag' data-channelid='{0}' data-name='{1}' data-cover='{2}' data-time='{3}' data-watch='0' data-zan='0' data-islive='0' data-duration='' data-size='' alt='{4}' ></div>" +
                "<i class='cfhlink_url langkezhibo' artcode='' roomtype='2' channelid='{5}' livetheme='' data-type='zbl'></i>";

        request.Content = MessageFormat.format(pattern.replace("'", "''"), "198191", "", "https://emav-test-1252033264.cos.ap-chongqing.myqcloud.com/roadshow_cover/channel/198191/198191_w960h432.jpg", "", ""
                , "198191");
        request.Ordertime = new Date();
        request.Showtime = new Date();
        request.ColumnIds = "2";
        //request.Ip = "*************";
        request.IsSimpleVideo = 0;
        ArticleVideo video = new ArticleVideo();
        video.VideoSize = 0;
        video.VideoWith = 0;
        video.VideoImageUrl = "https://emav-test-1252033264.cos.ap-chongqing.myqcloud.com/roadshow_cover/channel/198191/198191_w960h432.jpg";
        //video.VideoSrc = "http://1500000597.vod2.myqcloud.com/a274ebacvodtranssh1500000597/c3619a93243791578609401570/v.f230.m3u8";
        video.VideoHigh = 0;
        //video.VideoTitle = "客户端小视频-230114-02";
        video.VideoTime = "";
        video.ZhiboId = 198191;
        video.VideoType = 1;
        request.Videos = Arrays.asList(video);
        //request.IsOriginal = 0;
        //request.GubaTalkId = null;
        //request.IsVistor = 0;
        request.PassportUid = "9513336252103556";
        //request.ImportType = 0;
        request.Port = "80";
        request.ArticleType = 1;
        request.DeviceId = "8C67557BE560F5454573A4F9CDF472F6";
        request.Version = "200";
        request.Product = "CFH";
        request.Plat = "web";
        request.BusinessId = "2fb6a93b-cf6f-4d49-a0ab-44006b04c4da";
        request.PlatFrom = "LKFUND";
        //request.CatalogPattern = 0;
        request.IsComment = 1;
        request.IsReCommendRead = 0;
        //request.ArticleTag = 0;
        request.Categories = "1,2";
        request.Tabs = "1,2";
        //request.DomainLab = null;
        //request.Columns = new ArrayList<>();
        request.ZMTLKVideoID = "198191";
        request.RoomType = 2;
        request.IsSetTop = 0;
        request.OperaterName = "zhangshunxi";
        ArticleStock stock = new ArticleStock();
        stock.StockCode = "260104";
        stock.StockName = "景顺长城内需增长混合";
        stock.Type = 0;
        stock.MarketType = "150";
        stock.SecurityType = "17";
        stock.MarketCode = "150";
        stock.InnerCode = "37868513187537";
        ArticleStock stock1 = new ArticleStock();
        stock1.StockCode = "260108";
        stock1.StockName = "景顺长城新兴成长混合A";
        stock1.Type = 0;
        stock1.MarketType = "150";
        stock1.SecurityType = "17";
        stock1.MarketCode = "150";
        stock1.InnerCode = "49893924629585";
        request.Stocks = Arrays.asList(stock, stock1);

        //request.Stocks = new ArrayList<>();
        List<String> fundCodes = null;
        /*if (!CollectionUtils.isEmpty(model.getLiveFundModel())) {
            fundCodes = model.getLiveFundModel().stream().filter(o -> o.getProductType() != null && o.getProductType() == 0)
                    .map(o -> o.getfCode()).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(fundCodes)) {
            String path = appConstant.quotationCodeTableUrl;
            for (String fundCode : fundCodes) {
                String apiRes = HttpHelper.requestGet(String.format(path, fundCode));
                if (StringUtils.hasLength(apiRes)) {
                    QuotationCodeTableResponse response = JacksonUtil.string2Obj(apiRes, QuotationCodeTableResponse.class);
                    if (response != null && response.getQuotationCodeTable() != null && !CollectionUtils.isEmpty(response.getQuotationCodeTable().getData())) {
                        QuotationCodeTableItemModel fund = response.getQuotationCodeTable().getData().get(0);
                        ArticleStock stock = new ArticleStock();
                        stock.StockCode = fund.getCode();
                        stock.StockName = fund.getName();
                        stock.Type = 0;
                        stock.MarketType = fund.getMktNum();
                        stock.SecurityType = fund.getSecurityType();
                        stock.MarketCode = fund.getMktNum();
                        stock.InnerCode = fund.getInnerCode();
                        request.Stocks.add(stock);
                    }
                }
            }
        }*/

        String html = HttpHelper.requestPostJson(url, JacksonUtil.obj2String(request), 15000);
        logger.info(html);
    }
}
