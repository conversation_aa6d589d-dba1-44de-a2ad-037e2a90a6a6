package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.AnswerDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.text.MessageFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 基金吧问答，采纳统计
 */
@JobHandler(value = "QAAnswerAcceptBizJob")
@Component
public class QAAnswerAcceptBizJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(QAAnswerAcceptBizJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private AnswerDao answerDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws JsonProcessingException {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 1000;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.QAANSWERACCEPTBIZJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            answerAcceptConutSyncProcess(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }


        return ReturnT.SUCCESS;
    }

    private void answerAcceptConutSyncProcess(int batchReadCount) {

        String cacheKey = null;

        try {
            String breakpointName = UserRedisConfig.QAANSWERACCEPTBIZJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("第一步，读取断点。断点:{}", breakpoint);

            int round = 0;
            while (true) {

                round++;

                List<Map<String, Object>> list = answerDao.getAcceptCount(breakpointDate, batchReadCount);

                logger.info("第二步，读取数据-第{}轮。数量:{}，头部数据：{}",
                        round,
                        CollectionUtils.isEmpty(list) ? 0 : list.size(),
                        CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
                );

                if (!CollectionUtils.isEmpty(list)) {

                    breakpointDate = list.stream().map(a -> (Date) a.get("UpdateTime")).max(Date::compareTo).get();

                    int i = 0;
                    for (Map<String, Object> item : list) {
                        i++;

                        cacheKey = MessageFormat.format(BarRedisKey.QA_ACCEPT_COUNT, item.get("QID").toString());
                        Boolean flag = app.barredis.set(cacheKey, item.get("AcceptCount").toString());

                        if (!flag) {
                            logger.error("写redis失败。key：{}，value：{}", cacheKey, item.get("AcceptCount").toString());
                        }

                        logger.info("第三步，写redis详情-第{}轮。第{}/{}个，提问id：{}，值：{}",
                                round,
                                i,
                                list.size(),
                                item.get("QID").toString(),
                                item.get("AcceptCount").toString()
                        );
                    }

                }

                logger.info("第三步，写redis完成-第{}轮。数量:{}，头部数据：{}",
                        round,
                        CollectionUtils.isEmpty(list) ? 0 : list.size(),
                        CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
                );

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("第四步，更新断点-第{}轮。断点：{}", round, breakpoint);

                if (list == null || list.size() < batchReadCount) {
                    break;
                }
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }

}
