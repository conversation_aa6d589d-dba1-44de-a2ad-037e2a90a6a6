package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.HotPostExpressDao;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 热帖速递服务
 * 取出社区发现页指定分组的帖子，过滤无图帖后写入Redis
 */
@JobHandler(value = "HotPostExpressJob")
@Component
public class HotPostExpressJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(HotPostExpressJob.class);

    @Autowired
    private HotPostExpressDao hotPostExpressDao;

    @Value("${express.max.size}")
    private String expressMaxSize;

    @Autowired
    private App app;

    public ReturnT<String> execute(String param) {

        long start = System.currentTimeMillis();
        List<String> codeList = new ArrayList<>();
        Long timePoint = null;
        Long findScore = null;

        try {

            outer:
            while (codeList.size() < Integer.parseInt(expressMaxSize)) {

                List<Map> recommandPost = hotPostExpressDao.getRecommandPost(findScore, timePoint);

                if (CollectionUtils.isEmpty(recommandPost)) {
                    break;
                }

                List<String> postList = recommandPost.stream()
                        .map(item -> item.get("_id").toString())
                        .collect(Collectors.toList());

                List<Map> picPost = hotPostExpressDao.getPicPost(postList);

                Set<String> picSet = picPost.stream()
                        .map(item -> item.get("_id").toString())
                        .collect(Collectors.toSet());

                for (String postId : postList) {
                    if (picSet.contains(postId)) {
                        codeList.add(postId);
                        if (codeList.size() >= Integer.parseInt(expressMaxSize)) {
                            break outer;
                        }
                    }
                }

                timePoint = Long.parseLong(recommandPost.get(recommandPost.size() - 1).get("TIMEPOINT").toString());
                findScore = Long.parseLong(recommandPost.get(recommandPost.size() - 1).get("FINDSCORE").toString());
            }

            app.barredis.set(BarRedisKey.HOT_POST_EXPRESS, JacksonUtil.obj2String(codeList), 7 * 24 * 60 * 60L);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            XxlJobLogger.log(ex.getMessage());
        }

        logger.info("【热帖速递服务】本轮执行耗时：" + ((System.currentTimeMillis() - start) / 1000) + "秒");
        XxlJobLogger.log("【热帖速递服务】本轮执行耗时：" + ((System.currentTimeMillis() - start) / 1000) + "秒");
        return ReturnT.SUCCESS;

    }
}
