package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.producer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoForMysqlModel;
import ttfund.web.communityservice.bean.jijinBar.post.data.BigDataPostPushModel;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.PostInfoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 大数据帖子推送
 */
@JobHandler("BigDataPostPushFundJob")
@Component
public class BigDataPostPushFundJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(BigDataPostPushFundJob.class);

    @Autowired
    private PostInfoDao postInfoDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Qualifier(KafkaConfig.fundbar_kafka_temp_beanname)
    @Autowired()
    private KafkaTemplate<String, String> kafkaTemplate;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount
            );

            if (StringUtils.hasLength(initBreakpoint)) {
                //验证传入断点格式
                DateUtil.strToDate(initBreakpoint);
                userRedisDao.set(UserRedisConfig.BIGDATAPOSTPUSHFUNDJOB_BREAKPOINT, initBreakpoint, 3600 * 24 * 90L);

                logger.info("第零步：初始化断点。初始化断点值：" + initBreakpoint);

                return ReturnT.SUCCESS;
            }

            pushData(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void pushData(int batchReadCount) throws Exception {

        String breakpointName = UserRedisConfig.BIGDATAPOSTPUSHFUNDJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);

        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("第一步，读取断点。断点:{}", breakpoint);

        int round = 0;
        while (true) {
            round++;

            //获取需要推送帖子列表
            List<PostInfoForMysqlModel> list = postInfoDao.getList(breakpointDate, batchReadCount);

            logger.info("第二步-读取mysql-轮次{}。数量：{}，头部数据：{}",
                    round,
                    list == null ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

            List<BigDataPostPushModel> modelList = null;
            if (!CollectionUtils.isEmpty(list)) {

                breakpointDate = list.stream().max((Comparator.comparing(o -> o.UPDATETIME))).get().UPDATETIME;

                modelList = new ArrayList<>(list.size());
                for (PostInfoForMysqlModel a : list) {
                    BigDataPostPushModel model = new BigDataPostPushModel();
                    model.CODE = a.CODE;
                    model.CONTENT = a.CONTENT;
                    model.ID = a.ID;
                    model.TIME = a.TIME;
                    model.TITLE = a.TITLE;
                    model.PASSPORTID = a.UID;
                    model.UPDATETIME = a.UPDATETIME;

                    modelList.add(model);
                }
            }

            if (!CollectionUtils.isEmpty(modelList)) {
                for (BigDataPostPushModel a : modelList) {
                    try {
                        kafkaTemplate.send(KafkaTopicName.FUND_POSTINFO_BIGDATA, JSON.toJSONStringWithDateFormat(a, "yyyy/M/d HH:mm:ss"));
                    } catch (Exception ex) {
                        logger.error(ex.getMessage(), ex);
                    }
                }

            }

            logger.info("第三步-发送kafka-轮次{}。数量：{}，头部数据：{}",
                    round,
                    modelList == null ? 0 : modelList.size(),
                    CollectionUtils.isEmpty(modelList) ? null : JSON.toJSONStringWithDateFormat(modelList.get(0), DateUtil.datePattern)
            );

            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

            logger.info("第四步-更新断点-轮次{}。断点：{}", round, breakpoint);

            if (list == null || list.size() < batchReadCount) {
                break;
            }
        }

    }
}
