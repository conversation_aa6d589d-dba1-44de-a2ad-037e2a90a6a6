package ttfund.web.communityservice.timedtask.jijinbarJobs.circle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.CirclePostRelation;
import ttfund.web.communityservice.bean.jijinBar.data.CirclePostRelationForSimple;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.CirclePostRelationMysqlDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 圈子帖子全部job
 * 备注：    需求 #678045 【基金吧6.15】新增圈子功能
 */
@Slf4j
@JobHandler("CirclePostAllJob")
@Component
public class CirclePostAllJob extends IJobHandler {

    @Autowired
    private App app;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private CirclePostRelationMysqlDao circlePostRelationMysqlDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            Integer limit = null;
            Long expire = null;
            String hideCircleIds = null;
            Boolean runAll = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                limit = jsonObject.getInteger("limit");
                expire = jsonObject.getLong("expire");
                hideCircleIds = jsonObject.getString("hideCircleIds");
                runAll = jsonObject.getBoolean("runAll");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            if (limit == null) {
                limit = 500;
            }

            if (expire == null) {
                expire = 365 * 24 * 3600L;
            }

            if (runAll == null) {
                runAll = false;
            }

            log.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，limit：{}，expire：{}，hideCircleIds：{}，runAll：{}",
                initBreakpoint,
                batchReadCount,
                limit,
                expire,
                hideCircleIds,
                runAll
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.CIRCLEPOSTALLJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            if (runAll) {
                dealByAll(limit, expire, CommonUtils.toList(hideCircleIds, ","));
            } else {
                deal(batchReadCount, limit, expire, CommonUtils.toList(hideCircleIds, ","));
            }

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount, Integer limit, Long expire, List<String> hideCircleIds) {

        String breakpointName = UserRedisConfig.CIRCLEPOSTALLJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            log.error("deal-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("deal-第一步，读取断点。断点:{}", breakpoint);


        List<CirclePostRelation> list = circlePostRelationMysqlDao.getList(breakpointDate, batchReadCount);

        log.info("deal-第二步，读取圈子帖子。数量:{}，头部列表：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = list.get(list.size() - 1).getUpdateTime();

            List<Map<String, Object>> newList = doDeal(false, list, limit, expire, hideCircleIds);

            log.info("deal-第三步，处理全部帖子列表。数量：{}，头部数据：{}",
                CollectionUtils.isEmpty(newList) ? 0 : newList.size(),
                CollectionUtils.isEmpty(newList) ? null : JSON.toJSONStringWithDateFormat(newList.get(0), DateUtil.datePattern)
            );

        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        log.info("deal-第四步，更新断点。断点：{}", breakpoint);

    }

    private void dealByAll(Integer limit, Long expire, List<String> hideCircleIds) {

        log.info("dealByAll-第一步，打印参数。expire：{}，limit：{}，",
            expire,
            limit
        );

        List<CirclePostRelation> list = circlePostRelationMysqlDao.getTagListByAll(limit * 5);

        log.info("dealByAll-第二步，读取圈子帖子。数量:{}，头部列表：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            List<Map<String, Object>> newList = doDeal(true, list, limit, expire, hideCircleIds);

            log.info("dealByAll-第三步，写redis。数量:{}，头部数据：{}",
                CollectionUtils.isEmpty(newList) ? 0 : newList.size(),
                CollectionUtils.isEmpty(newList) ? null : JSON.toJSONStringWithDateFormat(newList.get(0), DateUtil.datePattern)
            );
        }
    }

    private List<Map<String, Object>> doDeal(boolean runAll, List<CirclePostRelation> list, int limit, Long expire, List<String> hideCircleIds) {

        Map<String, Map<String, Object>> distinctMapForAll = new HashMap<>();
        Map<String, Map<String, Object>> distinctMapForTag = new HashMap<>();
        Map<String, Object> map = null;
        List<CirclePostRelationForSimple> circleList = null;
        final String FIELD_CIRCLELIST = "circleList";

        /**
         * 处理全部签
         */
        String key = BarRedisKey.CIRCLE_POST_LIST_ALL;
        String value = null;
        if (!runAll) {
            value = app.barredis.get(key);
        }

        if (StringUtils.hasLength(value)) {
            List<JSONObject> jsonArray = JSON.parseArray(value, JSONObject.class);
            jsonArray.forEach(a -> {
                List<CirclePostRelationForSimple> tempList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(a.getJSONArray(FIELD_CIRCLELIST))) {
                    tempList.addAll(JSON.parseArray(JSON.toJSONString(a.getJSONArray(FIELD_CIRCLELIST)), CirclePostRelationForSimple.class));
                }
                a.put(FIELD_CIRCLELIST, tempList);
            });
            jsonArray.forEach(a -> distinctMapForAll.put(a.getString("_id"), a));
        }

        for (CirclePostRelation a : list) {
            map = distinctMapForAll.get(a.getPostId());
            if (!(a.getPostTimepoint() != null
                && a.getState() == 0
                && a.getIsDel() == 0
                && a.getPostDel() == 0
                && a.getPostTtjjdel() == 0)
            ) {
                if (map != null) {
                    circleList = (List<CirclePostRelationForSimple>)map.get(FIELD_CIRCLELIST);
                    if (!CollectionUtils.isEmpty(circleList)) {
                        for (CirclePostRelationForSimple o : circleList) {
                            if (Objects.equals(o.getCircleId(), a.getCircleId())) {
                                o.setIsDel(1);
                            }
                        }
                    }
                }

                continue;
            }

            if (map == null) {
                map = new HashMap<>();
                map.put("_id", a.getPostId());
                map.put(FIELD_CIRCLELIST, new ArrayList<CirclePostRelationForSimple>());
                distinctMapForAll.put((String)map.get("_id"), map);
            }

            map.put("TIMEPOINT", a.getPostTimepoint());
            map.put("DEL", a.getPostDel());
            map.put("TTJJDEL", a.getPostDel());

            circleList = (List<CirclePostRelationForSimple>)map.get(FIELD_CIRCLELIST);
            CirclePostRelationForSimple tempCircle = circleList.stream().filter(o -> Objects.equals(a.getCircleId(), o.getCircleId())).findFirst().orElse(null);
            if (tempCircle == null) {
                circleList.add(new CirclePostRelationForSimple(a.getCircleId(), a.getJoinTime(), a.getTag(), 0));
            } else {
                tempCircle.setJoinTime(a.getJoinTime());
                tempCircle.setTag(a.getTag());
            }
        }

        List<Map<String, Object>> newList = distinctMapForAll.values().stream().collect(Collectors.toList());

        for (Map<String, Object> a : newList) {
            circleList = (List<CirclePostRelationForSimple>)a.get(FIELD_CIRCLELIST);
            if (!CollectionUtils.isEmpty(circleList)) {
                circleList = circleList.stream()
                    .filter(o -> Objects.equals(o.getIsDel(), 0))
                    .sorted((o1, o2) -> {
                        Integer tag1 = o1.getTag();
                        Integer tag2 = o2.getTag();
                        if (Objects.equals(tag1, tag2)) {
                            return o1.getJoinTime().compareTo(o2.getJoinTime());
                        } else if (Objects.equals(tag1, 1)) {
                            return -1;
                        } else {
                            return 1;
                        }

                    }).collect(Collectors.toList());
                a.put(FIELD_CIRCLELIST, circleList);
            }
        }

        List<Map<String, Object>> allResultList = newList.stream()
            .filter(a -> !CollectionUtils.isEmpty((List<CirclePostRelationForSimple>)a.get(FIELD_CIRCLELIST)))
            .filter(a -> CollectionUtils.isEmpty(hideCircleIds) ||
                !hideCircleIds.containsAll(((List<CirclePostRelationForSimple>)a.get(FIELD_CIRCLELIST)).stream().map(o -> o.getCircleId()).collect(Collectors.toList())))
            .sorted(((o1, o2) -> Long.compare(Long.parseLong(o2.get("TIMEPOINT").toString()), Long.parseLong(o1.get("TIMEPOINT").toString()))))
            .limit(limit)
            .collect(Collectors.toList());

        app.barredis.set(key, JSON.toJSONStringWithDateFormat(allResultList, DateUtil.datePattern), expire);

        /**
         * 处理精华签
         */
        //跟后台约定过   取消精华：1变0
        Set<String> tagChangePostIds = list.stream().filter(a -> a.getTag() != null).map(a -> a.getPostId()).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(tagChangePostIds)) {
            key = BarRedisKey.CIRCLE_POST_LIST_TAG_ALL;
            value = null;
            if (!runAll) {
                value = app.barredis.get(key);
            }

            if (StringUtils.hasLength(value)) {
                List<JSONObject> jsonArray = JSON.parseArray(value, JSONObject.class);
                jsonArray.forEach(a -> {
                    List<CirclePostRelationForSimple> tempList = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(a.getJSONArray(FIELD_CIRCLELIST))) {
                        tempList.addAll(JSON.parseArray(JSON.toJSONString(a.getJSONArray(FIELD_CIRCLELIST)), CirclePostRelationForSimple.class));
                    }
                    a.put(FIELD_CIRCLELIST, tempList);
                });
                jsonArray.forEach(a -> distinctMapForTag.put(a.getString("_id"), a));
            }

            List<Map<String, Object>> tagChangePosts = newList.stream().filter(a -> tagChangePostIds.contains((String)a.get("_id"))).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(tagChangePosts)) {
                tagChangePosts.forEach(a -> distinctMapForTag.put((String)a.get("_id"), a));
            }

            List<Map<String, Object>> tagNewList = distinctMapForTag.values().stream().collect(Collectors.toList());
            List<Map<String, Object>> tagResultList = tagNewList.stream()
                .filter(a -> !CollectionUtils.isEmpty((List<CirclePostRelationForSimple>)a.get(FIELD_CIRCLELIST))
                    && Objects.equals(((List<CirclePostRelationForSimple>)a.get(FIELD_CIRCLELIST)).get(0).getTag(), 1)
                )
                .filter(a -> CollectionUtils.isEmpty(hideCircleIds) ||
                    !hideCircleIds.containsAll(((List<CirclePostRelationForSimple>)a.get(FIELD_CIRCLELIST)).stream().map(o -> o.getCircleId()).collect(Collectors.toList())))
                .sorted(((o1, o2) -> Long.compare(Long.parseLong(o2.get("TIMEPOINT").toString()), Long.parseLong(o1.get("TIMEPOINT").toString()))))
                .limit(limit)
                .collect(Collectors.toList());

            app.barredis.set(key, JSON.toJSONStringWithDateFormat(tagResultList, DateUtil.datePattern), expire);
        }

        return allResultList;
    }

}
