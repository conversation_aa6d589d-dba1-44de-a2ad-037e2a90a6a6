package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumerDoubleActive;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumPostType;
import ttfund.web.communityservice.bean.jijinBar.enums.MessagePushInfoSource;
import ttfund.web.communityservice.bean.jijinBar.enums.MessagePushInfoType;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.bean.messagepush.KafkaPostInfo;
import ttfund.web.communityservice.bean.messagepush.RemindGuba2PostAbout;
import ttfund.web.communityservice.bean.messagepush.RemindGuba2RequestModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.kafka.PushToolsToKafkaDao;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.utils.*;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 用户发帖 @用户
 * 备注1：对艾特用户进行降频处理， 来自需求(#656275 【基金吧6.13.1】问答功能优化)
 */
@Component
public class MessagePushPostAtMeDoubleActive {

    private static final Logger logger = LoggerFactory.getLogger(MessagePushPostAtMeDoubleActive.class);
    private static String logPre = "MessagePush=>MessagePushPostAtMe=>";

    public static final String KAFKA_LISTENER_ID = "MessagePushPostAtMeDoubleActive";

    @Autowired
    PassportUserInfoDao passportUserInfoDao;

    @Autowired
    PushToolsToKafkaDao pushToolsToKafkaDao;

    @Autowired
    private App app;

    @Autowired
    private AppConstant appConstant;

    @KafkaListener(topics = {KafkaTopicName.GubaPostInfo4FundQueue}, groupId = "message_push_post_20240707",
            containerFactory = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_pj)
    public void kafkaListener(String message) {
        try {
            if(DateUtil.getNowDate().compareTo(DateUtil.strToDate("2022-08-20 13:25:01.305","yyyy-MM-dd HH:mm:ss.SSS"))  <0){
                logger.info(logPre + "消费的信息=>notdeal=>:" + message);
                return;
            }
            logger.info(logPre + "=>消费信息：" + message);
            if (!NullUtil.isNull(message)) {
                KafkaPostInfo postInfo = JacksonUtil.deserialize(message,KafkaPostInfo.class);
                if (postInfo != null) {
                    MessagePushByApi(postInfo);
                }
            }
        } catch (Exception ex) {
            logger.error(logPre + "发帖@用户：" + ex.getMessage(), ex);
        }
    }

    private void MessagePushByApi(KafkaPostInfo post) {
        //需要触发消息提醒的流水数据类型
        String[] actionTypes = {"add", "rec"};
        //帖子未审核通过状态才进行推送
        if (post != null && post.Del != null &&
                post.Del == 0 &&
                Arrays.asList(actionTypes).contains(post.action_type) &&
                !NullUtil.isNull(post.Content)) {
            //对html标签进行解码
            post.Content = StringUtil.decode(post.Content);

            List<String> atUserList = DaoUtil.getAtUsersByPostContent(post.Content);

            if (!CollectionUtils.isEmpty(atUserList)) {
                //获取通行证用户信息
                List<PassportUserInfoModelNew> passportUsers = passportUserInfoDao.getPassportUserInfoByIds(atUserList);
                if (!CollectionUtils.isEmpty(passportUsers)) {
                    for (PassportUserInfoModelNew user : passportUsers) {
                        try {

                            //帖子艾特我推送次数 一天中
                            String pushCountKey = String.format(BarRedisKey.POST_AT_ME_PUSH_COUNT, user.PassportID);
                            String pushCount = app.barredis.get(pushCountKey);
                            if (StringUtils.hasLength(pushCount) && Integer.parseInt(pushCount) >= appConstant.postAtMePushCountLimit) {
                                Date now = new Date();
                                Date tomorrow = DateUtil.strToDate(DateUtil.dateToStr(DateUtil.calendarDateByDays(now, 1), "yyyy-MM-dd"), "yyyy-MM-dd");
                                app.barredis.expire(pushCountKey, (tomorrow.getTime() - now.getTime()) / 1000);
                                continue;
                            }

                            //用户昵称不为空
                            if (!NullUtil.isNull(user.NickName) && post.Type != null) {

                                RemindGuba2RequestModel requestModel = new RemindGuba2RequestModel();
                                {
                                    requestModel.EID = post.ID + "_" + user.PassportID + "_" + post.Type;//帖子ID+用户通行证ID+帖子类型
                                    requestModel.Content = StringUtil.decode(DaoUtil.filterHtml(post.Content));//保留100字符
                                    requestModel.InfoType = MessagePushInfoType.POST.getValue();
                                    requestModel.PassportID = user.PassportID;
                                    requestModel.PassportName = user.NickName;
                                    requestModel.PostID = String.valueOf(post.ID);
                                    requestModel.ReplyPassportID = post.UID;
                                    requestModel.ReplyPassportName = post.Nicheng;
                                    requestModel.Source = MessagePushInfoSource.FundJijinBarService.toString();
                                    requestModel.PostType = post.Type;
                                    requestModel.Title = post.Title;
                                }
                                ;

                                //判断是否是基金吧问答，如果是基金吧问答需要提供 问答的相关信息
                                if (post.Type == EnumPostType.QAQuestion.getValue() || post.Type == EnumPostType.QAAnswer.getValue()) {
                                    HashMap<String, Object> dic = JacksonUtil.deserialize(post.Extend, HashMap.class);
                                    if (dic != null) {
                                        RemindGuba2PostAbout qainfo = new RemindGuba2PostAbout();
                                        {
                                            qainfo.qid = (dic.containsKey("QID") && dic.get("QID") != null) ? String.valueOf(dic.get("QID")) : "";
                                            qainfo.aid = (dic.containsKey("AID") && dic.get("AID") != null) ? String.valueOf(dic.get("AID")) : "";
                                        }
                                        ;
                                        requestModel.PostAbout = JacksonUtil.serialize(qainfo);
                                    }
                                }
                                if (!requestModel.PassportID.equals(requestModel.ReplyPassportID)) {
                                    logger.info(logPre+"推送的帖子：" +JacksonUtil.serialize(post));
                                    pushToolsToKafkaDao.MentionMe(requestModel);
                                }

                            }
                        } catch (Exception ex) {
                            logger.error(logPre+"MessagePushByApi=>" + ex.getMessage(), ex);
                        }
                    }
                }
            }
        }
    }
}