package ttfund.web.communityservice.timedtask.jijinbarJobs.postOperate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.HtmlUtils;
import ttfund.web.communityservice.bean.jijinBar.post.FundPostExtras;
import ttfund.web.communityservice.bean.jijinBar.post.FundPostExtrasProP;
import ttfund.web.communityservice.bean.jijinBar.post.guba.ReqeustCodeDealRes;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.dao.mongo.ExcellentReplyIntoPostDao;
import ttfund.web.communityservice.dao.mongo.ExcellentReplyIntoPostUserDao;
import ttfund.web.communityservice.dao.mongo.PostInfoExtendDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoNewDao;
import ttfund.web.communityservice.service.common.CommonDataCache;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.MD5Util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 优质评论发帖job
 * 备注：    需求：#659633 【基金吧6.13.4】优质评论转发贴
 */
@JobHandler("ExcellentReplyIntoPostJob")
@Component
public class ExcellentReplyIntoPostJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(ExcellentReplyIntoPostJob.class);

    private static List<String> setOnInsertFields = Arrays.asList("CREATETIME");

    //过滤词
    private static List<String> FILTER_WORDS = Arrays.asList("参加", "参与", "活动", "老师", "怎么看", "怎么办");

    //html标签
    private static final String TAB_REGEX = "<[^>]*?>";
    private static final Pattern TAB_REGEX_PATTERN = Pattern.compile(TAB_REGEX);

    //4个连续表情及以上
    private static final String EMOJI_REGEX = "\\[[^\\]]*?\\]\\[[^\\]]*?\\]\\[[^\\]]*?\\]\\[[^\\]]*?\\]";
    private static final Pattern EMOJI_REGEX_PATTERN = Pattern.compile(EMOJI_REGEX);

    //艾特用户
    private static final String USER_REGEX = "\\[at=(.*?)\\]([\\s\\S]*?)\\[/at\\]";
    private static final Pattern USER_REGEX_PATTERN = Pattern.compile(USER_REGEX);

    @Autowired
    private ReplyInfoNewDao replyInfoNewDao;

    @Autowired
    private ExcellentReplyIntoPostDao excellentReplyIntoPostDao;

    @Autowired
    private ExcellentReplyIntoPostUserDao excellentReplyIntoPostUserDao;

    @Autowired
    private PostInfoExtendDao postInfoExtendDao;

    @Autowired
    private CommonDataCache commonDataCache;

    @Autowired
    private AppConstant appConstant;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            //发评论时间限制 负值  单位：小时
            Integer timeLimit = null;
            //字数限制
            Integer charCountLimit = null;
            //互动数量限制
            Integer interactCountLimit = null;

            Integer batchReadCount = null;

            String filterWords = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                timeLimit = jsonObject.getInteger("timeLimit");
                charCountLimit = jsonObject.getInteger("charCountLimit");
                interactCountLimit = jsonObject.getInteger("interactCountLimit");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                filterWords = jsonObject.getString("filterWords");

            }

            if (timeLimit == null) {
                timeLimit = -72;
            }
            if (charCountLimit == null) {
                charCountLimit = 50;
            }
            if (interactCountLimit == null) {
                interactCountLimit = 3;
            }
            if (batchReadCount == null) {
                batchReadCount = 10000;
            }

            if (filterWords != null) {
                FILTER_WORDS = CommonUtils.toList(filterWords, ",");
            }

            logger.info("第零步，打印参数。timeLimit：{}，charCountLimit：{}，interactCountLimit：{}，batchReadCount：{}",
                timeLimit,
                charCountLimit,
                interactCountLimit,
                batchReadCount
            );

            deal(timeLimit, charCountLimit, interactCountLimit, batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void deal(int timeLimit, int charCountLimit, int interactCountLimit, int batchReadCount) {

        Date time = DateUtil.calendarDateByHour(timeLimit);

        List<Map<String, Object>> list = replyInfoNewDao.getExcellentComment(time, interactCountLimit, batchReadCount);

        logger.info("1.读取评论数据。数量：{}，头部数据：{}，截止发送时间：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern),
            DateUtil.dateToStr(time)
        );

        List<Map<String, Object>> filterList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(list)) {
            String text = null;

            for (Map<String, Object> a : list) {
                if (a.get("TEXT") != null) {
                    text = a.get("TEXT").toString();
                    if (filterText(text, charCountLimit)) {
                        //html解码
                        text = HtmlUtils.htmlUnescape(text);

                        //处理艾特用户
                        Matcher atUserMatcher = USER_REGEX_PATTERN.matcher(text);
                        while (atUserMatcher.find()) {
                            text = text.replace(atUserMatcher.group(0), atUserMatcher.group(2));
                        }

                        if (text.length() >= charCountLimit) {
                            a.put("POSTTEXT", text);
                            filterList.add(a);
                        }
                    }
                }
            }

            //检查用户开关设置
            if (!CollectionUtils.isEmpty(filterList)) {
                List<String> uids = filterList.stream().map(a -> a.get("UID").toString()).distinct().collect(Collectors.toList());
                Set<String> filteredUids = new HashSet<>();
                List<List<String>> batchList = CommonUtils.toSmallList2(uids, 200);
                for (List<String> batch : batchList) {
                    List<Document> documents = excellentReplyIntoPostUserDao.getByIds(Arrays.asList("_id"), batch);
                    if (!CollectionUtils.isEmpty(documents)) {
                        filteredUids.addAll(documents.stream().map(a -> a.getString("_id")).collect(Collectors.toList()));
                    }
                }

                filterList = filterList.stream().filter(a -> filteredUids.contains(a.get("UID").toString())).collect(Collectors.toList());
            }

            //对评论去重
            if (!CollectionUtils.isEmpty(filterList)) {
                List<String> replyIds = filterList.stream().map(a -> a.get("ID").toString()).collect(Collectors.toList());
                Set<String> existIds = new HashSet<>();
                List<List<String>> batchList = CommonUtils.toSmallList2(replyIds, 200);
                for (List<String> batch : batchList) {
                    List<Document> documents = excellentReplyIntoPostDao.getListByReplyIds(Arrays.asList("_id"), batch);
                    if (!CollectionUtils.isEmpty(documents)) {
                        existIds.addAll(documents.stream().map(a -> a.getString("_id")).collect(Collectors.toList()));
                    }
                }

                filterList = filterList.stream().filter(a -> !existIds.contains(a.get("ID").toString())).collect(Collectors.toList());
            }

        }

        logger.info("2.过滤及去重处理。数量：{}，头部数据：{}",
            CollectionUtils.isEmpty(filterList) ? 0 : filterList.size(),
            CollectionUtils.isEmpty(filterList) ? null : JSON.toJSONStringWithDateFormat(filterList.get(0), DateUtil.datePattern)
        );


        List<Map<String, Object>> mapList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(filterList)) {
            Map<String, Object> map = null;
            ReqeustCodeDealRes codeInfo = null;
            for (Map<String, Object> a : filterList) {
                codeInfo = commonDataCache.reqeustCodeDeal(a.get("CODE").toString());
                map = new HashMap<>();
                map.put("REPLYID", a.get("ID"));
                map.put("TOPICID", a.get("TOPICID"));

                map.put("uid", a.get("UID"));
                map.put("code", codeInfo.getGuba_code());
                // 吧类型。jjcp：普通基金吧 ， tgcp：投顾吧  ， 不传：表示组合吧
                if (codeInfo.getGuba_type() == 1) {
                    map.put("jtype", "jjcp");
                } else if (codeInfo.getGuba_type() == 5) {
                    map.put("jtype", "tgcp");
                } else if (codeInfo.getGuba_type() == 3) {
                    map.put("jtype", "");
                } else {
                    continue;
                }

                //对属于财富号评论吧的评论，转成帖子时，给它发到基金动态吧去  因为cfhpl吧的普通帖子我们基金不会同步到底表去
                if ("cfhpl".equals(map.get("code"))) {
                    map.put("code", "jjdt");
                }

                map.put("text", HtmlUtils.htmlUnescape(a.get("POSTTEXT").toString()));
                map.put("pic", a.get("PIC").toString());

                mapList.add(map);

            }
        }

        logger.info("3.发帖前置准备。数量：{}，头部数据：{}",
            CollectionUtils.isEmpty(mapList) ? 0 : mapList.size(),
            CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONStringWithDateFormat(mapList.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(mapList)) {
            Map<String, Object> intoDbMap = null;

            int i = 0;
            for (Map<String, Object> a : mapList) {

                i++;

                uploadPost(a);

                if (a.get("postId") != null) {
                    intoDbMap = new HashMap<>();
                    intoDbMap.put("_id", a.get("REPLYID").toString());
                    intoDbMap.put("REPLYID", Long.parseLong(a.get("REPLYID").toString()));
                    intoDbMap.put("POSTID", Long.parseLong(a.get("postId").toString()));
                    intoDbMap.put("UID", a.get("uid").toString());
                    intoDbMap.put("CREATETIME", new Date());
                    intoDbMap.put("UPDATETIME", new Date());

                    excellentReplyIntoPostDao.upsertManyBySetWithSetOnInsertFields(Arrays.asList(intoDbMap), setOnInsertFields, "_id");

                    FundPostExtras fundPostExtras = new FundPostExtras();
                    fundPostExtras._id = a.get("postId").toString();
                    fundPostExtras.TID = Long.parseLong(a.get("postId").toString());
                    fundPostExtras.PROP = new ArrayList<>();
                    FundPostExtrasProP proP = new FundPostExtrasProP();
                    proP.CODE = Arrays.asList(a.get("TOPICID").toString());
                    proP.TYPE = 71;
                    fundPostExtras.PROP.add(proP);
                    fundPostExtras.PUBTIME = new Date();

                    postInfoExtendDao.insert(fundPostExtras);
                }

                logger.info("4.发帖及落库详情。第{}/{}个，评论id：{}，生成帖子id：{}，错误信息：{}",
                    i,
                    mapList.size(),
                    a.get("REPLYID"),
                    a.get("postId"),
                    a.get("message")
                );
            }
        }

        logger.info("4.发帖及落库完成。数量：{}，头部数据：{}",
            CollectionUtils.isEmpty(mapList) ? 0 : mapList.size(),
            CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONStringWithDateFormat(mapList.get(0), DateUtil.datePattern)
        );

    }

    private void uploadPost(Map<String, Object> map) {

        StringBuilder builder = new StringBuilder();
        builder.append("uid=").append(map.get("uid").toString())
            .append("&code=").append(map.get("code").toString())
            .append("&jtype=").append(map.get("jtype").toString())
            .append("&title=").append(map.get("title") == null ? "" : map.get("title").toString())
            .append("&text=").append(map.get("text").toString())
            .append("&pic=").append(map.get("pic") == null ? "" : map.get("pic").toString())
            .append("&isTopic=").append(map.get("isTopic") == null ? "0" : map.get("isTopic").toString())
            .append("&ip=").append(map.get("ip") == null ? "" : map.get("ip"))
            .append("&is_reply=false")
            .append("&approvalfirst=1")
            .append("&plat=Web")
            .append("&product=Fund")
            .append("&version=100")
            .append("&deviceid=").append(MD5Util.generateHash(map.get("uid").toString()));

        String path = appConstant.config_gubahostnewserver + "/thirdpartopt/api/ArticlePost/PostShipanInTime";

        String html = HttpHelper.requestPostFrom(path, builder.toString(), 15 * 1000);

        String message = null;
        if (StringUtils.hasLength(html)) {
            JSONObject res = JSON.parseObject(html);
            Integer postId = res.getInteger("main_post_id");
            if (postId != null && postId != 0) {
                map.put("postId", postId);
            } else {
                message = res.getString("me");
                if (!StringUtils.hasLength(message)) {
                    message = "发帖接口没有返回帖子id";
                }
            }
        } else {
            message = ("发帖接口返回为空");
        }

        map.put("message", message);
    }

    /**
     * 筛选
     */
    private boolean filterText(String text, int charCountLimit) {

        //html解码
        text = HtmlUtils.htmlUnescape(text);

        //去除html标签
        Matcher tabMatcher = TAB_REGEX_PATTERN.matcher(text);
        while (tabMatcher.find()) {
            text = text.replace(tabMatcher.group(0), "");
        }

        //过滤字符数   因为可能涉及富文本等特殊情况，在数据库端不方便处理，放在程序里做
        if (text.length() < charCountLimit) {
            return false;
        }

        //过滤指定词汇
        if (!CollectionUtils.isEmpty(FILTER_WORDS)) {
            for (String a : FILTER_WORDS) {
                if (text.contains(a)) {
                    return false;
                }
            }
        }

        //过滤情绪贴
        Matcher emojiMatcher = EMOJI_REGEX_PATTERN.matcher(text);
        if (emojiMatcher.find()) {
            return false;
        }

        //过滤艾特用户
        Matcher atUserMatcher = USER_REGEX_PATTERN.matcher(text);
        if (atUserMatcher.find()) {
            return false;
        }

        return true;
    }

}
