package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.dao.mongo.IntervalPostCountOfBarDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.mongo.IntervalPostCountOfTopicDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 区间帖子数量计算job
 * 备注：需求 #711535 【基金吧6.17】资讯改社区
 */
@Slf4j
@JobHandler("IntervalPostCountJob")
@Component
public class IntervalPostCountJob extends IJobHandler {

    private static final List<String> FIELDS = Arrays.asList("_id", "HTLIST");

    private static final List<String> SET_ON_INSERT_FIELDS = Arrays.asList("createTime");

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private PostDao postDao;

    @Autowired
    private IntervalPostCountOfTopicDao intervalPostCountOfTopicDao;

    @Autowired
    private IntervalPostCountOfBarDao intervalPostCountOfBarDao;


    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            Integer interval = null; //负值
            Boolean isWriteMongo = null;
            Boolean isWriteRedis = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                interval = jsonObject.getInteger("interval");
                isWriteMongo = jsonObject.getBoolean("isWriteMongo");
                isWriteRedis = jsonObject.getBoolean("isWriteRedis");
            }

            if (interval == null) {
                interval = -24 * 3600;
            }
            if (isWriteMongo == null) {
                isWriteMongo = true;
            }
            if (isWriteRedis == null) {
                isWriteRedis = true;
            }

            log.info("0，打印参数。interval：{}，isWriteMongo：{}，isWriteRedis：{}",
                    interval,
                    isWriteMongo,
                    isWriteRedis
            );


            deal(interval, isWriteMongo, isWriteRedis);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void deal(int interval, boolean isWriteMongo, boolean isWriteRedis) {
        if (isWriteMongo) {
            dealMongo(interval);
        }

        if (isWriteRedis) {
            dealMongo(interval);
        }

    }


    private void dealMongo(int interval) {
        Date now = new Date();

        Date end = now;
        Date start = DateUtil.calendarDateBySecond(end, interval);

        List<Map<String, Object>> list = postInfoNewDao.getUsefulPostsByTime("ID, TITLE, CODE, TIME", start, end);

        log.info("1，读取数据。start：{}，end：{}，数量:{}，头部列表：{}",
                DateUtil.dateToStr(start),
                DateUtil.dateToStr(end),
                list == null ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        dealMongoForTopic(list, now);

        dealMongoForBar(list, now);

    }

    private void dealMongoForTopic(List<Map<String, Object>> list, Date date) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Map<String, Integer> topicPostCountMap = new HashMap<>();
        List<Document> topicPostList = new ArrayList<>();

        List<String> postIds = list.stream().map(a -> a.get("ID").toString()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(postIds)) {
            List<List<String>> batchList = CommonUtils.toSmallList2(postIds, 100);
            for (List<String> batch : batchList) {
                List<Document> tempList = postDao.getTopicPosts(Document.class, FIELDS, batch);
                if (!CollectionUtils.isEmpty(tempList)) {
                    topicPostList.addAll(tempList);
                }
            }
        }


        if (!CollectionUtils.isEmpty(topicPostList)) {
            List<Document> tempList = null;
            for (Document a : topicPostList) {
                tempList = a.getList("HTLIST", Document.class);
                if (!CollectionUtils.isEmpty(tempList)) {
                    for (Document o : tempList) {
                        topicPostCountMap.put(o.getString("HtId"), (Integer) topicPostCountMap.getOrDefault(o.getString("HtId"), 0) + 1);
                    }
                }
            }
        }

        log.info("dealMongoForTopic-1，读取数据。数量:{}，头部列表：{}",
                topicPostList == null ? 0 : topicPostList.size(),
                CollectionUtils.isEmpty(topicPostList) ? null : JSON.toJSONStringWithDateFormat(topicPostList.get(0), DateUtil.datePattern)
        );

        List<Map<String, Object>> mapList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(topicPostCountMap)) {
            Set<Map.Entry<String, Integer>> entrySet = topicPostCountMap.entrySet();
            Map<String, Object> map = null;
            for (Map.Entry<String, Integer> entry : entrySet) {
                map = new HashMap<>();
                map.put("_id", entry.getKey());
                map.put("topicId", entry.getKey());
                map.put("lastDayPostCount", entry.getValue());
                map.put("createTime", new Date());
                map.put("updateTime", new Date());
                mapList.add(map);
            }

            List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 50);
            for (List<Map<String, Object>> batch : batchList) {
                intervalPostCountOfTopicDao.upsertManyBySetWithSetOnInsertFields(batch, SET_ON_INSERT_FIELDS, "_id");
            }

        }

        log.info("dealMongoForTopic-2，写库。数量:{}，头部列表：{}",
                mapList == null ? 0 : mapList.size(),
                CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONStringWithDateFormat(mapList.get(0), DateUtil.datePattern)
        );

        if (date != null) {
            intervalPostCountOfTopicDao.deleteByUpdateTime(date);
        }

        log.info("dealMongoForTopic-3，删除历史数据。date：{}", DateUtil.dateToStr(date));
    }


    private void dealMongoForBar(List<Map<String, Object>> list, Date date) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Map<String, Integer> barPostCountMap = new HashMap<>();

        if (!CollectionUtils.isEmpty(list)) {
            String temp = null;
            for (Map<String, Object> a : list) {
                temp = (String) a.get("CODE");
                if (StringUtils.hasLength(temp)) {
                    barPostCountMap.put(temp, (Integer) barPostCountMap.getOrDefault(temp, 0) + 1);
                }
            }
        }

        List<Map<String, Object>> mapList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(barPostCountMap)) {
            Set<Map.Entry<String, Integer>> entrySet = barPostCountMap.entrySet();
            Map<String, Object> map = null;
            for (Map.Entry<String, Integer> entry : entrySet) {
                map = new HashMap<>();
                map.put("_id", entry.getKey());
                map.put("code", entry.getKey());
                map.put("lastDayPostCount", entry.getValue());
                map.put("createTime", new Date());
                map.put("updateTime", new Date());
                mapList.add(map);
            }

            List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 50);
            for (List<Map<String, Object>> batch : batchList) {
                intervalPostCountOfBarDao.upsertManyBySetWithSetOnInsertFields(batch, SET_ON_INSERT_FIELDS, "_id");
            }

        }

        log.info("dealMongoForBar-1，写库。数量:{}，头部列表：{}",
                mapList == null ? 0 : mapList.size(),
                CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONStringWithDateFormat(mapList.get(0), DateUtil.datePattern)
        );

        if (date != null) {
            intervalPostCountOfBarDao.deleteByUpdateTime(date);
        }

        log.info("dealMongoForTopic-2，删除历史数据。date：{}", DateUtil.dateToStr(date));

    }


}
