package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.config.SetHideModel;
import ttfund.web.communityservice.dao.mongo.FundUserProfitDao;
import ttfund.web.communityservice.dao.msyql.SetHideDao;
import ttfund.web.communityservice.enums.EnumHidePlaceType;
import ttfund.web.communityservice.utils.CommonUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户持仓帖子帖子隐藏服务
 */
@JobHandler("FundUserProfitBusinessJob")
@Component
public class FundUserProfitBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(FundUserProfitBusinessJob.class);

    @Autowired
    private SetHideDao setHideDao;

    @Autowired
    private FundUserProfitDao fundUserProfitDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            syncToMong();

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    public void syncToMong() {

        List<SetHideModel> dataList = setHideDao.getListAll();

        if (!CollectionUtils.isEmpty(dataList)) {
            dataList = dataList.stream().sorted(((o1, o2) -> o2.UpdateTime.compareTo(o1.UpdateTime))).collect(Collectors.toList());
        }

        logger.info("1.读取数据。数量：{}，头部id列表：{}",
                dataList == null ? 0 : dataList.size(),
                dataList == null ? 0 : dataList.stream().map(a -> a.TID).limit(50).collect(Collectors.toList()));


        List<SetHideModel> updateList = null;
        if (!CollectionUtils.isEmpty(dataList)) {

            updateList = dataList.stream().filter(item -> {
                //如果是在单品吧隐藏则，设置 TTJJDEL=1
                if (StringUtils.hasLength(item.HidePlace)) {
                    String[] arr = item.HidePlace.split(",");
                    if (arr != null && arr.length > 0) {
                        for (String a : arr) {
                            if (EnumHidePlaceType.DPB.getValue().equals(a)) {
                                return true;
                            }
                        }
                    }
                }
                return false;
            }).collect(Collectors.toList());

            logger.info("2.筛选数据。数量：{}，头部id列表：{}",
                    updateList == null ? 0 : updateList.size(),
                    updateList == null ? 0 : updateList.stream().map(a -> a.TID).limit(50).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(updateList)) {
                List<Map<String, Object>> mapList = new ArrayList<>();
                for (SetHideModel a : updateList) {
                    Long postId = null;
                    try {
                        postId = Long.parseLong(a.TID);
                    } catch (Exception e) {

                    }

                    if (postId != null) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("POSTID", postId);
                        map.put("TTJJDEL", a.State == 1 ? 1 : 0);
                        map.put("TTJJDELTIME", new Date());
                        mapList.add(map);
                    }
                }

                if (!CollectionUtils.isEmpty(mapList)) {
                    List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 200);
                    for (List<Map<String, Object>> batch : batchList) {
                        fundUserProfitDao.updateManyByPostid(batch);
                    }
                }

                logger.info("3.数据写库。数量：{}，头部id列表：{}",
                        mapList == null ? 0 : mapList.size(),
                        mapList == null ? 0 : mapList.stream().map(a -> a.get("POSTID")).limit(50).collect(Collectors.toList()));
            }
        }
    }

}
