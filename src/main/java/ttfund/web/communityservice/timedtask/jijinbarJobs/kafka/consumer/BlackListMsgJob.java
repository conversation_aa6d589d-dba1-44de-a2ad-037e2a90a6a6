package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.alibaba.fastjson.JSON;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.guba.BlackList;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.BlackListDao;

/**
 * 黑名单 kafka -> mysql
 */
@Component
public class BlackListMsgJob {

    private static Logger logger = LoggerFactory.getLogger(BlackListMsgJob.class);

    public static final String KAFKA_LISTENER_ID = "BlackListMsgJob";

    @Autowired
    private BlackListDao blackListDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.GubaUserBlackList4FundQueue}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_BLACKLISTMSGJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_zp)
    private void onListen(ConsumerRecord<String, String> record) {
        blackListMsg((record));
    }


    /**
     * 黑名单
     */
    public void blackListMsg(ConsumerRecord<String, String> record) {

        try {

            logger.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            BlackList black = JSON.parseObject(record.value(), BlackList.class);
            switch (black.action_type) {
                case "del"://删除
                    blackListDao.disabled(black);
                    break;

                case "add"://增加
                    blackListDao.insertOrUpdate(black);
                    break;
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }
}
