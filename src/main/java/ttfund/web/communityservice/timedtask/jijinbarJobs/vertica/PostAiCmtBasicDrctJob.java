package ttfund.web.communityservice.timedtask.jijinbarJobs.vertica;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.data.AiCommentDo;
import ttfund.web.communityservice.constant.DateConstant;
import ttfund.web.communityservice.dao.mongo.AiReplyMongoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.dao.vertica.PostAiCmtDao;
import ttfund.web.communityservice.dao.vertica.VerticaTaskRecordDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Zhuyuang
 * @date : 2024-08-21 13:09
 * @description : 气氛组评论同步Mongo
 */
@JobHandler("PostAiCmtBasicDrctJob")
@Component
public class PostAiCmtBasicDrctJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(PostAiCmtBasicDrctJob.class);

    private static final String LOG_PREFIX = "PostAiCmtBasicDrctJob[气氛组评论同步Mongo]";

    private static final String BREAK_TIME_KEY = "PostAiCmtBasicDrctJobBreakTime";

    private static final String DATA_OFFSET_KEY = "PostAiCmtBasicDrctJobDataOffsetTime2";

    private static final String TABLE_NAME = "POST_AI_CMT_BASIC_DRCT_ALL";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private VerticaTaskRecordDao verticaTaskRecordDao;

    @Autowired
    private PostAiCmtDao postAiCmtDao;

    @Autowired
    private AiReplyMongoDao aiReplyMongoDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date breakTime = StringUtils.isEmpty(s) ?
            userRedisDao.getBreakTime(BREAK_TIME_KEY, new Date()) : DateUtil.strToDate(s);
        Date completeTime = verticaTaskRecordDao.selectLatestRecordExist(TABLE_NAME, breakTime);
        if (completeTime == null) {
            LOGGER.info("{}: Vertica write task has not completed", LOG_PREFIX);
            return null;
        }
        userRedisDao.setBreakTime(BREAK_TIME_KEY, completeTime);

        String dataOffsetTime = userRedisDao.get(DATA_OFFSET_KEY);
        if (StringUtils.isEmpty(dataOffsetTime)) {
            dataOffsetTime = s;
        }
        List<AiCommentDo> commentDoList = postAiCmtDao.select(dataOffsetTime);
        LOGGER.info("{}: 从Vertica拉取AI评论数量为: {}", LOG_PREFIX, commentDoList.size());
        if (CollectionUtils.isEmpty(commentDoList)) {
            return null;
        }
        Optional<AiCommentDo> latestTime = commentDoList.stream().
            max(Comparator.comparing(AiCommentDo::getUpdateTime));
        dataOffsetTime = latestTime.get().getUpdateTime();
        userRedisDao.set(DATA_OFFSET_KEY, dataOffsetTime, DateConstant.ONE_WEEK);

        boolean result = aiReplyMongoDao.upsertMany(commentDoList, "_id");
        LOGGER.info("{}: 同步结果为: {}", LOG_PREFIX, result);
        return ReturnT.SUCCESS;
    }
}
