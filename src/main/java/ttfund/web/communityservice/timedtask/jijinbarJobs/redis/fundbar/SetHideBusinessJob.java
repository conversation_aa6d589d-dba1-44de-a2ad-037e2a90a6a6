package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.config.SetHideModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.SetHideDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 瀑布流设置帖子隐藏位置配置缓存
 */
@JobHandler(value = "setHideBusinessJob")
@Component
public class SetHideBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SetHideBusinessJob.class);

    @Autowired
    private SetHideDao setHideDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String param) throws JsonProcessingException {

        try {

            setToCache();

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void setToCache() {
        try {

            List<SetHideModel> list = setHideDao.getListByState();

            logger.info("1.读取数据。数量:{}，头部数据：{}",
                    list == null ? 0 : list.size(),
                    list == null ? null : JSON.toJSONStringWithDateFormat(list.stream().limit(1).collect(Collectors.toList()), DateUtil.datePattern));

            if (!CollectionUtils.isEmpty(list)) {
                app.barredis.set(BarRedisKey.FUND_GUBA_HIDE_POST, JSON.toJSONStringWithDateFormat(list, "yyyy-MM-dd'T'HH:mm:ss"));

                logger.info("2.写redis。数量:{}，头部数据：{}",
                        list == null ? 0 : list.size(),
                        list == null ? null : JSON.toJSONStringWithDateFormat(list.stream().limit(1).collect(Collectors.toList()), DateUtil.datePattern));
            } else {
                app.barredis.del(BarRedisKey.FUND_GUBA_HIDE_POST);

                logger.info("2.删除redis。");
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }
}
