package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.useracc;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.favor.UserFavorBean;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.PersonalizedRedisConstantConfig;
import ttfund.web.communityservice.dao.msyql.UserFavorDao;
import ttfund.web.communityservice.utils.redis.RedisUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全量用户自选同步至个性化Redis
 */
@JobHandler(value = "userFavorFullJob")
@Component
public class UserFavorFullJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(UserFavorFullJob.class);

    @Autowired
    private UserFavorDao userFavorDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String updateTime) {

        ReturnT<String> result = ReturnT.SUCCESS;
        int size = 10000;

        try {
            // 基金自选
            syncFavorFund(updateTime, size);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }

        try {
            // 组合自选
            syncFavorCombine(updateTime, size);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }

        try {
            // 投顾自选
            syncFavorInvest(updateTime, size);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }

        return result;
    }

    /**
     * 投顾自选
     *
     * @param updateTime
     * @param size
     */
    private void syncFavorInvest(String updateTime, int size) {

        logger.info("投顾自选开始");

        String userId = null;
        String fundCode = null;

        int count = 0;

        while (true) {
            List<UserFavorBean> favors = userFavorDao.getUserFavorInvest(userId, fundCode, size, updateTime);
            if (CollectionUtils.isEmpty(favors)) {
                break;
            }

            userId = favors.get(favors.size() - 1).getPassportId();
            fundCode = favors.get(favors.size() - 1).getCode();

            // 设置缓存
            setFavorCache(favors);

            count += favors.size();
            logger.info("自选投顾已保存数量：{}", count);

            if (favors.size() < size) {
                break;
            }
        }

        logger.info("投顾自选结束");
    }

    /**
     * 组合自选
     *
     * @param updateTime
     * @param size
     */
    private void syncFavorCombine(String updateTime, int size) {

        logger.info("组合自选开始");

        String userId = null;
        String fundCode = null;

        int count = 0;

        while (true) {
            List<UserFavorBean> favors = userFavorDao.getUserFavorCombine(userId, fundCode, size, updateTime);
            if (CollectionUtils.isEmpty(favors)) {
                break;
            }

            userId = favors.get(favors.size() - 1).getPassportId();
            fundCode = favors.get(favors.size() - 1).getCode();

            // 设置缓存
            setFavorCache(favors);

            count += favors.size();
            logger.info("自选组合已保存数量：{}", count);

            if (favors.size() < size) {
                break;
            }
        }

        logger.info("组合自选结束");
    }

    /**
     * 基金自选
     *
     * @param updateTime
     * @param size
     */
    private void syncFavorFund(String updateTime, int size) {

        logger.info("基金自选开始");

        String[] tables = new String[]{
                "f_detail_p0",
                "f_detail_p1",
                "f_detail_p2",
                "f_detail_p3",
                "f_detail_p4",
                "f_detail_p5",
                "f_detail_p6",
                "f_detail_p7",
                "f_detail_p8",
                "f_detail_p9"
        };
        for (String table : tables) {

            String userId = null;
            String fundCode = null;
            int count = 0;

            while (true) {
                List<UserFavorBean> favors = userFavorDao.getUserFavorFund(table, userId, fundCode, size, updateTime);
                if (CollectionUtils.isEmpty(favors)) {
                    break;
                }

                userId = favors.get(favors.size() - 1).getPassportId();
                fundCode = favors.get(favors.size() - 1).getCode();

                // 设置缓存
                setFavorCache(favors);

                count += favors.size();
                logger.info("自选基金-【{}】已保存数量：{}", table, count);

                if (favors.size() < size) {
                    break;
                }
            }
        }

        logger.info("基金自选结束");
    }

    /**
     * 设置缓存
     *
     * @param favors
     */
    private void setFavorCache(List<UserFavorBean> favors) {

        Map<String, List<UserFavorBean>> userFavorsMap = favors.stream()
                .collect(Collectors.groupingBy(w -> w.getPassportId()));

        for (String key : userFavorsMap.keySet()) {
            List<String> codes = userFavorsMap.get(key).stream().map(w -> w.getCode()).distinct().collect(Collectors.toList());
            String cacheKey = PersonalizedRedisConstantConfig.JAVA_SERVICE_FAVOR_PASSPORT + key;
            RedisUtils.sadd(app.personalizedRedisWrite, cacheKey, codes.toArray(new String[codes.size()]));
            app.personalizedRedisWrite.expire(cacheKey, 90 * 24 * 3600L);
        }
    }
}
