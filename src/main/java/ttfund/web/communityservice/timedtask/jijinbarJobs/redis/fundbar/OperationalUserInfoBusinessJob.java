package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ttfund.web.base.helper.CacheHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.OperationalUserInfoModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.List;

/**
 * 公司内部官方号缓存(社区资讯互联互通)
 * 需求地址： http://172.16.89.168/redmine/issues/215451
 * 每10分钟执行一次
 */
@JobHandler(value = "operationalUserInfoBusinessJob")
@Component
public class OperationalUserInfoBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(OperationalUserInfoBusinessJob.class);

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String param) throws JsonProcessingException {

        try {

            setToCache();

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }


        return ReturnT.SUCCESS;
    }


    private void setToCache() {

        try {
            String apiUrl = appConstant.operationalUserInfoApi;
            List<OperationalUserInfoModel> list = CacheHelper.get(apiUrl);
            if (CollectionUtils.isEmpty(list)) {
                String content = HttpHelper.requestGet(apiUrl);
                if (StringUtils.hasLength(content)) {
                    list = JacksonUtil.string2Obj(content, List.class, OperationalUserInfoModel.class);
                    if (!CollectionUtils.isEmpty(list)) {
                        CacheHelper.put(apiUrl, list, 5 * 60 * 1000L);
                    }
                }
            }

            logger.info("1.读取数据。数量：{}，头部数据：{}",
                    list == null ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JSON.toJSONString(list.get(0))
            );

            if (!CollectionUtils.isEmpty(list)) {
                Boolean success = app.barredis.set(BarRedisKey.ASP_NET_FUND_OPERATIONALUSERINFO, JacksonUtil.obj2String(list));

                logger.info("2.写缓存。结果：{}，数量：{}，头部数据：{}",
                        success,
                        list == null ? 0 : list.size(),
                        CollectionUtils.isEmpty(list) ? null : JSON.toJSONString(list.get(0))
                );

                if (success == null || !success) {
                    logger.error("2.写缓存失败");
                }
            } else {
                // 如果没有数据则删除
                app.barredis.del(BarRedisKey.ASP_NET_FUND_OPERATIONALUSERINFO);

                logger.info("2.读取数据为空，删除redis键。");

            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }
}
