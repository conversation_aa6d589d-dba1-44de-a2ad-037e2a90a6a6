package ttfund.web.communityservice.timedtask.jijinbarJobs;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.dao.search.FollowPostDao;


@JobHandler(value = "test")
@Component
public class Test extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(PostAuthorFlagJob.class);
    @Autowired
    FollowPostDao followPostDao;

    @Override
    public ReturnT<String> execute(String param) {

        try{
//            List<String> ids = new ArrayList<>();
//            ids.add("SearchEasterEgg_1010_2");
//            ids.add("24");
//            List<AppFunctionModel> list1 = followPostDao.getByidFromES(ids);
//
//            ids.add("861003");
//            ids.add("861022");

        }
        catch (Exception ex){
            logger.error(ex.getMessage(),ex);
        }


        return  ReturnT.SUCCESS;
    }
}
