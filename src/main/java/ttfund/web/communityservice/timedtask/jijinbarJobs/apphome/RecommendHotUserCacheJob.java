package ttfund.web.communityservice.timedtask.jijinbarJobs.apphome;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.JsonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.enums.RecommonType;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.PopularityRankWModel;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.RecommendHotUserModel;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.SetRecommonInfoModel;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.constant.DateConstant;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.mongo.PopularityRankWDao;
import ttfund.web.communityservice.dao.msyql.SetRecommonInfoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 人气用户列表更新，设置为每5分钟更新一次
 */
@JobHandler("RecommendHotUserCacheJob")
@Component
public class RecommendHotUserCacheJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecommendHotUserCacheJob.class);

    private static final long EXPIRE_TIME = 10 * DateConstant.ONE_WEEK;

    @Autowired
    private SetRecommonInfoDao setRecommonInfoDao;

    @Autowired
    private PopularityRankWDao popularityRankWDao;

    @Autowired
    private PassportUserInfoDao passportUserInfoDao;

    @Autowired
    private UserRedisDao userRedisDao;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            logicDeal();
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
        }
        return ReturnT.SUCCESS;
    }

    public void logicDeal() {
        boolean result = false;
        List<String> userIdList = new ArrayList<>();

        //获取基金吧默认配置的数据
        List<SetRecommonInfoModel> recConfigList = setRecommonInfoDao.getList(RecommonType.HOT_USER.getValue());
        if (CollectionUtils.isNotEmpty(recConfigList)) {
            userIdList.addAll(recConfigList.stream().map(a -> a.ItemCode).collect(Collectors.toList()));
        }

        //获取人气用户周榜数据前100条合并
        List<PopularityRankWModel> hotUserList = popularityRankWDao.getList(100, Integer.parseInt(DateUtil.dateToStr(DateUtil.calendarDateByMonth(-1), "yyyyMMdd")));
        if (CollectionUtils.isNotEmpty(hotUserList)) {
            userIdList.addAll(hotUserList.stream().map(a -> a.UID).collect(Collectors.toList()));
        }

        LOGGER.info("RecommendHotUserCacheJob[人气用户列表更新]:基金吧配置数据为:{}, 人气用户周榜数据:{}", recConfigList, hotUserList);
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        //根据用户ID获取人气用户信息
        //获取通行证用户信息
        List<PassportUserInfoModelNew> passportUserList = passportUserInfoDao.getPassportUserInfoByIds(userIdList);
        if (CollectionUtils.isNotEmpty(passportUserList)) {
            String passportUserListJson = JsonUtils.toJsonString(passportUserList);
            LOGGER.info("RecommendHotUserCacheJob[人气用户列表更新]:获取通行证用户信息为{}", passportUserListJson);
            List<RecommendHotUserModel> resultList = JsonUtils.toList(passportUserListJson, RecommendHotUserModel.class);
            LOGGER.info("RecommendHotUserCacheJob[人气用户列表更新]:转换后通行证用户信息为:{}", resultList);
            if (CollectionUtils.isNotEmpty(resultList)) {
                result = userRedisDao.set(UserRedisConfig.APP_HOME_HOT_USER, JsonUtils.toJsonString(resultList), EXPIRE_TIME);
            }
        }
        LOGGER.info("RecommendHotUserCacheJob[人气用户列表更新]:存入redis结果为:{}", result);
    }

}
