package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumerDoubleActive;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.guba.TbLikeCount;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.LikeCountDao;
import ttfund.web.communityservice.utils.DateUtil;

@Component
public class ReplyLikeMsgJobDoubleActive {

    private static Logger logger = LoggerFactory.getLogger(ReplyLikeMsgJobDoubleActive.class);

    public static final String KAFKA_LISTENER_ID = "ReplyLikeMsgJobDoubleActive";

    @Autowired
    private LikeCountDao likeCountDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.GubaReplyLikeCount4FundQueue}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_REPLYLIKEMSGJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_pj)
    private void onListen(ConsumerRecord<String, String> record) {
        replyLikeMsg(record);
    }

    /**
     * 评论点赞
     */
    private void replyLikeMsg(ConsumerRecord<String, String> record) {
        try {

            logger.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            JSONObject tmp = JSON.parseObject(record.value());
            TbLikeCount count = new TbLikeCount();
            count.ID = Long.parseLong(tmp.get("reply_id").toString());
            count.Type = 2;
            count.LikeCount = Long.parseLong(tmp.get("like_count").toString());
            count.Time = DateUtil.strToDate(tmp.getString("time"), DateUtil.dateTimeDefaultPattern);

            likeCountDao.insertOrUpdate(count);

        } catch (Exception ex) {
            logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            logger.error(ex.getMessage(), ex);
        }
    }

}
