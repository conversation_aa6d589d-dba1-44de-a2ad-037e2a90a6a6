package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.guba.FundBarModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.FundBarDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基金吧信息入缓存
 */
@JobHandler(value = "fundBarJob")
@Component
public class FundBarJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(FundBarJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private FundBarDao fundBarDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.FUNDBARJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            setToCache(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void setToCache(int batchReadCount) {

        try {
            String breakpointName = UserRedisConfig.FUNDBARJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("第一步，读取断点。断点:{}", breakpoint);

            int round = 0;
            while (true) {
                round++;

                List<FundBarModel> list = fundBarDao.getList(breakpointDate, batchReadCount);

                logger.info("第二步，读取数据-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        list == null ? 0 : list.size(),
                        list == null ? null : list.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

                if (!CollectionUtils.isEmpty(list)) {
                    breakpointDate = list.stream().map(s -> s.UpDateTime).max(Comparator.naturalOrder()).get();

                    for (FundBarModel item : list) {
                        String cacheKey = String.format(BarRedisKey.FUND_BAR_INFO, item.ID);
                        app.barredis.set(cacheKey, JSON.toJSONStringWithDateFormat(item, "yyyy-MM-dd'T'HH:mm:ss"));
                    }
                }

                logger.info("第三步，数据写库-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        list == null ? 0 : list.size(),
                        list == null ? null : list.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("第四步，更新断点-第{}轮。断点：{}", round, breakpoint);


                if (list == null || list.size() < batchReadCount) {
                    break;
                }
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }
}
