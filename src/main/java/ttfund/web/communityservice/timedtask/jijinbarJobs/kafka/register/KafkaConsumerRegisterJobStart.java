package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.register;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * kafka消费启停控制job  启动用
 */
@JobHandler("KafkaConsumerRegisterJobStart")
@Component
public class KafkaConsumerRegisterJobStart extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(KafkaConsumerRegisterJobStart.class);

    @Resource
    private KafkaListenerEndpointRegistry registry;

    /**
     * kafka消费启停控制逻辑
     */
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            List<String> ids = new ArrayList<>();
            Boolean run = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                JSONArray jsonArray = jsonObject.getJSONArray("ids");
                if (jsonArray != null) {
                    jsonArray.forEach(a -> ids.add((String) a));
                }
                run = jsonObject.getBoolean("run");
            }

            if (run == null) {
                run = true;
            }

            logger.info("第零步，打印参数。ids：{}，run：{}", ids, run);

            if (!CollectionUtils.isEmpty(ids)) {
                int i = 0;
                for (String id : ids) {
                    i++;

                    MessageListenerContainer listenerContainer = registry.getListenerContainer(id);
                    if (listenerContainer != null) {
                        boolean running = listenerContainer.isRunning();
                        if (run && !running) {
                            listenerContainer.start();
                        } else if (!run && running) {
                            listenerContainer.stop();
                        }
                    }

                    logger.info("第一步，kafka启停详情。第{}/{}个。id：{}，run：{}",
                            i,
                            ids.size(),
                            id,
                            run);
                }

            }

            logger.info("第一步，kafka启停完成。ids：{}，run：{}", ids, run);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }


        return ReturnT.SUCCESS;
    }

}

