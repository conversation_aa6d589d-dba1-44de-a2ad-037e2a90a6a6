package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumerDoubleActive;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.barrage.PassportUserInfoModel;
import ttfund.web.communityservice.bean.jijinBar.enums.MessagePushInfoSource;
import ttfund.web.communityservice.bean.jijinBar.enums.MessagePushInfoType;
import ttfund.web.communityservice.bean.messagepush.RemindGubaRequestModel;
import ttfund.web.communityservice.bean.messagepush.UserRelationKafkaModel;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.kafka.PushToolsToKafkaDao;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;


/**
 * 关注我的
 * 需要验证 enum tostring 方法
 */
@Component
public class MessagePushFollowMeJobDoubleActive {

    private static final Logger logger = LoggerFactory.getLogger(MessagePushFollowMeJobDoubleActive.class);
    private static String logPre = "MessagePush=>MessagePushFollowMeJob=>";

    public static final String KAFKA_LISTENER_ID = "MessagePushFollowMeJobDoubleActive";

    @Autowired
    PassportUserInfoDao passportUserInfoDao;

    @Autowired
    PushToolsToKafkaDao pushToolsToKafkaDao;

    @KafkaListener(topics = {KafkaTopicName.Newsplatform_follow_guba}, groupId = "message_push_userfollow_20240707",
            containerFactory = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_pj)
    public void kafkaListener(String message) {
        try {

            if(DateUtil.getNowDate().compareTo(DateUtil.strToDate("2022-08-20 13:35:01.305","yyyy-MM-dd HH:mm:ss.SSS"))  <0){
                logger.info(logPre + "消费的信息=>notdeal=>:" + message);
                return;
            }
            logger.info(logPre + "消费的信息:" + message);
            UserRelationKafkaModel relation = JacksonUtil.deserialize(message, UserRelationKafkaModel.class);
            if (relation != null) {
                if (relation.RefType != null && relation.RefType == 1) {

                    String[] userIds = {relation.UserID, relation.ObjID};
                    List<PassportUserInfoModel> userList = passportUserInfoDao.getPassportUserInfoListById(Arrays.asList(userIds));

                    if (!CollectionUtils.isEmpty(userList)) {

                        RemindGubaRequestModel model = new RemindGubaRequestModel();
                        {
                            model.setEID(relation.UserID + "_" + relation.ObjID + "_" + MessagePushInfoType.Follow.getValue());
                            logger.info(logPre + "MessagePushFollowMeJobEID:" + relation.UserID + "_" + relation.ObjID + "_" + MessagePushInfoType.Follow.getValue());
                            model.setContent("");
                            model.setPassportID(relation.ObjID);
                            Optional<PassportUserInfoModel> userInfo = userList.stream().filter(a -> a.PassportID.equals(relation.ObjID)).findFirst();
                            if (!userInfo.isPresent()) return;

                            model.setPassportName(userInfo.get().NickName);
                            model.setReplyPassportID(relation.UserID);

                            Optional<PassportUserInfoModel> userInfo1 = userList.stream().filter(a -> a.PassportID.equals(relation.UserID)).findFirst();
                            if (!userInfo1.isPresent()) return;
                            model.setReplyPassportName(userInfo1.get().NickName);
                           model.setSource(MessagePushInfoSource.FundJijinBarService.toString());
                        } ;
                        pushToolsToKafkaDao.FollowMe(model);
                        logger.info(logPre+"=>推送成功:"+JacksonUtil.serialize(model));
                    }
                }
            }
        } catch (Exception ex) {
            logger.error(logPre+"用户关注消息推送，data:"+ex.getMessage(),ex);
        }
    }
}

