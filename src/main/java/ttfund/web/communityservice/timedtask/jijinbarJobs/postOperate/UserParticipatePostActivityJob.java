package ttfund.web.communityservice.timedtask.jijinbarJobs.postOperate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.config.*;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.PostActivityConfigDao;
import ttfund.web.communityservice.dao.mongo.PostActivityParticipateDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户参与帖子活动统计job
 */
@JobHandler("UserParticipatePostActivityJob")
@Component
public class UserParticipatePostActivityJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(UserParticipatePostActivityJob.class);

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private PostActivityConfigDao postActivityConfigDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private ReplyInfoNewDao replyInfoNewDao;

    @Autowired
    private PostActivityParticipateDao postActivityParticipateDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            //初始化断点
            String breakpoint = null;
            //过期时间
            Integer expireDays = null;
            //是否获取新配置
            String source = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                //初始化断点
                breakpoint = jsonObject.getString("breakpoint");
                //过期时间
                expireDays = jsonObject.getInteger("expireDays");
                //获取配置来源     0：全部  1：雨燕配置后台  2：配置后台-顾兴明
                source = jsonObject.getString("source");
            }

            if (expireDays == null) {
                expireDays = 0;
            }
            if (!StringUtils.hasLength(source)) {
                source = "0";
            }
            if (StringUtils.hasLength(breakpoint)) {
                breakpoint = DateUtil.dateToStr(DateUtil.strToDate(breakpoint, "yyyy-MM-dd HH:mm:ss.SSS"), "yyyy-MM-dd HH:mm:ss.SSS");
            }

            logger.info("零.打印参数。breakpoint：{}，expireDays：{}，source：{}", breakpoint, expireDays, source);

            //第零步，初始化断点
            if (StringUtils.hasLength(breakpoint)) {
                userRedisDao.set(UserRedisConfig.UserParticipatePostActivityJob_breakpoint, breakpoint, 3600 * 24 * 30L);
            }
            //第一步，读取最新配置
            List<PostActivityConfigModelInDb> latestConfig = null;
            switch (source) {
                case "0":
                    latestConfig = getLatestPostActivityConfigNew();
                    Map<String, String> distinctMap = new HashMap<>();
                    latestConfig.forEach(a -> distinctMap.put(a.getActivityId(), "1"));
                    List<PostActivityConfigModelInDb> tempList = getLatestPostActivityConfig();
                    for (PostActivityConfigModelInDb item : tempList) {
                        if (!distinctMap.containsKey(item.getActivityId())) {
                            latestConfig.add(item);
                        }
                    }
                    break;
                case "1":
                    latestConfig = getLatestPostActivityConfig();
                    break;
                case "2":
                    latestConfig = getLatestPostActivityConfigNew();
                    break;
            }


            logger.info("一.读取最新配置。latestConfig：{}", JSON.toJSONString(latestConfig));

            //第二步，读取历史配置
            List<PostActivityConfigModelInDb> historyConfig = getHistoryPostActivityConfig();
            logger.info("二.读取历史配置。historyConfig：{}", JSON.toJSONString(historyConfig));

            //第三步，处理配置，对配置项进行分类
            List<PostActivityConfigModelInDb> insertConfig = new ArrayList<>();   //新增的配置项
            List<PostActivityConfigModelInDb> deleteConfig = new ArrayList<>();   //删除的配置项
            List<PostActivityConfigModelInDb> updateConfig = new ArrayList<>();   //修改的配置项
            List<PostActivityConfigModelInDb> ordinaryConfig = new ArrayList<>(); //正常的配置项
            dealConfig(latestConfig, historyConfig, insertConfig, deleteConfig, updateConfig, ordinaryConfig);
            logger.info("三，对配置项进行分类。insertConfig：{}，deleteConfig：{}，updateConfig：{}，ordinaryConfig：{}"
                    , JSON.toJSONString(insertConfig)
                    , JSON.toJSONString(deleteConfig)
                    , JSON.toJSONString(updateConfig)
                    , JSON.toJSONString(ordinaryConfig));

            //第四步，对分类后的配置项进行处理
            Date now = new Date();
            dealOrdinary(ordinaryConfig, now);
            dealInsert(insertConfig, now);
            dealUpdate(updateConfig, now);
            dealDelete(deleteConfig, now);
            logger.info("四，对分类后的配置项进行处理");

            //第五步，更新配置项
            latestConfig.forEach(item -> item.setUpdateTime(now));
            List<PostActivityConfigModelInDb> totalConfig = new ArrayList<>();
            totalConfig.addAll(insertConfig);
            totalConfig.addAll(updateConfig);
            totalConfig.addAll(ordinaryConfig);
            totalConfig.addAll(deleteConfig);
            postActivityConfigDao.upsertBulk(totalConfig, PostActivityConfigModelInDb.class, BarMongodbConfig.TABLE_POST_ACTIVITY_CONFIG, postActivityConfigDao.getTemplate());
            logger.info("五，更新配置项");

            //第六步，更新断点
            String newBreakpoint = DateUtil.dateToStr(now, "yyyy-MM-dd HH:mm:ss.SSS");
            userRedisDao.set(UserRedisConfig.UserParticipatePostActivityJob_breakpoint, newBreakpoint, 3600 * 24 * 30L);
            logger.info("六，更新断点。newBreakpoint：{}", newBreakpoint);

            //第七步，删除过期数据
            delData(expireDays);
            logger.info("七，删除过期数据");
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 处理配置项，对配置项进行分类
     *
     * <AUTHOR>
     */
    private void dealConfig(List<PostActivityConfigModelInDb> latestConfig, List<PostActivityConfigModelInDb> historyConfig,
                            List<PostActivityConfigModelInDb> insertConfig, List<PostActivityConfigModelInDb> deleteConfig,
                            List<PostActivityConfigModelInDb> updateConfig, List<PostActivityConfigModelInDb> ordinaryConfig) {
        Map<String, PostActivityConfigModelInDb> latestConfigMap = new HashMap<>();
        Map<String, PostActivityConfigModelInDb> historyConfigMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(latestConfig)) {
            latestConfig.forEach(item -> latestConfigMap.put(item.getActivityId(), item));
        }
        if (!CollectionUtils.isEmpty(historyConfig)) {
            historyConfig.forEach(item -> historyConfigMap.put(item.getActivityId(), item));
        }
        if (!CollectionUtils.isEmpty(historyConfig)) {
            deleteConfig.addAll(historyConfig.stream().filter(item -> !latestConfigMap.containsKey(item.getActivityId())).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(latestConfig)) {
            for (PostActivityConfigModelInDb item : latestConfig) {
                if (!historyConfigMap.containsKey(item.getActivityId())) {
                    insertConfig.add(item);
                } else {
                    if (item.getStartTime().compareTo(historyConfigMap.get(item.getActivityId()).getStartTime()) == 0
                            && item.getEndTime().compareTo(historyConfigMap.get(item.getActivityId()).getEndTime()) == 0) {
                        ordinaryConfig.add(item);
                    } else {
                        updateConfig.add(item);
                    }
                }
            }
        }
    }

    /**
     * 处理过期数据
     *
     * <AUTHOR>
     */
    private void delData(int days) {
        if (days < 0) {
            Date date = DateUtil.calendarDateByDays(days);
            List<PostActivityConfigModelInDb> delConfigs = postActivityConfigDao.getList(PostActivityConfigModelInDb.class, "1", date);
            List<String> delPostIds = null;
            if (!CollectionUtils.isEmpty(delConfigs)) {
                delPostIds = delConfigs.stream().map(a -> a.getActivityId()).collect(Collectors.toList());
                postActivityParticipateDao.delByPostId(delPostIds);
            }

            postActivityConfigDao.del(delPostIds);

        }
    }

    /**
     * 处理删除的配置项
     *
     * <AUTHOR>
     */
    private void dealDelete(List<PostActivityConfigModelInDb> deleteList, Date now) {
        if (!CollectionUtils.isEmpty(deleteList)) {
            deleteList.stream().forEach(a -> {
                if (a.getIsDel() == null || "0".equals(a.getIsDel())) {
                    a.setIsDel("1");
                    a.setUpdateTime(now);
                }
            });
        }
    }

    /**
     * 处理普通的配置项
     *
     * <AUTHOR>
     */
    private void dealOrdinary(List<PostActivityConfigModelInDb> ordinaryList, Date now) {
        Date breakpoint = userRedisDao.getBreakTime(UserRedisConfig.UserParticipatePostActivityJob_breakpoint, null, "yyyy-MM-dd HH:mm:ss.SSS");

        if (breakpoint == null) {
            throw new RuntimeException("获取断点为null。因断点不能为null，特意设置为null时抛出异常");
        }

        if (!CollectionUtils.isEmpty(ordinaryList) && breakpoint != null) {
            String postId = null;
            Date start = null;
            Date end = null;
            List<PostActivityParticipateModel> upsertToMongoList = new ArrayList<>();
            for (PostActivityConfigModelInDb item : ordinaryList) {
                postId = item.getActivityId();
                if (item.getEndTime().compareTo(DateUtil.calendarDateByHour(breakpoint, -1)) < 0) {
                    continue;
                } else {
                    start = item.getStartTime();
                    end = item.getEndTime();
                }
                List<PostActivityParticipateModel> list = replyInfoNewDao.getUidsByPostidAndTime(Long.parseLong(postId), start, end, null);
                if (!CollectionUtils.isEmpty(list)) {
                    list.sort((Comparator.comparing(PostActivityParticipateModel::getUid).thenComparing(PostActivityParticipateModel::getTime)));
                    String tempUid = null;
                    for (PostActivityParticipateModel a : list) {
                        if (!a.getUid().equals(tempUid)) {
                            a.set_id(append(item.getActivityId(), a.getUid()));
                            a.setPostId(item.getActivityId());
                            a.setUpdateTime(now);
                            a.setIsDel("0");
                            upsertToMongoList.add(a);
                            tempUid = a.getUid();
                        }
                    }
                }

            }
            if (!CollectionUtils.isEmpty(upsertToMongoList)) {
                List<List<PostActivityParticipateModel>> batchList = CommonUtils.toSmallList2(upsertToMongoList, 400);
                for (List<PostActivityParticipateModel> item : batchList) {
                    if (!CollectionUtils.isEmpty(item)) {
                        postActivityParticipateDao.setOnInsertBulk(item);
                    }
                }
            }
        }
    }

    /**
     * 处理新增的配置项
     *
     * <AUTHOR>
     */
    private void dealInsert(List<PostActivityConfigModelInDb> insertList, Date now) {
        if (!CollectionUtils.isEmpty(insertList)) {
            String postId = null;
            Date start = null;
            Date end = null;
            List<PostActivityParticipateModel> upsertToMongoList = new ArrayList<>();
            for (PostActivityConfigModelInDb item : insertList) {
                postId = item.getActivityId();
                start = item.getStartTime();
                end = item.getEndTime();
                List<PostActivityParticipateModel> list = replyInfoNewDao.getUidsByPostidAndTime(Long.parseLong(postId), start, end, null);
                if (!CollectionUtils.isEmpty(list)) {
                    list.sort((Comparator.comparing(PostActivityParticipateModel::getUid).thenComparing(PostActivityParticipateModel::getTime)));
                    String tempUid = null;
                    for (PostActivityParticipateModel a : list) {
                        if (!a.getUid().equals(tempUid)) {
                            a.set_id(append(item.getActivityId(), a.getUid()));
                            a.setPostId(item.getActivityId());
                            a.setUpdateTime(now);
                            a.setIsDel("0");
                            upsertToMongoList.add(a);
                            tempUid = a.getUid();
                        }
                    }
                }
            }
            if (!CollectionUtils.isEmpty(upsertToMongoList)) {
                List<List<PostActivityParticipateModel>> batchList = CommonUtils.toSmallList2(upsertToMongoList, 400);
                for (List<PostActivityParticipateModel> item : batchList) {
                    if (!CollectionUtils.isEmpty(item)) {
                        postActivityParticipateDao.upsertBulk(item, PostActivityParticipateModel.class
                                , BarMongodbConfig.TABLE_POST_ACTIVITY_PARTICIPATE, postActivityParticipateDao.getTemplate());
                    }
                }
            }
        }
    }

    /**
     * 处理修改的配置项
     *
     * <AUTHOR>
     */
    private void dealUpdate(List<PostActivityConfigModelInDb> updateList, Date now) {
        if (!CollectionUtils.isEmpty(updateList)) {
            String postId = null;
            Date start = null;
            Date end = null;
            List<PostActivityParticipateModel> upsertToMongoList = new ArrayList<>();
            for (PostActivityConfigModelInDb item : updateList) {
                postId = item.getActivityId();
                start = item.getStartTime();
                end = item.getEndTime();
                List<PostActivityParticipateModel> list = replyInfoNewDao.getUidsByPostidAndTime(Long.parseLong(postId), start, end, null);
                if (!CollectionUtils.isEmpty(list)) {
                    list.sort((Comparator.comparing(PostActivityParticipateModel::getUid).thenComparing(PostActivityParticipateModel::getTime)));
                    String tempUid = null;
                    for (PostActivityParticipateModel a : list) {
                        if (!a.getUid().equals(tempUid)) {
                            a.set_id(append(item.getActivityId(), a.getUid()));
                            a.setPostId(item.getActivityId());
                            a.setUpdateTime(now);
                            a.setIsDel("0");
                            upsertToMongoList.add(a);
                            tempUid = a.getUid();
                        }
                    }
                }
            }

            postActivityParticipateDao.delByPostId(updateList.stream().map(a -> a.getActivityId()).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(upsertToMongoList)) {
                List<List<PostActivityParticipateModel>> batchList = CommonUtils.toSmallList2(upsertToMongoList, 400);
                for (List<PostActivityParticipateModel> item : batchList) {
                    if (!CollectionUtils.isEmpty(item)) {
                        postActivityParticipateDao.upsertBulk(item, PostActivityParticipateModel.class
                                , BarMongodbConfig.TABLE_POST_ACTIVITY_PARTICIPATE, postActivityParticipateDao.getTemplate());
                    }
                }
            }
        }
    }


    /**
     * 获取最新的帖子活动配置, 全量,只取帖子活动,并做格式、正确性校验
     *
     * <AUTHOR>
     */
    private List<PostActivityConfigModelInDb> getLatestPostActivityConfig() {
        List<PostActivityConfigModelInDb> result = new ArrayList<>();
        List<PostActivityConfigModel> tempList = new ArrayList<>();
        String html = HttpHelper.requestGet(appConstant.post_activity_config_url);
        if (StringUtils.hasLength(html)) {
            PostActivityConfigApiResponse apiResponse = JSON.parseObject(html, PostActivityConfigApiResponse.class);
            if (apiResponse != null && !CollectionUtils.isEmpty(apiResponse.getData())) {
                for (PostActivityConfigDataModel outItem : apiResponse.getData()) {
                    if (outItem != null && !CollectionUtils.isEmpty(outItem.getActivityList())) {
                        tempList.addAll(outItem.getActivityList());
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(tempList)) {
            tempList = tempList.stream().filter(item -> "post".equals(item.getActivityPageType())
                    && !StringUtils.isEmpty(item.getActivityId()) && isLong(item.getActivityId())
                    && item.getStartTime() != null && item.getEndTime() != null
            ).collect(Collectors.toList());
        }

        if (!CollectionUtils.isEmpty(tempList)) {
            for (PostActivityConfigModel item : tempList) {
                PostActivityConfigModelInDb model = new PostActivityConfigModelInDb();
                model.set_id(item.getActivityId());
                model.setActivityId(item.getActivityId());
                model.setActivityName(item.getActivityName());
                model.setStartTime(item.getStartTime());
                model.setEndTime(item.getEndTime());
                model.setSource("1");
                model.setIsDel("0");
                result.add(model);
            }
        }
        return result;
    }

    /**
     * 获取历史的帖子活动配置, 全量
     *
     * <AUTHOR>
     */
    private List<PostActivityConfigModelInDb> getHistoryPostActivityConfig() {
        List<PostActivityConfigModelInDb> result = postActivityConfigDao.getAll(PostActivityConfigModelInDb.class);
        if (result == null) {
            result = new ArrayList<>();
        }
        return result;
    }

    private String append(String str1, String str2) {
        StringBuilder builder = new StringBuilder(str1.length() + "_".length() + str2.length());
        builder.append(str1);
        builder.append("_");
        builder.append(str2);
        return builder.toString();
    }

    /**
     * 获取最新的帖子活动配置, 全量,只取帖子活动,并做格式、正确性校验-新
     *
     * <AUTHOR>
     */
    private List<PostActivityConfigModelInDb> getLatestPostActivityConfigNew() {
        List<PostActivityConfigModelInDb> result = new ArrayList<>();
        String html = HttpHelper.requestGet(appConstant.post_activity_config_url_new);
        if (StringUtils.hasLength(html)) {
            PostActivityConfigApiResponseNew apiResponse = JSON.parseObject(html, PostActivityConfigApiResponseNew.class);
            if (apiResponse != null && apiResponse.getResultCode() != null && apiResponse.getResultCode() == 0
                    && apiResponse.getDatas() != null && !CollectionUtils.isEmpty(apiResponse.getDatas().getContent())) {

                List<PostActivityConfigModelNew> list = apiResponse.getDatas().getContent();
                if (!CollectionUtils.isEmpty(list)) {
                    for (PostActivityConfigModelNew item : list) {
                        if ("2".equals(item.getActivityType())
                                && StringUtils.hasLength(item.getActivityId()) && isLong(item.getActivityId())
                                && item.getStartTime() != null && item.getEndTime() != null) {
                            PostActivityConfigModelInDb model = new PostActivityConfigModelInDb();
                            model.set_id(item.getActivityId());
                            model.setActivityId(item.getActivityId());
                            model.setActivityName(item.getActivityName());
                            model.setStartTime(item.getStartTime());
                            model.setEndTime(item.getEndTime());
                            model.setSource("2");
                            model.setIsDel("0");
                            result.add(model);
                        }
                    }
                }
            }
        }

        return result;
    }

    /**
     * 校验帖子id是否是合法的
     */
    private boolean isLong(String postId) {
        boolean result = false;
        try {
            if (StringUtils.hasLength(postId)) {
                Long.parseLong(postId);
                result = true;
            }
        } catch (Exception ex) {

        }
        return result;
    }

}
