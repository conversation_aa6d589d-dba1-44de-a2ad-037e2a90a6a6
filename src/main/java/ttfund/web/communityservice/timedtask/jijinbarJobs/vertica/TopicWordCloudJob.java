package ttfund.web.communityservice.timedtask.jijinbarJobs.vertica;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.data.TopicWordDo;
import ttfund.web.communityservice.dao.mongo.TopicWordMongoDao;
import ttfund.web.communityservice.dao.vertica.PostHtRelationBasicAllDao;

import java.util.List;

/**
 * <AUTHOR> Zhuyuang
 * @date : 2024-08-20 17:30
 * @description :
 */
@JobHandler("TopicWordCloudJob")
@Component
public class TopicWordCloudJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(TopicWordCloudJob.class);

    private static final String LOG_PREFIX = "TopicWordCloudJob[话题词云同步服务]";

    @Autowired
    private PostHtRelationBasicAllDao postHtRelationBasicAllDao;

    @Autowired
    private TopicWordMongoDao topicWordMongoDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        List<TopicWordDo> topicWordDoList = postHtRelationBasicAllDao.getTopicWordCloud();
        LOGGER.info("{}: 从Vertica拉取话题词云数量为: {}", LOG_PREFIX, topicWordDoList.size());
        if (CollectionUtils.isEmpty(topicWordDoList)) {
            return null;
        }

        boolean result = topicWordMongoDao.upsertMany(topicWordDoList, "_id");
        LOGGER.info("{}: 生成结果为: {}", LOG_PREFIX, result);
        return ReturnT.SUCCESS;
    }
}
