package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.post.guba.GubaApiResponseBase;
import ttfund.web.communityservice.bean.jijinBar.post.guba.TopicPostInfoEntity;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.msyql.PostTopicRetDao;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定投日记 —— 关联话题帖子服务
 * 展示三个帖子 优先级：置顶>评分>普通
 *
 * @author：liyaogang
 * @date：2023/4/3 18:07
 */
@JobHandler(value = "topicAssociatedPostJob")
@Component
public class TopicAssociatedPostJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(TopicAssociatedPostJob.class);

    @Resource
    AppConstant appConstant;

    @Resource
    PostDao postDao;

    @Resource
    PostTopicRetDao postTopicRetDao;

    @Autowired
    App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            List<String> postIds = new ArrayList<>();
            String htId = appConstant.fixedInvestmentDiaryTopic;
            String servicePath = appConstant.config_gubahostnew + "/fundfocustopic/api/topic/TopicPostListRead";
            StringBuilder urlParams = new StringBuilder(400);
            //自定义参数
            urlParams.append("&deviceid=").append("b239bbfa2ed9017cd9e20d8454bce864||iemi_tluafed_me");
            urlParams.append("&version=").append("6.6.0");
            urlParams.append("&product=").append("Fund");
            urlParams.append("&plat=").append("Android");
            urlParams.append("&htid=").append(htId);
            //0根据p、ps分页；1根据start、offset分页；默认0
            urlParams.append("&type=0");
            urlParams.append("&p=").append("1");
            urlParams.append("&ps=").append("10");
            urlParams.append("&sort=").append("99");
            urlParams.append("&system=1");

            String html = HttpHelper.requestGet(servicePath, urlParams.toString(), 3000, true);
            if (!"".equals(html)) {
                GubaApiResponseBase<List<TopicPostInfoEntity>> gubaApiRes = JSON.parseObject(html, new TypeReference<GubaApiResponseBase<List<TopicPostInfoEntity>>>() {
                });
                //操作成功
                if (gubaApiRes != null && gubaApiRes.getReturnCode() == 1 && gubaApiRes.getRe() != null) {
                    if (gubaApiRes.getCount() > 0) {
                        List<String> topPostIds = gubaApiRes.getRe().stream()
                                .filter(t -> t.getTopflag() == 1)
                                .map(TopicPostInfoEntity::getPostid)
                                .collect(Collectors.toList());
                        //过滤属于基金吧的帖子
                        List<String> jjbarPost = new ArrayList<>();
                        if (topPostIds.size() > 0) {
                            for (String postId : topPostIds) {
                                PostInfoNewModel postInfo = postDao.getFromMong(postId, null, PostInfoNewModel.class);
                                if (postInfo != null && postInfo.ID != null) {
                                    jjbarPost.add(postId);
                                }
                            }
                        }
                        if (jjbarPost.size() < 3) {
                            postIds.addAll(jjbarPost);
                            int left = 3 - jjbarPost.size();
                            //剩下取评分高的
                            List<String> highPost = getHighScorePost(left, htId);
                            postIds.addAll(highPost);
                        } else {
                            //取前三个返回
                            postIds.addAll(jjbarPost.subList(0, 3));
                        }
                    } else {
                        List<String> highPost = getHighScorePost(3, htId);
                        postIds.addAll(highPost);
                    }
                }

                //缓存
                String key = BarRedisKey.FIXED_INVESTMENT_TOPIC + htId;
                StringBuilder sb = new StringBuilder();
                for (String id : postIds) {
                    sb.append(id);
                    sb.append(",");
                }
                boolean res = app.barredis.set(key, postIds.size() > 0 ? sb.toString() : "", 60 * 60 * 24 * 7L);
                logger.info("话题：{}关联下优先展示帖子写入redis：{}，size:{}，帖子为：{}", htId, res, postIds.size(), postIds.toString());
            }
        } catch (Exception e) {
            logger.error("话题：{}关联下优先展示帖子写入redis失败，error:{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 拿出话题下所有帖子(del=0)，再到Mongo PostRankNew 查得分
     *
     * @param left 需要帖子数
     */
    private List<String> getHighScorePost(int left, String htId) {
        List<String> list = new ArrayList<>();
        //查话题帖子表
        List<String> postIds = postTopicRetDao.getPostOfTopic(htId);
        if (postIds != null && postIds.size() > 0) {
            //查帖子得分
            List<Document> docs = postDao.getPostScore(postIds, left);
            List<Object> ids = docs.stream().map(m -> m.get("ID")).collect(Collectors.toList());
            for (Object id : ids) {
                list.add(String.valueOf(id));
            }
        }
        return list;
    }
}
