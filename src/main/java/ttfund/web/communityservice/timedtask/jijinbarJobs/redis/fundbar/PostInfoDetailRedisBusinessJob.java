package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostDetailEntity;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 帖子基本信息同步Redis
 */
@JobHandler(value = "postInfoDetailRedisBusinessJob")
@Component
public class PostInfoDetailRedisBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PostInfoDetailRedisBusinessJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostDao postDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String param) throws JsonProcessingException {

        ReturnT<String> returnStatus = ReturnT.SUCCESS;

        try {
            String cacheKeyNew = BarRedisKey.FUND_GUBA_SERVICE_POSTINFO_DETAIL_NEW;
            String breakPointKey = "PostInfoDetailBreakPoint";
            Date lastTime = userRedisDao.getBreakTime(breakPointKey, DateUtil.calendarDateByDays(-7));

            if (StringUtils.hasLength(param)) {
                lastTime = DateUtil.strToDate(param, DateUtil.datePattern);
                logger.info("当前时间断点为：{}", param);
            }

            boolean isRunning = true;
            int batchReadCount = 12000;

            while (isRunning) {

                //如果断点时间比当前时间大，是不合理的
                if (lastTime.compareTo(DateUtil.getNowDate()) >= 0) {
                    logger.warn("非法的断点时间：" + DateUtil.dateToStr(lastTime));
                    lastTime = DateUtil.calendarDateByMinute(-5);
                }

                // 从MongoDb获取帖子
                List<PostDetailEntity> postList = postDao.getPostDetailIncrement(lastTime, batchReadCount);

                //logger.info("postList:{}", Arrays.toString(postList.toArray()));

                if (!CollectionUtils.isEmpty(postList)) {

                    Date maxUpdate = postList.stream().map(w -> w.UPDATETIME).max(Comparator.naturalOrder()).get();

                    if (lastTime.equals(maxUpdate) && postList.size() >= batchReadCount) {
                        logger.error("同一时间数据量过大,超过阀值");
                        break;
                    }

                    logger.info("PostInfoDetailRedisBusinessJob本次同步帖子：{}条,最大UpdateTime:{}", postList.size(),
                            DateUtil.dateToStr(maxUpdate));

                    long time = System.currentTimeMillis();

                    for (PostDetailEntity info : postList) {
                        if (info != null) {
                            try {
                                if (info.DEL != 0 || info.TTJJDEL == 1 || info.ISENABLED == 0) {
                                    app.barredis.del(cacheKeyNew + info.ID);
                                } else {
                                    try {
                                        app.barredis.set(cacheKeyNew + info.ID, JacksonUtil.obj2String(info), 90 * 24 * 3600L);
                                    } catch (Exception ex) {
                                        app.barredis.set(cacheKeyNew + info.ID, JacksonUtil.obj2String(info), 90 * 24 * 3600L);
                                    }
                                }
                            } catch (Exception e) {
                                logger.error("帖子id：{},info:{},更新redis失败，error：{}", info._id, info,e);
                            }
                        }
                    }
                    List<Integer> ids = postList.stream().map(p -> p.ID).collect(Collectors.toList());
                    logger.info("PostInfoDetailRedisBusinessJob本次同步帖子：{}条,分别为：{},耗时:{}", postList.size(), ids.toString()
                            , System.currentTimeMillis() - time);
                    //如果更新时间比当前时间大为不合法时间
                    if (maxUpdate.compareTo(DateUtil.getNowDate()) >= 0) {
                        logger.warn("非法的断点时间：" + DateUtil.dateToStr(maxUpdate) + ",帖子:"
                                + JacksonUtil.obj2String(postList.get(postList.size() - 1)));
                        maxUpdate = DateUtil.calendarDateByMinute(-5);
                    }

                    userRedisDao.setBreakTime(breakPointKey, maxUpdate);
                    lastTime = maxUpdate;

                    if (postList.size() < batchReadCount) {
                        isRunning = false;
                    }
                } else {
                    isRunning = false;
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            returnStatus = ReturnT.FAIL;
        }

        return returnStatus;
    }
}
