package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.dao.mongo.HotFundBarDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.time.LocalTime;
import java.util.*;

/**
 * 社区->榜单->热门基金吧
 * 热门基金吧 生成
 */
@JobHandler("HotFundBarCalcJob")
@Component
public class HotFundBarCalcJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(HotFundBarCalcJob.class);

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private ReplyInfoDao replyInfoDao;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private HotFundBarDao hotFundBarDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            hotFundBarCalcProcess();

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 分数 = 24 小时内帖子数*2+24 小时内总评论数*5+48 小时内帖子数*1
     * 筛选过滤掉基金点评吧、基金动态吧等非基金吧
     */
    private void hotFundBarCalcProcess() {
        try {
            if ((LocalTime.now().compareTo(LocalTime.of(12, 50)) > 0 && LocalTime.now().compareTo(LocalTime.of(15, 10)) < 0)
                    || (LocalTime.now().compareTo(LocalTime.of(9, 20)) > 0 && LocalTime.now().compareTo(LocalTime.of(11, 30)) < 0)) {

                logger.info("零.高峰时间段不执行");
                return;
            }

            List<String> hotFundBarCodes = CommonUtils.toList(appConstant.hotFundBarCodes, ",");
            String[] configs = appConstant.hotFundBarConfig.split(",");

            logger.info("零.打印配置。hotFundBarCodes：{}，configs：{}", hotFundBarCodes, configs);

            Map<String, Integer> postCount24h = postInfoNewDao.getPostCountOfFundBar(DateUtil.calendarDateByDays(-1), new Date(), hotFundBarCodes);
            Map<String, Integer> postCount48h = postInfoNewDao.getPostCountOfFundBar(DateUtil.calendarDateByDays(-2), new Date(), hotFundBarCodes);
            Map<String, Integer> replyCount24h = replyInfoDao.getReplyCountOfFundBar(DateUtil.calendarDateByDays(-1), new Date());
            if(postCount24h == null){
                postCount24h = new HashMap<>();
            }
            if(postCount48h == null){
                postCount48h = new HashMap<>();
            }
            if(replyCount24h == null){
                replyCount24h = new HashMap<>();
            }

            logger.info("一.读取数据库。postCount24h数量：{}，postCount48h数量：{}，replyCount24h数量：{}",
                    postCount24h == null ? 0 : postCount24h.size(),
                    postCount48h == null ? 0 : postCount48h.size(),
                    replyCount24h == null ? 0 : replyCount24h.size()
            );

            Set<String> keys = new HashSet<>();
            if (!CollectionUtils.isEmpty(postCount24h)) {
                keys.addAll(postCount24h.keySet());
            }
            if (!CollectionUtils.isEmpty(postCount48h)) {
                keys.addAll(postCount48h.keySet());
            }
            if (!CollectionUtils.isEmpty(replyCount24h)) {
                keys.addAll(replyCount24h.keySet());
            }
            if (!CollectionUtils.isEmpty(keys)) {
                List<Map<String, Object>> mapList = new ArrayList<>();
                Map<String, Object> map = null;
                for (String key : keys) {
                    if (!StringUtils.hasLength(key)) {
                        continue;
                    }
                    int score = 0;
                    if (postCount48h.containsKey(key)) {
                        score += postCount48h.get(key) * Integer.parseInt(configs[2]);
                    }
                    if (postCount24h.containsKey(key)) {
                        score += postCount24h.get(key) * Integer.parseInt(configs[0]);
                    }
                    if (replyCount24h.containsKey(key)) {
                        score += replyCount24h.get(key) * Integer.parseInt(configs[1]);
                    }

                    map = new HashMap<>();
                    map.put("_id", key);
                    map.put("CODE", key);
                    map.put("SCORE", score);
                    mapList.add(map);
                }

                logger.info("二.计算得分。数量：{}", keys == null ? 0 : keys.size());

                //拿存量的热吧ID
                List<String> existIDs = hotFundBarDao.getAllCodes();

                logger.info("三.读取存量热吧。数量：{}", existIDs == null ? 0 : existIDs.size());

                List<String> deleteCodes = new ArrayList<>();
                if (!CollectionUtils.isEmpty(existIDs)) {
                    for (String item : existIDs) {
                        if (!keys.contains(item)) {
                            deleteCodes.add(item);
                        }
                    }
                }
                //删掉过时的热吧
                if (!CollectionUtils.isEmpty(deleteCodes)) {
                    List<List<String>> batchList = CommonUtils.toSmallList2(deleteCodes, 200);
                    for (List<String> batch : batchList) {
                        hotFundBarDao.deleteByCodes(batch);
                    }
                }

                logger.info("四.删除过时热吧。数量：{}", deleteCodes == null ? 0 : deleteCodes.size());

                if (!CollectionUtils.isEmpty(mapList)) {
                    List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 200);
                    for (List<Map<String, Object>> batch : batchList) {
                        hotFundBarDao.upsertMany(batch);
                    }
                }

                logger.info("五.写入新的热吧。数量：{}", mapList == null ? 0 : mapList.size());
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }

}
