package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.CacheHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.text.StringEscapeUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumPostType;
import ttfund.web.communityservice.bean.jijinBar.mongo.PassportFundMrgModel;
import ttfund.web.communityservice.bean.jijinBar.post.FundPostExtras;
import ttfund.web.communityservice.bean.jijinBar.post.FundPostExtrasProP;
import ttfund.web.communityservice.bean.jijinBar.post.FundTopic;
import ttfund.web.communityservice.bean.jijinBar.post.KeyWordModel;
import ttfund.web.communityservice.bean.jijinBar.post.KeywordsEntity;
import ttfund.web.communityservice.bean.jijinBar.post.PostExtendInfo;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoExtendField;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoForMysqlModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoForSubAccount;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostVideoInfo;
import ttfund.web.communityservice.bean.jijinBar.post.ProfitEntity;
import ttfund.web.communityservice.bean.jijinBar.post.PushResultEntity;
import ttfund.web.communityservice.bean.jijinBar.post.ReviewScorePost;
import ttfund.web.communityservice.bean.jijinBar.post.RobotLikeEntity;
import ttfund.web.communityservice.bean.jijinBar.post.WordLenModel;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.RoomCoverResourceModel;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.VideoInfo;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.FundUserProfitDao;
import ttfund.web.communityservice.dao.mongo.PassportFundMrgDao;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.mongo.PostInfoExtendDao;
import ttfund.web.communityservice.dao.mongo.PostInfoExtendFlagDao;
import ttfund.web.communityservice.dao.mongo.ReviewScorePostDao;
import ttfund.web.communityservice.dao.mongo.SubAccountDtoDao;
import ttfund.web.communityservice.dao.mongo.SubAccountDtoModelPortfolioDao;
import ttfund.web.communityservice.dao.mongo.UserTransPostDao;
import ttfund.web.communityservice.dao.mongo.UserTransPostModelPortfolioDao;
import ttfund.web.communityservice.dao.mongo.VideoArticleDao;
import ttfund.web.communityservice.dao.msyql.FundTopicDao;
import ttfund.web.communityservice.dao.msyql.GroupTagPostRelationDao;
import ttfund.web.communityservice.dao.msyql.JjbconfigDao;
import ttfund.web.communityservice.dao.msyql.KeywordDao;
import ttfund.web.communityservice.dao.msyql.PostInfoDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.msyql.ProfitDao;
import ttfund.web.communityservice.dao.msyql.SetHideDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.enums.BarCodeInnerEnum;
import ttfund.web.communityservice.enums.EnumPostDel;
import ttfund.web.communityservice.enums.EnumTTJJDEL;
import ttfund.web.communityservice.service.hanlp.KeywordHanlpService;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;
import ttfund.web.communityservice.utils.OneTuple;
import ttfund.web.communityservice.utils.ThreeTuple;
import ttfund.web.communityservice.utils.TwoTuple;

import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLConnection;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.imageio.ImageIO;

@JobHandler("PostSyncBizMysqlJob")
@Component
public class PostSyncBizMysqlJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PostSyncBizMysqlJob.class);

    public static List<Integer> ROBOT_LIKE_TYPES = Arrays.asList(0, 50);

    //终结符
    private static final List<Character> TERMINATOR_CHAR_LIST = Arrays.asList(
        //中文符号 参考：https://blog.csdn.net/A_Lonely_Smile/article/details/83381392
        '，', '。', '？', '！', '；', '：', '、', '·', '~', '“', '”', '（', '）', '〔', '〕', '’', '‘', '【', '】', '《', '》', '—', '…', '–', '￥',
        //英文符号
        ',', '.', '?', '!', ';', ':', '/', '\'', '\"', '\\', '|', '`', '[', ']', '{', '}', '(', ')', '<', '>',
        '+', '-', '*', '=', '_',
        '@', '#', '$', '%', '^', '&',
        //特殊符号
        '\n', '\r', '\t', ' '
    );

    //帖子列表
    private static Set<Integer> postPushList = ConcurrentHashMap.newKeySet();

    private static final String VIDEO_ID_REGEX = "\\[videoId=(.*?)\\]";
    private static final Pattern VIDEO_ID_REGEX_PATTERN = Pattern.compile(VIDEO_ID_REGEX);

    private static final String ABSTRACT_REGEX = "data-abstract=\"(.*?)\"";
    private static final Pattern ABSTRACT_REGEX_PATTERN = Pattern.compile(ABSTRACT_REGEX);

    private static final String PIC_REGEX = "<img(.*?)src=\"(.*?)\"(.*?)/?>";
    private static final Pattern PIC_REGEX_PATTERN = Pattern.compile(PIC_REGEX);

    private static final String NEWPIC_REGEX = "<img\\b[^<>]*?\\bsrc[\\s\\t\\r\\n]*=[\\s\\t\\r\\n]*[\"\"']?[\\s\\t\\r\\n]*(?<imgUrl>[^\\s\\t\\r\\n\"\"'<>]*)[^<>]*?/?[\\s\\t\\r\\n]*>";
    private static final Pattern NEWPIC_REGEX_PATTERN = Pattern.compile(NEWPIC_REGEX, Pattern.CASE_INSENSITIVE);

    //private static final String SRC_REGEX = "\"(.*?)\"";
    private static final String SRC_REGEX = "(.*?)src=\"(.*?)\"(.*?)";
    private static final Pattern SRC_REGEX_PATTERN = Pattern.compile(SRC_REGEX);

    private static final String HOLDPLACE_REGEX = "<!--adr(.*?)-->";
    private static final Pattern HOLDPLACE_REGEX_PATTERN = Pattern.compile(HOLDPLACE_REGEX);

    private static final String HOLDPLACE = "<!--adr{0}-->";

    private static final String LINK_IMG = "<img\\b[^>]*>";
    private static final Pattern LINK_IMG_PATTERN = Pattern.compile(LINK_IMG);

    private static final String ImgPlace = "<!--img{0}-->";

    private static final String IMG_REGEX = "\\[img\\](.*?)\\[\\/img\\]";
    private static final Pattern IMG_REGEX_PATTERN = Pattern.compile(IMG_REGEX);

    private static final String USER_REGEX = "\\[at=(.*?)\\]([\\s\\S]*?)\\[/at\\]";
    private static final Pattern USER_REGEX_PATTERN = Pattern.compile(USER_REGEX);

    private static final String TOPIC_REGEX = "\\[topic_name=(.*?)##topic_id=(.*?)\\]";
    private static final Pattern TOPIC_REGEX_PATTERN = Pattern.compile(TOPIC_REGEX);

    private static final String TOPIC_NEW_REGEX = "#(.*?)#";//#债券基金#不错[大笑]
    private static final Pattern TOPIC_NEW_REGEX_PATTERN = Pattern.compile(TOPIC_NEW_REGEX);

    private static final String A_TAB_REGEX = "<a(.*?)>(.*?)</a>";
    private static final Pattern A_TAB_REGEX_PATTERN = Pattern.compile(A_TAB_REGEX, Pattern.CASE_INSENSITIVE);

    private static final String TAB_REGEX = "<[^>]*?>";
    private static final Pattern TAB_REGEX_PATTERN = Pattern.compile(TAB_REGEX, Pattern.CASE_INSENSITIVE);

    //private static final String TAB_HOLD_PLACE = "♫";

    private static final String TAB_HOLD_PLACE = "├{0}┤";

    private static final String NOT_IMG_TAB_REGEX = "</?((?!img).)*?/?>";
    private static final Pattern NOT_IMG_TAB_REGEX_PATTERN = Pattern.compile(NOT_IMG_TAB_REGEX);

    private static final String HTML_TAB_REGEX = "<[^>]+>|</[^>]+>";
    private static final Pattern HTML_TAB_REGEX_PATTERN = Pattern.compile(HTML_TAB_REGEX);

    private static final String BLANK_REGEX = "\\s+";
    private static final Pattern BLANK_REGEX_PATTERN = Pattern.compile(BLANK_REGEX);

    private static final String ZW_HEADER_REGEX = "<div id='zw_header'([^div>].*?)</div>";
    private static final Pattern ZW_HEADER_REGEX_PATTERN = Pattern.compile(ZW_HEADER_REGEX);

    private static final String IF_TAB_REGEX = "<\\!--\\[if.*?\\]>.*?<\\!\\[endif\\]-->";
    private static final Pattern IF_TAB_REGEX_PATTERN = Pattern.compile(IF_TAB_REGEX);

    private static final String ADR_REGEX = "<!--adr.*?-->";
    private static final Pattern ADR_REGEX_PATTERN = Pattern.compile(ADR_REGEX);

    private static final String ANNOTATE_REGEX = "<!--.*?-->";
    private static final Pattern ANNOTATE_REGEX_PATTERN = Pattern.compile(ANNOTATE_REGEX);

    private static final String NOT_ANNOTATE_REGEX = "<[^!--].*?>";
    private static final Pattern NOT_ANNOTATE_REGEX_PATTERN = Pattern.compile(NOT_ANNOTATE_REGEX);

    private static final String TOPIC_NAME_REGEX = "\\[topic_name=";
    private static final Pattern TOPIC_NAME_REGEX_PATTERN = Pattern.compile(TOPIC_NAME_REGEX);

    private static final String TOPIC_ID_REGEX = "\\#topic_id=.*?\\]";
    private static final Pattern TOPIC_ID_REGEX_PATTERN = Pattern.compile(TOPIC_ID_REGEX);

    private static final String AT_REGEX = "\\[at=.*?\\]";
    private static final Pattern AT_REGEX_PATTERN = Pattern.compile(AT_REGEX);

    private static final String OTC_FUND_1_REGEX = "<span class=\"guba_stock\" data-marketcode=\"of([\\d]+)\" data-markettype=\"150\" data-stockcode=\"([\\d]+)\">\\$(.*?)\\$</span>";
    private static final Pattern OTC_FUND_1_REGEX_PATTERN = Pattern.compile(OTC_FUND_1_REGEX);

    private static final String OTC_FUND_2_REGEX = "<span class=\"guba_stock\" data-marketcode=\"OTCFUND\\|([\\d]+)\" data-markettype=\"150\" data-stockcode=\"([\\d]+)\">\\$(.*?)\\$</span>";
    private static final Pattern OTC_FUND_2_REGEX_PATTERN = Pattern.compile(OTC_FUND_2_REGEX);

    private static final String HIGH_FUND_REGEX = "<span class=\"fund-data\" data-code=\"([\\w]+)\" data-type=\"GD\">\\$(.*?)\\$</span>";
    private static final Pattern HIGH_FUND_REGEX_PATTERN = Pattern.compile(HIGH_FUND_REGEX);

    //所有匹配链接的正则和type 后续如若添加则加进去即可
    private static final Map<String, String> urlPositionTypeAndRegexDicInfos = new HashMap<>();
    private static final Map<String, Pattern> urlPositionTypeAndRegexDicInfosPattern = new HashMap<>();

    static {
        urlPositionTypeAndRegexDicInfos.put("19_3_7", "<a[^>]*href=('|\\\")(http|https)://iguba.eastmoney.com/(\\d{1,})(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("18_3_7", "<a[^>]*href=('|\\\")(http|https)://gubatopic.eastmoney.com/jj/topic.html\\?htid=(\\d{1,})(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("22_4_8", "<a[^>]*href=('|\\\")(http|https)://guba.eastmoney.com/news,(.*?),(\\d{1,}).html(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("23_3_7", "<a[^>]*href=('|\\\")(http|https)://fund.eastmoney.com/news/\\d{4},(\\d{8,}).html(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("24_3_7", "<a[^>]*href=('|\\\")(http|https)://caifuhao.eastmoney.com/news/(\\d{8,})(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("25_3_6", "<a[^>]*href=('|\\\")(http|https)://topic.1234567.com.cn/(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("28_3_6", "<a[^>]*href=('|\\\")(http|https)://act.1234567.com.cn/(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("26_3_5", "<a[^>]*href=('|\\\")(http|https)://appunit.1234567.com.cn/combodetailv2/index.html\\?id=(\\d{8,})(.*)>(.*?)</a>");//子账户
        urlPositionTypeAndRegexDicInfos.put("27_2_3", "<a[^>]*href=('|\\\")fund://mp.1234567.com.cn/weex/(.*?)>(.*?)</a>");//小程序
    }

    static {
        for (Map.Entry<String, String> entry : urlPositionTypeAndRegexDicInfos.entrySet()) {
            urlPositionTypeAndRegexDicInfosPattern.put(entry.getKey(), Pattern.compile(entry.getValue(), Pattern.CASE_INSENSITIVE));
        }
    }

    @Autowired
    private SetHideDao setHideDao;

    @Autowired
    private PassportFundMrgDao passportFundMrgDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private KeywordDao keywordDao;

    @Autowired
    private PostInfoDao postInfoDao;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private PostDao postDao;

    @Autowired
    private PassportUserInfoDao passportUserInfoDao;

    @Autowired
    private FundTopicDao fundTopicDao;

    @Autowired
    private PostInfoExtendDao postInfoExtendDao;

    @Autowired
    private ReviewScorePostDao reviewScorePostDao;

    @Autowired
    private JjbconfigDao jjbconfigDao;

    @Autowired
    private PostInfoExtendFlagDao postInfoExtendFlagDao;

    @Autowired
    private PostInfoExtraDao postInfoExtraDao;

    @Autowired
    private UserTransPostDao userTransPostDao;

    @Autowired
    private UserTransPostModelPortfolioDao userTransPostModelPortfolioDao;

    @Autowired
    private SubAccountDtoDao subAccountDtoDao;

    @Autowired
    private SubAccountDtoModelPortfolioDao subAccountDtoModelPortfolioDao;

    @Autowired
    private ProfitDao profitDao;

    @Autowired
    private FundUserProfitDao fundUserProfitDao;

    @Qualifier("pushRobotLikeExecutor")
    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;

    @Value("${post.async.parallel.num}")
    private int postAsyncParallelNum;

    @Autowired
    private VideoArticleDao videoArticleDao;

    @Autowired
    private KeywordHanlpService keywordHanlpService;

    @Autowired
    private GroupTagPostRelationDao groupTagPostRelationDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            //帖子id列表，多个逗号分隔
            String initPostIds = null;
            //初始化断点
            String initBreakpoint = null;
            //提取图片数量限制
            Integer extractPicCountLimit = null;

            Boolean forceRun = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initPostIds = jsonObject.getString("initPostIds");
                initBreakpoint = jsonObject.getString("initBreakpoint");
                extractPicCountLimit = jsonObject.getInteger("extractPicCountLimit");
                forceRun = jsonObject.getBoolean("forceRun");
            }

            if (extractPicCountLimit == null) {
                extractPicCountLimit = 4;
            }

            if (forceRun == null) {
                forceRun = false;
            }

            if (StringUtils.hasLength(initBreakpoint)) {
                //验证传入断点格式
                DateUtil.strToDate(initBreakpoint, "yyyy-MM-dd HH:mm:ss.SSS");
                userRedisDao.set(UserRedisConfig.postSyncBizMysqlJob_syncPostInfoTable_breakpoint, initBreakpoint, 3600 * 24 * 90L);
                logger.info("第零步：初始化断点。初始化断点值：" + initBreakpoint);
                return ReturnT.SUCCESS;
            }


            List<String> initPostIdList = null;
            if (StringUtils.hasLength(initPostIds)) {
                initPostIdList = CommonUtils.toList(initPostIds, ",");
                logger.info("第零步：初始化帖子id列表。初始化帖子id值：" + initPostIds);
            }

            syncPostInfoTable(extractPicCountLimit, initPostIdList, forceRun);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    /**
     * 同步主贴信息表
     */
    private void syncPostInfoTable(Integer extractPicCountLimit, List<String> initPostIdList, boolean forceRun) {
        String logpre = DateUtil.dateToStr(new Date(), "yyyy-MM-dd_HH:mm:ss") + ":";
        try {

            Map<String, Integer> dicHidePostIds = setHideDao.getList(DateUtil.calendarDateByMonth(-13));
            logger.info(logpre + "第一步：获取所有隐藏帖列表。数量：" + dicHidePostIds.size());

            //基金经理关系
            Map<String, List<PassportFundMrgModel>> fundMrgList = getFundMrgDic();
            logger.info(logpre + "第二步：获取基金经理关系。数量：" + fundMrgList.size());

            String breakpoint = userRedisDao.get(UserRedisConfig.postSyncBizMysqlJob_syncPostInfoTable_breakpoint);
            if (!StringUtils.hasLength(breakpoint)) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-30), "yyyy-MM-dd HH:mm:ss.SSS");
                logger.error(logpre + "第三步：读取redis获取断点失败，采用默认断点。断点：" + breakpoint);
            }
            logger.info(logpre + "第三步：获取断点。值：" + breakpoint);

            //获取关键字
            Map<String, KeywordsEntity> keyWordDics = getKeyWordDic();
            logger.info(logpre + "第四步：获取关键字。数量：" + keyWordDics.size());

            List<PostInfoForMysqlModel> postList = null;

            if (!CollectionUtils.isEmpty(initPostIdList)) {

                postList = postInfoDao.getPostListByIds(initPostIdList.stream().map(item -> Integer.parseInt(item)).collect(Collectors.toList()));
                if (postList == null) {
                    postList = new ArrayList<>();
                }

                logger.info(logpre + "第五.1步-轮次0" + "：指定id获取主贴列表。数量：" + postList.size());

                if (!CollectionUtils.isEmpty(postList)) {
                    // 处理帖子
                    if (postList.size() > appConstant.dealPostAsyncSizeLimit) {
                        dealPostAsync(dicHidePostIds, fundMrgList, keyWordDics, 0, postList, extractPicCountLimit, forceRun);
                    } else {
                        for (int i = 0; i < postList.size(); i++) {
                            dealPost(dicHidePostIds, fundMrgList, keyWordDics, 0, postList.get(i), i, postList.size(), extractPicCountLimit, forceRun);
                        }
                    }
                }

                return;
            }


            int round = 0;//仅作打日志记录循环次数用
            while (true) {
                ++round;

                long time1 = System.currentTimeMillis();

                postList = postInfoDao.getPostList(DateUtil.calendarDateBySecond(
                        DateUtil.strToDate(breakpoint, "yyyy-MM-dd HH:mm:ss.SSS"), -3)
                    , appConstant.batchPostReadCount, false);

                if (postList == null) {
                    postList = new ArrayList<>();
                }

                logger.info(logpre + "第五.1步-轮次" + round + "：增量获取主贴列表。数量：" + postList.size() + "，断点：" + breakpoint);

                if (!CollectionUtils.isEmpty(postList)) {

                    // 处理帖子
                    if (postList.size() > appConstant.dealPostAsyncSizeLimit) {
                        dealPostAsync(dicHidePostIds, fundMrgList, keyWordDics, round, postList, extractPicCountLimit, forceRun);
                    } else {
                        for (int i = 0; i < postList.size(); i++) {
                            dealPost(dicHidePostIds, fundMrgList, keyWordDics, round, postList.get(i), i, postList.size(), extractPicCountLimit, forceRun);
                        }
                    }


                    Date maxDate = postList.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;
                    if (breakpoint.equals(DateUtil.dateToStr(maxDate, "yyyy-MM-dd HH:mm:ss.SSS"))
                        && postList.size() == appConstant.batchPostReadCount) {
                        logger.error(logpre + "PostSyncBizMysqlJob.syncPostInfoTable():同一秒有大量数据，超过阈值！!");
                        break;
                    }
                    breakpoint = DateUtil.dateToStr(maxDate, "yyyy-MM-dd HH:mm:ss.SSS");
                    userRedisDao.set(UserRedisConfig.postSyncBizMysqlJob_syncPostInfoTable_breakpoint, breakpoint, 3600 * 24 * 90L);
                    logger.info(logpre + "第六.1步-轮次" + round + "：保存断点。" + "断点：" + breakpoint);

                }

                logger.info(logpre + "轮次：" + round + "，数量：" + (CollectionUtils.isEmpty(postList) ? 0 : postList.size())
                    + "，耗时：" + ((System.currentTimeMillis() - time1) / 1000) + "秒");

                if (postList == null || postList.size() < appConstant.batchPostReadCount) {
                    break;
                }

            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 异步处理帖子
     *
     * @param dicHidePostIds
     * @param fundMrgList
     * @param keyWordDics
     * @param round
     * @param postList
     * @throws InterruptedException
     */
    private void dealPostAsync(Map<String, Integer> dicHidePostIds,
                               Map<String, List<PassportFundMrgModel>> fundMrgList,
                               Map<String, KeywordsEntity> keyWordDics,
                               int round,
                               List<PostInfoForMysqlModel> postList,
                               int extractPicCountLimit,
                               boolean forceRun
    ) throws InterruptedException {
        ExecutorService excutor = Executors.newFixedThreadPool(postAsyncParallelNum);
        int finalRound = round;
        CountDownLatch countDownLatch = new CountDownLatch(postList.size());
        String taceid = MDC.get(CoreConstant.logtraceid);
        for (int i = 0; i < postList.size(); i++) {
            PostInfoForMysqlModel p = postList.get(i);
            int finalI = i;
            excutor.submit(() -> {
                try {
                    MDC.put(CoreConstant.logtraceid, taceid);
                    dealPost(dicHidePostIds, fundMrgList, keyWordDics, finalRound, p, finalI, postList.size(), extractPicCountLimit, forceRun);
                } finally {
                    MDC.remove(CoreConstant.logtraceid);
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        excutor.shutdown();
    }

    /**
     * 处理帖子
     *
     * @param dicHidePostIds
     * @param fundMrgList
     * @param keyWordDics
     * @param round
     * @param i
     * @param count
     * @return
     */
    private void dealPost(Map<String, Integer> dicHidePostIds, Map<String, List<PassportFundMrgModel>> fundMrgList,
                          Map<String, KeywordsEntity> keyWordDics, int round, PostInfoForMysqlModel p, int i,
                          int count,
                          int extractPicCountLimit,
                          boolean forceRun
    ) {

        logger.info("第五.2步-轮次{}：记录帖子id。第{}/{}条-帖子id：{}",
            round,
            (i + 1),
            count,
            p.ID
        );

        StringBuilder log = new StringBuilder();//仅用作打日志
        try {
            PostInfoNewModel pNew = JacksonUtil.string2Obj(JacksonUtil.obj2String(p), PostInfoNewModel.class);
            log.append("1.反序列化\n");

            //只处理近一年的帖子,删除的帖子需要去更新
            try {
                if (!forceRun && p.TIME.compareTo(DateUtil.calendarDateByMonth(-13)) < 0) {
                    if (p.DEL == 1) {
                        boolean isSuccess1 = postInfoNewDao.deletePostInfoNew(p.ID, p.DEL, new Date());
                        if (!isSuccess1) {
                            pNew.UPDATETIME = new Date();
                            pNew.TIMEPOINT = DateUtil.getTimePoint(p.TIME);
                            isSuccess1 = postInfoNewDao.syncPost(pNew, new OneTuple<>(false));
                        }
                        //设置删除状态
                        postDao.updateDel(String.valueOf(p.ID), p.DEL, new Date());
                    }
                    log.append("2.非近一年的帖子\n");
                    logger.info("第五.2步-轮次" + round + "：帖子同步详情。帖子id：" + p.ID + "，详情：\n" + log
                        + "\n第" + (i + 1) + "/" + count + "条");
                    return;
                }

                log.append("2.判断发帖时间\n");
            } catch (Exception ex) {
                logger.error(ex.getMessage(), ex);
            }

            //初始化帖子用户昵称信息
            if (StringUtils.hasLength(p.UID)) {
                try {
                    PassportUserInfoModelNew passportInfo = passportUserInfoDao.getPassportUserInfoById(p.UID);
                    if (passportInfo != null) {
                        pNew.NICHENG = passportInfo.NickName;
                    }
                } catch (Exception ex) {
                    logger.error(ex.getMessage(), ex);
                }
                log.append("3.处理帖子用户昵称\n");
            }

            p.CONTENT = p.CONTENT == null ? "" : p.CONTENT;
            p.PIC = p.PIC == null ? "" : p.PIC;
            String content = StringEscapeUtils.unescapeHtml4(p.CONTENT);

            //处理暂时不兼容的视频标签  [videoId=4265094##videoType=1##videoFrom=news]
            Matcher videoIdMatcher = VIDEO_ID_REGEX_PATTERN.matcher(content);
            while (videoIdMatcher.find()) {
                content = content.replace(videoIdMatcher.group(0), "");
            }

            log.append("4.处理CONTENT使用html解码\n");

            //剔除  2021 07 03
            OneTuple<String> oneTuple0 = new OneTuple<>(content);
            List<String> allImg = getAllImg(oneTuple0, p.ID);
            content = oneTuple0.first;
            log.append("5.处理getAllImg()\n");

            //处理标题
            pNew.TITLE = titleFormat(p.TITLE);
            pNew.ISSHOW = (p.TYPE == 49 || p.TYPE == 50) ? 0 : processTitle(p.TITLE, content, p.CODE);//问答的要展示标题
            log.append("6.处理TITLE、ISSHOW\n");

            int number = 0;
            //备注：关键字剔除 20210703
            TwoTuple<String, Integer> twoTuple1 = new TwoTuple<>(content, number);
            List<KeyWordModel> keyWordList = StringUtils.hasLength(content) ? processKeyWordList(twoTuple1, keyWordDics, p.EXTEND) : new ArrayList<>();
            content = twoTuple1.first;
            number = twoTuple1.second;
            log.append("7.处理生成关键字\n");

            //备注：可考虑兼容历史版本..用CONTENT替换 20210703
            /*
            pNew.CONTENTEND = attachPic(content, keyWordList, p.ID, p.PIC, number);
            pNew.PICRATIO = getPicsRatio(pNew.PIC);
            pNew.KEYWORDLIST = JacksonUtil.obj2String(keyWordList);
            */

            int contentLen = 0;
            String summaryPic = content;

            /**
             * 将下面代码块注释掉，注释原因是目前不会使用
             * 下面的功能是对转发贴生成type=100的关键字。但接口目前没有直接使用type=100的地方，都是间接用的type=100（type=0转成type=100）
             * 关联需求编号：#564931
             */
            //备注：正文关键字剔除 20210703
            //处理图文混排中的图片 20190524
            /*if ("zf".equals(pNew.CODE.toLowerCase()) && pNew.TYPE != 49 && pNew.TYPE != 50) {
                String contentEndPic = pNew.CONTENTEND;
                Pattern pattern = PIC_REGEX_PATTERN;
                Matcher matcher = pattern.matcher(contentEndPic);
                int keywordCount = keyWordList.size();
                while (matcher.find()) {
                    String srcUrl = matcher.group(2);
                    KeyWordModel contentEndPicKeyWord = new KeyWordModel();
                    contentEndPicKeyWord.Text = srcUrl.replace("\"", "").replace("\\", "");
                    contentEndPicKeyWord.Type = 100;
                    contentEndPicKeyWord.Lable = MessageFormat.format(HOLDPLACE, keywordCount);
                    keyWordList.add(contentEndPicKeyWord);
                    keywordCount++;
                    contentEndPic = contentEndPic.replace(matcher.group(0), contentEndPicKeyWord.Lable);
                    summaryPic = summaryPic.replace(matcher.group(0), contentEndPicKeyWord.Lable);
                }
                pNew.CONTENTEND = contentEndPic;
                pNew.KEYWORDLIST = JacksonUtil.obj2String(keyWordList);
                log.append("9.是转发且不是提问回答\n");
            }*/
            //log.append("9.判断是否转发且不是提问回答\n");

            OneTuple<Integer> oneTuple = new OneTuple<>(contentLen);
            pNew.SUMMARY = getSummary(summaryPic, oneTuple, keyWordList);
            contentLen = oneTuple.first;
            log.append("8.处理SUMMARY\n");

            //备注：所有图片剔除 20210703
            pNew.ALLPIC = "";//兼容.net代码逻辑  .net代码里一个null值字符串+=一个有值字符串与java逻辑不同  .net： null+="123"="123"   java:"null123"
            if (!StringUtils.hasLength(pNew.PIC)) {
                pNew.ALLPIC += String.join(",", allImg);
            } else {
                if (allImg.size() > 0) {
                    pNew.ALLPIC += String.join(",", allImg) + "," + pNew.PIC;
                } else {
                    pNew.ALLPIC += pNew.PIC;
                }
            }
//            //兼容app发帖改为富文本发帖，需要在列表页展示富文本中的图片
            if (!StringUtils.hasLength(pNew.PIC) && StringUtils.hasLength(pNew.ALLPIC)) {
                List<String> extractPicList = new ArrayList<>();
                List<String> extractPicRatioList = new ArrayList<>();
                if (StringUtils.hasLength(appConstant.whiteListForDomainNamePostPicExtract)) {
                    List<String> picList = CommonUtils.toList(pNew.ALLPIC, ",");
                    List<String> whiteDomainNameList = CommonUtils.toList(appConstant.whiteListForDomainNamePostPicExtract, ",");
                    picList = picList.stream().filter(a -> {
                        for (String domainName : whiteDomainNameList) {
                            if (a.contains(domainName)) {
                                return true;
                            }
                        }
                        return false;
                    }).collect(Collectors.toList());
                    String picRatio = null;
                    for (String pic : picList) {
                        if (extractPicList.size() >= extractPicCountLimit) {
                            break;
                        }
                        picRatio = getPicsRatioSpecial(pic);
                        if (StringUtils.hasLength(picRatio)) {
                            extractPicList.add(pic);
                            extractPicRatioList.add(picRatio);
                        }
                    }
                }
                pNew.PIC = String.join(",", extractPicList);
                //pNew.PICRATIO = String.join(",", extractPicRatioList);
            }
            log.append("9.处理ALLPIC\n");

            //备注：可考虑兼容历史版本..用CONTENT替换 20210703
            pNew.CONTENTEND = attachPic(content, keyWordList, p.ID, pNew.PIC, number);
            pNew.PICRATIO = getPicsRatio(pNew.PIC);
            pNew.KEYWORDLIST = JacksonUtil.obj2String(keyWordList);
            log.append("10.处理CONTENTEND、PICRATIO、KEYWORDLIST\n");

            int delState = 1;
            if (pNew.PROJECT == 1 || (pNew.PROJECT != 1 && jjbconfigDao.getCodeTypes().contains(pNew.CODE.toLowerCase()))) {
                pNew.TTJJDEL = 0;
                //这里判断是否是基金后台隐藏贴
                if (!CollectionUtils.isEmpty(dicHidePostIds) && dicHidePostIds.containsKey(String.valueOf(p.ID))) {
                    pNew.TTJJDEL = EnumTTJJDEL.JJBARConfig.getValue();
                }
            } else {
                pNew.TTJJDEL = 1;
            }

            //接入股吧隐藏状态
            if ((p.F1 != null && p.F1 == 1) && (pNew.TTJJDEL == null || pNew.TTJJDEL == 0)) {
                pNew.TTJJDEL = 3;
            }
            if ((p.F1 != null && p.F1 == 2) && (pNew.TTJJDEL != null && pNew.TTJJDEL == 3)) {
                pNew.TTJJDEL = 0;
            }

            // 私域群组（以股吧状态优先）
            if (pNew.TTJJDEL == 0 || pNew.TTJJDEL == 4) {
                Integer visibility = groupTagPostRelationDao.getPostVisibilityById(String.valueOf(p.ID));
                if (visibility != null) {
                    if (visibility == 1) {
                        pNew.TTJJDEL = EnumTTJJDEL.PRIVATE_SPHERE_GROUP.getValue();
                    } else if (visibility == 0) {
                        pNew.TTJJDEL = 0;
                    }
                }
            }

            if (pNew.DEL == 0 && pNew.TTJJDEL == 0 && pNew.ISENABLED == 1) {
                delState = 0;
            }

            //接入删除类型标识whodel字段
            if (pNew.DEL == 1 && StringUtils.hasLength(p.F3)) {
                pNew.whoDel = p.F3;
            }

            pNew.HASEXTEND = hasExtendInfo(pNew.ID);
            pNew.UPDATETIME = new Date();
            pNew.TIMEPOINT = getPostInfoTimePoint(p.TIME, p.ID);
            log.append("11.处理TTJJDEL、HASEXTEND、UPDATETIME、TIMEPOINT\n");

            //同步tb_postinfo_new表
            boolean isInsert = false;
            pNew.CODE = p.CODE.toLowerCase();
            PostInfoExtendField extendField = null;
            if (StringUtils.hasLength(p.EXTEND)) {
                extendField = JacksonUtil.string2Obj(p.EXTEND, PostInfoExtendField.class);
            }
            //处理模拟组合吧code、type转换
            if ("jjmnzh".equals(pNew.CODE)) {
                if (extendField != null) {
                    if ((pNew.TYPE == 49 || pNew.TYPE == 50) && StringUtils.hasLength(extendField.Ext)) {
                        pNew.CODE = BarCodeInnerEnum.MNZHB.getPrefix() + extendField.Ext;
                    } else if (StringUtils.hasLength(extendField.code)) {
                        if (pNew.TYPE == 0) {
                            pNew.TYPE = BarCodeInnerEnum.MNZHB.getType();
                        }
                        pNew.CODE = BarCodeInnerEnum.MNZHB.getPrefix() + extendField.code;
                    }
                }
            }

            OneTuple<Boolean> oneTuple1 = new OneTuple<>(isInsert);
            boolean isSuccess = postInfoNewDao.syncPost(pNew, oneTuple1);
            isInsert = oneTuple1.first;
            log.append("12.处理更新mysql的new表:" + isSuccess + "\n");

            // tb_postinfo_new表同步成功，直接生成extra扩展表
            if (isSuccess) {
                //备注：分值更新逻辑是否单独事务处理
                ReviewScorePost searchItem = reviewScorePostDao.getById(String.valueOf(pNew.ID));
                if (searchItem != null) {
                    reviewScorePostDao.updateDel(Long.parseLong(String.valueOf(pNew.ID)), delState, new Date());
                    log.append("13.处理更新策略吧帖子分值表：" + (searchItem != null) + "\n");
                }
                log.append("13.判断是否更新策略吧帖子分值表：" + (searchItem != null) + "\n");

                isSuccess = postInfoExtraDao.syncPostExtraByPostInfoNew(pNew, contentLen);
                log.append("14.处理更新mysql的extra表\n");

                try {
                    //视频信息
                    pNew.Videos = new ArrayList<>();
                    if (extendField != null) {
                        if (StringUtils.hasLength(extendField.ZMTLKVideoID)) {
                            PostVideoInfo videoInfo = new PostVideoInfo();
                            videoInfo.setZMTLKVideoID(extendField.ZMTLKVideoID);
                            //视频封面顺序优先级
                            String tempCover = null;
                            if (StringUtils.hasLength(extendField.ZMTLKHCover)) {
                                tempCover = extendField.ZMTLKHCover;
                            } else if (StringUtils.hasLength(extendField.ZMTLKCover)) {
                                tempCover = extendField.ZMTLKCover;
                            }
                            RoomCoverResourceModel resourceModel = new RoomCoverResourceModel();
                            TwoTuple<List<VideoInfo>, String> viedoAndCover = videoArticleDao.getViedoInfo(extendField.ZMTLKVideoID, resourceModel);
                            if (viedoAndCover != null) {
                                tempCover = viedoAndCover.getSecond();
                                videoInfo.setZMTLKHCover(resourceModel.getH_cover());
                                videoInfo.setZMTLKVCover(resourceModel.getV_cover());
                                videoInfo.setZMTLKDefaultCover(resourceModel.getCover());
                                videoInfo.setZMTLKFrameCover(resourceModel.getFrame_cover());
                            }

                            videoInfo.setZMTLKCover(tempCover);
                            pNew.Videos = new ArrayList<>();
                            pNew.Videos.add(videoInfo);
                            log.append("15.处理有视频信息\n");
                        }
                        pNew.ZMTLKType = extendField.ZMTLKType;
                    }

                    log.append("15.判断是否有视频信息\n");

                    pNew._id = String.valueOf(pNew.ID);
                    Map<String, Object> beanToMap = CommonUtils.beanToMap(pNew);
                    beanToMap.remove("CONTENT");
                    if (!StringUtils.hasLength(pNew.whoDel)) {
                        beanToMap.remove("whoDel");
                    }
                    postDao.upsert(beanToMap);
                    log.append("16.处理更新mongo的表\n");

                    //将分账户帖子信息同步到 分账户动态表
                    if (pNew.CODE.startsWith("43-") && pNew.TYPE != 49 && pNew.TYPE != 50) {
                        PostInfoForSubAccount subpostinfo = new PostInfoForSubAccount();
                        subpostinfo._id = "POST_" + pNew.ID;
                        subpostinfo.ID = pNew.ID;
                        subpostinfo.SubAccountNo = pNew.CODE.replace("43-", "");
                        subpostinfo.PID = pNew.UID;
                        subpostinfo.AppTime = DateUtil.dateToStr(pNew.CREATETIME, "yyyy-MM-dd HH:mm:ss");
                        subpostinfo.TIMEPOINT = pNew.TIMEPOINT;
                        subpostinfo.TYPE = pNew.TYPE;
                        subpostinfo.YUANID = pNew.YUANID;
                        subpostinfo.YUANTYPE = Integer.parseInt(String.valueOf(pNew.YUANTYPE == null ? 0L : pNew.YUANTYPE));
                        subpostinfo.DEL = pNew.DEL;
                        subpostinfo.TTJJDEL = pNew.TTJJDEL;
                        subpostinfo.AppType = "POST";
                        subpostinfo.ISTRADEPOST = 0;
                        subpostinfo.ISENABLED = pNew.ISENABLED;
                        if (userTransPostDao.getDocByPostid(pNew.ID) != null) {
                            subpostinfo.ISTRADEPOST = 1;
                        }
                        boolean subres = subAccountDtoDao.upsert(CommonUtils.beanToMap(subpostinfo));
                        log.append("17.处理更新分账户动态表\n");
                    }

                    //将分账户帖子信息同步到 分账户动态表 模拟组合
                    if (pNew.CODE.startsWith(BarCodeInnerEnum.MNZHB.getPrefix()) && pNew.TYPE != 49 && pNew.TYPE != 50) {
                        PostInfoForSubAccount subpostinfo = new PostInfoForSubAccount();
                        subpostinfo._id = "POST_" + pNew.ID;
                        subpostinfo.ID = pNew.ID;
                        subpostinfo.SubAccountNo = pNew.CODE.substring(BarCodeInnerEnum.MNZHB.getPrefix().length()).toUpperCase();
                        subpostinfo.PID = pNew.UID;
                        subpostinfo.AppTime = DateUtil.dateToStr(pNew.CREATETIME, "yyyy-MM-dd HH:mm:ss");
                        subpostinfo.TIMEPOINT = pNew.TIMEPOINT;
                        subpostinfo.TYPE = pNew.TYPE;
                        subpostinfo.YUANID = pNew.YUANID;
                        subpostinfo.YUANTYPE = Integer.parseInt(String.valueOf(pNew.YUANTYPE == null ? 0L : pNew.YUANTYPE));
                        subpostinfo.DEL = pNew.DEL;
                        subpostinfo.TTJJDEL = pNew.TTJJDEL;
                        subpostinfo.AppType = "POST";
                        subpostinfo.ISTRADEPOST = 0;
                        subpostinfo.ISENABLED = pNew.ISENABLED;
                        if (userTransPostModelPortfolioDao.getDocByPostid(pNew.ID) != null) {
                            subpostinfo.ISTRADEPOST = 1;
                        }
                        boolean subres = subAccountDtoModelPortfolioDao.upsert(CommonUtils.beanToMap(subpostinfo));
                        log.append("17.处理更新分账户动态表-模拟组合\n");
                    }

                    log.append("17.判断是否更新分账户动态表\n");

                    try {

                        //备注：持仓是否需要更新
                        List<ProfitEntity> profitList = profitDao.getProfitsIncrementFromMysql(pNew.CODE, pNew.UID, Integer.MAX_VALUE, DateUtil.calendarDateByYears(-1));
                        /*ProfitEntity profit = new ProfitEntity();
                        if (CollectionUtils.isEmpty(profitList)) {
                            profit.Id = 0;
                            profit.HoldMonth = -1;
                            profit.ProfitState = 0;
                        } else {
                            profit = profitList.stream().findFirst().orElse(null);
                        }*/

                        ProfitEntity profit = CollectionUtils.isEmpty(profitList) ? null : profitList.get(0);
                        log.append("18.获取用户持仓：" + !CollectionUtils.isEmpty(profitList) + "\n");

                        ////若存在则更新,否则插入记录,如果是基金经理发帖则不显示持有人标签
                        if (profit != null
                            && pNew.DEL == EnumPostDel.YiShenHe.getValue()
                            && fundMrgList != null
                            && !fundMrgList.containsKey(pNew.UID)) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("_id", String.format("%s_%s_%s", pNew.CODE, pNew.UID, pNew.ID));
                            map.put("ID", profit.Id);
                            map.put("PID", pNew.UID);
                            map.put("FCODE", pNew.CODE);
                            map.put("POSTID", pNew.ID);
                            map.put("POSTTIME", pNew.TIME);
                            map.put("HOLDMONTH", profit.HoldMonth);
                            map.put("PROFITSTATE", profit.ProfitState);
                            map.put("FINDSCORE", 0);
                            map.put("EUTIME", new Date());
                            map.put("TIMEPOINT", pNew.TIMEPOINT);
                            map.put("YUANID", pNew.YUANID);
                            map.put("TYPE", p.TYPE);
                            map.put("YUANTYPE", pNew.YUANTYPE == null ? 0 : pNew.YUANTYPE);
                            boolean temp = fundUserProfitDao.upsert(map);
                            log.append("19.更新用户持仓\n");
                        }
                        log.append("19.判断是否更新用户持仓\n");

                    } catch (Exception ex) {
                        logger.error(ex.getMessage(), ex);
                    }
                } catch (Exception ex) {
                    logger.error(ex.getMessage(), ex);
                }
            }

            //帖子点赞推送
            pushRobotLike(pNew);
            log.append("20.帖子点赞推送\n");
        } catch (Exception ex) {
            logger.error("帖子处理详情报错。帖子id:" + p.ID + "，ex.getMessage:" + ex.getMessage(), ex);
        }

        logger.info("第五.2步-轮次{}：帖子同步详情。第{}/{}条-帖子id：{}，详情：{}",
            round,
            (i + 1),
            count,
            p.ID,
            log.toString()
        );
    }

    void pushRobotLike(PostInfoNewModel postInfo) {
        try {
            //  删除的帖子，不可用的帖子，非普通的帖子,更新时间和创建时间超过30分钟 则不推送
            if (postInfo == null || !Objects.equals(postInfo.DEL, 0) ||
                !Objects.equals(postInfo.TTJJDEL, 0) || !Objects.equals(postInfo.ISENABLED, 1) ||
                !ROBOT_LIKE_TYPES.contains(postInfo.TYPE) ||
                postInfo.UPDATETIME.compareTo(DateUtil.calendarDateByMinute(postInfo.CREATETIME, 30)) > 0) {
                return;
            }
            //推送过就不再推送
            if (postPushList.contains(postInfo.ID)) {
                return;
            }

            //异步post数据
            threadPoolExecutor.execute(() -> {
                try {
                    RobotLikeEntity obj = new RobotLikeEntity();
                    obj.ID = String.valueOf(postInfo.ID);
                    obj.PTYPE = postInfo.TYPE == EnumPostType.QAAnswer.getValue() ? 3 : 1;
                    obj.TID = "";
                    obj.TIME = postInfo.TIME;
                    obj.TYPE = postInfo.TYPE;
                    obj.UID = postInfo.UID;
                    obj.PUSHTYPE = 0;
                    obj.TEXTLENGTH = getTextLength(postInfo.CONTENTEND);

                    String html = HttpHelper.requestPostJson(MessageFormat.format(appConstant.robotLikePushUrl, postInfo.ID), JacksonUtil.obj2String(Arrays.asList(obj)), true);
                    PushResultEntity result = null;
                    if (StringUtils.hasLength(html)) {
                        result = JacksonUtil.string2Obj(html, PushResultEntity.class);
                    }
                    if (result == null || !result.isSuccess()) {
                        logger.error("帖子点赞PushRobotLike推送失败。id：{}，数据：{}，结果：{}", JacksonUtil.obj2String(obj), JacksonUtil.obj2String(result));
                    } else {
                        if (postPushList.size() > 1000) {
                            postPushList.clear();
                        }
                        postPushList.add(postInfo.ID);
                    }
                } catch (Exception ex) {
                    logger.error("【帖子点赞】PushRobotLike推送失败。帖子id：" + postInfo.ID + "，ex.getMessage()：" + ex.getMessage(), ex);
                    logger.error(ex.getMessage(), ex);
                }
            });
        } catch (Exception ex) {
            logger.error("【帖子点赞】PushRobotLike推送失败。帖子id：" + postInfo.ID + "，ex.getMessage()：" + ex.getMessage(), ex);
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 获取文本的长度（剔除html,空格，制表符，换行符后）
     */
    public int getTextLength(String html) {
        int length = 0;
        try {
            if (!StringUtils.hasLength(html)) length = 0;
            html = removeHtmlTag(html);
            if (StringUtils.hasLength(html)) {
                html = html.replace(" ", "").replace("\n", "")
                    .replace("\r", "").replace("\t", " ");
                length = html.length();
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return length;
    }

    /**
     * 移除html标签
     */
    public String removeHtmlTag(String strhtml) {
        if (!StringUtils.hasLength(strhtml)) {
            return strhtml;
        }
        String stroutput = strhtml;
        stroutput = StringEscapeUtils.unescapeHtml4(strhtml);

        Matcher matcher = HTML_TAB_REGEX_PATTERN.matcher(stroutput);
        while (matcher.find()) {
            stroutput = stroutput.replace(matcher.group(0), "");
        }
        return stroutput;
    }


    /**
     * 为了保证timepoint 不会变
     */
    public static long getPostInfoTimePoint(Date time, int postid) {
        String postidStr = String.valueOf(postid);
        if (postidStr.length() <= 5) {
            return Long.parseLong(DateUtil.dateToStr(time, "yyyyMMddHHmmss") + DateUtil.dateToStr(time, "ssSSS"));
        } else {
            return Long.parseLong(DateUtil.dateToStr(time, "yyyyMMddHHmmss") + postidStr.substring(postidStr.length() - 5));
        }
    }

    private int hasExtendInfo(long postId) {
        int result = 0;
        Document document = postInfoExtendFlagDao.getDocumentByTid(postId);
        if (document != null) {
            result = document.getInteger("HASEXTENDINFO");
        }
        return result;
    }

    /**
     * 获取摘要，返回正文有效长度
     */
    private String getSummary(String content, OneTuple<Integer> oneTuple, List<KeyWordModel> kewordList) {
        oneTuple.first = content.length();
        Pattern pattern = ABSTRACT_REGEX_PATTERN;
        Matcher matcher = pattern.matcher(content);
        if (matcher.find()) {
            return matcher.group(1);
        }
        //替换换行符、多余空格
        content = content.replace("\n", "").replace("\r", "").replace("\t", " ").replace("\\n", "");
        //摘要替换html标签
        content = content.replace("<br>", "").replace("<br/>", "");

        Matcher blankMatcher = BLANK_REGEX_PATTERN.matcher(content);
        while (blankMatcher.find()) {
            content = content.replace(blankMatcher.group(0), " ");
        }

        //替换资讯来源
        Matcher zwHeaderMatcher = ZW_HEADER_REGEX_PATTERN.matcher(content);
        while (zwHeaderMatcher.find()) {
            content = content.replace(zwHeaderMatcher.group(0), "");
        }

        //剔除模板标签，浏览器兼容标签 <!--[if gte mso 9]>.*?<![endif]-->
        Matcher ifHeaderMatcher = IF_TAB_REGEX_PATTERN.matcher(content);
        while (ifHeaderMatcher.find()) {
            content = content.replace(ifHeaderMatcher.group(0), "");
        }

        content = trimStart(content);
        int summaryLength = 200;

        Pattern pattern1 = HOLDPLACE_REGEX_PATTERN;
        Matcher matcher1 = pattern1.matcher(content);
        List<String> placeholderContent = new ArrayList<>();
        while (matcher1.find()) {
            placeholderContent.add(matcher1.group(0));
        }

        /*关键字占位符恢复为文字*/
        if (!CollectionUtils.isEmpty(kewordList)) {
            for (KeyWordModel item : kewordList) {

                //type=100时，关键字的text为图片地址，不可以被还原到SUMMARY展示出来
                if (item.Type != 100) {
                    content = content.replace(item.Lable, item.Text);
                } else {
                    content = content.replace(item.Lable, "");
                }
            }
        }
        //替换占位符标签
        Matcher adrMatcher = ADR_REGEX_PATTERN.matcher(content);
        while (adrMatcher.find()) {
            content = content.replace(adrMatcher.group(0), "");
        }

        //替换注释标签
        Matcher annotateMatcher = ANNOTATE_REGEX_PATTERN.matcher(content);
        while (annotateMatcher.find()) {
            content = content.replace(annotateMatcher.group(0), "");
        }

        //替换html标签
        Matcher notAnnotateMatcher = NOT_ANNOTATE_REGEX_PATTERN.matcher(content);
        while (notAnnotateMatcher.find()) {
            content = content.replace(notAnnotateMatcher.group(0), "");
        }

        oneTuple.first = content.length();

        String summary = "";
        if (content.length() > summaryLength) {
            summary = content.substring(0, summaryLength);
        } else {
            summary = content;
        }
        if (summary != null) {
            if (!StringUtils.hasLength(summary)) {
                summary = "";
            } else {
                summary = trimEnd(summary);
            }
            summary = summary.replace("&gt;", ">").replace("&lt;", "<");
        }

        return summary;
    }

    private String trimEnd(String str) {
        String result = str;
        if (StringUtils.hasLength(str)) {
            char[] chars = str.toCharArray();
            int len = chars.length;
            while (len > 0 && chars[len - 1] <= ' ') {
                len--;
            }
            if (len < chars.length) {
                result = str.substring(0, len);
            }
        }
        return result;
    }

    private String trimStart(String str) {
        String result = str;
        if (StringUtils.hasLength(str)) {
            char[] chars = str.toCharArray();
            int len = chars.length;
            int st = 0;
            while ((st < len) && (chars[st] <= ' ')) {
                st++;
            }
            if (st > 0) {
                result = str.substring(st);
            }
        }
        return result;
    }

    /**
     * 获取图片宽高比例
     */
    public String getPicsRatio(String pic) {
        String result = "";
        //可用图片列表
        List<String> enableList = new ArrayList<>();
        if (StringUtils.hasLength(pic)) {
            String[] picItem = pic.split(",");
            List<String> ratios = new ArrayList<>();
            for (String p : picItem) {
                boolean isEnable = false;
                OneTuple<Boolean> oneTuple = new OneTuple<>(isEnable);
                String ratioResult = getPicRatio(p, oneTuple);
                isEnable = oneTuple.first;
                //判断图片的结果和图片的大小
                if (StringUtils.hasLength(ratioResult) && isEnable) {
                    enableList.add(p);
                    ratios.add(ratioResult);
                }
            }
            //处理后的pic
            pic = String.join(",", enableList);
            result = String.join(",", ratios);
        }
        return result;
    }

    /**
     * 获取图片宽高比例
     */
    public String getPicsRatioSpecial(String pic) {
        String result = "";
        //可用图片列表
        List<String> enableList = new ArrayList<>();
        if (StringUtils.hasLength(pic)) {

            boolean isEnable = false;
            OneTuple<Boolean> oneTuple = new OneTuple<>(isEnable);
            String ratioResult = getPicRatio(pic, oneTuple);
            isEnable = oneTuple.first;
            //判断图片的结果和图片的大小
            if (StringUtils.hasLength(ratioResult) && isEnable) {
                result = ratioResult;
            }
        }
        return result;
    }

    /**
     * 获取单张图片宽高比
     */
    private String getPicRatio(String pic, OneTuple<Boolean> oneTuple) {
        String defaultResult = "1";
        InputStream is = null;
        try {
            //判断是否是白名单图片，白名单图片不验证
            if (isWhiteUrl(pic)) {
                oneTuple.first = true;
                return defaultResult;
            } else {
                oneTuple.first = false;

                URL url = new URL(pic);
                URLConnection conn = url.openConnection();
                conn.setConnectTimeout(200);
                conn.setReadTimeout(3000);
                is = conn.getInputStream();
                BufferedImage image = ImageIO.read(is);
                oneTuple.first = imageSizeEnable(image);

                if (!oneTuple.first) {
                    return defaultResult;
                } else {
                    //将下面写法注释，因为用下面写法时当两数相除的结果出现无限循环小数时会报错
                    //return new BigDecimal(image.getWidth()).divide(new BigDecimal(image.getHeight())).setScale(2, RoundingMode.HALF_EVEN).toPlainString();
                    return new BigDecimal(image.getWidth()).divide(new BigDecimal(image.getHeight()), 2, RoundingMode.HALF_UP).toPlainString();
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return defaultResult;
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (Exception ex) {
                logger.error(ex.getMessage(), ex);
            }
        }
    }

    private String attachPic(String content, List<KeyWordModel> keyWordList, int postID, String pic, int number) {
        if (!StringUtils.hasLength(pic)) {
            return content;
        }

        int num = number > keyWordList.size() ? number : keyWordList.size();
        Map<String, FundPostExtrasProP> picWithLink = new HashMap<>();
        FundPostExtras extraInfo = postInfoExtendDao.getByTid(Long.parseLong(String.valueOf(postID)));
        if (extraInfo != null && !CollectionUtils.isEmpty(extraInfo.PROP)) {
            List<FundPostExtrasProP> retList = extraInfo.PROP.stream()
                .filter(item -> StringUtils.hasLength(item.IMGURL) && (item.TYPE == 14 || item.TYPE == 15))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(retList)) {
                for (FundPostExtrasProP r : retList) {
                    picWithLink.put(r.IMGURL, r);
                }
            }
        }

        List<String> picList = CommonUtils.toList(pic, ",");
        for (String i : picList) {
            String label = MessageFormat.format(HOLDPLACE, num);
            if (picWithLink.containsKey(i)) {
                KeyWordModel keyWordModel = new KeyWordModel();
                keyWordModel.Code = picWithLink.get(i).CODE.get(0);
                keyWordModel.Type = picWithLink.get(i).TYPE;
                keyWordModel.Text = picWithLink.get(i).IMGURL;
                keyWordModel.Lable = label;
                keyWordList.add(keyWordModel);
            } else {
                KeyWordModel keyWordModel = new KeyWordModel();
                keyWordModel.Text = i;
                keyWordModel.Lable = label;
                keyWordList.add(keyWordModel);
            }
            content += label;
            num++;
        }
        return content;
    }


    private List<KeyWordModel> processKeyWordList(TwoTuple<String, Integer> twoTuple, Map<String, KeywordsEntity> keyWordDics, String extend) {
        String label = "";
        String holdPlace = HOLDPLACE;
        //处理链接替换任务
        ThreeTuple<String, List<KeyWordModel>, Integer> threeTuple = new ThreeTuple<>(twoTuple.first, new ArrayList<>(), twoTuple.second);
        handleAllLinks(threeTuple);
        twoTuple.first = threeTuple.first;
        twoTuple.second = threeTuple.third;

        Matcher matcher = A_TAB_REGEX_PATTERN.matcher(threeTuple.first);
        while (matcher.find()) {
            //处理链接
            threeTuple.first = threeTuple.first.replace(matcher.group(0), matcher.group(2));
        }
        //处理图片
        Pattern pattern1 = IMG_REGEX_PATTERN;
        Matcher matcher1 = pattern1.matcher(threeTuple.first);
        while (matcher1.find()) {
            threeTuple.first = threeTuple.first.replace(matcher1.group(0), "");
        }

        //匹配用户ID关键字
        //[at=6487093977122828]@rzb1016[/at] 招呼您
        Pattern pattern2 = USER_REGEX_PATTERN;
        Matcher matcher2 = pattern2.matcher(threeTuple.first);
        for (int i = 0; matcher2.find(); i++, threeTuple.third++) {
            if (StringUtils.hasLength(matcher2.group(1))) {
                label = MessageFormat.format(holdPlace, threeTuple.third);
                KeyWordModel keyWordModel = new KeyWordModel();
                keyWordModel.Code = matcher2.group(1);
                keyWordModel.Text = matcher2.group(2);
                keyWordModel.Type = 19;
                keyWordModel.Lable = label;
                threeTuple.second.add(keyWordModel);
                threeTuple.first = threeTuple.first.replace(matcher2.group(0), label);
            } else {
                threeTuple.first = threeTuple.first.replace(matcher2.group(0), matcher2.group(2));
            }
        }
        //匹配话题关键字
        // 看看今天走势[topic_name=5G时代渐行渐近##topic_id=53]
        Pattern pattern3 = TOPIC_REGEX_PATTERN;
        Matcher matcher3 = pattern3.matcher(threeTuple.first);
        for (int i = 0; matcher3.find(); i++, threeTuple.third++) {
            if (StringUtils.hasLength(matcher3.group(2))) {
                label = MessageFormat.format(holdPlace, threeTuple.third);
                KeyWordModel keyWordModel = new KeyWordModel();
                keyWordModel.Code = matcher3.group(2);
                keyWordModel.Text = "#" + matcher3.group(1) + "#";
                keyWordModel.Type = 18;
                keyWordModel.Lable = label;
                threeTuple.second.add(keyWordModel);
                threeTuple.first = threeTuple.first.replace(matcher3.group(0), label);
            } else {
                threeTuple.first = threeTuple.first.replace(matcher3.group(0), matcher3.group(1));
            }
        }

        //解析关联基金标签
        String code = null;
        String text = null;
        String subStr = null;
        String temp = null;
        Matcher otcMatcher1 = OTC_FUND_1_REGEX_PATTERN.matcher(threeTuple.first);
        while (otcMatcher1.find()) {
            code = otcMatcher1.group(2);
            text = otcMatcher1.group(3);
            subStr = "$" + text + "$";
            temp = MessageFormat.format("(SH{0})", code);
            if (text.endsWith(temp)) {
                text = text.substring(0, text.lastIndexOf(temp));
            }
            temp = MessageFormat.format("(OTCFUND|{0})", code);
            if (text.endsWith(temp)) {
                text = text.substring(0, text.lastIndexOf(temp));
            }
            label = MessageFormat.format(holdPlace, threeTuple.third);
            KeyWordModel keyWordModel = new KeyWordModel();
            keyWordModel.Code = code;
            keyWordModel.Text = "$" + text + "$";
            keyWordModel.Type = 1;
            keyWordModel.Lable = label;
            threeTuple.second.add(keyWordModel);
            threeTuple.first = insertStr(threeTuple.first, label, threeTuple.first.indexOf(subStr), subStr.length());
            threeTuple.third++;
        }
        Matcher otcMatcher2 = OTC_FUND_2_REGEX_PATTERN.matcher(threeTuple.first);
        while (otcMatcher2.find()) {
            code = otcMatcher2.group(2);
            text = otcMatcher2.group(3);
            subStr = "$" + text + "$";
            temp = MessageFormat.format("(SH{0})", code);
            if (text.endsWith(temp)) {
                text = text.substring(0, text.lastIndexOf(temp));
            }
            temp = MessageFormat.format("(OTCFUND|{0})", code);
            if (text.endsWith(temp)) {
                text = text.substring(0, text.lastIndexOf(temp));
            }
            label = MessageFormat.format(holdPlace, threeTuple.third);
            KeyWordModel keyWordModel = new KeyWordModel();
            keyWordModel.Code = code;
            keyWordModel.Text = "$" + text + "$";
            keyWordModel.Type = 1;
            keyWordModel.Lable = label;
            threeTuple.second.add(keyWordModel);
            threeTuple.first = insertStr(threeTuple.first, label, threeTuple.first.indexOf(subStr), subStr.length());
            threeTuple.third++;
        }
        Matcher highFundMatcher = HIGH_FUND_REGEX_PATTERN.matcher(threeTuple.first);
        while (highFundMatcher.find()) {
            code = highFundMatcher.group(1);
            text = highFundMatcher.group(2);
            subStr = "$" + text + "$";
            label = MessageFormat.format(holdPlace, threeTuple.third);
            KeyWordModel keyWordModel = new KeyWordModel();
            keyWordModel.Code = code;
            keyWordModel.Text = "$" + text + "$";
            keyWordModel.Type = 3;
            keyWordModel.Lable = label;
            threeTuple.second.add(keyWordModel);
            threeTuple.first = insertStr(threeTuple.first, label, threeTuple.first.indexOf(subStr), subStr.length());
            threeTuple.third++;
        }

        //匹配话题关键字（话题2.0版本）
        if (StringUtils.hasLength(extend)) {
            List<FundTopic> topicList = handleFundTopic(extend);
            if (!CollectionUtils.isEmpty(topicList)) {

                String tempContent = threeTuple.first;
                Matcher tabMatcher = TAB_REGEX_PATTERN.matcher(tempContent);
                while (tabMatcher.find()) {
                    tempContent = tempContent.replace(tabMatcher.group(0), "");
                }

                List<String> topicNameList = new ArrayList<>();
                Pattern pattern4 = TOPIC_NEW_REGEX_PATTERN;
                Matcher matcher4 = pattern4.matcher(tempContent);
                while (matcher4.find()) {
                    temp = matcher4.group(1);
                    if (StringUtils.hasLength(temp)) {
                        topicNameList.add(temp);
                    }
                }

                String topicNameText = null;
                if (!CollectionUtils.isEmpty(topicNameList)) {
                    topicNameList = topicNameList.stream().distinct().collect(Collectors.toList());
                    for (int i = 0; i < topicNameList.size(); i++, threeTuple.third++) {
                        String topicName = topicNameList.get(i);
                        FundTopic topic = topicList.stream().filter(item -> topicName.equals(item.name)).findFirst().orElse(null);
                        if (topic != null) {
                            topicNameText = "#" + topic.name + "#";
                            label = MessageFormat.format(holdPlace, threeTuple.third);
                            KeyWordModel keyWordModel = new KeyWordModel();
                            keyWordModel.Code = String.valueOf(topic.htid);
                            keyWordModel.Text = topicNameText;
                            keyWordModel.Type = 18;
                            keyWordModel.Lable = label;
                            threeTuple.second.add(keyWordModel);
                            threeTuple.first = threeTuple.first.replace(topicNameText, label);
                        }
                    }
                }
            }
        }

        //处理基金关键字
        handleFundKeywords(threeTuple, keyWordDics);

        twoTuple.first = threeTuple.first;
        twoTuple.second = threeTuple.third;

        return threeTuple.second;
    }

    private void handleFundKeywords(ThreeTuple<String, List<KeyWordModel>, Integer> threeTuple, Map<String, KeywordsEntity> keyWordDics) {

        List<KeywordsEntity> sortedKeywords = new ArrayList<>();
        if (!CollectionUtils.isEmpty(keyWordDics)) {
            sortedKeywords = keyWordDics.values().stream().sorted((o1, o2) ->
                {
                    int i = Integer.compare(o2.Name.length(), o1.Name.length());
                    if (i == 0) {
                        return o1.Name.compareTo(o2.Name);
                    } else {
                        return i;
                    }
                }
            ).collect(Collectors.toList());
        }

        if (appConstant.hanlpFlag) {
            getHaveKeyWordWithNlp(threeTuple, sortedKeywords);
        } else {
            getHaveKeyWord(threeTuple, sortedKeywords);
        }
    }

    /**
     * 匹配内容并生成待处理关键字列表
     */
    private void getHaveKeyWord(ThreeTuple<String, List<KeyWordModel>, Integer> threeTuple, List<KeywordsEntity> sortKeywords) {
        if (StringUtils.hasLength(threeTuple.first) && !CollectionUtils.isEmpty(sortKeywords)) {
            String label = "";
            String text = null;
            String subStr = null;
            String temp = null;
            boolean isSpecial;
            boolean isBad;

            Map<String, String> tabMap = new LinkedHashMap<>();
            int i = 0;
            String tabHoldPlace = null;
            Matcher tabMatcher = TAB_REGEX_PATTERN.matcher(threeTuple.first);
            while (tabMatcher.find()) {
                ++i;
                tabHoldPlace = MessageFormat.format(TAB_HOLD_PLACE, i);
                tabMap.put(tabHoldPlace, tabMatcher.group(0));
                threeTuple.first = threeTuple.first.replace(tabMatcher.group(0), tabHoldPlace);
            }

            for (KeywordsEntity item : sortKeywords) {

                if (threeTuple.first.length() < item.Name.length()) {
                    continue;
                }

                if (threeTuple.first.contains(item.Name)) {

                    isSpecial = false;
                    isBad = false;

                    text = item.Name;
                    subStr = item.Name;

                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}[{1}]$", item.Name, item.Code);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}(OTCFUND{1})$", item.Name, item.Code);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}(OTCFUND|{1})$", item.Name, item.Code);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}({1})$", item.Name, item.Code);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }

                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}$", item.Name);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }

                    String safeName = item.Name.replaceAll("\\(", "\\\\(").replaceAll("\\)", "\\\\)");
                    if (!isSpecial && !isBad) {
                        temp = MessageFormat.format("\\$([^♫]*?){0}([^♫]*?)\\$", safeName);
                        Pattern pattern = Pattern.compile(temp);
                        Matcher matcher = pattern.matcher(threeTuple.first);
                        if (matcher.find()) {
                            isBad = true;
                        }
                    }

                    if (!isSpecial && !isBad) {
                        temp = MessageFormat.format("#([^♫]*?){0}([^♫]*?)#", safeName);
                        Pattern pattern = Pattern.compile(temp);
                        Matcher matcher = pattern.matcher(threeTuple.first);
                        if (matcher.find()) {
                            isBad = true;
                        }
                    }


                    if (!isSpecial && !isBad) {
                        temp = MessageFormat.format("@([\\u4e00-\\u9fa5|A-Za-z0-9]*?){0}", safeName);
                        Pattern pattern = Pattern.compile(temp);
                        Matcher matcher = pattern.matcher(threeTuple.first);
                        if (matcher.find()) {
                            isBad = true;
                        }
                    }


                    if (!isBad) {
                        label = MessageFormat.format(HOLDPLACE, threeTuple.third);
                        KeyWordModel keyWordModel = new KeyWordModel();
                        keyWordModel.Id = item.Id;
                        keyWordModel.Code = item.Code;
                        keyWordModel.Text = text;
                        keyWordModel.Type = item.Type;
                        keyWordModel.Lable = label;
                        threeTuple.second.add(keyWordModel);
                        threeTuple.first = insertStr(threeTuple.first, label, threeTuple.first.indexOf(subStr), subStr.length());
                        threeTuple.third++;
                    }

                }
            }

            if (!CollectionUtils.isEmpty(tabMap)) {
                for (Map.Entry<String, String> entry : tabMap.entrySet()) {
                    threeTuple.first = threeTuple.first.replace(entry.getKey(), entry.getValue());
                }
            }

        }
    }

    /**
     * 替换指定区间字符串
     */
    private String insertStr(String originStr, String insertStr, int start, int length) {
        return originStr.substring(0, start) + insertStr + originStr.substring(start + length);
    }

    /**
     * 匹配内容并生成待处理关键字列表
     */
    private void getHaveKeyWord(ThreeTuple<String, List<KeyWordModel>, Integer> threeTuple, Map<Integer, List<String>> keywordlen2, Map<String, KeywordsEntity> keyWordDic) {
        if (StringUtils.hasLength(threeTuple.first) && keywordlen2 != null && keyWordDic != null) {
            String label = "";
            String holdPlace = HOLDPLACE;
            String tempContent = threeTuple.first;
            Set<Map.Entry<Integer, List<String>>> entrySet = keywordlen2.entrySet();
            for (Map.Entry<Integer, List<String>> item : entrySet) {

                if (tempContent.length() >= item.getKey()) {
                    List<String> temp1 = item.getValue();
                    if (temp1 != null) {
                        for (String checkword : temp1) {
                            if (tempContent.contains(checkword) && keyWordDic.keySet().contains(checkword)) {

                                StringBuilder builder = new StringBuilder(item.getKey());
                                for (int i = 0; i < item.getKey(); i++) {
                                    builder.append(" ");
                                }
                                tempContent = tempContent.replace(checkword, builder.toString());

                                KeywordsEntity temp2 = keyWordDic.get(checkword);
                                if (temp2 != null) {
                                    Pattern pattern = Pattern.compile(MessageFormat.format("<[^>]*({0})+[^>]*?>", checkword));
                                    Matcher matcher = pattern.matcher(threeTuple.first);
                                    if (!matcher.find()) {

                                        KeywordsEntity tempkeyword = keyWordDic.get(checkword);
                                        String temp = MessageFormat.format("${0}[{1}]$", tempkeyword.Name, tempkeyword.Code);
                                        if (threeTuple.first.contains(temp)) {
                                            label = MessageFormat.format(holdPlace, threeTuple.third);
                                            KeyWordModel keyWordModel = new KeyWordModel();
                                            keyWordModel.Id = tempkeyword.Id;
                                            keyWordModel.Code = tempkeyword.Code;
                                            keyWordModel.Text = temp;
                                            keyWordModel.Type = tempkeyword.Type;
                                            keyWordModel.Lable = label;
                                            threeTuple.second.add(keyWordModel);
                                            threeTuple.first = threeTuple.first.replace(temp, label);
                                        } else {
                                            label = MessageFormat.format(holdPlace, threeTuple.third);
                                            KeyWordModel keyWordModel = new KeyWordModel();
                                            keyWordModel.Id = tempkeyword.Id;
                                            keyWordModel.Code = tempkeyword.Code;
                                            keyWordModel.Text = tempkeyword.Name;
                                            keyWordModel.Type = tempkeyword.Type;
                                            keyWordModel.Lable = label;
                                            threeTuple.second.add(keyWordModel);
                                            threeTuple.first = threeTuple.first.replace(checkword, label);
                                        }
                                        threeTuple.third++;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private List<WordLenModel> getWordLenMode() {
        List<WordLenModel> result = new ArrayList<>();
        String cacheKey = "Fund_JJB_Service_KeyWordLenModel";
        result = CacheHelper.get(cacheKey);
        if (result == null) {
            result = keywordDao.getKeyWordLen();
            if (!CollectionUtils.isEmpty(result)) {
                CacheHelper.put(cacheKey, result, 10 * 60 * 1000);
            }
        }
        return result;
    }

    /**
     * 获取话题列表
     */
    private List<FundTopic> handleFundTopic(String extend) {
        List<FundTopic> result = null;
        try {
            if (StringUtils.hasLength(extend)) {
                PostExtendInfo postFundTopic = JacksonUtil.string2Obj(extend, PostExtendInfo.class);
                if (postFundTopic != null && !CollectionUtils.isEmpty(postFundTopic.FundTopicPost)) {
                    //获取话题ID 列表
                    List<Integer> htids = postFundTopic.FundTopicPost.stream().filter(item -> StringUtils.hasLength(item.htid))
                        .map(item -> Integer.parseInt(item.htid)).collect(Collectors.toList());
                    //根据话题ID 列表获取话题信息
                    if (!CollectionUtils.isEmpty(htids)) {
                        result = fundTopicDao.getListByIds(htids);
                    }
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return result;
    }

    /**
     * 处理链接占位
     */
    private void handleAllLinks(ThreeTuple<String, List<KeyWordModel>, Integer> threeTuple) {
        //替换链接---帖子详情页 咨询文章等
        if (!CollectionUtils.isEmpty(urlPositionTypeAndRegexDicInfos)) {
            Set<Map.Entry<String, String>> entrySet = urlPositionTypeAndRegexDicInfos.entrySet();
            for (Map.Entry<String, String> urlPositionTypeAndRegexDicInfo : entrySet) {
                //获取type 和各项值
                String[] keyInfos = urlPositionTypeAndRegexDicInfo.getKey().split("_");
                Pattern pattern = urlPositionTypeAndRegexDicInfosPattern.get(urlPositionTypeAndRegexDicInfo.getKey());
                Matcher matcher = pattern.matcher(threeTuple.first);
                //生成相应的type信息
                for (int i = 0; matcher.find(); i++, threeTuple.third++) {
                    String label = MessageFormat.format(HOLDPLACE, threeTuple.third);
                    String text = removeHtmlLablesExceptImg(matcher.group(Integer.parseInt(keyInfos[2])));

                    Matcher matcher1 = LINK_IMG_PATTERN.matcher(text);
                    if (matcher1.find()) {
                        label = MessageFormat.format(ImgPlace, threeTuple.third);
                    }
                    KeyWordModel keyWordModel = new KeyWordModel();
                    keyWordModel.Code = matcher.group(Integer.parseInt(keyInfos[1]));
                    keyWordModel.Text = text;
                    keyWordModel.Type = Integer.parseInt(keyInfos[0]);
                    keyWordModel.Lable = label;
                    threeTuple.second.add(keyWordModel);
                    threeTuple.first = threeTuple.first.replace(matcher.group(0), label);
                }
            }
        }
    }

    /**
     * 去除文本中除img标签以外的所有的html标签
     */
    public String removeHtmlLablesExceptImg(String content) {
        try {
            Matcher matcher = NOT_IMG_TAB_REGEX_PATTERN.matcher(content);
            while (matcher.find()) {
                content = content.replace(matcher.group(0), "");
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return null;
        }
        return content;
    }

    private int processTitle(String title, String content, String code) {
        //股吧提供逻辑(微信群聊)
        String _title = title;
        Matcher matcher1 = TAB_REGEX_PATTERN.matcher(_title);
        while (matcher1.find()) {
            _title = _title.replace(matcher1.group(0), "");
        }

        _title = _title.replace(" ", "").replace("\n", "")
            .replace("\r", "").replace("\t", " ");

        String _content = content;
        Matcher matcher2 = TAB_REGEX_PATTERN.matcher(_content);
        while (matcher2.find()) {
            _content = _content.replace(matcher2.group(0), "");
        }

        Matcher matcher3 = TOPIC_NAME_REGEX_PATTERN.matcher(_content);
        while (matcher3.find()) {
            _content = _content.replace(matcher3.group(0), "#");
        }

        Matcher matcher4 = TOPIC_ID_REGEX_PATTERN.matcher(_content);
        while (matcher4.find()) {
            _content = _content.replace(matcher4.group(0), "");
        }

        Matcher matcher5 = AT_REGEX_PATTERN.matcher(_content);
        while (matcher5.find()) {
            _content = _content.replace(matcher5.group(0), "");
        }

        _content = _content.replace("[/at]", "");

        _content = _content.replace(" ", "").replace("\n", "")
            .replace("\r", "").replace("\t", " ");
        if (_content.startsWith(_title) || "zf".equals(code.toLowerCase())) {
            return -1;
        }
        return 0;
    }

    private String titleFormat(String title) {
        if (StringUtils.hasLength(title)) {
            //如果是老话题才做处理，新话题无需处理 ##
            if (title.contains("[topic_name=")) {
                String temp = title.replace("[topic_name=", "");
                int index = temp.indexOf("##");
                if (index > 0) {
                    temp = temp.substring(0, index);
                }
                return temp;
            }
        }
        return title;
    }

    private List<String> getAllImg(OneTuple<String> oneTuple, int id) {
        List<String> result = new ArrayList<>();
        if (StringUtils.hasLength(oneTuple.first)) {
            Matcher matcher = NEWPIC_REGEX_PATTERN.matcher(oneTuple.first);
            while (matcher.find()) {
                String src = matcher.group("imgUrl");
                if (StringUtils.hasLength(src) && picIsEnabled(src, id)) {
                    result.add(src);
                } else {
                    if (!StringUtils.hasLength(src)) {
                        //剔除url为空的img标签
                        oneTuple.first = oneTuple.first.replace(matcher.group(0), "");
                    }
                }
            }
        }
        return result;
    }

    /**
     * 是否可用
     */
    private boolean picIsEnabled(String pic, int id) {
        boolean result = false;
        InputStream is = null;
        try {
            if (isWhiteUrl(pic)) {
                result = true;
            } else {
                URL url = new URL(pic);
                URLConnection conn = url.openConnection();
                conn.setConnectTimeout(200);
                conn.setReadTimeout(3000);
                is = conn.getInputStream();
                BufferedImage image = ImageIO.read(is);
                result = imageSizeEnable(image);
            }
        } catch (Exception ex) {
            logger.warn(logger + "picIsEnabled()报错。pic:" + pic + "，帖子ID：" + id);
            logger.warn(ex.getMessage(), ex);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (Exception ex) {
                logger.error(ex.getMessage(), ex);
            }
        }
        return result;
    }

    /**
     * 判断图片的尺寸是否可用
     */
    private boolean imageSizeEnable(BufferedImage img) {
        return img != null && img.getWidth() > 50 && img.getHeight() > 50;
    }

    private boolean isWhiteUrl(String pic) {
        boolean result = false;
        List<String> list = CommonUtils.toList(appConstant.whitepicurllist, ",");
        if (!CollectionUtils.isEmpty(list)) {
            for (String item : list) {
                if (pic.contains(item)) {
                    result = true;
                    break;
                }
            }
        }
        return result;
    }

    private Map<String, List<PassportFundMrgModel>> getFundMrgDic() {
        Map<String, List<PassportFundMrgModel>> result = new HashMap<>();
        try {
            List<PassportFundMrgModel> fundMrgList = passportFundMrgDao.getAll();
            if (!CollectionUtils.isEmpty(fundMrgList)) {
                result = fundMrgList.stream().filter(item -> StringUtils.hasLength(item.PassportUID) && item.IsDel == 0)
                    .collect(Collectors.groupingBy(item -> item.PassportUID));
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        if (result == null) {
            result = new HashMap<>();
        }
        return result;
    }

    public Map<String, KeywordsEntity> getKeyWordDic() {
        Map<String, KeywordsEntity> result = null;
        String cacheKey = "Fund_JJB_Service_KeyWordsDic";
        result = CacheHelper.get(cacheKey);
        if (result == null) {
            result = keywordDao.getKeywordsDic();
            if (!CollectionUtils.isEmpty(result)) {
                CacheHelper.put(cacheKey, result, 1000 * 60 * 30L);
            }
        }
        if (result == null) {
            result = new HashMap<>();
        }
        return result;
    }

    public TwoTuple<Integer, Integer> subStrBylength(String content, int fromIndex, int limit) {
        TwoTuple<Integer, Integer> result = new TwoTuple<>(fromIndex, fromIndex);

        int start = fromIndex - limit < 0 ? 0 : fromIndex - limit;
        int end = fromIndex + limit >= content.length() ? content.length() - 1 : fromIndex + limit;

        int i = fromIndex;
        while (true) {
            /*if (TERMINATOR_CHAR_LIST.contains(content.charAt(i))) {
                i++;
                break;
            }*/
            if (i <= start) {
                break;
            }
            i--;
        }
        start = i;

        i = fromIndex + 1;
        while (true) {
            if (i >= end) {
                break;
            }
            /*if (TERMINATOR_CHAR_LIST.contains(content.charAt(i))) {
                break;
            }*/
            i++;
        }
        end = i;

        result.first = start;
        result.second = end;

        return result;
    }

    /**
     * 预处理
     */
    public TwoTuple<String, Integer> preDealBeforeNlp(String content, int fromIndex, List<KeyWordModel> keyWordModelList) {
        TwoTuple<String, Integer> result = new TwoTuple<>(content, fromIndex);
        if (!CollectionUtils.isEmpty(keyWordModelList)) {
            int tempIndex = 0;
            for (KeyWordModel item : keyWordModelList) {
                tempIndex = content.indexOf(item.Lable);
                if (tempIndex > 0) {
                    if (tempIndex < fromIndex) {
                        fromIndex += (item.Text.length() - item.Lable.length());
                    }
                    content = content.replace(item.Lable, item.Text);
                }

            }
        }
        result.first = content;
        result.second = fromIndex;
        return result;
    }

    /**
     * 匹配内容并生成待处理关键字列
     * 备注：引入nlp词性标注
     */
    private void getHaveKeyWordWithNlp(ThreeTuple<String, List<KeyWordModel>, Integer> threeTuple, List<KeywordsEntity> sortKeywords) {
        if (StringUtils.hasLength(threeTuple.first) && !CollectionUtils.isEmpty(sortKeywords)) {
            String label = "";
            String text = null;
            String subStr = null;
            String temp = null;
            boolean isSpecial;
            boolean isBad;

            Map<String, String> tabMap = new LinkedHashMap<>();
            int i = 0;
            String tabHoldPlace = null;
            Matcher tabMatcher = TAB_REGEX_PATTERN.matcher(threeTuple.first);
            while (tabMatcher.find()) {
                ++i;
                tabHoldPlace = MessageFormat.format(TAB_HOLD_PLACE, i);
                tabMap.put(tabHoldPlace, tabMatcher.group(0));
                threeTuple.first = threeTuple.first.replace(tabMatcher.group(0), tabHoldPlace);
            }

            int fromIndex = 0;
            int matchIndex = -1;
            boolean isForce = false;
            int fromIndexCandidate = -1;
            int matchIndexCandidate = -1;
            for (KeywordsEntity item : sortKeywords) {

                if (threeTuple.first.length() < item.Name.length()) {
                    continue;
                }


                fromIndex = 0;
                matchIndex = -1;
                isForce = false;
                fromIndexCandidate = -1;
                matchIndexCandidate = -1;
                while (true) {

                    if (fromIndex >= threeTuple.first.length()) {
                        break;
                    }

                    fromIndex = threeTuple.first.indexOf(item.Name, fromIndex);

                    if (fromIndex < 0) {
                        if (fromIndexCandidate != -1) {
                            fromIndex = fromIndexCandidate;
                            matchIndex = matchIndexCandidate - 1;
                            isForce = true;
                        } else {
                            break;
                        }
                    }

                    matchIndex++;

                    isSpecial = false;
                    isBad = false;

                    text = item.Name;
                    subStr = item.Name;

                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}[{1}]$", item.Name, item.Code);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}(OTCFUND{1})$", item.Name, item.Code);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}(OTCFUND|{1})$", item.Name, item.Code);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}({1})$", item.Name, item.Code);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }

                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}$", item.Name);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }

                    String safeName = item.Name.replaceAll("\\(", "\\\\(").replaceAll("\\)", "\\\\)");
                    if (!isSpecial && !isBad) {
                        temp = MessageFormat.format("\\$([^♫]*?){0}([^♫]*?)\\$", safeName);
                        Pattern pattern = Pattern.compile(temp);
                        Matcher matcher = pattern.matcher(threeTuple.first);
                        if (matcher.find()) {
                            isBad = true;
                        }
                    }

                    if (!isSpecial && !isBad) {
                        temp = MessageFormat.format("#([^♫]*?){0}([^♫]*?)#", safeName);
                        Pattern pattern = Pattern.compile(temp);
                        Matcher matcher = pattern.matcher(threeTuple.first);
                        if (matcher.find()) {
                            isBad = true;
                        }
                    }


                    if (!isSpecial && !isBad) {
                        temp = MessageFormat.format("@([\\u4e00-\\u9fa5|A-Za-z0-9]*?){0}", safeName);
                        Pattern pattern = Pattern.compile(temp);
                        Matcher matcher = pattern.matcher(threeTuple.first);
                        if (matcher.find()) {
                            isBad = true;
                        }
                    }


                    if (!isBad) {
                        if (!isSpecial && item.Type == 17) {

                            if (!isForce) {
                                //nlp词性标注
                                TwoTuple<Integer, Integer> subStrBylength = subStrBylength(threeTuple.first, fromIndex, 50);
                                String subContent = threeTuple.first.substring(subStrBylength.first, subStrBylength.second);
                                TwoTuple<String, Integer> preDealBeforeNlp = preDealBeforeNlp(subContent, fromIndex - subStrBylength.first, threeTuple.second);
                                int analyzeRes = keywordHanlpService.analyze(preDealBeforeNlp.first, preDealBeforeNlp.second, item);

                                if (matchIndex == 0) {
                                    fromIndexCandidate = fromIndex;
                                    matchIndexCandidate = matchIndex;
                                }
                                if (analyzeRes == 0) {
                                    fromIndex = fromIndex + item.Name.length();
                                    continue;
                                } else if (analyzeRes == 2) {

                                    fromIndexCandidate = fromIndex;
                                    matchIndexCandidate = matchIndex;

                                    fromIndex = fromIndex + item.Name.length();
                                    continue;
                                }
                            }

                            label = MessageFormat.format(HOLDPLACE, threeTuple.third);
                            KeyWordModel keyWordModel = new KeyWordModel();
                            keyWordModel.Id = item.Id;
                            keyWordModel.Code = item.Code;
                            keyWordModel.Text = text;
                            keyWordModel.Type = item.Type;
                            keyWordModel.Lable = label;
                            keyWordModel.MatchIndex = matchIndex;
                            threeTuple.second.add(keyWordModel);
                            threeTuple.first = insertStr(threeTuple.first, label, threeTuple.first.indexOf(subStr, fromIndex), subStr.length());
                            threeTuple.third++;

                        } else {
                            label = MessageFormat.format(HOLDPLACE, threeTuple.third);
                            KeyWordModel keyWordModel = new KeyWordModel();
                            keyWordModel.Id = item.Id;
                            keyWordModel.Code = item.Code;
                            keyWordModel.Text = text;
                            keyWordModel.Type = item.Type;
                            keyWordModel.Lable = label;
                            keyWordModel.MatchIndex = matchIndex;
                            threeTuple.second.add(keyWordModel);
                            threeTuple.first = insertStr(threeTuple.first, label, threeTuple.first.indexOf(subStr), subStr.length());
                            threeTuple.third++;
                        }
                    }

                    break;
                }
            }

            if (!CollectionUtils.isEmpty(tabMap)) {
                for (Map.Entry<String, String> entry : tabMap.entrySet()) {
                    threeTuple.first = threeTuple.first.replace(entry.getKey(), entry.getValue());
                }
            }

        }
    }


}
