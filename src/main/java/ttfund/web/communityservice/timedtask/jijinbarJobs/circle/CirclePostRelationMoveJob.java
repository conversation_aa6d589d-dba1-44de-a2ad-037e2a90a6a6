package ttfund.web.communityservice.timedtask.jijinbarJobs.circle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.CirclePostRelation;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.CirclePostRelationDao;
import ttfund.web.communityservice.dao.msyql.CirclePostRelationMysqlDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;
import java.util.List;

/**
 * 圈子帖子关系表移动job
 * mongo->mysql
 */
@Slf4j
@JobHandler("CirclePostRelationMoveJob")
@Component
public class CirclePostRelationMoveJob extends IJobHandler {

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private CirclePostRelationDao circlePostRelationDao;

    @Autowired
    private CirclePostRelationMysqlDao circlePostRelationMysqlDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            log.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                initBreakpoint,
                batchReadCount
            );

            if (StringUtils.hasLength(initBreakpoint)) {
                userRedisDao.set(UserRedisConfig.CIRCLEPOSTRELATIONMOVEJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);


                return ReturnT.SUCCESS;
            }

            deal(batchReadCount);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount) throws Exception {

        String breakpointName = UserRedisConfig.CIRCLEPOSTRELATIONMOVEJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            log.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("第一步，读取断点。断点:{}", breakpoint);

        List<CirclePostRelation> list = circlePostRelationDao.getList(null, CirclePostRelation.class, breakpointDate, batchReadCount);

        log.info("第二步，读取数据。数量:{}，头部数据：{}",
            list == null ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = list.get(list.size() - 1).getUpdateTime();

            if (!CollectionUtils.isEmpty(list)) {
                List<List<CirclePostRelation>> batchList = CommonUtils.toSmallList2(list, 200);
                for (List<CirclePostRelation> batch : batchList) {
                    circlePostRelationMysqlDao.replaceMany(batch);
                }
            }

            log.info("第三步，读取数据。数量:{}，头部数据：{}",
                list == null ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );
        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        log.info("第四步，更新断点。断点：{}", breakpoint);
    }

}
