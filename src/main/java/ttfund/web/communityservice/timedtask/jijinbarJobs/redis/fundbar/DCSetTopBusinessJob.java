package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ttfund.web.base.helper.CacheHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.EMSetPostItem;
import ttfund.web.communityservice.bean.jijinBar.data.FundTopicDataItem;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.post.guba.GuBaResponseResultBase;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.EmSetTopDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 东财帖子设置置顶
 */
@JobHandler(value = "dcSetTopBusinessJob")
@Component
public class DCSetTopBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(DCSetTopBusinessJob.class);

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private EmSetTopDao emSetTopDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        try {

            setDataToCahche();

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 设置缓存
     */
    private void setDataToCahche() {
        try {

            List<EMSetPostItem> result = getListFromApi();

            logger.info("1.读取数据。数量：{}，头部数据：{}",
                    result == null ? 0 : result.size(),
                    CollectionUtils.isEmpty(result) ? null : JSON.toJSONString(result.get(0))
            );


            if (result != null) {

                List<Long> postids = result.stream().map(a -> Long.parseLong(a.post_id)).collect(Collectors.toList());
                List<PostInfoNewModel> postList = postInfoNewDao.getPostByIds(postids);

                logger.info("2.读取帖子。数量：{}，头部数据：{}",
                        postList == null ? 0 : postList.size(),
                        CollectionUtils.isEmpty(postList) ? null : JSON.toJSONString(postList.get(0))
                );

                if (postList != null) {

                    postList.sort(Comparator.comparing(o -> o.TIME, Comparator.reverseOrder()));
                    List<String> list = postList.stream().map(a -> String.valueOf(a.ID)).collect(Collectors.toList());

                    //数据插入数据库
                    emSetTopDao.insertOrUpdate(result.stream().filter(a -> list.contains(a.post_id)).collect(Collectors.toList()));

                    logger.info("3.写库。数量：{}，头部数据：{}",
                            postList == null ? 0 : postList.size(),
                            CollectionUtils.isEmpty(postList) ? null : JSON.toJSONString(postList.get(0))
                    );

                    //插入缓存
                    app.barredis.set(BarRedisKey.ASP_NET_SERVICE_GUBA_SETTOP, JacksonUtil.obj2String(list), 24 * 3600L);

                    logger.info("4.写缓存。数量：{}，头部数据：{}",
                            list == null ? 0 : list.size(),
                            CollectionUtils.isEmpty(list) ? null : list.get(0)
                    );

                } else {
                    app.barredis.del(BarRedisKey.ASP_NET_SERVICE_GUBA_SETTOP);

                    logger.info("2.读取数据为空，删除redis键。");
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 根据api地址获取 东财置顶信息
     */
    private List<EMSetPostItem> getListFromApi() {

        String api = appConstant.gubaFundTopicListApi;

        List<EMSetPostItem> list = new ArrayList<>();

        try {
            list = CacheHelper.get(api);
            if (CollectionUtils.isEmpty(list)) {
                String content = HttpHelper.requestGet(api);

                if (StringUtils.hasLength(content)) {
                    TypeReference<GuBaResponseResultBase<FundTopicDataItem>> typeReference = new TypeReference<GuBaResponseResultBase<FundTopicDataItem>>() {
                    };
                    GuBaResponseResultBase<FundTopicDataItem> result = JacksonUtil.string2Obj(content, typeReference);

                    if (result != null && result.rc == 1 && result.re != null) {
                        list = result.re.stream().map(item -> item.post).collect(Collectors.toList());
                        CacheHelper.put(api, list, 5 * 60 * 1000L);
                    }
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return list;
    }
}
