package ttfund.web.communityservice.timedtask.jijinbarJobs.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.barrage.PassportUserInfoModel;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserBindInfo;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.CalVCountDataUserDao;
import ttfund.web.communityservice.dao.mongo.PassportUserBindInfoDao;
import ttfund.web.communityservice.dao.msyql.UserRelationDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.service.redis.UserRedisService;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 创作中心用户计算job
 * 需求：#653737 【基金吧6.12】新增创作中心
 */
@JobHandler("CreativeCenterUserCalculateJob")
@Component
public class CreativeCenterUserCalculateJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(CreativeCenterUserCalculateJob.class);

    private static List<String> setFields = Arrays.asList("NickName");

    @Autowired
    private UserRelationDao userRelationDao;

    @Autowired
    private PassportUserBindInfoDao passportUserBindInfoDao;

    @Autowired
    private UserRedisService userRedisService;

    @Autowired
    private CalVCountDataUserDao calVCountDataUserDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            Integer fansCount = null;
            Boolean isRunTotal = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                fansCount = jsonObject.getInteger("fansCount");
                isRunTotal = jsonObject.getBoolean("isRunTotal");
            }

            if (batchReadCount == null) {
                batchReadCount = 1000;
            }
            if (fansCount == null) {
                fansCount = 10;
            }
            if (isRunTotal == null) {
                isRunTotal = false;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，fansCount：{}，isRunTotal：{}",
                    initBreakpoint,
                    batchReadCount,
                    fansCount,
                    isRunTotal
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.CREATIVECENTERUSERCALCULATEJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            if (isRunTotal) {
                totalDeal(fansCount, batchReadCount);
            } else {
                incrementalDeal(fansCount, batchReadCount);
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 增量处理
     */
    private void incrementalDeal(int fansCount, int batchReadCount) throws Exception {

        String breakpointName = UserRedisConfig.CREATIVECENTERUSERCALCULATEJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("增量模式-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("增量模式-第一步，读取断点。断点:{}", breakpoint);


        List<Map<String, Object>> list = userRelationDao.getByUpdateTime(breakpointDate, batchReadCount);

        logger.info("增量模式-第二步，读取关注关系更新。数量:{}，头部列表：{}",
                list == null ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONString(list.get(0)));

        List<PassportUserInfoModel> userList = null;
        List<Map<String, Object>> mapList = null;
        Map<String, Object> map = null;

        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = (Date) list.get(list.size() - 1).get("UPDATETIME");

            List<PassportUserBindInfo> passportBindList = null;

            List<String> objidList = list.stream().map(a -> (String) a.get("OBJID")).distinct().collect(Collectors.toList());

            //满足粉丝数数量条件
            List<Map<String, Object>> objUserList = userRelationDao.getUserByFansCountAndObjIds(fansCount, objidList);

            logger.info("增量模式-第三步，读取粉丝数量筛选。数量:{}，头部列表：{}",
                    objUserList == null ? 0 : objUserList.size(),
                    CollectionUtils.isEmpty(objUserList) ? null : JSON.toJSONString(objUserList.get(0)));

            //满足是天天基金用户条件
            if (!CollectionUtils.isEmpty(objUserList)) {
                objidList = objUserList.stream().map(a -> (String) a.get("OBJID")).collect(Collectors.toList());
                passportBindList = passportUserBindInfoDao.getByUids(objidList);

                logger.info("增量模式-第四步，读取通行证绑定关系筛选。数量:{}，头部列表：{}",
                        passportBindList == null ? 0 : passportBindList.size(),
                        CollectionUtils.isEmpty(passportBindList) ? null : JSON.toJSONString(passportBindList.get(0)));

                if (!CollectionUtils.isEmpty(passportBindList)) {
                    objidList = passportBindList.stream().map(a -> a.UID).collect(Collectors.toList());
                    userList = userRedisService.passportUserInfoCache(objidList);

                    logger.info("增量模式-第五步，读取通行证筛选。数量:{}，头部列表：{}",
                            userList == null ? 0 : userList.size(),
                            CollectionUtils.isEmpty(userList) ? null : JSON.toJSONString(userList.get(0)));
                }
            }

            if (!CollectionUtils.isEmpty(userList)) {
                mapList = new ArrayList<>(userList.size());
                for (PassportUserInfoModel a : userList) {
                    map = new HashMap<>();
                    map.put("UID", a.PassportID);
                    map.put("NickName", a.NickName);
                    map.put("IsDel", 0);
                    map.put("CreateTime", new Date());
                    map.put("UpdateTime", new Date());
                    map.put("Remark", "创作中心用户");
                    mapList.add(map);
                }
                calVCountDataUserDao.upsertManyBySetOnInsertWithSetFields(mapList, null, "UID");

                logger.info("增量模式-第六步，数据写库。数量:{}，头部列表：{}",
                        mapList == null ? 0 : mapList.size(),
                        CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONString(mapList.get(0)));
            }


        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        logger.info("增量模式-第七步，更新断点。断点：{}", breakpoint);


    }

    /**
     * 全量处理
     */
    private void totalDeal(int fansCount, int batchReadCount) throws Exception {

        List<PassportUserInfoModel> userList = null;
        List<Map<String, Object>> mapList = null;
        Map<String, Object> map = null;

        String objId = null;
        List<Map<String, Object>> objUserList = null;
        List<String> objidList = null;
        List<PassportUserBindInfo> passportBindList = null;

        int totalCount = 0;
        int round = 0;
        while (true) {

            round++;

            //满足粉丝数数量条件
            objUserList = userRelationDao.getUserByFansCount(objId, fansCount, batchReadCount);

            logger.info("全量模式-第一步，读取粉丝数量筛选-第{}轮。objId：{}，数量:{}，头部列表：{}",
                    round,
                    objId,
                    objUserList == null ? 0 : objUserList.size(),
                    CollectionUtils.isEmpty(objUserList) ? null : JSON.toJSONString(objUserList.get(0)));

            //满足是天天基金用户条件
            if (!CollectionUtils.isEmpty(objUserList)) {

                objidList = objUserList.stream().map(a -> (String) a.get("OBJID")).collect(Collectors.toList());
                passportBindList = passportUserBindInfoDao.getByUids(objidList);

                logger.info("全量模式-第二步，读取通行证绑定关系筛选-第{}轮。数量:{}，头部列表：{}",
                        round,
                        passportBindList == null ? 0 : passportBindList.size(),
                        CollectionUtils.isEmpty(passportBindList) ? null : JSON.toJSONString(passportBindList.get(0)));

                if (!CollectionUtils.isEmpty(passportBindList)) {
                    objidList = passportBindList.stream().map(a -> a.UID).collect(Collectors.toList());
                    userList = userRedisService.passportUserInfoCache(objidList);

                    logger.info("全量模式-第三步，读取通行证筛选-第{}轮。数量:{}，头部列表：{}",
                            round,
                            userList == null ? 0 : userList.size(),
                            CollectionUtils.isEmpty(userList) ? null : JSON.toJSONString(userList.get(0)));
                }
            }

            if (!CollectionUtils.isEmpty(userList)) {
                totalCount += userList.size();

                mapList = new ArrayList<>(userList.size());
                for (PassportUserInfoModel a : userList) {
                    map = new HashMap<>();
                    map.put("UID", a.PassportID);
                    map.put("NickName", a.NickName);
                    map.put("IsDel", 0);
                    map.put("CreateTime", new Date());
                    map.put("UpdateTime", new Date());
                    map.put("Remark", "创作中心用户");
                    mapList.add(map);
                }
                calVCountDataUserDao.upsertManyBySetOnInsertWithSetFields(mapList, setFields, "UID");

                logger.info("全量模式-第四步，数据写库-第{}轮。数量:{}，头部列表：{}",
                        round,
                        mapList == null ? 0 : mapList.size(),
                        CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONString(mapList.get(0)));
            }


            if (!CollectionUtils.isEmpty(objUserList)) {
                objId = (String) objUserList.get(objUserList.size() - 1).get("OBJID");
            }

            if (objUserList == null || objUserList.size() < batchReadCount) {
                break;
            }
        }

        logger.info("全量模式-第五步，完成。共{}轮，共筛选{}个用户",
                round,
                totalCount
        );

    }


}
