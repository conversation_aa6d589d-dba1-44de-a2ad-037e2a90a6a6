package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.SetKeyUrlModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.KeyUrlDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 关键字同步Redis
 */
@JobHandler(value = "keywordsRedisBusinessJob")
@Component
public class KeywordsRedisBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(KeywordsRedisBusinessJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private KeyUrlDao keyUrlDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws JsonProcessingException {

        try {

            String initBreakpoint = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
            }

            logger.info("第零步，打印参数。initBreakpoint：{}", initBreakpoint);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.KEYWORDSREDISBUSINESSJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            syncKeywordsTypeRedis();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 关键字类型同步到Redis
     */
    private void syncKeywordsTypeRedis() {
        try {
            String cacheKeyType = BarRedisKey.KEYWORD_TYPE;

            String breakpointName = UserRedisConfig.KEYWORDSREDISBUSINESSJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("第一步，读取断点。断点:{}", breakpoint);


            List<SetKeyUrlModel> keytypelist = keyUrlDao.getKeyUrls(breakpointDate);

            logger.info("第二步，读取数据。数量:{}，头部列表：{}",
                    keytypelist == null ? 0 : keytypelist.size(),
                    CollectionUtils.isEmpty(keytypelist) ? null : JSON.toJSONString(keytypelist.get(0))
            );


            if (!CollectionUtils.isEmpty(keytypelist)) {

                breakpointDate = keytypelist.stream().map(l -> l.UpdateTime).max(Comparator.naturalOrder()).get();

                int i = 0;
                for (SetKeyUrlModel item : keytypelist) {

                    i++;

                    app.barredis.set(cacheKeyType + item.Type, JSON.toJSONStringWithDateFormat(item, "yyyy-MM-dd'T'HH:mm:ss"));

                    logger.info("第三步，写缓存详情。第{}/{}个，数据：{}",
                            i,
                            keytypelist.size(),
                            JSON.toJSONStringWithDateFormat(item, "yyyy-MM-dd'T'HH:mm:ss")
                    );

                }

                logger.info("第三步，写缓存完成。数量:{}，头部列表：{}",
                        keytypelist == null ? 0 : keytypelist.size(),
                        CollectionUtils.isEmpty(keytypelist) ? null : JSON.toJSONString(keytypelist.get(0))
                );

            }

            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

            logger.info("第四步，更新断点。断点：{}", breakpoint);


        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

}
