package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.config.SetHideModel;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.PostRecommendExtendModel;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.PostRecommendModelExt;
import ttfund.web.communityservice.dao.mongo.FindRecommendPostDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.msyql.SetHideDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;
import ttfund.web.communityservice.utils.StringUtil;

import java.util.*;

/**
 * 基金吧帖子在瀑布流设置为隐藏
 */
@JobHandler(value = "findRecommendPostSetHideJob")
@Component
public class FindRecommendPostSetHideJob extends IJobHandler {
    private  String logpre="[findRecommendPostSetHideJob]=>";
    private static final Logger logger = LoggerFactory.getLogger(FindRecommendPostSetHideJob.class);

    @Autowired
    SetHideDao setHideDao;

    @Autowired
    UserRedisDao userRedisDao;

    @Autowired
    FindRecommendPostDao findRecommendPostDao;

    @Autowired
    PostDao postDao;

    public ReturnT<String> execute(String param) {
        try{

            /**
             * 根据断点时间 获取增量更新数据，设置帖子为是否隐藏
             */
            String breakTimeName="findRecommendPostSetHideJob";
            Date lastUpdatime=  userRedisDao.getBreakTime(breakTimeName);
            if(lastUpdatime==null){
                lastUpdatime= DateUtil.calendarDateByDays(-10000);
            }
            logger.info(logpre+"=>lastUpdatime=>本次断点时间;"+DateUtil.dateToStr(lastUpdatime));
            List<SetHideModel> setHideModels= setHideDao.getListByUpdateTime(lastUpdatime);
            if(!CollectionUtils.isEmpty(setHideModels)){
                //下次断点时间
                lastUpdatime=setHideModels.stream().
                max(Comparator.comparing(o->o.UpdateTime)).get().UpdateTime;

                for(SetHideModel item:setHideModels){
                    if(!StringUtil.isNull(item.HidePlace)){
                      String[] arrHidePlace=  item.HidePlace.split("\\,");
                      if(arrHidePlace!=null && arrHidePlace.length>0 &&
                              Arrays.asList(arrHidePlace).contains("4")){
                          Map<String,Object> map= new HashMap<>();
                         int isdel= item.State==-1?0:1;

                         map.put("IsDel",isdel);
                         map.put("_id",item.TID);
                         map.put("UpdateTime",DateUtil.getNowDate());

                         findRecommendPostDao.updateFirst(map);
                      }
                    }
                }
                logger.info(logpre+"=>lastUpdatime=>下次断点断点时间;"+DateUtil.dateToStr(lastUpdatime));
                logger.info(logpre+"=>本轮更新数量=>;"+setHideModels.size()+"涉及数据："+ JacksonUtil.obj2String(setHideModels));

                userRedisDao.setBreakTime(breakTimeName,lastUpdatime);
            }
            return  ReturnT.SUCCESS;
        }
        catch (Exception e){
            logger.info(logpre+e.getMessage(),e);
            return  ReturnT.FAIL;
        }

    }
}
