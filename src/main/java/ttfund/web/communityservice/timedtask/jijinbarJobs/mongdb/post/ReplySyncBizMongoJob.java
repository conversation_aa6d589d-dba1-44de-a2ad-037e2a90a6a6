package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoNewModel;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.ReplyInfoMongoDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoExtraDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步子评论 + 评论打分
 */
@JobHandler("ReplySyncBizMongoJob")
@Component
public class ReplySyncBizMongoJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(ReplySyncBizMongoJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private ReplyInfoExtraDao replyInfoExtraDao;

    @Autowired
    private ReplyInfoMongoDao replyInfoMongoDao;

    @Autowired
    private ReplyInfoNewDao replyInfoNewDao;

    @Autowired
    private AppConstant appConstant;


    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint1 = null;
            String initBreakpoint2 = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint1 = jsonObject.getString("initBreakpoint1");
                initBreakpoint2 = jsonObject.getString("initBreakpoint2");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint1：{}，initBreakpoint2：{}，batchReadCount：{}",
                    initBreakpoint1,
                    initBreakpoint2,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint1) || StringUtils.hasLength(initBreakpoint2)) {

                if (StringUtils.hasLength(initBreakpoint1)) {
                    userRedisDao.set(UserRedisConfig.REPLYSYNCBIZMONGOJOB_REPLYSCORE_BREAKPOINT, initBreakpoint1, 30 * 24 * 3600L);
                    logger.info("第零步，初始化断点。initBreakpoint1：{}", initBreakpoint1);
                }


                if (StringUtils.hasLength(initBreakpoint2)) {
                    userRedisDao.set(UserRedisConfig.REPLYSYNCBIZMONGOJOB_SYNCSUBREPLYNEW_BREAKPOINT, initBreakpoint2, 30 * 24 * 3600L);
                    logger.info("第零步，初始化断点。initBreakpoint2：{}", initBreakpoint2);
                }

                return ReturnT.SUCCESS;
            }

            replyScore(batchReadCount);
            syncSubReplyNew(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    /**
     * 评论打分
     * 分数 =  点赞数*3 +回复数*2
     */
    private void replyScore(int batchReadCount) {
        try {

            logger.info("第零步-replyScore，打印配置。config:{}", appConstant.replyRankConfig);

            String breakpointName = UserRedisConfig.REPLYSYNCBIZMONGOJOB_REPLYSCORE_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("第零步-replyScore，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("第一步-replyScore，读取断点。断点:{}", breakpoint);

            int round = 0;
            while (true) {
                round++;

                List<Map<String, Object>> dataList = replyInfoExtraDao.getReplyExtra(breakpointDate, batchReadCount);

                logger.info("第二步-replyScore，读取数据-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        dataList == null ? 0 : dataList.size(),
                        dataList == null ? null : dataList.stream().map(a -> a.get("ID")).limit(20).collect(Collectors.toList()));

                if (!CollectionUtils.isEmpty(dataList)) {

                    breakpointDate = (Date) dataList.get(dataList.size() - 1).get("UPDATETIME");

                    double score = 0;
                    String[] configs = appConstant.replyRankConfig.split(",");
                    for (Map<String, Object> a : dataList) {

                        if (a.get("LIKECOUNT") == null) {
                            a.put("LIKECOUNT", 0L);
                        }
                        if (a.get("HUIFUNUM") == null) {
                            a.put("HUIFUNUM", 0L);
                        }

                        score = Long.parseLong(a.get("LIKECOUNT").toString()) * Integer.parseInt(configs[0])
                                + Integer.parseInt(a.get("HUIFUNUM").toString()) * Integer.parseInt(configs[1]);

                        a.put("SCORE", score);
                    }

                    dataList.forEach(a -> a.put("_id", String.valueOf(a.get("ID"))));
                    List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(dataList, 200);
                    for (List<Map<String, Object>> batch : batchList) {
                        replyInfoMongoDao.upsertMany(batch);
                    }

                }

                logger.info("第三步-replyScore，数据写库-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        dataList == null ? 0 : dataList.size(),
                        dataList == null ? null : dataList.stream().map(a -> a.get("ID")).limit(20).collect(Collectors.toList()));

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("第四步-replyScore，更新断点-第{}轮。断点：{}", round, breakpoint);

                if (dataList == null || dataList.size() < batchReadCount) {
                    break;
                }
            }


        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 同步子评论
     */
    private void syncSubReplyNew(int batchReadCount) {
        try {

            String breakpointName = UserRedisConfig.REPLYSYNCBIZMONGOJOB_SYNCSUBREPLYNEW_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("第零步-syncSubReplyNew，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("第一步-syncSubReplyNew，读取断点。断点:{}", breakpoint);

            int round = 0;
            while (true) {
                round++;

                List<ReplyInfoNewModel> replyList = replyInfoNewDao.getReplyTable(breakpointDate, batchReadCount);

                logger.info("第二步-syncSubReplyNew，读取数据-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        replyList == null ? 0 : replyList.size(),
                        replyList == null ? null : replyList.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

                if (!CollectionUtils.isEmpty(replyList)) {

                    breakpointDate = replyList.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;

                    List<Integer> topicIds = replyList.stream().map(i -> i.TOPICID).distinct().collect(Collectors.toList());

                    for (Integer topic : topicIds) {

                        List<ReplyInfoNewModel> replys = replyInfoNewDao.getReplyListById(topic, false);
                        if (!CollectionUtils.isEmpty(replys)) {
                            //只更新近一年的评论
                            List<Long> replyIds = replys.stream()
                                    .filter(l -> l.TIME.compareTo(DateUtil.calendarDateByMonth(-13)) > 0 && !StringUtils.hasLength(l.HUIFUIDLIST))
                                    .map(l -> l.ID).collect(Collectors.toList());

                            if (!CollectionUtils.isEmpty(replyIds)) {
                                for (Long id : replyIds) {
                                    List<ReplyInfoNewModel> treeNodes = new ArrayList<>();
                                    getAllSubReply(replys, id, treeNodes);

                                    if (!CollectionUtils.isEmpty(treeNodes)) {
                                        Map<String, Object> map = new HashMap<>();
                                        map.put("_id", String.valueOf(id));
                                        map.put("ZIHUIFULIST", JSON.toJSONStringWithDateFormat(treeNodes, "yyyy-MM-dd'T'HH:mm:ss"));
                                        replyInfoMongoDao.upsertMany(Arrays.asList(map));
                                    }
                                }
                            }
                        }

                    }

                }

                logger.info("第三步-syncSubReplyNew，数据写库-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        replyList == null ? 0 : replyList.size(),
                        replyList == null ? null : replyList.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("第四步-syncSubReplyNew，更新断点-第{}轮。断点：{}", round, breakpoint);

                if (replyList == null || replyList.size() < batchReadCount) {
                    break;
                }
            }


        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 获取所有子评论
     */
    public void getAllSubReply(List<ReplyInfoNewModel> list, long id, List<ReplyInfoNewModel> treeNodes) {
        if (list == null)
            return;

        List<ReplyInfoNewModel> sublist = new ArrayList<>();
        if (id > 0) {
            sublist = list.stream().filter(t -> StringUtils.hasLength(t.HUIFUIDLIST) && t.HUIFUIDLIST.contains(String.valueOf(id)))
                    .collect(Collectors.toList());
        } else {
            sublist = list.stream().filter(t -> !StringUtils.hasLength(t.HUIFUIDLIST))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(sublist))
            return;
        for (ReplyInfoNewModel item : sublist) {
            treeNodes.add(item);
            getAllSubReply(list, item.ID, treeNodes);
        }

    }


}
