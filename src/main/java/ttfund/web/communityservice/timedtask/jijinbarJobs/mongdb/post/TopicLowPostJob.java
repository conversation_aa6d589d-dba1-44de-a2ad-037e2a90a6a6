package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.mongo.TopicLowPost;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.dao.mongo.TopicLowPostDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 话题水帖同步至MongoDB
 */
@JobHandler("TopicLowPostJob")
@Component
public class TopicLowPostJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(TopicLowPostJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private TopicLowPostDao topicLowPostDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        int size = 0;

        try {

            String breakTimeName = "topicLowPostJob_breakTime";
            Date breakTime = userRedisDao.getBreakTime(breakTimeName, DateUtil.calendarDateByYears(-1));

            logger.info("话题水帖同步至MongoDB任务开始，时间断点：" + DateUtil.dateToStr(breakTime, DateUtil.dateTimeDefaultPattern));

            String sql = " select HTID,POSTID,ISLOW,EUTIME from CONTENT.JJB_TB_POST_HT_LOW_DECODE_APP_ALL " +
                " where EUTIME >= ? order by EUTIME limit 5000";

            for (int i = 0; i < 100; i++) {

                List<Object> params = new ArrayList<>();
                params.add(breakTime);

                List<Map> result = app.bigdataVertica.executeQuery(sql, params);

                if (CollectionUtils.isEmpty(result)) {
                    break;
                }

                List<TopicLowPost> list = result.stream()
                    .map(w -> {
                        TopicLowPost bean = new TopicLowPost();
                        bean.setTopicId((String) w.get("HTID"));
                        bean.setPostId((String) w.get("POSTID"));
                        bean.setLow("f".equals(w.get("ISLOW")) ? 1 : 0);
                        bean.setCreateTime(new Date());
                        bean.setUpdateTime(new Date());
                        bean.setIsDel(0);
                        bean.set_id(bean.getTopicId() + "_" + bean.getPostId());
                        return bean;
                    }).collect(Collectors.toList());

                // 保存
                topicLowPostDao.saveAll(list);

                size += list.size();

                logger.info("已更新{}条数据：" + size);

                breakTime = (Date) result.get(result.size() - 1).get("EUTIME");

                if (result.size() < 5000) {
                    break;
                }
            }

            userRedisDao.setBreakTime(breakTimeName, breakTime);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        logger.info("话题水帖同步至MongoDB任务结束，共更新{}条数据", size);

        return ReturnT.SUCCESS;
    }
}
