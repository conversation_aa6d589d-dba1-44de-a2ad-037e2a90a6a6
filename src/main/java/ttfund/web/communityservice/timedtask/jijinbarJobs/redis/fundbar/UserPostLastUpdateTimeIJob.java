package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.JsonUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.UserFollowPostInfo;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.UserPostUpdateModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.constant.DateConstant;
import ttfund.web.communityservice.dao.mongo.AppHomePostInfoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户帖子最新更新时间
 */
@JobHandler("UserPostLastUpdateTimeIJob")
@Component
public class UserPostLastUpdateTimeIJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserPostLastUpdateTimeIJob.class);

    private static final String LOG_PREFIX = "UserPostLastUpdateTimeIJob[用户帖子最新更新时间]";

    private static final int BATCH_SIZE = 100000;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private AppHomePostInfoDao appHomePostInfoDao;

    @Autowired
    private App app;

    @Autowired
    private AppConstant appConstant;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            logicDeal(s);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }


    public void logicDeal(String s) {
        //上次更新时间
        String breakpoint = userRedisDao.get(UserRedisConfig.USER_POST_LAST_UPDATE_TIME_IJOB_BREAKPOINT);
        Date breakpointDate = StringUtils.isEmpty(breakpoint) ? DateUtil.calendarDateByDays(-1) :
            DateUtil.strToDate(breakpoint, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT);
        LOGGER.info("{}: 当前断点时间为: {}", LOG_PREFIX, breakpointDate);

        List<UserFollowPostInfo> postList = appHomePostInfoDao.getListByTime(breakpointDate, BATCH_SIZE, Arrays.asList("UID", "ID", "TIME"));
        // 测试时，仅更新部分用户的最新发帖时间
        if (StringUtils.isNotEmpty(s)) {
            postList = appHomePostInfoDao.getListByUser(Arrays.asList(s.split(",")), BATCH_SIZE, breakpointDate);
            LOGGER.info("{}: TESTING - only get post list of user {}, post list size {}", LOG_PREFIX, s, postList.size());
        }

        if (CollectionUtils.isNotEmpty(postList)) {
            Map<String, List<UserFollowPostInfo>> groupList = postList.stream().collect(Collectors.groupingBy(a -> a.UID));

            Set<Map.Entry<String, List<UserFollowPostInfo>>> entries = groupList.entrySet();

            for (Map.Entry<String, List<UserFollowPostInfo>> entry : entries) {
                String cacheKey = String.format(UserRedisConfig.USER_LAST_POST_TIME, entry.getKey());
                Date maxTime = entry.getValue().stream().max(Comparator.comparing(o -> o.TIME)).get().TIME;
                UserPostUpdateModel model = new UserPostUpdateModel();
                model.RecordUpDateTime = maxTime;
                boolean setResult = userRedisDao.set(cacheKey,
                    JsonUtils.toJsonString(model),
                    30 * DateConstant.ONE_DAY);
                if (!setResult) {
                    LOGGER.error("{}, key:{}, set redis failed.", LOG_PREFIX, cacheKey);
                }
            }
            breakpointDate = postList.stream().max(Comparator.comparing(o -> o.TIME)).get().TIME;
        }

        boolean result = userRedisDao.set(UserRedisConfig.USER_POST_LAST_UPDATE_TIME_IJOB_BREAKPOINT,
            DateUtil.dateToStr(breakpointDate, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT), 90 * DateConstant.ONE_DAY);
        LOGGER.info("{}: 存入缓存结果: {}", LOG_PREFIX, result);
    }
}
