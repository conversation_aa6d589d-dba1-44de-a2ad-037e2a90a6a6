package ttfund.web.communityservice.timedtask.jijinbarJobs.circle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.hold.UserHoldStatisticsData;
import ttfund.web.communityservice.bean.jijinBar.data.CircleUserCustomTag;
import ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelation;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserBindInfo;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.PassportUserBindInfoDao;
import ttfund.web.communityservice.dao.mongo.UserHoldStatisticsDataDao;
import ttfund.web.communityservice.dao.mongo.UserSwitchDao;
import ttfund.web.communityservice.dao.msyql.CircleUserRelationDao;
import ttfund.web.communityservice.enums.UserSwitchEnum;
import ttfund.web.communityservice.service.AccountOtherApiServiceImpl;
import ttfund.web.communityservice.service.entity.AccountIndexPercentage;
import ttfund.web.communityservice.service.entity.BaseAccountApiResponse;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 圈子用户定制化标签job
 */
@Slf4j
@JobHandler("CircleUserCustomTagJob")
@Component
public class CircleUserCustomTagJob extends IJobHandler {

    @Autowired
    private CircleUserRelationDao circleUserRelationDao;

    @Autowired
    private PassportUserBindInfoDao passportUserBindInfoDao;

    @Autowired
    private AccountOtherApiServiceImpl accountOtherApiService;

    @Autowired
    private UserHoldStatisticsDataDao userHoldStatisticsDataDao;

    @Autowired
    private UserSwitchDao userSwitchDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            String circleIds = null;
            Integer batchReadCount = null;
            Double indexPercentageLimit = null;
            Integer batchUserCount = null;
            Boolean dealMongodb = null;
            Boolean dealRedis = null;
            Long expire = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                circleIds = jsonObject.getString("circleIds");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                indexPercentageLimit = jsonObject.getDouble("indexPercentageLimit");
                batchUserCount = jsonObject.getInteger("batchUserCount");
                dealMongodb = jsonObject.getBoolean("dealMongodb");
                dealRedis = jsonObject.getBoolean("dealRedis");
                expire = jsonObject.getLong("expire");
            }

            if (circleIds == null) {
                circleIds = "240012";
            }
            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (indexPercentageLimit == null) {
                indexPercentageLimit = 0.5;
            }
            if (batchUserCount == null) {
                batchUserCount = 400;
            }
            if (dealMongodb == null) {
                dealMongodb = true;
            }
            if (dealRedis == null) {
                dealRedis = true;
            }
            if (expire == null) {
                expire = 30 * 24 * 3600L;
            }

            log.info("0.打印参数。circleIds：{}，batchReadCount：{}，indexPercentageLimit：{}，batchUserCount：{}，dealMongodb：{}，dealRedis：{}，expire：{}",
                circleIds,
                batchReadCount,
                indexPercentageLimit,
                batchUserCount,
                dealMongodb,
                dealRedis,
                expire
            );

            if (dealMongodb) {
                dealMongodb(CommonUtils.toList(circleIds, ","), batchReadCount, batchUserCount);
            }
            if (dealRedis) {
                dealRedis(CommonUtils.toList(circleIds, ","), batchReadCount, indexPercentageLimit, expire);
            }

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void dealMongodb(List<String> circleIds, int batchReadCount, int batchUserCount) {

        if (CollectionUtils.isEmpty(circleIds)) {
            return;
        }

        Date now = new Date();

        int i = 0;
        for (String circleId : circleIds) {
            i++;

            int round = 0;
            String uid = null;
            boolean run = true;
            while (run) {
                round++;

                List<CircleUserRelation> list = circleUserRelationDao.getListIncrementally(circleId, uid, batchReadCount);

                log.info("dealMongodb-1.读取用户-第{}/{}个圈子-{}-第{}轮。用户id：{}，数量：{}，数据：{}",
                    i,
                    circleIds.size(),
                    circleId,
                    round,
                    uid,
                    CollectionUtils.isEmpty(list) ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
                );

                if (CollectionUtils.isEmpty(list) || list.size() < batchReadCount) {
                    run = false;
                }

                if (!CollectionUtils.isEmpty(list)) {
                    uid = list.get(list.size() - 1).getUid();
                }

                Map<String, Double> dataMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(list)) {
                    Map<String, String> bindMap = new HashMap<>();

                    List<String> uidList = list.stream().map(a -> a.getUid()).collect(Collectors.toList());
                    List<List<String>> batchList = CommonUtils.toSmallList2(uidList, batchUserCount);
                    for (List<String> batch : batchList) {
                        List<PassportUserBindInfo> tempList = passportUserBindInfoDao.getByUids(batch);
                        if (!CollectionUtils.isEmpty(tempList)) {
                            tempList.forEach(a -> bindMap.put(a.CUSTOMERNO, a.UID));
                        }

                        List<String> customerNos = tempList.stream().map(a -> a.CUSTOMERNO).collect(Collectors.toList());
                        BaseAccountApiResponse<List<AccountIndexPercentage>> response = accountOtherApiService.getHoldIndexPctBatch(customerNos);
                        if (response != null && !CollectionUtils.isEmpty(response.getData())) {
                            response.getData().forEach(a -> dataMap.put(bindMap.get(a.getCustomerNo()), a.getPercentage()));
                        }
                    }
                }

                log.info("dealMongodb-2.读取指数持仓占比-第{}/{}个圈子-{}-第{}轮。数量：{}，数据：{}",
                    i,
                    circleIds.size(),
                    circleId,
                    round,
                    CollectionUtils.isEmpty(dataMap) ? 0 : dataMap.size(),
                    CollectionUtils.isEmpty(dataMap) ? null : JSON.toJSONStringWithDateFormat(dataMap.entrySet().iterator().next(), DateUtil.datePattern)
                );

                List<Map<String, Object>> mapList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(dataMap)) {
                    Map<String, Object> map = null;
                    for (Map.Entry<String, Double> entry : dataMap.entrySet()) {
                        map = new HashMap<>();
                        map.put("_id", entry.getKey());
                        map.put("uid", entry.getKey());
                        map.put("indexPercentage", entry.getValue());
                        map.put("state", 1);
                        map.put("createTime", new Date());
                        map.put("updateTime", new Date());

                        mapList.add(map);
                    }

                    List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 50);
                    for (List<Map<String, Object>> batch : batchList) {
                        userHoldStatisticsDataDao.upsertManyBySetWithSetOnInsertFields(batch, Arrays.asList("createTime"), "_id");
                    }
                }

                log.info("dealMongodb-3.写库-第{}/{}个圈子-{}-第{}轮。数量：{}，数据：{}",
                    i,
                    circleIds.size(),
                    circleId,
                    round,
                    CollectionUtils.isEmpty(mapList) ? 0 : mapList.size(),
                    CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONStringWithDateFormat(mapList.get(0), DateUtil.datePattern)
                );

            }
        }

        userHoldStatisticsDataDao.deleteLogically(now);
        log.info("dealMongodb-4.逻辑删除。时间：{}", DateUtil.dateToStr(now));

    }

    private void dealRedis(List<String> circleIds, int batchReadCount, Double indexPercentageLimit, Long expire) {

        int round = 0;
        String uid = null;
        boolean run = true;
        while (run) {
            round++;

            List<UserHoldStatisticsData> list = userHoldStatisticsDataDao.getList(UserHoldStatisticsData.class, null, batchReadCount, uid);

            log.info("dealRedis-1.读取用户-第{}轮。用户id：{}，数量：{}，数据：{}",
                round,
                uid,
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

            if (CollectionUtils.isEmpty(list) || list.size() < batchReadCount) {
                run = false;
            }

            if (!CollectionUtils.isEmpty(list)) {
                uid = list.get(list.size() - 1).getUid();
            }

            if (!CollectionUtils.isEmpty(list)) {

                for (UserHoldStatisticsData a : list) {

                    String key = String.format(BarRedisKey.CIRCLE_USER_CUSTOM_TAG, a.getUid());
                    Map<String, String> oldHashCache = app.barredis.hgetAll(key);
                    Map<String, String> newHashCache = new HashMap<>();

                    Document document = userSwitchDao.getOneByKeyIsValue("_id", String.format("%s_%s", a.getUid(), UserSwitchEnum.SHOW_CIRCLE_CUSTOM_TAG.getValue()), null, Document.class);
                    newHashCache.put(String.format("userSwitch_%s", UserSwitchEnum.SHOW_CIRCLE_CUSTOM_TAG.getValue()), document == null ? "" : String.valueOf(document.getInteger("value")));

                    for (String circleId : circleIds) {
                        List<CircleUserCustomTag> newCacheList = new ArrayList<>();
                        if (!CollectionUtils.isEmpty(oldHashCache) && StringUtils.hasLength(oldHashCache.get(circleId))) {
                            List<CircleUserCustomTag> tempList = JSON.parseArray(oldHashCache.get(circleId), CircleUserCustomTag.class);
                            if (!CollectionUtils.isEmpty(tempList)) {
                                newCacheList.addAll(tempList.stream().filter(o -> !Objects.equals(o.getType(), 1)).collect(Collectors.toList()));
                            }
                        }

                        if (Objects.equals(a.getState(), 1) && a.getIndexPercentage() != null && a.getIndexPercentage() >= indexPercentageLimit) {
                            CircleUserCustomTag newTag = new CircleUserCustomTag();
                            newTag.setType(1);
                            newTag.setValue(truncateToDecimalPlaces(a.getIndexPercentage(), 1));
                            newCacheList.add(newTag);
                        }

                        newHashCache.put("customTag_" + circleId, JSON.toJSONString(newCacheList));
                    }

                    app.barredis.hmset(key, newHashCache);
                    app.barredis.expire(key, expire);
                }

            }

            log.info("dealRedis-2.写缓存-第{}轮。数量：{}，数据：{}",
                round,
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );
        }

    }


    public static String truncateToDecimalPlaces(Double value, int decimalPlaces) {
        if (value == null) {
            return "";
        }
        double multiplier = Math.pow(10, decimalPlaces);
        double d = Math.floor(value * multiplier) / multiplier;
        return String.format("%." + decimalPlaces + "f", d);
    }


}
