package ttfund.web.communityservice.timedtask.jijinbarJobs.apphome;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.JsonUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.RecommendHotUserModel;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.UserFollowPostInfo;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.constant.DateConstant;
import ttfund.web.communityservice.dao.mongo.AppHomePostInfoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.List;
import java.util.stream.Collectors;

/**
 * app首页人气用户-帖子列表
 */
@JobHandler("RecommendHotUserPostCacheJob")
@Component
public class RecommendHotUserPostCacheJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecommendHotUserPostCacheJob.class);

    private static final long EXPIRE_TIME = 10 * DateConstant.ONE_WEEK;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private AppHomePostInfoDao appHomePostInfoDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            logicDeal();
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
        }
        return ReturnT.SUCCESS;
    }

    private void logicDeal() {
        List<RecommendHotUserModel> userList = null;
        //从redis获取人气用户列表
        String value = userRedisDao.get(UserRedisConfig.APP_HOME_HOT_USER);
        if (StringUtils.isNotEmpty(value)) {
            userList = JsonUtils.toList(value, RecommendHotUserModel.class);
        }

        LOGGER.info("RecommendHotUserPostCacheJob[app首页人气用户帖子列表]:人气用户列表为{}", userList);
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }

        //根据人气用户列表获取帖子信息
        List<String> uidList = userList.stream().map(a -> a.PassportID).collect(Collectors.toList());
        //获取人气用户的近3天发帖的前200条数据
        List<UserFollowPostInfo> postList = appHomePostInfoDao.getListByUser(uidList, 200, DateUtil.calendarDateByDays(-3));
        if (CollectionUtils.isEmpty(postList)) {
            postList = appHomePostInfoDao.getListByUser(uidList, 200, null);
        }

        if (CollectionUtils.isEmpty(postList)) {
            LOGGER.error("RecommendHotUserPostCacheJob[app首页人气用户帖子列表]:人气用户发帖列表为空");
            return;
        }

        boolean result = userRedisDao.set(UserRedisConfig.APP_HOME_RECOMMEND_HOT_USER_POST_LIST, JsonUtils.toJsonString(postList), EXPIRE_TIME);
        LOGGER.info("RecommendHotUserPostCacheJob[app首页人气用户帖子列表]: 缓存结果:{}", result);
    }

}
