package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.ModelPortfolioManagerFansModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.ModelPortfolioManagerFansRankDao;
import ttfund.web.communityservice.dao.msyql.ModelPortfolioDao;
import ttfund.web.communityservice.dao.msyql.UserRelationDao;
import ttfund.web.communityservice.utils.CommonUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 模拟组合管理人关注榜job
 * 备注：需求 #596771 【组合3.9】模拟组合重构
 */
@JobHandler("ModelPortfolioManagerFansRankJob")
@Component
public class ModelPortfolioManagerFansRankJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(ModelPortfolioManagerFansRankJob.class);

    @Autowired
    private ModelPortfolioDao modelPortfolioDao;

    @Autowired
    private UserRelationDao userRelationDao;

    @Autowired
    private ModelPortfolioManagerFansRankDao modelPortfolioManagerFansRankDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            Integer batchReadCount = null;
            Boolean isWriteMongo = null;
            Boolean isWriteRedis = null;
            Integer writeMongoSize = null;
            Integer writeRedisSize = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                batchReadCount = jsonObject.getInteger("batchReadCount");
                isWriteMongo = jsonObject.getBoolean("isWriteMongo");
                isWriteRedis = jsonObject.getBoolean("isWriteRedis");
                writeMongoSize = jsonObject.getInteger("writeMongoSize");
                writeRedisSize = jsonObject.getInteger("writeRedisSize");
            }

            if (batchReadCount == null) {
                batchReadCount = 20000;
            }
            if (isWriteMongo == null) {
                isWriteMongo = true;
            }
            if (isWriteRedis == null) {
                isWriteRedis = true;
            }
            if (writeMongoSize == null) {
                writeMongoSize = 200;
            }
            if (writeRedisSize == null) {
                writeRedisSize = 200;
            }

            deal(batchReadCount, isWriteMongo, isWriteRedis, writeMongoSize, writeRedisSize);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount, boolean isWriteMongo, boolean isWriteRedis, int writeMongoSize, int writeRedisSize) throws Exception {
        if (isWriteMongo) {
            writeToMongo(batchReadCount, writeMongoSize);
        }
        if (isWriteRedis) {
            writeToRedis(writeRedisSize);
        }
    }

    /**
     * 计算排行榜写mongo
     */
    private void writeToMongo(int batchReadCount, int writeMongoSize) throws Exception {

        Date now = new Date();

        PriorityQueue<ModelPortfolioManagerFansModel> priorityQueue = new PriorityQueue<>(
                writeMongoSize,
                ((o1, o2) -> o2.getFansCount().compareTo(o1.getFansCount()))
        );

        ModelPortfolioManagerFansModel model = null;
        String uid = null;
        int totalCount = 0;
        int round = 0;
        while (true) {
            round++;

            List<String> managerUids = modelPortfolioDao.getManagerUids(uid, batchReadCount);

            logger.info("1.writeToMongo-读取模拟组合管理人-第{}轮。uid：{}，数量:{}，头部id列表：{}",
                    round,
                    uid,
                    managerUids == null ? 0 : managerUids.size(),
                    managerUids == null ? null : managerUids.stream().limit(20).collect(Collectors.toList()));


            if (!CollectionUtils.isEmpty(managerUids)) {

                totalCount += managerUids.size();

                uid = managerUids.get(managerUids.size() - 1);

                Map<String, Integer> fansCountMap = userRelationDao.getUserFansCountByUserIds(managerUids);
                if (fansCountMap == null) {
                    fansCountMap = new HashMap<>();
                }

                logger.info("2.writeToMongo-读取管理人关注人数-第{}轮。uid：{}，数量:{}，头部id列表：{}",
                        round,
                        uid,
                        fansCountMap == null ? 0 : fansCountMap.size(),
                        fansCountMap == null ? null : JSON.toJSONString(fansCountMap.entrySet().stream().limit(20).collect(Collectors.toList())));

                Integer fansCount = null;
                for (String a : managerUids) {
                    //过滤粉丝数=0
                    fansCount = fansCountMap.get(a);
                    if (fansCount == null || fansCount <= 0) {
                        continue;
                    }

                    model = new ModelPortfolioManagerFansModel();
                    model.set_id(a);
                    model.setFansCount(fansCount);
                    model.setCreateTime(new Date());
                    model.setUpdateTime(new Date());

                    priorityQueue.offer(model);
                }
            }

            if (managerUids == null || managerUids.size() < batchReadCount) {
                break;
            }
        }

        logger.info("3.writeToMongo-生成榜单。榜单头部列表:{}，总用户数：{}",
                priorityQueue == null ? null : JSON.toJSONString(priorityQueue.stream().limit(10).collect(Collectors.toList())),
                totalCount);

        List<ModelPortfolioManagerFansModel> modelList = new ArrayList<>();
        Iterator<ModelPortfolioManagerFansModel> iterator = priorityQueue.iterator();
        while (iterator.hasNext()) {
            model = iterator.next();
            modelList.add(model);
        }

        if (!CollectionUtils.isEmpty(modelList)) {

            List<Map<String, Object>> mapList = new ArrayList<>(modelList.size());
            for (ModelPortfolioManagerFansModel item : modelList) {
                mapList.add(CommonUtils.beanToMap(item));
            }

            List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 200);
            for (List<Map<String, Object>> batch : batchList) {
                modelPortfolioManagerFansRankDao.upsertMany(batch);
            }

            logger.info("4.writeToMongo-帮忙写库。数量:{}，头部列表：{}",
                    mapList == null ? 0 : mapList.size(),
                    mapList == null ? null : JSON.toJSONString(mapList.stream().limit(20).collect(Collectors.toList())));

            modelPortfolioManagerFansRankDao.deleteByUpdateTime(now);

            logger.info("5.writeToMongo-删除库中历史数据。");
        }

    }

    /**
     * 排行榜写redis
     */
    private void writeToRedis(int writeRedisSize) throws Exception {

        List<ModelPortfolioManagerFansModel> list = modelPortfolioManagerFansRankDao.getList(writeRedisSize);

        logger.info("1.writeToRedis-读取数据。数量:{}，头部列表：{}",
                list == null ? 0 : list.size(),
                list == null ? null : JSON.toJSONString(list.stream().limit(20).collect(Collectors.toList())));

        if (!CollectionUtils.isEmpty(list)) {

            list.sort(((o1, o2) -> {
                int i = o2.getFansCount().compareTo(o1.getFansCount());
                if (i == 0) {
                    i = o1.get_id().compareTo(o2.get_id());
                }
                return i;
            }));

            List<Map<String, Object>> mapList = new ArrayList<>(list.size());
            Map<String, Object> map = null;
            for (ModelPortfolioManagerFansModel a : list) {
                map = new HashMap<>();
                map.put("_id", a.get_id());
                map.put("passportId", a.get_id());
                map.put("fansCount", a.getFansCount());
                map.put("updateTime", a.getUpdateTime());
                mapList.add(map);
            }
            app.barredis.set(BarRedisKey.MODEL_PORTFOLIO_MANAGER_FANS_RANK,
                    JSON.toJSONStringWithDateFormat(mapList, "yyyy-MM-dd HH:mm:ss.SSS"),
                    30 * 24 * 3600L);

            logger.info("1.writeToRedis-数据写redis。数量:{}，头部列表：{}",
                    mapList == null ? 0 : mapList.size(),
                    mapList == null ? null : JSON.toJSONString(mapList.stream().limit(20).collect(Collectors.toList())));
        }

        app.barredis.expire(BarRedisKey.MODEL_PORTFOLIO_MANAGER_FANS_RANK, 30 * 24 * 3600L);

    }

}
