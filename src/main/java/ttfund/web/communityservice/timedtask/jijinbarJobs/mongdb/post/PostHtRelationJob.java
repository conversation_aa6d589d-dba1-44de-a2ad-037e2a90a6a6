package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostHtRelationItem1;
import ttfund.web.communityservice.bean.jijinBar.post.PostTopicModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostTopicModelWithPost;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.mongo.PostHtRelationMongoDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.msyql.PostTopicRetDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 帖子话题关系job
 */
@Slf4j
@JobHandler("PostHtRelationJob")
@Component
public class PostHtRelationJob extends IJobHandler {

    private static final String SELECT_FIELDS = "ID,TIMEPOINT,DEL,TTJJDEL,UPDATETIME";
    private static final List<String> SET_ON_INSERT_FIELDS = Arrays.asList("createTime");

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private PostTopicRetDao postTopicRetDao;

    @Autowired
    private PostHtRelationMongoDao postHtRelationMongoDao;

    @Autowired
    private PostDao postDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            Integer backTime = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                backTime = jsonObject.getInteger("backTime");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (backTime == null) {
                backTime = -30;
            }

            log.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，backTime：{}",
                initBreakpoint,
                batchReadCount,
                backTime
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.POSTHTRELATIONJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            deal(batchReadCount, backTime);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void deal(int batchReadCount, Integer backTime) throws Exception {


        String breakpointName = UserRedisConfig.POSTHTRELATIONJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            log.error("0.读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("1.读取断点。断点:{}", breakpoint);

        Date end = DateUtil.calendarDateBySecond(backTime);
        List<Map<String, Object>> dataList = postInfoNewDao.getListByUpdateTimeInterval(SELECT_FIELDS, breakpointDate, end, batchReadCount);

        log.info("2.读取数据。start：{}，end：{}，数量:{}，头部列表：{}",
            DateUtil.dateToStr(breakpointDate),
            DateUtil.dateToStr(end),
            dataList == null ? 0 : dataList.size(),
            CollectionUtils.isEmpty(dataList) ? null : JSON.toJSONStringWithDateFormat(dataList.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(dataList)) {

            breakpointDate = (Date)dataList.get(dataList.size() - 1).get("UPDATETIME");
        }

        //dealMongoPostInfo(dataList);
        dealMongoPostHtRelation(dataList);

        log.info("3.处理。数量:{}，头部列表：{}",
            dataList == null ? 0 : dataList.size(),
            CollectionUtils.isEmpty(dataList) ? null : JSON.toJSONStringWithDateFormat(dataList.get(0), DateUtil.datePattern)
        );

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        log.info("4.更新断点。断点：{}", breakpoint);

    }


    private void dealMongoPostInfo(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        LinkedMultiValueMap<String, PostTopicModel> multiValueMap = new LinkedMultiValueMap<>();
        List<PostTopicModelWithPost> relationList = new ArrayList<>();
        List<String> postIds = dataList.stream().map(a -> a.get("ID").toString()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(postIds)) {
            List<List<String>> batchList = CommonUtils.toSmallList2(postIds, 50);
            for (List<String> batch : batchList) {
                List<PostTopicModelWithPost> tempList = postTopicRetDao.getRelationTopicByPostIds(batch);
                if (!CollectionUtils.isEmpty(tempList)) {
                    relationList.addAll(tempList);
                }
            }

            if (!CollectionUtils.isEmpty(relationList)) {

                String _id = null;
                Set<String> distinctSet = new HashSet<>();
                for (PostTopicModelWithPost o : relationList) {
                    _id = o.PostId + "_" + o.HtId;
                    if (!distinctSet.contains(_id)) {
                        distinctSet.add(_id);
                        multiValueMap.add(o.PostId, PostTopicModelWithPost.generate(o));
                    }
                }
            }
        }

        log.info("dealMongoPostInfo-1.读取数据。数量:{}，头部列表：{}",
            relationList == null ? 0 : relationList.size(),
            CollectionUtils.isEmpty(relationList) ? null : JSON.toJSONStringWithDateFormat(relationList.get(0), DateUtil.datePattern)
        );


        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = null;
        for (Map.Entry<String, List<PostTopicModel>> entry : multiValueMap.entrySet()) {
            map = new HashMap<>();
            map.put("_id", entry.getKey());
            map.put("HTLIST", entry.getValue());
            map.put("UPDATETIME", new Date());
            mapList.add(map);
        }

        List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 50);
        for (List<Map<String, Object>> batch : batchList) {
            postDao.upsertManyBySetWithSetOnInsertFields(batch, null, "_id");
        }

        log.info("dealMongoPostInfo-2.写库。数量:{}，头部列表：{}",
            mapList == null ? 0 : mapList.size(),
            CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONStringWithDateFormat(mapList.get(0), DateUtil.datePattern)
        );

    }


    private void dealMongoPostHtRelation(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        List<PostHtRelationItem1> relationList = new ArrayList<>();
        List<String> postIds = dataList.stream().map(a -> a.get("ID").toString()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(postIds)) {

            List<List<String>> batchList = CommonUtils.toSmallList2(postIds, 50);
            for (List<String> batch : batchList) {
                List<PostHtRelationItem1> tempList = postTopicRetDao.getListByPostIds(batch);
                if (!CollectionUtils.isEmpty(tempList)) {
                    relationList.addAll(tempList);
                }
            }

        }

        log.info("dealMongoPostHtRelation-1.读取数据。数量:{}，头部列表：{}",
            relationList == null ? 0 : relationList.size(),
            CollectionUtils.isEmpty(relationList) ? null : JSON.toJSONStringWithDateFormat(relationList.get(0), DateUtil.datePattern)
        );

        List<PostHtRelationItem1> filterList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(relationList)) {
            LinkedMultiValueMap<String, PostHtRelationItem1> map = new LinkedMultiValueMap<>();
            relationList.forEach(a -> map.add(String.format("%s_%s", a.postid, a.htid), a));

            for (Map.Entry<String, List<PostHtRelationItem1>> entry : map.entrySet()) {
                List<PostHtRelationItem1> tempList = entry.getValue().stream().sorted((o1, o2) -> {
                    Integer voteid1 = StringUtils.hasLength(o1.voteid) ? Integer.parseInt(o1.voteid) : 100;
                    Integer voteid2 = StringUtils.hasLength(o2.voteid) ? Integer.parseInt(o2.voteid) : 100;
                    int compare = Integer.compare(voteid1, voteid2);
                    if (compare == 0) {
                        return o2.UpdateTime.compareTo(o1.UpdateTime);
                    }
                    return compare;
                }).collect(Collectors.toList());

                filterList.add(tempList.get(0));
            }
        }

        log.info("dealMongoPostHtRelation-2.分类。数量:{}，头部列表：{}",
            filterList == null ? 0 : filterList.size(),
            CollectionUtils.isEmpty(filterList) ? null : JSON.toJSONStringWithDateFormat(filterList.get(0), DateUtil.datePattern)
        );

        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = null;
        if (!CollectionUtils.isEmpty(filterList)) {

            Map<String, Map<String, Object>> upsertPostMap = new HashMap<>();
            Set<String> upsertPostIds = filterList.stream().map(a -> a.postid).collect(Collectors.toSet());
            dataList.forEach(a -> {
                if (upsertPostIds.contains(a.get("ID").toString())) {
                    upsertPostMap.put(a.get("ID").toString(), a);
                }
            });

            for (PostHtRelationItem1 o : filterList) {
                map = new HashMap<>();
                map.put("_id", String.format("%s_%s", o.postid, o.htid));
                map.put("htId", String.valueOf(o.htid));
                map.put("postId", String.valueOf(o.postid));
                map.put("createTime", new Date());
                map.put("updateTime", new Date());
                map.put("postTimepoint", (Long)upsertPostMap.get(o.postid).get("TIMEPOINT"));
                map.put("del", o.Del);
                map.put("postTtjjdel", (Integer)upsertPostMap.get(o.postid).get("TTJJDEL"));
                map.put("postDel", (Integer)upsertPostMap.get(o.postid).get("DEL"));
                mapList.add(map);
            }

            List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 50);
            for (List<Map<String, Object>> batch : batchList) {
                postHtRelationMongoDao.upsertManyBySetWithSetOnInsertFields(batch, SET_ON_INSERT_FIELDS, "_id");
            }
        }

        log.info("dealMongoPostHtRelation-3.upsert写库。数量:{}，头部列表：{}",
            mapList == null ? 0 : mapList.size(),
            CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONStringWithDateFormat(mapList.get(0), DateUtil.datePattern)
        );


    }

}

