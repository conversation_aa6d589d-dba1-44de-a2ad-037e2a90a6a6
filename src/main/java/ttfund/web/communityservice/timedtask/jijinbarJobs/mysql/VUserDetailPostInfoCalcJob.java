package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;


import com.mysql.cj.util.StringUtils;
import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.BigVDataUserModel;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.VUserDayPostDetailInfosModel;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.VUserSummaryPostDetailInfosModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.dao.mongo.PassportFundMrgDao;
import ttfund.web.communityservice.dao.mongo.VUserPostInfoDao;
import ttfund.web.communityservice.dao.msyql.JjbconfigDao;
import ttfund.web.communityservice.dao.msyql.VUserPostDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 大V每日发帖统计数据Mongodb
 *
 * <AUTHOR>
 * @date 2023/3/9
 */
@JobHandler(value = "VUserDetailPostInfoCalcJob")
@Component
public class VUserDetailPostInfoCalcJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(VUserDetailPostInfoCalcJob.class);

    @Autowired
    App app;

    @Autowired
    JjbconfigDao jjbconfigDao;

    @Autowired
    PassportFundMrgDao passportFundMrgDao;

    //操作mongo
    @Autowired
    VUserPostInfoDao vUserPostInfoDao;

    //操作mysql
    @Autowired
    VUserPostDao vUserPostDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            Date today;
            if(!StringUtils.isNullOrEmpty(s)) {
                today = DateUtil.strToDate(s, DateHelper.FORMAT_YYYY_MM_DD);
            }else {
                today = DateUtil.getNowDate(DateHelper.FORMAT_YYYY_MM_DD);
            }

            //获取大V用户信息
            List<BigVDataUserModel> userList = vUserPostInfoDao.getCalBigVDataUser();
            List<String> bigVUids = userList.stream().map(BigVDataUserModel::getUID).distinct().collect(Collectors.toList());
            logger.info("合并去重后的uidList size：{}", bigVUids.size());
            if (!bigVUids.isEmpty()) {
                //每页的数量
                int pageSize = 20;
                //总页数据
                int totalPage = (bigVUids.size() + pageSize - 1) / pageSize;
                for (int index = 0; index < totalPage; index++) {
                    int beginPage = index * pageSize;
                    int endPage = (index + 1) * pageSize;
                    endPage = Math.min(bigVUids.size(), endPage);
                    List<String> vUids = new LinkedList<>(bigVUids.subList(beginPage, endPage));
                    if (!CollectionUtils.isEmpty(vUids)) {
                        //获取所有mongodb中的累计数据和每天的数据
                        List<VUserSummaryPostDetailInfosModel> vUserSummaryPostDetailInfos = vUserPostInfoDao.getVUserSummary();
                        //logger.info("vUserSummaryPostDetailInfos size:{}",vUserSummaryPostDetailInfos.size());

                        //每天大V用户帖子详情 已经插入过数据的
                        List<String> hasInsertedVUserIds = vUserPostInfoDao.hasInsertVUserDayPostDetailIds(DateUtil.getNowDate());

                        //已经更新过帖子累计详情的UserId集合
                        List<String> hasUpdatedVUserSummaryPostDetailIds = vUserSummaryPostDetailInfos.stream()
                                .filter(x -> x.UpdateTime.getTime() == DateUtil.getNowDate().getTime())
                                .map(x -> x.UserId)
                                .collect(Collectors.toList());

                        //找到两个集合中都存在的UserId 即为已经执行完毕的UserId
                        List<String> hasHandledVUserIds = hasInsertedVUserIds.stream()
                                .filter(x -> new HashSet<>(hasUpdatedVUserSummaryPostDetailIds).contains(x))
                                .collect(Collectors.toList());

                        //去除vUids中已经执行完毕的，并对余下的进行记录
                        vUids.removeIf(hasHandledVUserIds::contains);
                        //logger.info("vUid size:{}", vUids.size());

                        if (vUids.size() > 0) {
  							logger.info("第{}批需要记录的大V数量为{}，分别为：{}", index, vUids.size(), vUids);
                            List<String> codeList = jjbconfigDao.getCodeTypes();
                            //根据VUserIds获取用户的文章量、阅读量、点赞量和评论量
                            Map<String, Long> userPostNumsInfos = vUserPostDao.getPostNumsFromDb(codeList, vUids, today);
                            //logger.info("userPostNumsInfos:{}",userPostNumsInfos);
                            //根据VUserIds获取用户某天的帖子数量
                            Map<String, Long> userPostNumsInfosDay = vUserPostDao.getPostNumsForDay(codeList, vUids, DateUtil.calendarDateByDays(today,-1));

                            Map<String, Long> userClickNumsInfos = vUserPostDao.getPostClickNumsFromDb(codeList, vUids, today);

                            Map<String, Long> userLikeNumsInfos = vUserPostDao.getPostLikeNumsFromDb(codeList, vUids, today);

                            Map<String, Long> userCommentNumsInfos = vUserPostDao.getPostCommentNumsFromDb(codeList, vUids, today);

                            if (userPostNumsInfos != null && userPostNumsInfos.size() > 0 && userClickNumsInfos != null && userClickNumsInfos.size() > 0 &&
                                    userLikeNumsInfos != null && userLikeNumsInfos.size() > 0 && userCommentNumsInfos != null && userCommentNumsInfos.size() > 0) {
                                //统计
                                for (String vUid : vUids) {
                                    List<String> result = handleOneUserPostInfo(vUid, vUserSummaryPostDetailInfos, userPostNumsInfos, userPostNumsInfosDay, userClickNumsInfos,
                                            userLikeNumsInfos, userCommentNumsInfos, hasInsertedVUserIds);
                                    logger.info("用户Id：" + vUid + " 每日插入表情况：" + result.get(1) + " 历史更新表情况：" + result.get(2) + " 其它情况：" + result.get(3) + "");
                                }
                            } else {
                                logger.error("获取大V用户帖子信息异常---db异常");
                            }
                        }
                    } else {
                        logger.error("获取大V用户异常");
                    }
                }

            } else {
                logger.error("获取大V用户为空或异常");
            }

        } catch (Exception e) {
            logger.error("大方法报错：{},{}", e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 处理单个用户的帖子详情
     */
    private List<String> handleOneUserPostInfo(String vUid, List<VUserSummaryPostDetailInfosModel> vUserSummaryPostDetailInfos,
                                               Map<String, Long> userPostNumsInfos, Map<String, Long> userPostNumsInfosDay,
                                               Map<String, Long> userClickNumsInfos, Map<String, Long> userLikeNumsInfos,
                                               Map<String, Long> userCommentNumsInfos, List<String> hasInsertVUserDayPostDetailIds) {
        String msgInsertDayInfo = null;
        String msgUpdateSummaryInfo = null;
        String otherMsg = null;
        try {
            List<VUserSummaryPostDetailInfosModel> collect = vUserSummaryPostDetailInfos.stream().filter(e -> e.UserId.equals(vUid)).limit(1).collect(Collectors.toList());
            VUserSummaryPostDetailInfosModel vUserSummaryPostDetailOneUser = null;
            long clickNumsDay = userClickNumsInfos.get(vUid);
            long likeNumsDay = userLikeNumsInfos.get(vUid);
            long commentNumsDay = userCommentNumsInfos.get(vUid);
            long postNumsDay = userPostNumsInfos.get(vUid);    //帖子总数
            long postNumsForDay = userPostNumsInfosDay.get(vUid);  //每天帖子数
            boolean addOrUpdate = true;            //true insert - false update
            boolean isUpdateSummaryInfo = true;

            Date yesterday = DateUtil.calendarDateByDays(-1);
            VUserDayPostDetailInfosModel vUserDayPostDetailInfosInsert = new VUserDayPostDetailInfosModel();
            vUserDayPostDetailInfosInsert.setUpdateTime(DateUtil.getNowDate());
            vUserDayPostDetailInfosInsert.setCreateTime(DateUtil.getNowDate());
            vUserDayPostDetailInfosInsert.setDateMonth(new SimpleDateFormat("yyyy-MM").format(yesterday));
            vUserDayPostDetailInfosInsert.setDayTime(yesterday);
            vUserDayPostDetailInfosInsert.setUserId(vUid);
            vUserDayPostDetailInfosInsert.setId(DateUtil.dateToStr(DateUtil.getNowDate(), DateHelper.FORMAT_YYYYMMDD) + vUid);
            if (!CollectionUtils.isEmpty(collect)) {
                vUserSummaryPostDetailOneUser = collect.get(0);
                addOrUpdate = false;
                clickNumsDay = clickNumsDay - vUserSummaryPostDetailOneUser.ClickNums;
                likeNumsDay = likeNumsDay - vUserSummaryPostDetailOneUser.LikeNums;
                commentNumsDay = commentNumsDay - vUserSummaryPostDetailOneUser.CommentNums;
            } else { // vUserSummaryPostDetailOneUser = null;
                if (vUserSummaryPostDetailInfos.size() == 0) {
                    clickNumsDay = 0;
                    likeNumsDay = 0;
                    commentNumsDay = 0;
                    postNumsDay = 0;
                }
            }

            vUserDayPostDetailInfosInsert.ClickNums = clickNumsDay;
            vUserDayPostDetailInfosInsert.LikeNums = likeNumsDay;
            vUserDayPostDetailInfosInsert.CommentNums = commentNumsDay;
            vUserDayPostDetailInfosInsert.PostNums = postNumsForDay;
            if (!hasInsertVUserDayPostDetailIds.contains(vUid)) {
                boolean insertResult = vUserPostInfoDao.upsertVUserPostDay(vUserDayPostDetailInfosInsert);
                if (!insertResult) {
                    msgInsertDayInfo = "每天帖子详情信息插入mongodb异常";
                    isUpdateSummaryInfo = false;
                } else {
                    msgInsertDayInfo = "OK";
                }
            } else {
                msgInsertDayInfo = "OK";
            }
            //都要执行累计数据的插入或者更新
            if (isUpdateSummaryInfo) {
                vUserSummaryPostDetailOneUser = new VUserSummaryPostDetailInfosModel();
                vUserSummaryPostDetailOneUser.setUpdateTime(DateUtil.getNowDate());
                vUserSummaryPostDetailOneUser.setCreateTime(DateUtil.getNowDate());
                vUserSummaryPostDetailOneUser.setClickNums(userClickNumsInfos.get(vUid));
                vUserSummaryPostDetailOneUser.setCommentNums(userCommentNumsInfos.get(vUid));
                vUserSummaryPostDetailOneUser.setUserId(vUid);
                vUserSummaryPostDetailOneUser.setId(vUid);
                vUserSummaryPostDetailOneUser.setLikeNums(userLikeNumsInfos.get(vUid));
                vUserSummaryPostDetailOneUser.setPostNums(userPostNumsInfos.get(vUid));

                if (addOrUpdate) {
                    // insert
                    boolean insertResult = vUserPostInfoDao.insertVUserPostSummary(vUserSummaryPostDetailOneUser);
                    msgUpdateSummaryInfo = insertResult ? "OK" : "历史帖子详情信息插入MongoDb异常";
                } else {
                    // update
                    boolean updateResult = vUserPostInfoDao.upsertVUserSummary(vUserSummaryPostDetailOneUser);
                    msgUpdateSummaryInfo = updateResult ? "OK" : "历史帖子详情信息更新MongoDb异常";
                }
            }
        } catch (Exception e) {
            //记录日志
            otherMsg = e.getMessage() + "\r\n" + e.getStackTrace();
            logger.error("历史帖子详情信息插入MongoDb异常，error:{}",e.getMessage(),e);
        }
        List<String> result = new ArrayList<>();
        result.add(0, vUid);
        result.add(1, msgInsertDayInfo);
        result.add(2, msgUpdateSummaryInfo);
        result.add(3, otherMsg);
        return result;
    }

}
