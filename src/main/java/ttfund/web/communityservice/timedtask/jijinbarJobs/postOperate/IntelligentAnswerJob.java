package ttfund.web.communityservice.timedtask.jijinbarJobs.postOperate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.HttpHelper;
import com.ttfund.web.core.utils.MD5Utils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.guba.ReqeustCodeDealRes;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.dao.vertica.QuespostAnswerBasicDrctAllDao;
import ttfund.web.communityservice.service.common.CommonDataCache;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能回答job
 */
@JobHandler("IntelligentAnswerJob")
@Component
public class IntelligentAnswerJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(IntelligentAnswerJob.class);

    private static String CLAIM = "<p>%s</p><p>本内容由小助理生成，点击头像查看更多精彩内容。</p><blockquote><p>免责声明：本内容由AI生成。 针对AI生成的内容，不代表天天基金的立场、态度或观点，也不能作为专业性建议或意见，仅供参考。您须自行对其中包含的数字、时间以及各类事实性描述等内容进行核实。</p></blockquote>";

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private QuespostAnswerBasicDrctAllDao quespostAnswerBasicDrctAllDao;

    @Autowired
    private CommonDataCache commonDataCache;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            String ip = null;
            String claim = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                ip = jsonObject.getString("ip");
                claim = jsonObject.getString("claim");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (ip == null) {
                ip = "************";
            }
            if (claim == null) {
                claim = CLAIM;
            }

            logger.info("0.打印参数。initBreakpoint：{}，batchReadCount：{}，ip：{}，claim：{}",
                    initBreakpoint,
                    batchReadCount,
                    ip,
                    claim
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.INTELLIGENTANSWERJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("0.初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            deal(batchReadCount, ip, claim);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount, String ip, String claim) {
        String breakpointName = UserRedisConfig.INTELLIGENTANSWERJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("0.读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("1.读取断点。断点:{}", breakpoint);


        List<Map<String, Object>> dataList = quespostAnswerBasicDrctAllDao.getListByUpdatetime(breakpointDate, batchReadCount);

        logger.info("2.读取数据。数量:{}，头部id列表：{}",
                dataList == null ? 0 : dataList.size(),
                dataList == null ? null : dataList.stream().map(a -> a.get("QUESTIONID")).limit(20).collect(Collectors.toList()));

        if (!CollectionUtils.isEmpty(dataList)) {

            breakpointDate = (Date) dataList.stream().max((Comparator.comparing(o -> (Date) o.get("EUTIME")))).get().get("EUTIME");

            //取帖子信息
            List<Long> postIds = dataList.stream().map(a -> Long.parseLong(a.get("QUESTIONID").toString())).collect(Collectors.toList());
            Map<String, Map<String, Object>> postInfoMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(postIds)) {
                List<List<Long>> batchList = CommonUtils.toSmallList2(postIds, 100);
                for (List<Long> batch : batchList) {
                    List<Map<String, Object>> tempList = postInfoNewDao.getPostWithQuestionInfo(batch);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        tempList.forEach(a -> postInfoMap.put(a.get("ID").toString(), a));
                    }
                }
            }


            Map<String, Object> postInfo = null;
            ReqeustCodeDealRes barInfo = null;
            int i = 0;
            for (Map<String, Object> a : dataList) {
                i++;

                a.put("ip", ip);
                a.put("claim", claim);

                barInfo = null;
                postInfo = postInfoMap.get(a.get("QUESTIONID").toString());
                if (postInfo != null) {

                    a.put("UID", postInfo.get("UID"));
                    a.put("QID", postInfo.get("QID"));

                    String code = (String) postInfo.get("CODE");
                    barInfo = commonDataCache.reqeustCodeDeal(code);
                }

                addAnswer(a, barInfo);

                quespostAnswerBasicDrctAllDao.updateOne(a);

                logger.info("3.发回答详情-第{}/{}个。帖子id：{}，生成回答id：{}，错误信息：{}",
                        i,
                        dataList.size(),
                        a.get("QUESTIONID"),
                        a.get("ANSWERID"),
                        a.get("MESSAGE")
                );
            }

            logger.info("3.发回答完成。数量:{}，头部id列表：{}",
                    dataList == null ? 0 : dataList.size(),
                    dataList == null ? null : dataList.stream().map(a -> a.get("QUESTIONID")).limit(20).collect(Collectors.toList()));

        }


        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        logger.info("4.更新断点。断点：{}", breakpoint);

    }

    private void addAnswer(Map<String, Object> data, ReqeustCodeDealRes barInfo) {
        //1：待发送
        //2：发送成功
        //3：发送失败
        int status = 3;
        Long answerid = null;
        String message = null;

        String url = appConstant.config_gubahostnew + "/qaplatopt/api/answer/addIntelligentAnswer";
        //分类调用股吧接口
        if (barInfo == null) {
            message = "吧信息为空";
        } else {
            //添加回答
            Map<String, String> paramMap = new HashMap<>();

            paramMap.put("deviceid", MD5Utils.stringToMD5(String.valueOf(data.get("ip"))));
            paramMap.put("version", "200");
            paramMap.put("product", "Fund");
            paramMap.put("plat", "Web");

            paramMap.put("CreatorID", appConstant.intelligentAnswerUid);
            paramMap.put("code", barInfo.getGuba_code());
            paramMap.put("qid", data.get("QID").toString());
            paramMap.put("content", String.format(data.get("claim").toString(), data.get("AIANSWER").toString()));
            paramMap.put("ip", data.get("ip") == null ? "" : data.get("ip").toString());

            //区分策略吧、实盘吧，吴宇晨那会把code转化为基金吧需要的吧code
            if (barInfo.getGuba_type() == 3) {
                paramMap.put("code", "jjspzh");
                paramMap.put("ext", barInfo.getGuba_code());

            } else if (barInfo.getGuba_type() == 4) {
                paramMap.put("code", "jjcl");
                paramMap.put("ext", barInfo.getGuba_code());
            } else if (barInfo.getGuba_type() == 5) {
                paramMap.put("code", "tgcp");
                paramMap.put("ext", barInfo.getGuba_code());
            } else if (barInfo.getGuba_type() == 6) {
                paramMap.put("code", "jjmnzh");
                paramMap.put("ext", barInfo.getGuba_code());
            }

            if (StringUtils.hasLength(paramMap.get("ext"))) {
                paramMap.put("specificbar", paramMap.get("code") + paramMap.get("ext"));
            } else {
                paramMap.put("specificbar", paramMap.get("code"));
            }

            String html = HttpHelper.requestPostJson(url, JSON.toJSONString(paramMap), true);

            if (StringUtils.hasLength(html)) {
                JSONObject jsonObject = JSON.parseObject(html);
                if (jsonObject != null && jsonObject.getJSONObject("re") != null
                        && StringUtils.hasLength(jsonObject.getJSONObject("re").getString("article_id"))) {
                    answerid = Long.parseLong(jsonObject.getJSONObject("re").getString("article_id"));
                    status = 2;
                } else if (jsonObject != null) {
                    message = jsonObject.getString("me") == null ? null : jsonObject.getString("me");
                }
            } else {
                message = "股吧接口返回为空";
            }

        }

        data.put("STATUS", status);
        data.put("ANSWERID", answerid);
        data.put("MESSAGE", (message != null && message.length() > 12) ? message.substring(0, 12) : message);
        data.put("UPDATETIME", new Date());
    }

}
