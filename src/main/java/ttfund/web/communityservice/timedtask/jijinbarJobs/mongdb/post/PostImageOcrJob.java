package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.eastmoney.particle.common.concurrent.ThreadFactoryImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.common.Term;
import com.ttfund.web.base.helper.DateHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.combine.CombineResponse;
import ttfund.web.communityservice.bean.combine.OpenIcStrategyResponse;
import ttfund.web.communityservice.bean.infer.OcrItem;
import ttfund.web.communityservice.bean.infer.Point;
import ttfund.web.communityservice.bean.infer.ProductIndex;
import ttfund.web.communityservice.bean.jijinBar.mongo.FundJBXXModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostImageFund;
import ttfund.web.communityservice.bean.jijinBar.post.PostImageFundRelation;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.FundJbxxDao;
import ttfund.web.communityservice.dao.mongo.HighFinanceDao;
import ttfund.web.communityservice.dao.mongo.PostImageFundDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.service.CacheService;
import ttfund.web.communityservice.service.infer.InferService;
import ttfund.web.communityservice.utils.CoordinateUtil;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.net.URL;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.imageio.ImageIO;

/**
 * 帖子图片中的基金识别
 */
@JobHandler("PostImageOcrJob")
@Component
public class PostImageOcrJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(PostImageOcrJob.class);

    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(2, 2, 5L, TimeUnit.MINUTES,
        new LinkedBlockingQueue<>(20000), new ThreadFactoryImpl("PostImageOcrThreadPool"));

    public static final Pattern CODE_PATTERN = Pattern.compile("[^0-9]*([0-9]{6})[^0-9]*");

    public static final String BREAK_TIME_KEY = "PostImageOcrBreakTime";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private PostImageFundDao postImageFundDao;

    @Autowired
    private InferService inferService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private FundJbxxDao fundJbxxDao;

    @Autowired
    private HighFinanceDao highFinanceDao;

    @Autowired
    private App app;

    @Value("${api.investAdvise.all.url}")
    private String investAdviseAllUrl;

    // 基金同义词
    private static final Map<String, String> fundSynonymMap = new HashMap<>();

    static {
        fundSynonymMap.put("定期开放", "定开");
        fundSynonymMap.put("（", "");
        fundSynonymMap.put("）", "");
        fundSynonymMap.put("(", "");
        fundSynonymMap.put(")", "");
        fundSynonymMap.put("证券投资基金", "");
        fundSynonymMap.put("基金", "");
    }


    // 高端理财同义词
    private static final Map<String, String> highFinanceSynonymMap = new HashMap<>();

    static {
        highFinanceSynonymMap.put("证券", "");
    }

    // 非前缀匹配基金
    private static final Set<String> noPrefixSet = new HashSet<>();

    static {
        noPrefixSet.add("沪深300ETF永赢");
        noPrefixSet.add("中证500ETF");
    }

    // 停用词
    private static final Set<String> stopWords = new LinkedHashSet<>();

    static {
        stopWords.add("人民币");
        stopWords.add("美元汇");
        stopWords.add("美元现汇");
        stopWords.add("美元");
        stopWords.add("QDII-LOF");
        stopWords.add("QDII");
        stopWords.add("ETF发起式联接");
        stopWords.add("ETF联接");
        stopWords.add("ETF");
        stopWords.add("LOF");
        stopWords.add("指数");
    }

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        LOGGER.info("帖子图片OCR开始");

        ReturnT<String> result = ReturnT.SUCCESS;

        try {

            int batchSize = 200;

            if (StringUtils.hasText(s)) {
                batchSize = Integer.parseInt(s);
            }

            // 初始化倒排索引-公募
            ProductIndex fundIndex = cacheService.get(this::fundReverseIndex, "FundReverseIndex", 24 * 60 * 60L);
            // 初始化倒排索引-投顾
            ProductIndex investAdviseIndex = cacheService.get(this::investAdviseReverseIndex, "InvestAdviseReverseIndex", 24 * 60 * 60L);
            // 初始化倒排索引-高端
            ProductIndex highFinanceIndex = cacheService.get(this::highFinanceReverseIndex, "HighFinanceReverseIndex", 24 * 60 * 60L);

            if (fundIndex == null && investAdviseIndex == null && highFinanceIndex == null) {
                LOGGER.error("获取倒排索引失败");
                return ReturnT.FAIL;
            }

            // 获取断点
            Date lastUpdatetime = userRedisDao.getBreakTime(BREAK_TIME_KEY, DateUtil.calendarDateByDays(-1));
            LOGGER.info("获取断点：" + DateUtil.dateToStr(lastUpdatetime));

            // 增量获取帖子
            List<PostInfoNewModel> list = postInfoNewDao.getPostList(lastUpdatetime, batchSize, "ID", "ALLPIC", "DEL", "TTJJDEL", "UPDATETIME");

            if (CollectionUtils.isEmpty(list)) {
                LOGGER.info("没有新增数据，任务结束");
                return result;
            } else {
                LOGGER.info("获取到的增量帖子数：" + list.size());
            }

            // 获取已识别帖子
            Set<String> existedSet = getExsitedOcrPost(list);

            LOGGER.info("已识别帖子的帖子数：" + existedSet.size());

            // 识别解析图片
            List<PostImageFund> resultList = ocrAndMatchImages(list, existedSet, fundIndex, investAdviseIndex, highFinanceIndex);

            LOGGER.info("识别解析出的内容：" + JacksonUtil.obj2String(resultList));

            if (!CollectionUtils.isEmpty(resultList)) {

                // 保存至MongoDB（只存有图片的）
                List<PostImageFund> toSaveList = resultList.stream()
                    .filter(w -> !CollectionUtils.isEmpty(w.getImages()))
                    .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(toSaveList)) {
                    boolean flag = postImageFundDao.upsertBulk(toSaveList);
                    if (!flag) {
                        LOGGER.error("帖子图片基金保存失败，数据：" + JacksonUtil.obj2String(resultList));
                    } else {
                        LOGGER.info("帖子图片基金保存成功，数量：{}", resultList.size());
                    }
                }

                // 保存至Redis
                for (PostImageFund item : resultList) {
                    app.barredis.set(
                        String.format(BarRedisKey.POST_IMAGE_FUNDS, item.getPostId()),
                        JacksonUtil.obj2String(item),
                        7 * 24 * 60 * 60L
                    );
                }
            }

            // 设置时间断点
            Date updatetime = list.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;

            if (list.size() == batchSize && lastUpdatetime.equals(updatetime)) {
                LOGGER.error("相同时间点帖子数超过阈值：{}", DateUtil.dateToStr(updatetime));
                updatetime = DateUtil.calendarDateBySecond(updatetime, 1);
            }

            userRedisDao.setBreakTime(BREAK_TIME_KEY, updatetime);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }

        LOGGER.info("帖子图片OCR结束");

        return result;
    }

    /**
     * 识别解析图片
     *
     * @param list
     * @param existedSet
     * @param fundIndex
     * @param investAdviseIndex
     * @param highFinanceIndex
     * @return
     */
    private List<PostImageFund> ocrAndMatchImages(List<PostInfoNewModel> list, Set<String> existedSet,
        ProductIndex fundIndex, ProductIndex investAdviseIndex, ProductIndex highFinanceIndex) {

        List<PostImageFund> resultList = new ArrayList<>();
        Map<String, Future<List<List<OcrItem>>>> futureMap = new HashMap<>();

        // 异步识别
        for (PostInfoNewModel item : list) {

            try {
                if (existedSet.contains(String.valueOf(item.ID))) {
                    LOGGER.info("已存在的帖子：" + item.ID);
                    continue;
                }

                PostImageFund postImageFund = new PostImageFund();
                postImageFund.set_id(String.valueOf(item.ID));
                postImageFund.setPostId(String.valueOf(item.ID));
                postImageFund.setImages(new ArrayList<>());
                postImageFund.setUpdateTime(new Date());

                resultList.add(postImageFund);

                // 没有图片的直接不需识别
                if (!StringUtils.hasText(item.ALLPIC)) {
                    LOGGER.info("无图的帖子：" + item.ID);
                    continue;
                }

                // 异步调用推理接口
                String[] images = item.ALLPIC.split(",");

                String traceId = MDC.get(CoreConstant.logtraceid);

                for (String image : images) {

                    PostImageFundRelation postImageFundRelation = new PostImageFundRelation();

//                    // 判断是否有缓存
//                    String content = app.barredis.get("OCR_MATCH_RESULT_ALL_" + image);
//                    if (StringUtils.hasLength(content)) {
//                        postImageFundRelation = JacksonUtil.string2Obj(content, PostImageFundRelation.class);
//                    } else {
                        Future<List<List<OcrItem>>> future = EXECUTOR.submit(() -> inferImage(image, traceId));
                        futureMap.put(item.ID + "_" + image, future);
                        postImageFundRelation.setImage(image);
//                    }
                    postImageFund.getImages().add(postImageFundRelation);
                }
            } catch (Exception e) {
                LOGGER.error("{}，帖子：{}", e.getMessage(), item.ID, e);
            }
        }

        // 获取识别结果
        for (PostImageFund postImageFund : resultList) {

            for (PostImageFundRelation image : postImageFund.getImages()) {

                try {

                    // 有缓存的跳过
                    if (image.getFunds() != null) {
                        LOGGER.info("图片：【{}】命中缓存", image.getImage());
                        continue;
                    }

                    // 获取任务返回值
                    String key = postImageFund.getPostId() + "_" + image.getImage();

                    if (!futureMap.containsKey(key)) {
                        continue;
                    }

                    List<List<OcrItem>> lists = futureMap.get(key).get(5, TimeUnit.MINUTES);

                    // 匹配基金
                    if (fundIndex != null) {
                        Set<String> set = matchImage(fundIndex, lists, fundSynonymMap);
                        image.setFunds(set.stream().map(fundIndex::getByCode).collect(Collectors.toList()));
                    }
                    // 匹配投顾
                    if (investAdviseIndex != null) {
                        Set<String> set = matchImage(investAdviseIndex, lists, new HashMap<>());
                        image.setInvestmentAdvisers(set.stream().map(investAdviseIndex::getByCode).collect(Collectors.toList()));
                    }
                    // 匹配高端理财
                    if (highFinanceIndex != null) {
                        Set<String> set = matchImage(highFinanceIndex, lists, highFinanceSynonymMap);
                        image.setHighFinances(set.stream().map(highFinanceIndex::getByCode).collect(Collectors.toList()));
                    }

                    // 识别结果缓存
                    app.barredis.set(
                        "OCR_MATCH_RESULT_ALL_" + image.getImage(),
                        JacksonUtil.obj2String(image),
                        7 * 24 * 60 * 60L
                    );

                } catch (Exception e) {
                    LOGGER.error("{}，图片地址：{}", e.getMessage(), image, e);
                }
            }
        }

        return resultList;
    }

    /**
     * 匹配图片
     *
     * @param productIndex
     * @param lists
     * @param synonymMap 同义词
     * @return
     */
    private Set<String> matchImage(ProductIndex productIndex, List<List<OcrItem>> lists, Map<String, String> synonymMap) {

        Set<String> set = new LinkedHashSet<>();

        try {
            for (List<OcrItem> ocrItems : lists) {

                if (CollectionUtils.isEmpty(ocrItems)) {
                    continue;
                }

                List<OcrItem> items = JacksonUtil.string2Obj(JacksonUtil.obj2String(ocrItems), List.class, OcrItem.class);
                set.addAll(matchSubImage(productIndex, items, synonymMap));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return set;
    }

    /**
     * 匹配图片
     *
     * @param productIndex
     * @param ocrItems
     * @param synonymMap 同义词
     * @return
     */
    private Set<String> matchSubImage(ProductIndex productIndex, List<OcrItem> ocrItems, Map<String, String> synonymMap) {

        Set<String> fundSet = new LinkedHashSet<>();

        try {
            // 识别出的文字可能不是按顺序的，需要排序一下
            sortOcrResult(ocrItems);

            // 同义词替换
            ocrItems.forEach(w -> {
                for (String key : synonymMap.keySet()) {
                    if (w.getText().contains(key)) {
                        w.setText(w.getText().replace(key, synonymMap.get(key)));
                    }
                }
            });

            // 分割文本
            splitText(ocrItems, productIndex);

            OcrItem preItem = null;
            List<OcrItem> preItems = new ArrayList<>();
            List<Set<String>> codeList = new ArrayList<>();
            Set<String> codeSet = new HashSet<>();

            // 匹配代码
            for (OcrItem ocrItem : ocrItems) {

                if (preItem == null || CoordinateUtil.isChangeRow(ocrItem.getCoordinate(), preItem.getCoordinate())) {
                    codeSet = new HashSet<>();
                    codeList.add(codeSet);
                }

                matchFundCode(productIndex, ocrItem.getText(), codeSet);
                preItem = ocrItem;
            }
            codeList.add(codeSet);

            preItem = null;
            int i = 0;

            for (OcrItem ocrItem : ocrItems) {

                if (preItem != null && CoordinateUtil.isChangeRow(ocrItem.getCoordinate(), preItem.getCoordinate())) {
                    i++;
                }

                preItem = ocrItem;

                // 单行匹配
                matchSingleRow(productIndex, ocrItem, codeList.get(i));

                // 多行匹配
                matchMultiRow(productIndex, ocrItem, preItems);
                preItems.add(ocrItem);
            }

            for (OcrItem ocrItem : ocrItems) {
                fundSet.addAll(ocrItem.getExtractList());
                fundSet.addAll(ocrItem.getFuzzyList());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }

        return fundSet;
    }

    /**
     * 多行匹配
     *
     * @param productIndex
     * @param ocrItem
     * @param preItems
     */
    private void matchMultiRow(ProductIndex productIndex, OcrItem ocrItem, List<OcrItem> preItems) {

        boolean hasMatched = false;

        for (int i = preItems.size() - 1; i >= 0; i--) {
            OcrItem preRowItem = preItems.get(i);

            // 处于同一列，并且当前行不存在基金/以非基金文字开始
            boolean flag = !CoordinateUtil.onLeft(ocrItem.getCoordinate(), preRowItem.getCoordinate())
                && !CoordinateUtil.onRight(ocrItem.getCoordinate(), preRowItem.getCoordinate())
                && ocrItem.getTextList().size() != 1;

            if (!flag) {
                if (hasMatched) {
                    break;
                } else {
                    continue;
                }
            }

            hasMatched = true;

            if (ocrItem.getCoordinate().getLeftTop().getY() - preRowItem.getCoordinate().getLeftBottom().getY() > 20) {
                break;
            }

            // 多行文字拼接
            String text = multiRowJoint(ocrItem, preRowItem);

            if (text.length() < 4) {
                continue;
            }

            if (!productIndex.getLongPrefixSet().contains(text.substring(0, 4))
                && !productIndex.getShortPrefixSet().contains(text.substring(0, 2))) {
                continue;
            }

            // 精确匹配名称
            String name = matchFundNameExact(productIndex, text);
            if (name != null) {
                preRowItem.getFuzzyList().clear();
                preRowItem.getFuzzyList().add(productIndex.getByName(name).FCODE);
                preRowItem.setMinDistance(minDistance(name, text));
                continue;
            }

            // 模糊匹配名称
            matchFundNameFuzzy(productIndex, ocrItem, text, preRowItem, null);
        }
    }

    /**
     * 多行文字拼接
     *
     * @param ocrItem
     * @param preRowItem
     * @return
     */
    private static String multiRowJoint(OcrItem ocrItem, OcrItem preRowItem) {
        String preText = preRowItem.getText();

        if (!CollectionUtils.isEmpty(preRowItem.getTextList())) {
            preText = preRowItem.getTextList().get(preRowItem.getTextList().size() - 1);
        }

        String currentText = ocrItem.getText();

        if (!CollectionUtils.isEmpty(ocrItem.getTextList())) {
            currentText = ocrItem.getTextList().get(0);
        }

        return preText + currentText;
    }

    /**
     * 单行匹配
     *
     * @param productIndex
     * @param ocrItem
     * @param codeSet
     */
    private void matchSingleRow(ProductIndex productIndex, OcrItem ocrItem, Set<String> codeSet) {

        if (CollectionUtils.isEmpty(ocrItem.getTextList())) {
            return;
        }

        for (String subText : ocrItem.getTextList()) {

            // 精确匹配名称
            String name = matchFundNameExact(productIndex, subText);
            if (name != null) {
                ocrItem.getFuzzyList().add(productIndex.getByName(name).FCODE);
                ocrItem.setMinDistance(minDistance(name, subText));
                continue;
            }

            // 模糊匹配名称
            matchFundNameFuzzy(productIndex, ocrItem, subText, null, codeSet);
        }
    }

    /**
     * 匹配基金编码
     *
     * @param productIndex
     * @param text
     * @param fundSet
     * @return
     */
    private void matchFundCode(ProductIndex productIndex, String text, Set<String> fundSet) {

        Matcher matcher = CODE_PATTERN.matcher(text);
        int matcher_start = 0;

        while (matcher.find(matcher_start)) {
            String fundCode = matcher.group(1);
            if (productIndex.containsCode(fundCode)) {
                FundJBXXModel fund = productIndex.getByCode(fundCode);
                fundSet.add(fund.FCODE);
            }
            matcher_start = matcher.end();
        }
    }

    /**
     * 识别出的文字排序
     *
     * @param ocrItems
     */
    private static void sortOcrResult(List<OcrItem> ocrItems) {
        try {
            ocrItems.sort((o1, o2) -> {
                Point p1 = o1.getCoordinate().getLeftTop();
                Point p2 = o2.getCoordinate().getLeftTop();
                if (Math.abs(p1.getY() - p2.getY()) > 10) {
                    return Double.compare(p1.getY(), p2.getY());
                }
                return Double.compare(p1.getX(), p2.getX());
            });
        } catch (Exception e) {
            LOGGER.warn("排序传递性不满足，降级");
        }

        ocrItems.sort((o1, o2) -> {
            Point p1 = o1.getCoordinate().getLeftTop();
            Point p2 = o2.getCoordinate().getLeftTop();
            if (Double.compare(p1.getY(), p2.getY()) != 0) {
                return Double.compare(p1.getY(), p2.getY());
            }
            return Double.compare(p1.getX(), p2.getX());
        });
    }

    /**
     * 图片处理、识别
     *
     * @param image
     * @param traceId
     */
    private List<List<OcrItem>> inferImage(String image, String traceId) {

        MDC.put(CoreConstant.logtraceid, traceId);
        List<List<OcrItem>> list = new ArrayList<>();

        LOGGER.info("开始识别图片：" + image);

        try {
            BufferedImage bufferedImage = ImageIO.read(new URL(image));

            // 动态 Shape 会导致 FastDeploy 内存泄露，所以固定大小为 960 * 960
            bufferedImage = scaleImageByWidth(bufferedImage, 960);

            int height = bufferedImage.getHeight();

            List<BufferedImage> imageList = new ArrayList<>();

            if (height > 960) {
                // 长图识别率较低，需要切割成多张图
                int stepHeight = 800;
                for (int i = 0; stepHeight * i < height; i++) {
                    int y = stepHeight * i;
                    if (y + 960 > height) {
                        y = height - 960;
                    }
                    BufferedImage subimage = bufferedImage.getSubimage(0, y, bufferedImage.getWidth(), 960);
                    imageList.add(subimage);
                }
            } else {
                if (height < 960) {
                    bufferedImage = expandImageHeight(bufferedImage);
                }
                imageList.add(bufferedImage);
            }

            for (BufferedImage subImage : imageList) {
                List<OcrItem> subList = inferService.infer(subImage);
                list.add(subList);
            }
        } catch (Exception e) {
            LOGGER.error("{}，图片地址：{}", e.getMessage(), image, e);
        }

        MDC.remove(CoreConstant.logtraceid);

        return list;
    }

    /**
     * 扩充高度
     */
    public static BufferedImage expandImageHeight(BufferedImage originalImage) {

        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();
        int newHeight = 960;

        // 创建新图像（保持原宽度，高度960，类型不变）
        BufferedImage expandedImage = new BufferedImage(
            originalWidth,
            newHeight,
            originalImage.getType()
        );

        // 获取绘图对象
        Graphics2D g2d = expandedImage.createGraphics();

        // 绘制原图像（从左上角(0,0)开始）
        g2d.drawImage(originalImage, 0, 0, null);

        // 填充新增的底部像素（默认透明/黑色）
        if (originalImage.getTransparency() == Transparency.OPAQUE) {
            g2d.setColor(Color.BLACK); // 不透明图像用黑色填充
        } else {
            g2d.setColor(new Color(0, 0, 0, 0)); // 透明图像用透明填充
        }
        g2d.fillRect(0, originalHeight, originalWidth, newHeight - originalHeight);

        g2d.dispose();
        return expandedImage;
    }

    /**
     * 根据宽度缩放图像
     *
     * @param source
     * @param targetWidth
     * @return
     */
    private static BufferedImage scaleImageByWidth(BufferedImage source, int targetWidth) {

        // 计算等比例高度
        int originalWidth = source.getWidth();
        int originalHeight = source.getHeight();
        int targetHeight = (int) Math.round((double) originalHeight * targetWidth / originalWidth);

        // 创建目标图像（保留透明度）
        BufferedImage scaledImage = new BufferedImage(
            targetWidth,
            targetHeight,
            source.getTransparency() == BufferedImage.OPAQUE
                ? BufferedImage.TYPE_INT_RGB
                : BufferedImage.TYPE_INT_ARGB
        );

        // 高质量绘制配置
        Graphics2D g2d = scaledImage.createGraphics();
        g2d.setRenderingHint(
            RenderingHints.KEY_INTERPOLATION,
            RenderingHints.VALUE_INTERPOLATION_BILINEAR
        );
        g2d.setRenderingHint(
            RenderingHints.KEY_RENDERING,
            RenderingHints.VALUE_RENDER_QUALITY
        );
        g2d.setRenderingHint(
            RenderingHints.KEY_ANTIALIASING,
            RenderingHints.VALUE_ANTIALIAS_ON
        );

        // 执行缩放绘制
        g2d.drawImage(source, 0, 0, targetWidth, targetHeight, null);
        g2d.dispose();

        return scaledImage;
    }

    /**
     * 获取已识别帖子
     *
     * @param list
     * @return
     */
    private Set<String> getExsitedOcrPost(List<PostInfoNewModel> list) {

        List<String> ids = list.stream()
            .map(w -> String.valueOf(w.ID))
            .collect(Collectors.toList());

        return postImageFundDao.getListByPostIds(ids)
            .stream()
            .map(PostImageFund::get_id)
            .collect(Collectors.toSet());
    }

    /**
     * 分割文本
     *
     * @param ocrItems
     * @param productIndex
     */
    private void splitText(List<OcrItem> ocrItems, ProductIndex productIndex) {

        for (OcrItem ocrItem : ocrItems) {

            List<String> result = splitText(ocrItem.getText(), 4, productIndex.getLongPrefixSet(), productIndex.getNameIndex());

            if (CollectionUtils.isEmpty(result)) {
                result = splitText(ocrItem.getText(), 2, productIndex.getShortPrefixSet(), productIndex.getNameIndex());
            }

            ocrItem.setTextList(result);
        }
    }

    /**
     * 分割文本
     *
     * @param text
     * @param length
     * @param prefixSet
     * @param nameIndex
     * @return
     */
    private static List<String> splitText(String text, int length, Set<String> prefixSet, Map<String, FundJBXXModel> nameIndex) {

        List<String> result = new ArrayList<>();

        String[] arr = text.split("[`~!@#$%^&*+=|{}':;',\\[\\].<>?~！@#￥%……&*+|{}【】‘；：”“’。， 、？]");

        for (String subText : arr) {

            if (nameIndex.containsKey(subText)) {
                result.add(subText);
                continue;
            }

            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < subText.length();) {

                // 检查当前位置是否匹配子字符串
                if (i + length <= subText.length()
                    && prefixSet.contains(subText.substring(i, i + length))) {
                    // 如果匹配，将前一部分和子字符串添加到结果中
                    if (sb.length() > 0) {
                        result.add(sb.toString());
                        sb.setLength(0);
                    }
                    sb.append(subText.substring(i, i + length));
                    i += length;
                } else {
                    // 如果没有找到匹配的子字符串，将当前字符添加到缓冲区
                    if (sb.length() > 0) {
                        sb.append(subText.charAt(i));
                    }
                    i++;
                }
            }

            // 如果缓冲区中还有剩余字符，添加到最后
            if (sb.length() > 0) {
                result.add(sb.toString());
            }
        }
        return result;
    }

    /**
     * 模糊匹配基金名称
     *
     * @param productIndex
     * @param ocrItem
     * @param text
     * @param preRowItem
     * @param codeSet
     * @return
     */
    private void matchFundNameFuzzy(ProductIndex productIndex, OcrItem ocrItem, String text, OcrItem preRowItem,
        Set<String> codeSet) {

        // 分词
        List<String> segments = segment(text);

        if (CollectionUtils.isEmpty(segments)) {
            return;
        }

        // 计算基金匹配次数
        Map<String, Integer> countMap = new HashMap<>();
        calMatchCount(productIndex, segments, countMap);

        if (countMap.isEmpty()) {
            return;
        }

        // 获取匹配超过5次&&包含基金名称前缀的基金
        List<Map.Entry<String, Integer>> list = countMap.entrySet()
            .stream()
            .filter(e -> e.getValue() >= 3
                && text.contains(productIndex.getNameByCode(e.getKey()).substring(0, 2)))
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 如果与匹配的基金代码符合，直接返回
        if (!CollectionUtils.isEmpty(codeSet)) {

            List<String> matchList = list.stream()
                .map(Map.Entry::getKey)
                .filter(codeSet::contains)
                .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(matchList)) {
                ocrItem.getExtractList().addAll(matchList);
                return;
            }
        }

        // 获取TF-IDF得分最高的若干基金
        List<String> topTfIdfList = getTopTfIdfList(productIndex, list, text);

        // 计算编辑距离
        int minDistance = topTfIdfList.stream()
            .map(w -> minDistance(productIndex.getNameByCode(w), text))
            .min(Integer::compare).get();

        if (text.length() < minDistance + 3) {
            return;
        }

        // 获取编辑距离最小并且基金名称匹配字符大于阈值
        List<String> minDistanceList = topTfIdfList.stream()
            .filter(w -> Objects.equals(minDistance(productIndex.getNameByCode(w), text), minDistance))
            .filter( w -> {
                String name = removeStopWords(productIndex.getNameByCode(w), text);
                int count = 0;
                int minIndex = text.length();
                int maxIndex = 0;
                for (int i = 0; i < name.length(); i++) {
                    int index = text.indexOf(name.charAt(i));
                    if (index > -1) {
                        minIndex = Math.min(minIndex, index);
                        maxIndex = Math.max(maxIndex, index);
                        count++;
                    }
                }
                if (count <= 3) {
                    return false;
                }
                double ratio = (double) count / name.length();
                if (text.contains(name.substring(0, 4)) && count <= 6 && ratio < 0.8) {
                    return false;
                } else if (ratio <= 0.5) {
                    return false;
                } else if ((double) count / (maxIndex - minIndex + 1) <= 0.5) {
                    return false;
                } else if ((double) count / name.length() <= 0.5) {
                    return false;
                } else if (name.length() > 4 && text.length() > 4) {
                    if (minDistance(name.substring(4), text.substring(4)) > (name.length() - 4) / 2) {
                        return false;
                    }
                }
                return true;
            })
            .collect(Collectors.toList());

        if (preRowItem != null) {

            List<String> fundList = preRowItem.getFuzzyList();
            fundList.addAll(minDistanceList);

            // 获取匹配词数最多的基金
            List<String> topMatchList = getTopMatchCountList(fundList, countMap);

            preRowItem.setFuzzyList(topMatchList);

        } else {

            ocrItem.setMinDistance(minDistance);
            if (!CollectionUtils.isEmpty(ocrItem.getFuzzyList())) {
                ocrItem.getExtractList().addAll(ocrItem.getFuzzyList());
                ocrItem.getFuzzyList().clear();
            }
            ocrItem.getFuzzyList().addAll(minDistanceList);
        }
    }

    /**
     * 移除停用词
     *
     * @param text
     * @param targetText
     * @return
     */
    private String removeStopWords(String text, String targetText) {
        for (String stopWord : stopWords) {
            if (!targetText.contains(stopWord)) {
                text = text.replace(stopWord, "");
            }
        }
        return text;
    }

    /**
     * 获取匹配词数最多的基金
     *
     * @param fundList
     * @param countMap
     * @return
     */
    private static List<String> getTopMatchCountList(List<String> fundList, Map<String, Integer> countMap) {

        Integer maxCount = fundList.stream()
            .map(w -> countMap.getOrDefault(w, 0))
            .max(Integer::compare)
            .orElse(null);

        return fundList.stream()
            .filter(w -> Objects.equals(countMap.get(w), maxCount))
            .collect(Collectors.toList());
    }

    /**
     * 获取TF-IDF得分最高的若干基金
     *
     * @param productIndex
     * @param list
     * @param text
     * @return
     */
    private List<String> getTopTfIdfList(ProductIndex productIndex, List<Map.Entry<String, Integer>> list, String text) {

        Map<String, Double> tfIdfMap = new HashMap<>();
        double max = 0.0;

        // 获取最大的TF-IDF得分
        for (Map.Entry<String, Integer> entry : list) {
            List<String> words = segment(productIndex.getNameByCode(entry.getKey()), false);
            words = words.stream()
                .filter(w -> !stopWords.contains(w) || text.contains(w))
                .collect(Collectors.toList());
            int length = String.join("", words).length();
            double sum = 0.0;
            for (String word : words) {
                if (text.contains(word)) {
                    double tf = (double) word.length() / length;
                    double idf = productIndex.getIdfMap().get(word);
                    sum += tf * idf;
                }
            }
            tfIdfMap.put(entry.getKey(), sum);
            max = Math.max(max, sum);
        }

        // 筛选TF-IDF得分为最大值*0.7的基金
        double finalMax = max;
        return tfIdfMap.entrySet()
            .stream()
            .filter(w -> w.getValue() > finalMax * 0.7)
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
    }

    /**
     * 计算匹配次数
     * @param productIndex
     * @param segments
     * @param countMap
     * @return
     */
    private static Map<String, List<String>> calMatchCount(ProductIndex productIndex, List<String> segments, Map<String, Integer> countMap) {

        Map<String, List<String>> originalMap = new HashMap<>();

        for (String segment : segments) {
            if (productIndex.containsSegment(segment)) {
                Set<String> matchList = productIndex.getBySegment(segment);
                for (String matchFund : matchList) {
                    Integer count = countMap.getOrDefault(matchFund, 0);
                    countMap.put(matchFund, count + 1);
                    List<String> list = originalMap.getOrDefault(matchFund, new ArrayList<>());
                    list.add(segment);
                    originalMap.put(matchFund, list);
                }
            }
        }
        return originalMap;
    }

    /**
     * 计算编辑距离
     *
     * @param word1
     * @param word2
     * @return
     */
    public int minDistance(String word1, String word2) {
        word1 = removeStopWords(word1, word2);
        int length1 = word1.length();
        int length2 = word2.length();
        if (length1 * length2 == 0) {
            // 如果有一个为空，直接返回另一个的长度即可
            return length1 + length2;
        }
        int dp[][] = new int[length1 + 1][length2 + 1];
        for (int i = 0; i <= length1; i++) {
            // 边界条件，相当于word1的删除操作
            dp[i][0] = i;
        }
        for (int i = 0; i <= length2; i++) {
            // 边界条件，相当于word1的添加操作
            dp[0][i] = i;
        }
        for (int i = 1; i <= word1.length(); i++) {
            for (int j = 1; j <= length2; j++) {
                // 判断两个字符是否相等
                if (word1.charAt(i - 1) == word2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]) + 1;
                }
            }
        }
        return dp[length1][length2];
    }

    /**
     * 精确匹配基金名称
     *
     * @param productIndex
     * @param text
     * @return
     */
    private String matchFundNameExact(ProductIndex productIndex, String text) {

        List<String> fundList = new ArrayList<>();

        for (String name : productIndex.getNameIndex().keySet()) {
            if (text.contains(name)) {
                fundList.add(name);
            }
        }
        return fundList.stream().max(Comparator.comparingInt(String::length)).orElse(null);
    }

    /**
     * 获取公募码表
     */
    private ProductIndex fundReverseIndex() {

        LOGGER.info("公募基金码表获取");

        List<FundJBXXModel> fundList = fundJbxxDao.getFundJBXX();

        if (CollectionUtils.isEmpty(fundList)) {
            LOGGER.error("获取公募基金码表失败");
            return null;
        }

        LOGGER.info("公募基金倒排索引");

        return reverseIndex(fundList, fundSynonymMap);
    }

    /**
     * 获取投顾码表
     */
    private ProductIndex investAdviseReverseIndex() {

        LOGGER.info("投顾码表获取");

        String content = HttpHelper.requestGet(investAdviseAllUrl);

        if (!StringUtils.hasText(content)) {
            LOGGER.error("获取投顾码表失败");
            return null;
        }

        try {
            CombineResponse<List<OpenIcStrategyResponse>> response = JacksonUtil.string2Obj(content, new TypeReference<CombineResponse<List<OpenIcStrategyResponse>>>() {});

            if (response != null && !CollectionUtils.isEmpty(response.getData())) {
                List<FundJBXXModel> investAdviseList = response.getData()
                    .stream()
                    .map(w -> {
                        FundJBXXModel model = new FundJBXXModel();
                        model.FCODE = w.getStrategyId();
                        model.SHORTNAME = w.getStrategyName();
                        return model;
                    }).collect(Collectors.toList());

                return reverseIndex(investAdviseList, new HashMap<>());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }

        return null;
    }

    /**
     * 获取高端码表
     */
    private ProductIndex highFinanceReverseIndex() {

        LOGGER.info("高端理财码表获取");

        List<FundJBXXModel> highFinanceList = highFinanceDao.getHighFinanceAll();

        if (CollectionUtils.isEmpty(highFinanceList)) {
            LOGGER.error("获取高端理财码表失败");
            return null;
        }

        LOGGER.info("高端理财倒排索引");

        return reverseIndex(highFinanceList, highFinanceSynonymMap);
    }

    /**
     * 倒排索引
     *
     * @param infoList
     * @param synonymMap 同义词
     * @return
     */
    private ProductIndex reverseIndex(List<FundJBXXModel> infoList, Map<String, String> synonymMap) {

        Map<String, Set<String>> reverseIndex = new HashMap<>();
        Map<String, FundJBXXModel> codeIndx = new HashMap<>();
        Map<String, FundJBXXModel> nameIndex = new HashMap<>();
        Map<String, List<String>> segmentIndex = new HashMap<>();
        Map<String, String> nameMap = new HashMap<>();
        Map<String, Double> idfMap = new HashMap<>();
        Set<String> longPrefixSet = new HashSet<>();
        Set<String> shortPrefixSet = new HashSet<>();

        for (FundJBXXModel info : infoList) {

            // 代码
            codeIndx.put(info.FCODE, info);

            // 简称
            String name = info.SHORTNAME;
            for (String key : synonymMap.keySet()) {
                if (name.contains(key)) {
                    name = name.replace(key, synonymMap.get(key));
                }
            }

            name = name.replaceAll("[（）()\\s]", "");
            nameMap.put(info.FCODE, name);

            nameIndex.put(name, info);

            if (!noPrefixSet.contains(name)) {
                List<String> setments = reverseIndex(info, name, reverseIndex);
                segmentIndex.put(info.FCODE, setments);

                if (name.length() >= 4) {
                    longPrefixSet.add(name.substring(0, 4));
                }
                if (name.length() >= 2) {
                    shortPrefixSet.add(name.substring(0, 2));
                }
            }
        }

        for (String key : reverseIndex.keySet()) {
            double idf = Math.log((double) infoList.size() / (reverseIndex.get(key).size() + 1));
            idfMap.put(key, idf);
        }

        ProductIndex productIndex = new ProductIndex();
        productIndex.setCodeIndex(codeIndx);
        productIndex.setNameIndex(nameIndex);
        productIndex.setReverseIndex(reverseIndex);
        productIndex.setSegmentIndex(segmentIndex);
        productIndex.setIdfMap(idfMap);
        productIndex.setNameMap(nameMap);
        productIndex.setLongPrefixSet(longPrefixSet);
        productIndex.setShortPrefixSet(shortPrefixSet);
        return productIndex;
    }

    /**
     * 将基金名称倒排索引
     *
     * @param fund
     * @param fundName
     * @param reverseIndex
     * @return
     */
    private List<String> reverseIndex(FundJBXXModel fund, String fundName, Map<String, Set<String>> reverseIndex) {

        // 分词
        List<String> words = segment(fundName);
        if (CollectionUtils.isEmpty(words)) {
            return words;
        }

        for (String word : words) {
            if (!StringUtils.hasLength(word)) {
                continue;
            }
            Set<String> fundSet = reverseIndex.getOrDefault(word, new HashSet<>());
            fundSet.add(fund.FCODE);
            reverseIndex.put(word, fundSet);
        }

        return words;
    }

    /**
     * 分词
     *
     * @param text
     * @param link
     * @return
     */
    private List<String> segment(String text, boolean link) {
        String regEx = "[\n`~!@#$%^&*+=|{}':;',\\[\\].<>?~！@#￥%……&*+|{}【】‘；：”“’。， 、？]";
        text = text.trim().replaceAll(regEx, "");
        HanLP.newSegment().enableCustomDictionary(false);
        List<Term> obj = HanLP.segment(text);
        List<String> segments = obj.stream().map(m -> m.word).collect(Collectors.toList());
        List<String> result = new ArrayList<>();
        for (String segment : segments) {
            if (segment.startsWith("QDII") && segment.length() > 4) {
                result.add("QDII");
                result.add(segment.substring(4));
            } else if (segment.startsWith("ETF") && segment.length() > 3) {
                result.add("ETF");
                result.add(segment.substring(3));
            } else if (segment.startsWith("LOF") && segment.length() > 3) {
                result.add("LOF");
                result.add(segment.substring(3));
            } else {
                result.add(segment);
            }
        }
        if (link) {
            return linkSegments(result);
        }
        return result;
    }

    /**
     * 分词
     *
     * @param text
     * @return
     */
    private List<String> segment(String text) {
        return segment(text, true);
    }

    /**
     * 分词连接组合
     * [a,b,c] -> [a,b,c,ab,bc,abc]
     *
     * @param segments
     * @return
     */
    private List<String> linkSegments(List<String> segments) {
        List<String> result = new ArrayList<>(segments);
        for (int linkCount = 2; linkCount <= segments.size(); linkCount++) {
            for (int i = 0; i + linkCount <= segments.size(); i++) {
                StringBuilder sb = new StringBuilder();
                for (int j = i; j < segments.size() && j - i < linkCount; j++) {
                    sb.append(segments.get(j));
                }
                result.add(sb.toString());
            }
        }
        return result;
    }
}
