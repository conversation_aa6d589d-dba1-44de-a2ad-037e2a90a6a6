package ttfund.web.communityservice.timedtask.jijinbarJobs.repair;

import com.google.common.collect.Sets;
import com.ttfund.web.core.register.AppCore;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.msyql.PassportUserInfoMysqlDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 通行证用户信息修复任务
 * 从 MySQL tb_passport_user_info 表 修复至基金吧库&用户库 PassportUserInfo 表
 */
@JobHandler(value = "passportUserInfoRepairJob")
@Component
public class PassportUserInfoRepairJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PassportUserInfoRepairJob.class);

    @Autowired
    private PassportUserInfoMysqlDao passportUserInfoMysqlDao;

    @Autowired
    private PassportUserInfoDao passportUserInfoDao;

    @Autowired
    private AppCore appCore;

    @Override
    public ReturnT<String> execute(String param) {

        ReturnT resultStatus = ReturnT.SUCCESS;

        try {
            logger.info("通行证用户信息修复任务开始");

            String breakName = "passportUserInfoRepairJob_break";
            String breakValue = appCore.redisuserread.get(breakName);

            PassportUserInfoModelNew userInfo = new PassportUserInfoModelNew();
            if (StringUtils.isEmpty(breakValue)) {
                userInfo.Registertime = "";
                userInfo.PassportID = "";
            } else {
                userInfo.Registertime = breakValue.split("_")[0];
                userInfo.PassportID = breakValue.split("_")[1];
            }

            int sum = 0;
            int batchCount = 2000;

            for (int i = 0; i < 100; i++) {

                Thread.sleep(100L);

                List<PassportUserInfoModelNew> userinfos = passportUserInfoMysqlDao.queryPassportUserByRegisterTime(userInfo, batchCount);

                if (CollectionUtils.isEmpty(userinfos)) {
                    break;
                }

                Set<String> ids = userinfos.stream().map(w -> w.PassportID).collect(Collectors.toSet());

                // 基金吧MongoDB修复
                List<String> existedIds = passportUserInfoDao.queryBarMongoExistedIds(ids)
                    .stream().map(w -> w._id).collect(Collectors.toList());

                Sets.SetView<String> barDiff = Sets.difference(ids, new HashSet<>(existedIds));

                if (!barDiff.isEmpty()) {
                    List<PassportUserInfoModelNew> toSaveData = userinfos.stream()
                        .filter(w -> barDiff.contains(w.PassportID))
                        .collect(Collectors.toList());

                    passportUserInfoDao.saveBarMongo(toSaveData);

                    logger.info("吧MongoDB修复的数据：{}", JacksonUtil.obj2String(barDiff));
                }

                // 用户MongoDB修复
                existedIds = passportUserInfoDao.queryUserMongoExistedIds(ids)
                    .stream().map(w -> w._id).collect(Collectors.toList());

                Sets.SetView<String> userDiff = Sets.difference(ids, new HashSet<>(existedIds));

                if (!userDiff.isEmpty()) {
                    List<PassportUserInfoModelNew> toSaveData = userinfos.stream()
                        .filter(w -> userDiff.contains(w.PassportID))
                        .collect(Collectors.toList());

                    passportUserInfoDao.saveUserMongo(toSaveData);

                    logger.info("用户MongoDB修复的数据：{}", JacksonUtil.obj2String(userDiff));
                }

                sum += userinfos.size();
                userInfo = userinfos.get(userinfos.size() - 1);

                logger.info("已处理{}条数据，当前数据：{}", sum, JacksonUtil.obj2String(userInfo));

                if (userinfos.size() < batchCount) {
                    break;
                }
            }

            appCore.redisuserwrite.set(breakName, userInfo.Registertime + "_" + userInfo.PassportID);

            logger.info("通行证用户信息修复任务结束");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            resultStatus = ReturnT.FAIL;
        }
        return resultStatus;
    }
}
