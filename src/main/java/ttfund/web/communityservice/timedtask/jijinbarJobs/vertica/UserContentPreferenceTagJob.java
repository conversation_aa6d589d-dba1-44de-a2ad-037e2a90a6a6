package ttfund.web.communityservice.timedtask.jijinbarJobs.vertica;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.data.UserContentPreferenceTagDo;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserBindInfo;
import ttfund.web.communityservice.bean.jijinBar.user.UserTagModel;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.PassportUserBindInfoDao;
import ttfund.web.communityservice.dao.mongo.UserTagDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.dao.vertica.UserContentPreferenceTagDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chenlianghao
 * 用户兴趣标签同步至Mongo
 */
@JobHandler("UserContentPreferenceTagJob")
@Component
public class UserContentPreferenceTagJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserContentPreferenceTagJob.class);

    private static final String LOG_PREFIX = "UserContentPreferenceTagJob[用户兴趣标签同步服务]";

    private static final String TYPE_USER_CONTENT_PREFERENCE_TAG = "UserContentPreferenceTag";

    private static final Long expireTime = 60 * 60 * 24 *7L;

    @Autowired
    private UserContentPreferenceTagDao userContentPreferenceTagDao;

    @Autowired
    private UserTagDao userTagDao;

    @Autowired
    private PassportUserBindInfoDao passportUserBindInfoDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        List<UserContentPreferenceTagDo> userContentPreferenceTagDos = new ArrayList<>();
        int batchSize = 1000; // 每次查询的记录数
        boolean hasMoreData = true;
        String lastCustomerNo = ""; // 初始值为空
        while (hasMoreData) {
            List<UserContentPreferenceTagDo> batch = userContentPreferenceTagDao.getUserContentPreferenceTag(lastCustomerNo, batchSize);
            if (batch.isEmpty()) {
                hasMoreData = false;
            } else {
                userContentPreferenceTagDos.addAll(batch);
                lastCustomerNo = batch.get(batch.size() - 1).getCustomerNo(); // 更新最后一个主键值
            }
        }
        LOGGER.info("{}: 从Vertica拉取用户标签数量为: {}", LOG_PREFIX, userContentPreferenceTagDos.size());
        if (CollectionUtils.isEmpty(userContentPreferenceTagDos)) {
            return null;
        }
        // 转换全量数据
        List<UserTagModel> userTagModels = convertToUserContentPreferenceTags(userContentPreferenceTagDos);
        LOGGER.info("{}: 转换后的用户标签总数量为: {}", LOG_PREFIX, userTagModels.size());

        // 分批更新到MongoDB
        int totalSize = userTagModels.size();
        int startIndex = 0;
        int index = 1;
        while (startIndex < totalSize) {
            int endIndex = Math.min(startIndex + batchSize, totalSize);
            List<UserTagModel> batchList = userTagModels.subList(startIndex, endIndex);
            boolean result = userTagDao.upsertMany(batchList, "_id");
            LOGGER.info("{}:批量更新第{}批数据，批量更新结果为: {}", LOG_PREFIX, index, result);
            startIndex = endIndex; // 更新起始索引
            index++;
        }
        List<String> hotTags = userContentPreferenceTagDao.getHot10Tags();
        LOGGER.info("{}：热门标签列表: {}", LOG_PREFIX,  hotTags);
        userRedisDao.set(UserRedisConfig.USERCONTENTPREFERENCETAG_HOT10TAGS, String.join(",", hotTags),expireTime);
        return ReturnT.SUCCESS;
    }

    private List<UserTagModel> convertToUserContentPreferenceTags(List<UserContentPreferenceTagDo> userContentPreferenceTagDos) {
        Map<String, UserTagModel> idToObjectMap = new HashMap<>();
        List<UserTagModel> resultList = new ArrayList<>();
        LOGGER.info("{}: 开始转换用户标签数据", LOG_PREFIX);
        // 提取所有 CustomerNo
        List<String> customerNos = new ArrayList<>();
        for (UserContentPreferenceTagDo userContentPreferenceTagDo : userContentPreferenceTagDos) {
            customerNos.add(userContentPreferenceTagDo.getCustomerNo());
        }
        LOGGER.info("开始批量查询 PassportUserBindInfo，数量: {}", customerNos.size());
        // 批量查询 PassportUserBindInfo
        List<PassportUserBindInfo> passportUserBindInfos = new ArrayList<>();
        int batchSize = 10000; // 每批查询的最大数量
        int totalSize = customerNos.size();
        int startIndex = 0;
        int index = 1;
        while (startIndex < totalSize) {
            LOGGER.info("批量查询 PassportUserBindInfo 第 {} 批，起始索引: {}, 批量大小: {}", index, startIndex, batchSize);
            int endIndex = Math.min(startIndex + batchSize, totalSize);
            List<String> batchCustomerNos = customerNos.subList(startIndex, endIndex);
            passportUserBindInfos.addAll(passportUserBindInfoDao.getByCustomerNos(batchCustomerNos));
            startIndex = endIndex; // 更新起始索引
            index++; // 更新批次索引
        }
        Map<String, PassportUserBindInfo> customerNoToPassportMap = new HashMap<>();
        for (PassportUserBindInfo passportUserBindInfo : passportUserBindInfos) {
            customerNoToPassportMap.put(passportUserBindInfo.CUSTOMERNO, passportUserBindInfo);
        }
        LOGGER.info("{}: 批量查询 PassportUserBindInfo 完成，数量: {}", LOG_PREFIX, passportUserBindInfos.size());
        // 转换数据
        for (UserContentPreferenceTagDo userContentPreferenceTagDo : userContentPreferenceTagDos) {
            UserTagModel userTagModel = new UserTagModel();
            String customerNo = userContentPreferenceTagDo.getCustomerNo();
            PassportUserBindInfo passportUserBindInfo = customerNoToPassportMap.get(customerNo);

            String passportId = passportUserBindInfo == null ? customerNo : passportUserBindInfo.UID;

            if (idToObjectMap.containsKey(passportId)) {
                userTagModel = idToObjectMap.get(passportId);
                List<String> tags = userTagModel.getTags();
                if(!tags.contains(userContentPreferenceTagDo.getTag())){
                    tags.add(userContentPreferenceTagDo.getTag());
                }
            } else {
                userTagModel.set_id(passportId);
                userTagModel.setUid(passportId);
                userTagModel.setType(TYPE_USER_CONTENT_PREFERENCE_TAG);
                List<String> tags = new ArrayList<>();
                tags.add(userContentPreferenceTagDo.getTag());
                userTagModel.setTags(tags);
                userTagModel.setCreateTime(DateUtil.getNowDate());
                userTagModel.setUpdateTime(DateUtil.getNowDate());
                resultList.add(userTagModel);
                idToObjectMap.put(passportId, userTagModel);
            }
        }
        return resultList;
    }
}
