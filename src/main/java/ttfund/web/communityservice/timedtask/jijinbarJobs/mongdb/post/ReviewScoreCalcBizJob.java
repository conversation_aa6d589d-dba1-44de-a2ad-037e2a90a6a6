package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.ReviewScorePost;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.ReviewScoreCodeDao;
import ttfund.web.communityservice.dao.mongo.ReviewScorePostDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 策略吧点评打分服务
 */
@JobHandler("ReviewScoreCalcBizJob")
@Component
public class ReviewScoreCalcBizJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(ReviewScoreCalcBizJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private ReviewScorePostDao reviewScorePostDao;

    @Autowired
    private ReviewScoreCodeDao reviewScoreCodeDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 1000;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.REVIEWSCORECALCBIZJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            reviewScoreCalcFromScorePost(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 数据同步到mongdb
     */
    private void reviewScoreCalcFromScorePost(int batchReadCount) {


        String breakpointName = UserRedisConfig.REVIEWSCORECALCBIZJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("第一步，读取断点。断点:{}", breakpoint);

        int round = 0;
        while (true) {
            round++;

            List<ReviewScorePost> postScoreList = reviewScorePostDao.findPaygedList(breakpointDate, batchReadCount);

            logger.info("第二步，读取帖子数据-第{}轮。数量:{}，头部id列表：{}",
                    round,
                    postScoreList == null ? 0 : postScoreList.size(),
                    postScoreList == null ? null : postScoreList.stream().map(a -> a._id).limit(20).collect(Collectors.toList()));

            List<ReviewScorePost> list = null;
            List<String> codeList = null;
            Set<String> delSet = new HashSet<>();
            if (!CollectionUtils.isEmpty(postScoreList)) {
                breakpointDate = postScoreList.stream().max((Comparator.comparing(o -> o.EUTIME))).get().EUTIME;
                codeList = postScoreList.stream().map(a -> a.BARCODE).distinct().collect(Collectors.toList());
                list = reviewScorePostDao.getListByCodeList(codeList);

                delSet = codeList.stream().collect(Collectors.toSet());
            }

            logger.info("第三步，按吧读取数据-第{}轮。codeList头部列表：{}，数量:{}，头部id列表：{}",
                    round,
                    codeList == null ? null : codeList.stream().limit(50).collect(Collectors.toList()),
                    list == null ? 0 : list.size(),
                    list == null ? null : list.stream().map(a -> a._id).limit(20).collect(Collectors.toList()));

            List<Map<String, Object>> scorePostGroupList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(list)) {
                Map<String, Object> map = null;

                Map<String, List<ReviewScorePost>> collect = list.stream().collect(Collectors.groupingBy(a -> a.BARCODE));
                for (Map.Entry<String, List<ReviewScorePost>> entry : collect.entrySet()) {
                    map = new HashMap<>();

                    map.put("BARCODE", entry.getKey());
                    double temp = (double) entry.getValue().stream().mapToInt(a -> a.REVIEWSCORE).sum() / entry.getValue().size();
                    BigDecimal b = new BigDecimal(temp);
                    temp = b.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
                    map.put("REVIEWSCOREAVERAGE", temp);

                    Map<String, Integer> scoreCountMap = new HashMap<>();
                    Map<Integer, List<ReviewScorePost>> groupByScoreMap = entry.getValue().stream().collect(Collectors.groupingBy(a -> a.REVIEWSCORE));
                    groupByScoreMap.entrySet().forEach(a -> scoreCountMap.put(String.valueOf(a.getKey()), a.getValue().size()));
                    map.put("REVIEWSCORESECTION", JSON.toJSONString(scoreCountMap));

                    map.put("REVIEWSCORECOUNT", entry.getValue().size());
                    map.put("EUTIME", new Date());

                    scorePostGroupList.add(map);
                }


                scorePostGroupList.forEach(a -> {
                    a.put("_id", a.get("BARCODE"));
                });

                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(scorePostGroupList, 100);
                for (List<Map<String, Object>> batch : batchList) {
                    reviewScoreCodeDao.upsertMany(batch);
                }

                for (Map<String, Object> a : scorePostGroupList) {
                    delSet.remove(a.get("BARCODE"));
                }

            }

            if (!CollectionUtils.isEmpty(delSet)) {
                reviewScoreCodeDao.deleteByCodes(delSet.stream().collect(Collectors.toList()));
            }

            logger.info("第四步，数据写库-第{}轮。插入数量：{}，删除数量:{}",
                    round,
                    scorePostGroupList == null ? 0 : scorePostGroupList.size(),
                    delSet == null ? 0 : delSet.size()
            );


            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

            logger.info("第五步，更新断点-第{}轮。断点：{}", round, breakpoint);

            if (postScoreList == null || postScoreList.size() < batchReadCount) {
                break;
            }
        }

    }
}