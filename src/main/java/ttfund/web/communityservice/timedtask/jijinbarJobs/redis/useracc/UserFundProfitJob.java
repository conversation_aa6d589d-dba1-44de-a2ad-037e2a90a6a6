package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.useracc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.core.register.AppCore;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.mongo.PassportFundMrgModel;
import ttfund.web.communityservice.bean.jijinBar.post.ProfitEntity;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.PassportFundMrgDao;
import ttfund.web.communityservice.dao.msyql.ProfitDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户持仓信息缓存
 */
@JobHandler(value = "userFundProfitJob")
@Component
public class UserFundProfitJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(UserFundProfitJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PassportFundMrgDao passportFundMrgDao;

    @Autowired
    private ProfitDao profitDao;

    @Autowired
    private AppCore appCore;

    @Override
    public ReturnT<String> execute(String s) {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.USERFUNDPROFITJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            logicDeal(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void logicDeal(int batchReadCount) {

        try {
            String breakpointName = UserRedisConfig.USERFUNDPROFITJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-3));

                logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("第一步，读取断点。断点:{}", breakpoint);

            int round = 0;
            //基金经理
            Map<String, List<PassportFundMrgModel>> dicFundMrg = getFundMrg();

            boolean isRunning = true;
            while (isRunning) {
                round++;

                //分批获取数据，缓解数据量过大对系统的造成的压力
                List<ProfitEntity> ret = profitDao.getProfitsIncrement(breakpointDate, batchReadCount);

                logger.info("第二步，读取数据-第{}轮。数量:{}，头部数据：{}",
                        round,
                        ret == null ? 0 : ret.size(),
                        ret == null ? null : JSON.toJSONStringWithDateFormat(ret.stream().limit(1).collect(Collectors.toList()), "yyyy-MM-dd HH:mm:ss.SSS"));

                if (!CollectionUtils.isEmpty(ret)) {
                    // 最大更新时间
                    breakpointDate = ret.stream().map(l -> l.UpdateTime).max(Date::compareTo).get();

                    int i = 0;
                    for (ProfitEntity item : ret) {
                        i++;

                        String cacheName = String.format(UserRedisConfig.FUND_GUBA_SERVICE_PROFIT_FCODE_UID, item.FCode, item.PId);

                        //基金经理不设置
                        if (dicFundMrg.containsKey(item.PId)) {
                            appCore.redisuserwrite.del(cacheName);
                        } else {
                            if (item.HoldMonth >= 0) {
                                appCore.redisuserwrite.set(cacheName, JSON.toJSONStringWithDateFormat(item, "yyyy-MM-dd'T'HH:mm:ss"));
                                logger.info("第三步-详情-第{}/{}个，设置缓存key:{}",
                                        i,
                                        ret.size(),
                                        cacheName);
                            } else {
                                appCore.redisuserwrite.del(cacheName);
                                logger.info("第三步-详情-第{}/{}个，用户{}当前持有基金（{}）状态变更为不持有,删除缓存",
                                        i,
                                        ret.size(),
                                        item.PId,
                                        item.FCode);
                            }
                        }
                    }

                    logger.info("第三步，数据写库-第{}轮。数量:{}，头部id列表：{}",
                            round,
                            ret == null ? 0 : ret.size(),
                            ret == null ? null : ret.stream().map(a -> a.Id).limit(20).collect(Collectors.toList()));

                    breakpoint = DateUtil.dateToStr(breakpointDate);
                    userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                    logger.info("第四步，更新断点-第{}轮。断点：{}", round, breakpoint);

                    if (ret.size() < batchReadCount) {
                        isRunning = false;
                    }
                } else {
                    isRunning = false;
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 基金经理绑定关系
     *
     * @return
     */
    private Map<String, List<PassportFundMrgModel>> getFundMrg() {

        try {
            List<PassportFundMrgModel> fundMrgList = passportFundMrgDao.getAll();

            if (!CollectionUtils.isEmpty(fundMrgList)) {
                return fundMrgList.stream()
                        .filter(a -> !StringUtils.isEmpty(a.PassportUID) && a.IsDel == 0)
                        .collect(Collectors.groupingBy(a -> a.PassportUID));
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return new HashMap<>();
    }
}
