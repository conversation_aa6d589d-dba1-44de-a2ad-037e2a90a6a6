package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.dao.msyql.JjbconfigDao;
import ttfund.web.communityservice.dao.msyql.PopularityhismDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.utils.CommonUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 人气月榜排行历史基数计算
 */
@JobHandler("PopularityRankMBizJob")
@Component
public class PopularityRankMBizJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(PopularityRankMBizJob.class);

    @Autowired
    private JjbconfigDao jjbconfigDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private PopularityhismDao popularityhismDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String codeType = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                codeType = jsonObject.getString("codeType");
            }

            logger.info("0.打印参数。codeType：{}", codeType);

            popularityRankMHisCalc(codeType);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 人气月榜排行历史基数计算
     */
    private void popularityRankMHisCalc(String codeType) {
        try {
            if (LocalDateTime.now().toLocalDate().getDayOfMonth() == 1 && LocalTime.now().getHour() == 0) {

                List<String> codeTypes = getCodeTypesSpecial(codeType);

                logger.info("1.打印参数。codeTypes：{}", JSON.toJSONString(codeTypes));

                List<Map<String, Object>> list = postInfoNewDao.getPopularityInfo(codeTypes);

                logger.info("2，读取数据。数量:{}，头部数据：{}",
                        list == null ? 0 : list.size(),
                        list == null ? null : JSON.toJSONString(list.get(0)));

                if (!CollectionUtils.isEmpty(list)) {
                    for (Map<String, Object> a : list) {
                        if(a.get("PINGLUNNUM") == null){
                            a.put("PINGLUNNUM", 0);
                        }
                        if(a.get("LIKECOUNT") == null){
                            a.put("LIKECOUNT", 0);
                        }
                        a.put("CREATETIME", new Date());
                        a.put("UPDATETIME", new Date());
                    }

                    List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(list, 100);
                    for (List<Map<String, Object>> batch : batchList) {
                        popularityhismDao.insertOrUpdate(batch);
                    }
                }

                logger.info("3，数据写库。数量:{}，头部数据：{}",
                        list == null ? 0 : list.size(),
                        list == null ? null : JSON.toJSONString(list.get(0)));

            } else {
                logger.info("处于非运行时间");
            }


        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    private List<String> getCodeTypesSpecial(String initConfigs) {
        List<String> result = new ArrayList<>();
        if (StringUtils.hasLength(initConfigs)) {
            result.addAll(CommonUtils.toList(initConfigs, ","));
        }
        List<String> codeTypes = jjbconfigDao.getCodeTypes();
        if (!CollectionUtils.isEmpty(codeTypes)) {
            result.addAll(codeTypes);
        }

        result = result.stream().distinct().collect(Collectors.toList());
        return result;
    }

}
