package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.dao.msyql.SetRecommonDao;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.List;

/**
 * 推荐策略缓存
 */
@JobHandler(value = "recommendStrategyRedisBusinessJob")
@Component
public class RecommendStrategyRedisBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(RecommendStrategyRedisBusinessJob.class);

    @Autowired
    private SetRecommonDao setRecommonDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String param) throws JsonProcessingException {

        try {

            syncRecommendStrategy();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void syncRecommendStrategy() {
        try {

            String cacheKey = "fund_guba_service_recommendstrategycodeList";

            List<String> recommonConfigCodeList = setRecommonDao.getRecommonConfigCodeList();

            logger.info("1.读取数据。数量：{}，头部数据：{}",
                    recommonConfigCodeList == null ? 0 : recommonConfigCodeList.size(),
                    CollectionUtils.isEmpty(recommonConfigCodeList) ? null : recommonConfigCodeList.get(0)
            );

            if (recommonConfigCodeList != null) {
                app.barredis.set(cacheKey, JacksonUtil.obj2String(recommonConfigCodeList));
            }

            logger.info("2.写缓存。数量：{}，头部数据：{}",
                    recommonConfigCodeList == null ? 0 : recommonConfigCodeList.size(),
                    CollectionUtils.isEmpty(recommonConfigCodeList) ? null : recommonConfigCodeList.get(0)
            );

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

}
