package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.post.ProfitEntity;
import ttfund.web.communityservice.bean.jijinBar.post.QA.FundJJBPZDTCountModel;
import ttfund.web.communityservice.bean.jijinBar.post.QA.FundQARewarddingCountModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.ElitePostDao;
import ttfund.web.communityservice.dao.mongo.FundManagerPostDao;
import ttfund.web.communityservice.dao.mongo.FundUserProfitDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.msyql.ProfitDao;
import ttfund.web.communityservice.dao.msyql.QuestionDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基金吧品种动态统计信息
 */
@JobHandler(value = "pingZhongDTCountBusinessJob")
@Component
public class PingZhongDTCountBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PingZhongDTCountBusinessJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private QuestionDao questionDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private ElitePostDao elitePostDao;

    @Autowired
    private FundManagerPostDao fundManagerPostDao;

    @Autowired
    private ProfitDao profitDao;

    @Autowired
    private FundUserProfitDao fundUserProfitDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws JsonProcessingException {

        try {

            String initBreakpoint1 = null;
            String initBreakpoint2 = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint1 = jsonObject.getString("initBreakpoint1");
                initBreakpoint2 = jsonObject.getString("initBreakpoint2");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint1：{}，initBreakpoint2：{}，batchReadCount：{}",
                    initBreakpoint1,
                    initBreakpoint2,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint1) || StringUtils.hasLength(initBreakpoint2)) {

                if (StringUtils.hasLength(initBreakpoint1)) {
                    userRedisDao.set(UserRedisConfig.PINGZHONGDTCOUNTBUSINESSJOB_SETTOCACHE_BREAKPOINT, initBreakpoint1, 30 * 24 * 3600L);
                    logger.info("第零步，初始化断点。initBreakpoint1：{}", initBreakpoint1);
                }

                if (StringUtils.hasLength(initBreakpoint2)) {
                    userRedisDao.set(UserRedisConfig.PINGZHONGDTCOUNTBUSINESSJOB_SETHOLDCOUNT_BREAKPOINT, initBreakpoint2, 30 * 24 * 3600L);
                    logger.info("第零步，初始化断点。initBreakpoint2：{}", initBreakpoint2);
                }

                return ReturnT.SUCCESS;
            }


            // 基金吧问答悬赏中的贴子
            boolean result1 = setQARewardIng();
            // 精华帖，基金经理帖缓存设置
            boolean result2 = setToCache(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 基金吧问答悬赏中的贴子
     *
     * @return
     */
    private boolean setQARewardIng() {

        boolean result = true;

        List<FundJJBPZDTCountModel> list = questionDao.getRewardIngCount();

        logger.info("setQARewardIng-第一步，读取数据。数量:{}，头部id列表：{}",
                list == null ? 0 : list.size(),
                list == null ? null : list.stream().map(a -> a.Code).limit(20).collect(Collectors.toList()));

        if (!CollectionUtils.isEmpty(list)) {

            int i = 0;
            for (FundJJBPZDTCountModel item : list) {
                i++;

                String cacheKey = String.format(BarRedisKey.FUND_QA_REWARDDING_COUNT, item.Code);

                try {
                    FundQARewarddingCountModel model = new FundQARewarddingCountModel();
                    model.Code = item.Code;
                    model.RewardIngCount = item.RewardIngCount;

                    app.barredis.set(cacheKey, JacksonUtil.obj2String(model), 15 * 60L);


                } catch (Exception ex) {

                    logger.error(String.format("setQARewardIng-写redis错误。第%s/%s个。吧代码：%s，异常信息：%s",
                            i,
                            list.size(),
                            item.Code,
                            ex.getMessage())
                            , ex);
                    result = false;
                }
            }
        }

        logger.info("setQARewardIng-第二步，数据写库。数量:{}，头部id列表：{}",
                list == null ? 0 : list.size(),
                list == null ? null : list.stream().map(a -> a.Code).limit(20).collect(Collectors.toList()));

        return result;
    }

    /**
     * 精华帖，基金经理帖缓存设置
     */
    public boolean setToCache(int batchReadCount) {

        boolean result = true;

        try {

            String breakpointName = UserRedisConfig.PINGZHONGDTCOUNTBUSINESSJOB_SETTOCACHE_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("setToCache-第零步-精华和基金经理，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("setToCache-第一步-精华和基金经理，读取断点。断点:{}", breakpoint);

            // 精华帖，基金经理帖统计
            long startTime = System.currentTimeMillis();

            //获取帖子的更新信息
            List<PostInfoNewModel> listPost = postInfoNewDao.getPostCodes(breakpointDate, batchReadCount);

            logger.info("setToCache-第二步-精华和基金经理，读取数据。数量:{}，头部id列表：{}",
                    listPost == null ? 0 : listPost.size(),
                    listPost == null ? null : listPost.stream().map(a -> a.CODE).limit(20).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(listPost)) {

                // 设置精华帖和基金经理帖子缓存
                List<String> listCodes = listPost.stream().map(a -> a.CODE).collect(Collectors.toList());

                int i = 0;
                for (String code : listCodes) {

                    i++;

                    //缓存名称
                    String cacheKey = String.format(BarRedisKey.FUND_BAR_DETAIL_DYNAMIC_COUNT, code);
                    try {
                        Map<String, String> dic = new HashMap<>();
                        dic.put("Code", code);

                        //精华帖数量
                        long elitePostCount = elitePostDao.elitePostCount(code);
                        dic.put("ElitePostCount", String.valueOf(elitePostCount));

                        //基金经理帖数量
                        long fundManagerCount = fundManagerPostDao.fundManagerPostCount(code);
                        dic.put("FundManagerPostCount", String.valueOf(fundManagerCount));

                        app.barredis.hmset(cacheKey, dic);

                    } catch (Exception ex) {
                        logger.error(String.format("setToCache-设置精华帖和基金经理帖子缓存错误。第%s/%s个。吧代码：%s，异常信息：%s",
                                i,
                                listCodes.size(),
                                code,
                                ex.getMessage())
                                , ex);
                        result = false;
                    }
                }

                logger.info("setToCache-第三步-精华和基金经理，数据写库。数量:{}，头部id列表：{}",
                        listCodes == null ? 0 : listCodes.size(),
                        listCodes == null ? null : listCodes.stream().limit(20).collect(Collectors.toList()));

                logger.info("setToCache-【结束设置精华帖和基金经理帖子缓存】耗时:{}毫秒", System.currentTimeMillis() - startTime);

                breakpointDate = listPost.stream().map(l -> l.UPDATETIME).max(Comparator.naturalOrder()).orElse(null);
                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("setToCache-第四步-精华和基金经理，更新断点。断点：{}", breakpoint);
            }


            // 持有人帖统计
            startTime = System.currentTimeMillis();
            breakpointName = UserRedisConfig.PINGZHONGDTCOUNTBUSINESSJOB_SETHOLDCOUNT_BREAKPOINT;
            breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("setToCache-第零步-持有，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("setToCache-第一步-持有，读取断点。断点:{}", breakpoint);

            List<ProfitEntity> profitList = profitDao.getList(breakpointDate, batchReadCount);

            logger.info("setToCache-第二步-持有，读取数据。数量:{}，头部id列表：{}",
                    profitList == null ? 0 : profitList.size(),
                    profitList == null ? null : profitList.stream().map(a -> a.FCode).limit(20).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(profitList)) {

                // 基金持有更新列表
                List<String> listCode = profitList.stream().map(a -> a.FCode).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(listPost)) {
                    //帖子持有更新列表
                    List<String> postCodeList = listPost.stream().map(a -> a.CODE).collect(Collectors.toList());
                    listCode.addAll(postCodeList);
                    listCode = listCode.stream().distinct().collect(Collectors.toList());
                }

                int i = 0;
                for (String code : listCode) {
                    i++;

                    //缓存名称
                    String cacheKey = String.format(BarRedisKey.FUND_BAR_DETAIL_DYNAMIC_COUNT, code);

                    try {
                        Map<String, String> dic = new HashMap<>();
                        dic.put("Code", code);

                        //持有该基金的人的发帖数量
                        long profitUserPostCount = fundUserProfitDao.profitPostCount(code);
                        dic.put("ProfitUserPostCount", String.valueOf(profitUserPostCount));

                        app.barredis.hmset(cacheKey, dic);

                    } catch (Exception ex) {
                        logger.error(String.format("setToCache-设置持有该基金帖的人发帖数统计错误。第%s/%s个。吧代码：%s，异常信息：%s",
                                i,
                                listCode.size(),
                                code,
                                ex.getMessage())
                                , ex);
                        result = false;
                    }
                }

                logger.info("setToCache-第三步-持有，数据写库。数量:{}，头部id列表：{}",
                        listCode == null ? 0 : listCode.size(),
                        listCode == null ? null : listCode.stream().limit(20).collect(Collectors.toList()));

                logger.info("setToCache-【设置持有该基金帖的人发帖数统计结束】耗时:{}毫秒", (System.currentTimeMillis() - startTime));

                // 最大更新时间
                breakpointDate = profitList.stream().map(l -> l.UpdateTime).max(Comparator.naturalOrder()).orElse(null);
                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("setToCache-第四步-持有，更新断点。断点：{}", breakpoint);
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            result = false;
        }

        return result;
    }


}
