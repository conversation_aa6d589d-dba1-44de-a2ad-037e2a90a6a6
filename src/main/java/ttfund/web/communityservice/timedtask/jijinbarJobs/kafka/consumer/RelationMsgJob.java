package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.alibaba.fastjson.JSON;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.messagepush.UserRelationKafkaModel;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.UserRelationCountDao;
import ttfund.web.communityservice.dao.msyql.UserRelationDao;
import ttfund.web.communityservice.utils.JacksonUtil;

@Component
public class RelationMsgJob {
    private static Logger logger = LoggerFactory.getLogger(RelationMsgJob.class);

    public static final String KAFKA_LISTENER_ID = "listener_id_RelationMsgJob";

    @Autowired
    private UserRelationDao userRelationDao;

    @Autowired
    private UserRelationCountDao userRelationCountDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.Newsplatform_follow_guba}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_RELATIONMSGJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_zp)
    private void onListen(ConsumerRecord<String, String> record) {
        relationMsg(record);
    }

    /**
     * 关注
     */
    private void relationMsg(ConsumerRecord<String, String> record) {
        try {

            logger.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            UserRelationKafkaModel relation = JacksonUtil.string2Obj(record.value(), UserRelationKafkaModel.class);
            relation.UserOtherID = JSON.toJSONString(relation.UserOtherIDDic);
            relation.ObjOtherID = JSON.toJSONString(relation.ObjOtherIDDic);

            switch (relation.RefType) {
                //关注
                case 1:
                    userRelationDao.insertOrResume(relation);

                    if (relation.MyFollowCount != null && relation.TaFansCount != null) {
                        userRelationCountDao.upsertOneByFollowNum(relation.UserID, relation.MyFollowCount);
                        userRelationCountDao.upsertOneByFansNum(relation.ObjID, relation.TaFansCount);
                    }
                    break;
                //取消关注
                case 2:
                    userRelationDao.disabled(relation);

                    if (relation.MyFollowCount != null && relation.TaFansCount != null) {
                        userRelationCountDao.upsertOneByFollowNum(relation.UserID, relation.MyFollowCount);
                        userRelationCountDao.upsertOneByFansNum(relation.ObjID, relation.TaFansCount);
                    }
                    break;
                default:
                    logger.error(String.format("Ref_Type的值不正确。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                            record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }
}
