package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.HtmlUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostFundRelation;
import ttfund.web.communityservice.bean.jijinBar.post.ReviewScorePost;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.SimplePostModel;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.SimplePostModelNew;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.ReviewScorePostDao;
import ttfund.web.communityservice.dao.msyql.JjbconfigDao;
import ttfund.web.communityservice.dao.msyql.PostFundRelationDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 单品吧帖子列表job服务
 */
@JobHandler("FundBarPostListJob")
@Component
public class FundBarPostListJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(FundBarPostListJob.class);

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";

    private static final String HIGH_FUND_REGEX = "<span class=\"fund-data\" data-code=\"([\\w]+)\" data-type=\"GD\">\\$(.*?)\\$</span>";
    private static final Pattern HIGH_FUND_REGEX_PATTERN = Pattern.compile(HIGH_FUND_REGEX);

    private static final String INVEST_ADVISER_REGEX = "<span class=\"fund-data\" data-code=\"([\\w]+)\" data-partnerid=\"([\\w]+)\" data-type=\"TG\">\\$(.*?)\\$</span>";
    private static final Pattern INVEST_ADVISER_REGEX_PATTERN = Pattern.compile(INVEST_ADVISER_REGEX);

    private static final String OTC_FUND_REGEX = "<span class=\"guba_stock\" data-marketcode=\"OTCFUND\\|([\\w]+)\" data-markettype=\"150\" data-stockcode=\"([\\w]+)\">\\$(.*?)\\$</span>";
    private static final Pattern OTC_FUND_REGEX_PATTERN = Pattern.compile(OTC_FUND_REGEX);

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private JjbconfigDao jjbconfigDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private ReviewScorePostDao reviewScorePostDao;

    @Autowired
    private PostFundRelationDao postFundRelationDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        //限制多少天
        Integer keepDays = -90;
        //每页大小
        Integer cachePageSize = 500;
        if (StringUtils.hasLength(s)) {
            JSONObject jsonObject = JSON.parseObject(s);
            if (jsonObject != null) {
                keepDays = jsonObject.getInteger("keepDays");
                cachePageSize = jsonObject.getInteger("cachePageSize");
            }
        }

        keepDays = (keepDays != null) ? keepDays : -90;
        cachePageSize = (cachePageSize != null) ? cachePageSize : 500;

        logger.info("0.读取参数。keepDays：{}，cachePageSize：{}", keepDays, cachePageSize);
        logicDeal(keepDays, cachePageSize);
        return ReturnT.SUCCESS;
    }

    public void logicDeal(Integer keepDays, Integer cachePageSize) {

        try {
            List<SimplePostModel> simpleList = null;
            String breakpointName = UserRedisConfig.FUND_BAR_POST_LIST_JOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);
            if (!StringUtils.hasLength(breakpoint)) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1), DATE_FORMAT);
            }
            Date dateForBreakpoint = DateUtil.strToDate(breakpoint, DATE_FORMAT);
            logger.info("1.读取断点。断点：{}", breakpoint);

            List<SimplePostModel> temp = null;
            int round = 0;
            while (true) {
                ++round;

                simpleList = new ArrayList<>();

                List<SimplePostModelNew> list = postInfoNewDao.getUpdateSimpleListNew(dateForBreakpoint, keepDays, appConstant.batchCount, jjbconfigDao.getCodeTypes());
                List<Integer> postIds = new ArrayList<>();
                if (!CollectionUtils.isEmpty(list)) {
                    List<SimplePostModelNew> tempList = list.size() > 200 ? list.subList(list.size() - 200, list.size()) : list;
                    postIds = tempList.stream().map(a -> a.Id).collect(Collectors.toList());
                }
                logger.info("2.读取帖子数据。轮次：{}，数量：{}，尾部帖子id列表：{}", round, CollectionUtils.isEmpty(list) ? 0 : list.size(), postIds);


                if (!CollectionUtils.isEmpty(list)) {

                    //设置最后更新时间
                    dateForBreakpoint = list.stream().max((Comparator.comparing(o -> o.UpdateTime))).get().UpdateTime;

                    //解析分发标签
                    resolveDistributeTab(list);

                    //reivescore分数赋值
                    reviewScorePost(list);

                    //处理一贴多发逻辑
                    for (SimplePostModelNew post : list) {
                        temp = multipleBar(post);
                        if (!CollectionUtils.isEmpty(temp)) {
                            simpleList.addAll(temp);
                        }
                        logger.info("帖子分发吧解析。帖子id：{}，吧列表：{}", post.Id, CollectionUtils.isEmpty(temp) ? null : temp.stream().map(a -> a.Code).collect(Collectors.toList()));
                    }

                    Map<String, List<SimplePostModel>> groupList = simpleList.stream().collect(Collectors.groupingBy(a -> a.Code));
                    Set<Map.Entry<String, List<SimplePostModel>>> entries = groupList.entrySet();
                    for (Map.Entry<String, List<SimplePostModel>> item : entries) {
                        //缓存名字
                        String cacheKey = String.format(BarRedisKey.FUND_GUBA_SERVICE_FUND_POST_INFO_POST_LIST_LIST_NEW, item.getKey());
                        //老的缓存数据
                        List<SimplePostModel> cacheList = null;
                        String value = app.barredis.get(cacheKey);
                        if (StringUtils.hasLength(value)) {
                            cacheList = JacksonUtil.string2Obj(value, List.class, SimplePostModel.class);
                        }
                        if (cacheList == null) {
                            cacheList = new ArrayList<>();
                        }

                        //增量更新的帖子ID列表
                        List<Integer> updatePostIds = item.getValue().stream().map(a -> a.Id).collect(Collectors.toList());
                        //从老的缓存中剔除已经存在的帖子
                        cacheList = cacheList.stream().filter(a -> !updatePostIds.contains(a.Id)).collect(Collectors.toList());

                        //添加新增的帖子列表
                        cacheList.addAll(item.getValue());
                        //单品吧过滤删除的帖子
                        cacheList = cacheList.stream().filter(a -> a.DEL == 0 && a.TTJJDEL == 0).collect(Collectors.toList());

                        //数据补充逻辑，当前数据不足时补充近一年的历史数据
                        if (cacheList != null && cacheList.size() < 5) {
                            List<SimplePostModelNew> lastPostList = postInfoNewDao.getLastPostsByCode(item.getKey(), DateUtil.calendarDateByYears(-1), 10);
                            if (!CollectionUtils.isEmpty(lastPostList)) {
                                List<Integer> lastPostIds = lastPostList.stream().map(a -> a.Id).collect(Collectors.toList());
                                cacheList = cacheList.stream().filter(a -> !lastPostIds.contains(a.Id)).collect(Collectors.toList());
                                cacheList.addAll(lastPostList);
                            }
                        }

                        //重新排序准备再次插入
                        cacheList = cacheList.stream().sorted((o1, o2) -> Long.compare(o2.TimePoint, o1.TimePoint)).limit(cachePageSize).collect(Collectors.toList());

                        logger.info("吧聚合帖子。吧：{}，头部帖子列表：{}", item.getKey(), CollectionUtils.isEmpty(cacheList) ? null : cacheList.stream().map(a -> a.Id).limit(20).collect(Collectors.toList()));

                        Boolean setResult = app.barredis.set(cacheKey, JacksonUtil.obj2String(cacheList), 90 * 24 * 3600L);
                        if (setResult != null && !setResult) {
                            setResult = app.barredis.set(cacheKey, JacksonUtil.obj2String(cacheList), 90 * 24 * 3600L);
                            if (setResult != null && !setResult) {
                                logger.error("写入吧聚合帖子列表缓存失败。吧:{}", item.getKey());
                            }
                        }
                    }

                }

                logger.info("3.处理帖子数据。轮次：{}，数量：{}", round, CollectionUtils.isEmpty(list) ? 0 : list.size());

                if (list == null || list.size() < appConstant.batchCount) {
                    breakpoint = DateUtil.dateToStr(dateForBreakpoint, DATE_FORMAT);
                    userRedisDao.set(breakpointName, breakpoint, 90 * 24 * 3600L);
                    logger.info("4.更新断点。断点：{}", breakpoint);
                    break;
                }
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }

    /**
     * 解析分发标签：关联投顾、关联高端理财、关联基金
     */
    private void resolveDistributeTab(List<SimplePostModelNew> list) {

        if (!CollectionUtils.isEmpty(list)) {
            String content = null;
            List<PostFundRelation> relationList = new ArrayList<>();
            for (SimplePostModelNew item : list) {

                if (StringUtils.hasLength(item.CONTENT)) {
                    Matcher matcher = null;
                    String code = null;
                    String name = null;
                    PostFundRelation relation = null;

                    content = HtmlUtils.htmlUnescape(item.CONTENT);

                    matcher = HIGH_FUND_REGEX_PATTERN.matcher(content);
                    while (matcher.find()) {
                        code = matcher.group(1);
                        name = matcher.group(2);
                        if (StringUtils.hasLength(code)) {
                            relation = new PostFundRelation();
                            relation.PostId = String.valueOf(item.Id);
                            relation.FCode = code;
                            relation.FType = 2;
                            relation.FName = name;
                            relation.UpDateTime = new Date();
                            relation.ID = relation.PostId + "_" + relation.FCode + "_" + relation.FType;
                            relationList.add(relation);
                        }
                    }

                    matcher = INVEST_ADVISER_REGEX_PATTERN.matcher(content);
                    while (matcher.find()) {
                        code = matcher.group(1);
                        name = matcher.group(3);
                        if (StringUtils.hasLength(code)) {
                            relation = new PostFundRelation();
                            relation.PostId = String.valueOf(item.Id);
                            relation.FCode = code;
                            relation.FType = 1;
                            relation.FName = name;
                            relation.UpDateTime = new Date();
                            relation.ID = relation.PostId + "_" + relation.FCode + "_" + relation.FType;
                            relationList.add(relation);
                        }
                    }

                    matcher = OTC_FUND_REGEX_PATTERN.matcher(content);
                    while (matcher.find()) {
                        code = matcher.group(2);
                        name = matcher.group(3);
                        if (StringUtils.hasLength(code)) {
                            relation = new PostFundRelation();
                            relation.PostId = String.valueOf(item.Id);
                            relation.FCode = code;
                            relation.FType = 3;
                            relation.FName = name;
                            relation.UpDateTime = new Date();
                            relation.ID = relation.PostId + "_" + relation.FCode + "_" + relation.FType;
                            relationList.add(relation);
                        }
                    }
                }
            }

            if (!CollectionUtils.isEmpty(relationList)) {
                List<List<PostFundRelation>> batchList = CommonUtils.toSmallList2(relationList, 100);
                for (List<PostFundRelation> batch : batchList) {
                    postFundRelationDao.insertIgnore(batch);
                }
            }

        }

    }

    private void reviewScorePost(List<SimplePostModelNew> list) {
        try {
            if (!CollectionUtils.isEmpty(list)) {
                List<String> postIds = list.stream().map(a -> String.valueOf(a.Id)).collect(Collectors.toList());
                List<ReviewScorePost> reviewScoreList = reviewScorePostDao.getList(postIds);
                if (!CollectionUtils.isEmpty(reviewScoreList)) {
                    Map<String, Integer> idToScoreMap = new HashMap<>(list.size() * 4 / 3 + 1);
                    Integer temp = null;
                    reviewScoreList.forEach(a -> idToScoreMap.put(a.POSTID, a.REVIEWSCORE));
                    for (SimplePostModelNew item : list) {
                        temp = idToScoreMap.get(String.valueOf(item.Id));
                        if (temp != null) {
                            item.REVIEWSCORE = temp;
                        } else {
                            item.REVIEWSCORE = 0;
                        }
                    }
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 基金吧帖子，codelist 拆分
     */
    private List<SimplePostModel> multipleBar(SimplePostModelNew simplePost) {
        List<SimplePostModel> result = new ArrayList<>();
        if (StringUtils.hasLength(simplePost.Code)) {
            result.add(simplePost);
        }
        List<String> tempCodeArr = new ArrayList<>();

        if (StringUtils.hasLength(simplePost.CODELIST)) {
            List<String> tempList = CommonUtils.toList(simplePost.CODELIST, ",");
            for (String code : tempList) {
                String tempCode = code.toLowerCase();
                String startStr = "of";
                //判断是否是普通基金代码, 是否是组合代码
                if (tempCode.startsWith(startStr) || tempCode.startsWith("43-") || tempCode.startsWith("58-")) {
                    //如果是普通基金需要特殊处理
                    if (tempCode.startsWith(startStr)) {
                        tempCode = tempCode.replace(startStr, "");
                    }
                    tempCodeArr.add(tempCode);
                }
            }
        }


        List<PostFundRelation> relationFundList = postFundRelationDao.getByPostId(String.valueOf(simplePost.Id));
        if (!CollectionUtils.isEmpty(relationFundList)) {
            for (PostFundRelation item : relationFundList) {
                item.FCode = item.FCode.toLowerCase();
                if (item.FType == 43) {
                    item.FCode = "43-" + item.FCode;
                } else if (item.FType == 1) {
                    item.FCode = "58-" + item.FCode;
                } else if (item.FType == 2) {

                } else if (item.FType == 3) {

                } else {
                    continue;
                }
                tempCodeArr.add(item.FCode);
            }
        }

        tempCodeArr = tempCodeArr.stream().distinct().collect(Collectors.toList());

        List<String> listCode = new ArrayList<>();
        for (String tempCode : tempCodeArr) {

            if (!listCode.contains(tempCode)) {

                listCode.add(tempCode);

                SimplePostModel tempModel = new SimplePostModel();
                tempModel.Code = tempCode;
                tempModel.DEL = simplePost.DEL;
                tempModel.Id = simplePost.Id;
                tempModel.REVIEWSCORE = simplePost.REVIEWSCORE;
                tempModel.Time = simplePost.Time;
                tempModel.TimePoint = simplePost.TimePoint;
                tempModel.TTJJDEL = simplePost.TTJJDEL;
                tempModel.Type = simplePost.Type;
                tempModel.UId = simplePost.UId;
                tempModel.UpdateTime = simplePost.UpdateTime;
                tempModel.YuanId = simplePost.YuanId;
                tempModel.YuanType = simplePost.YuanType;
                result.add(tempModel);
            }
        }

        return result;
    }

}
