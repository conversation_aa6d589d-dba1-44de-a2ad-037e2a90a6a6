package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.eastmoney.particle.common.utils.JsonUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.ttfund.web.base.Constant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.VideoAuthorConfigDo;
import ttfund.web.communityservice.bean.jijinBar.post.VideoPostConfigDo;
import ttfund.web.communityservice.dao.mongo.CommunityVideoDao;
import ttfund.web.communityservice.dao.msyql.VideoAuthorConfigDao;
import ttfund.web.communityservice.dao.msyql.VideoPostConfigDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Zhuyuang
 * @date : 2025-01-22 15:31
 * @description :
 */
@JobHandler("CommunityVideoConfigSyncJob")
@Component
public class CommunityVideoConfigSyncJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommunityVideoConfigSyncJob.class);

    private static final String POST_CACHE_KEY = "CommunityVideoConfigBreakPoint";

    private static final String AUTHOR_CACHE_KEY = "CommunityVideoConfigBreakPoint2";

    @Autowired
    private CommunityVideoDao communityVideoDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private VideoPostConfigDao videoPostConfigDao;

    @Autowired
    private VideoAuthorConfigDao videoAuthorConfigDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date updateTime1 = StringUtils.isEmpty(s) ? userRedisDao.getBreakTime(POST_CACHE_KEY, new Date()) : DateUtil.strToDate(s);
        Date updateTime2 = StringUtils.isEmpty(s) ? userRedisDao.getBreakTime(AUTHOR_CACHE_KEY, new Date()) : DateUtil.strToDate(s);
        LOGGER.info("CommunityVideoConfigSyncJob CommunityVideoConfigBreakPoint:{}", updateTime1);
        LOGGER.info("CommunityVideoConfigSyncJob CommunityVideoConfigBreakPoint2:{}", updateTime2);

        List<VideoPostConfigDo> videoPostConfig = videoPostConfigDao.getVideoPostConfig(updateTime1);
        List<VideoAuthorConfigDo> videoAuthorConfig = videoAuthorConfigDao.getVideoAuthorConfig(updateTime2);

        LOGGER.info("CommunityVideoConfigSyncJob get post config:{}", JsonUtils.toJsonString(videoPostConfig));
        LOGGER.info("CommunityVideoConfigSyncJob get author config:{}", JsonUtils.toJsonString(videoAuthorConfig));

        if (!CollectionUtils.isEmpty(videoPostConfig)) {
            List<Map<String, Object>> mapList = new ArrayList<>(videoPostConfig.size());
            for (VideoPostConfigDo configDo : videoPostConfig) {
                Map<String, Object> map = CommonUtils.beanToMap(configDo);
                map.put("invisibleLocation", Arrays.asList(configDo.getInvisibleLocation().split(",")));
                map.put("priority", configDo.getPriority() == 0 ? null : 100 - configDo.getPriority());
                map.put("updateTime", new Date());
                mapList.add(map);
            }
            communityVideoDao.updateMany(mapList, "postId");
            LOGGER.info("CommunityVideoConfigSyncJob post config update done.{}", JsonUtils.toJsonString(mapList));
            userRedisDao.setBreakTime(POST_CACHE_KEY, videoPostConfig.stream().
                max(Comparator.comparing(a -> a.getUpdateTime())).get().getUpdateTime(), 50 * Constant.CACHETIMEDAY7);
        }

        if (!CollectionUtils.isEmpty(videoAuthorConfig)) {
            List<Map<String, Object>> mapList = new ArrayList<>(videoAuthorConfig.size());
            for (VideoAuthorConfigDo configDo : videoAuthorConfig) {
                Map<String, Object> map = CommonUtils.beanToMap(configDo);
                map.put("authorInvisible", Arrays.asList(configDo.getInvisibleLocation().split(",")));
                map.put("updateTime", new Date());
                map.put("uid", configDo.getAuthorId());
                map.remove("authorId");
                map.remove("invisibleLocation");
                mapList.add(map);
            }
            communityVideoDao.updateMany(mapList, "uid");
            LOGGER.info("CommunityVideoConfigSyncJob author config update done.", JsonUtils.toJsonString(mapList));
            userRedisDao.setBreakTime(AUTHOR_CACHE_KEY, videoAuthorConfig.stream().
                max(Comparator.comparing(a -> a.getUpdateTime())).get().getUpdateTime(), 50 * Constant.CACHETIMEDAY7);
        }
        return ReturnT.SUCCESS;
    }
}
