package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.CacheHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.*;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.ReplyInfoMongoDao;
import ttfund.web.communityservice.dao.msyql.FundTopicDao;
import ttfund.web.communityservice.dao.msyql.KeywordDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;
import ttfund.web.communityservice.utils.ThreeTuple;
import ttfund.web.communityservice.utils.TwoTuple;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@JobHandler("ReplySyncBizJob")
@Component
public class ReplySyncBizJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(ReplySyncBizJob.class);

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";

    //评论列表
    private static List<Long> replyPushList = new LinkedList<>();

    private static final String HOLDPLACE = "<!--adr{0}-->";

    private static final String LINK_IMG = "<img\\b[^>]*>";
    private static final Pattern LINK_IMG_PATTERN = Pattern.compile(LINK_IMG);

    private static final String ImgPlace = "<!--img{0}-->";

    private static final String IMG_REGEX = "\\[img\\](.*?)\\[\\/img\\]";
    private static final Pattern IMG_REGEX_PATTERN = Pattern.compile(IMG_REGEX);

    private static final String USER_REGEX = "\\[at=(.*?)\\]([\\s\\S]*?)\\[/at\\]";
    private static final Pattern USER_REGEX_PATTERN = Pattern.compile(USER_REGEX);

    private static final String TOPIC_REGEX = "\\[topic_name=(.*?)##topic_id=(.*?)\\]";
    private static final Pattern TOPIC_REGEX_PATTERN = Pattern.compile(TOPIC_REGEX);

    private static final String TOPIC_NEW_REGEX = "#(.*?)#";//#债券基金#不错[大笑]
    private static final Pattern TOPIC_NEW_REGEX_PATTERN = Pattern.compile(TOPIC_NEW_REGEX);

    private static final String A_TAB_REGEX = "<a(.*?)>(.*?)</a>";
    private static final Pattern A_TAB_REGEX_PATTERN = Pattern.compile(A_TAB_REGEX, Pattern.CASE_INSENSITIVE);

    private static final String TAB_REGEX = "<[^>]*?>";
    private static final Pattern TAB_REGEX_PATTERN = Pattern.compile(TAB_REGEX, Pattern.CASE_INSENSITIVE);

    private static final String NOT_IMG_TAB_REGEX = "</?((?!img).)*?/?>";
    private static final Pattern NOT_IMG_TAB_REGEX_PATTERN = Pattern.compile(NOT_IMG_TAB_REGEX);

    private static final String HTML_TAB_REGEX = "<[^>]+>|</[^>]+>";
    private static final Pattern HTML_TAB_REGEX_PATTERN = Pattern.compile(HTML_TAB_REGEX);

    //private static final String TAB_HOLD_PLACE = "♫";

    private static final String TAB_HOLD_PLACE = "♫{0}>";

    private static final String OTC_FUND_1_REGEX = "<span class=\"guba_stock\" data-marketcode=\"of([\\d]+)\" data-markettype=\"150\" data-stockcode=\"([\\d]+)\">\\$(.*?)\\$</span>";
    private static final Pattern OTC_FUND_1_REGEX_PATTERN = Pattern.compile(OTC_FUND_1_REGEX);

    private static final String OTC_FUND_2_REGEX = "<span class=\"guba_stock\" data-marketcode=\"OTCFUND\\|([\\d]+)\" data-markettype=\"150\" data-stockcode=\"([\\d]+)\">\\$(.*?)\\$</span>";
    private static final Pattern OTC_FUND_2_REGEX_PATTERN = Pattern.compile(OTC_FUND_2_REGEX);

    private static final Map<String, String> urlPositionTypeAndRegexDicInfos = new HashMap<>();

    static {
        urlPositionTypeAndRegexDicInfos.put("19_3_7", "<a[^>]*href=('|\\\")(http|https)://iguba.eastmoney.com/(\\d{1,})(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("18_3_7", "<a[^>]*href=('|\\\")(http|https)://gubatopic.eastmoney.com/jj/topic.html\\?htid=(\\d{1,})(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("22_4_8", "<a[^>]*href=('|\\\")(http|https)://guba.eastmoney.com/news,(.*?),(\\d{1,}).html(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("23_3_7", "<a[^>]*href=('|\\\")(http|https)://fund.eastmoney.com/news/\\d{4},(\\d{8,}).html(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("24_3_7", "<a[^>]*href=('|\\\")(http|https)://caifuhao.eastmoney.com/news/(\\d{8,})(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("25_3_6", "<a[^>]*href=('|\\\")(http|https)://topic.1234567.com.cn/(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("28_3_6", "<a[^>]*href=('|\\\")(http|https)://act.1234567.com.cn/(.*?)('|\\\")(.*?)>(.*?)</a>");
        urlPositionTypeAndRegexDicInfos.put("26_3_5", "<a[^>]*href=('|\\\")(http|https)://appunit.1234567.com.cn/combodetailv2/index.html\\?id=(\\d{8,})(.*)>(.*?)</a>");//子账户
        urlPositionTypeAndRegexDicInfos.put("27_2_3", "<a[^>]*href=('|\\\")fund://mp.1234567.com.cn/weex/(.*?)>(.*?)</a>");//小程序
    }

    private static final Map<String, Pattern> urlPositionTypeAndRegexDicInfosPattern = new HashMap<>();

    static {
        for (Map.Entry<String, String> entry : urlPositionTypeAndRegexDicInfos.entrySet()) {
            urlPositionTypeAndRegexDicInfosPattern.put(entry.getKey(), Pattern.compile(entry.getValue(), Pattern.CASE_INSENSITIVE));
        }
    }

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private KeywordDao keywordDao;

    @Autowired
    private ReplyInfoDao replyInfoDao;

    @Autowired
    private ReplyInfoNewDao replyInfoNewDao;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private FundTopicDao fundTopicDao;

    @Autowired
    private ReplyInfoMongoDao replyInfoMongoDao;

    @Qualifier("pushRobotLikeExecutor")
    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        syncReplyInfoNewTable(s);
        return ReturnT.SUCCESS;
    }

    /**
     * 同步主贴信息表
     */
    private void syncReplyInfoNewTable(String s) {
        String logpre = DateUtil.dateToStr(new Date(), "yyyy-MM-dd_HH:mm:ss") + ":";
        try {

            Integer batchReadCount = null;
            Integer asyncLimit = null;
            Integer threadCount = null;
            List<Object> ids = null;
            String initBreakpoint = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                batchReadCount = jsonObject.getInteger("batchReadCount");
                asyncLimit = jsonObject.getInteger("asyncLimit");
                threadCount = jsonObject.getInteger("threadCount");
                initBreakpoint = jsonObject.getString("initBreakpoint");
                ids = jsonObject.getJSONArray("ids");
            }

            if (batchReadCount == null) {
                batchReadCount = 10000;
            }
            if (asyncLimit == null) {
                asyncLimit = 2000;
            }
            if (threadCount == null) {
                threadCount = 3;
            }

            logger.info(logpre + "第零步-打印参数。batchReadCount：{}，asyncLimit：{}，threadCount：{}，initBreakpoint：{}，ids：{}",
                    batchReadCount, asyncLimit, threadCount, initBreakpoint, ids);

            if (StringUtils.hasLength(initBreakpoint)) {
                //验证传入断点格式
                DateUtil.strToDate(initBreakpoint, DATE_FORMAT);
                userRedisDao.set(UserRedisConfig.REPLYSYNCBIZJOB_BREAKPOINT, initBreakpoint, 3600 * 24 * 30L);
                return;
            }

            boolean useDefault = false;
            String breakpoint = userRedisDao.get(UserRedisConfig.REPLYSYNCBIZJOB_BREAKPOINT);
            if (!StringUtils.hasLength(breakpoint)) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-30), DATE_FORMAT);
                useDefault = true;
            }
            logger.info(logpre + "第一步-获取断点。断点：{}，是否默认：{}", breakpoint, useDefault);

            //获取关键字
            Map<String, KeywordsEntity> keyWordDics = getKeyWordDic();
            logger.info(logpre + "第二步-获取关键字库。数量：" + keyWordDics.size());

            int round = 0;//仅作打日志记录循环次数用
            boolean isRun = true;
            List<ReplyInfo> replyList = null;
            if (!CollectionUtils.isEmpty(ids)) {
                replyList = replyInfoDao.getReplyListByIds(ids.stream().map(item -> Long.parseLong(String.valueOf(item))).collect(Collectors.toList()));
                logger.info(logpre + "第三步-指定id获取评论。轮次{}，数量：{}，尾部id:{}",
                        round,
                        CollectionUtils.isEmpty(replyList) ? 0 : replyList.size(),
                        CollectionUtils.isEmpty(replyList) ? null : replyList.stream().map(a -> a.ID).limit(50).collect(Collectors.toList()));
            } else {

                while (isRun) {

                    long time1 = System.currentTimeMillis();
                    ++round;

                    replyList = replyInfoDao.getReplyList(DateUtil.strToDate(breakpoint, DATE_FORMAT), batchReadCount);
                    logger.info(logpre + "第三步-增量获取评论。轮次{}，数量：{}，尾部id:{}",
                            round,
                            CollectionUtils.isEmpty(replyList) ? 0 : replyList.size(),
                            CollectionUtils.isEmpty(replyList) ? null : replyList.stream().map(a -> a.ID).limit(50).collect(Collectors.toList()));

                    if (!CollectionUtils.isEmpty(replyList)) {

                        // 处理帖子
                        if (replyList.size() > asyncLimit) {
                            dealReplyAsync(replyList, keyWordDics, threadCount, round);
                        } else {
                            for (int i = 0; i < replyList.size(); i++) {
                                dealReply(replyList.get(i), keyWordDics, replyList.size(), round, i);
                            }
                        }

                        if (CollectionUtils.isEmpty(ids)) {
                            Date maxDate = replyList.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;
                            if (breakpoint.equals(DateUtil.dateToStr(maxDate, DATE_FORMAT)) && replyList.size() == batchReadCount) {
                                logger.error(logpre + "同一秒有大量数据，超过阈值！!");
                                break;
                            }
                            breakpoint = DateUtil.dateToStr(maxDate, DATE_FORMAT);
                            userRedisDao.set(UserRedisConfig.REPLYSYNCBIZJOB_BREAKPOINT, breakpoint, 3600 * 24 * 30L);
                            logger.info(logpre + "第五步-保存断点。轮次{}，断点：{}", round, breakpoint);
                            if (replyList.size() < batchReadCount) {
                                isRun = false;
                            }
                        } else {
                            //清空初始化帖子id列表
                            ids = null;
                            isRun = false;
                        }
                    } else {
                        isRun = false;
                    }

                    logger.info(logpre + "第六步-统计本轮同步耗时。轮次：{}，数量：{}，耗时：{}秒",
                            round,
                            CollectionUtils.isEmpty(replyList) ? 0 : replyList.size(),
                            ((System.currentTimeMillis() - time1) / 1000));

                }
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 异步处理评论
     */
    private void dealReplyAsync(List<ReplyInfo> replyList, Map<String, KeywordsEntity> keyWordDics, int threadCount, int round) throws InterruptedException {
        ExecutorService excutor = Executors.newFixedThreadPool(threadCount);
        int finalRound = round;
        CountDownLatch countDownLatch = new CountDownLatch(replyList.size());
        String taceid = MDC.get(CoreConstant.logtraceid);
        for (int i = 0; i < replyList.size(); i++) {
            ReplyInfo p = replyList.get(i);
            int finalI = i;
            excutor.submit(() -> {
                try {
                    MDC.put(CoreConstant.logtraceid, taceid);
                    dealReply(p, keyWordDics, replyList.size(), finalRound, finalI);
                } finally {
                    MDC.remove(CoreConstant.logtraceid);
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        excutor.shutdown();
    }

    /**
     * 处理评论
     */
    private void dealReply(ReplyInfo p, Map<String, KeywordsEntity> keyWordDics, int size, int round, int i) {
        StringBuilder log = new StringBuilder();//仅用作打日志
        try {
            //只处理近一年的帖子,删除的帖子需要去更新
            if (p.TIME.compareTo(DateUtil.calendarDateByMonth(-13)) < 0 && p.DEL == 0 && p.TTJJDEL == 0) {
                log.append("0.不是近一年的帖子，不予以处理\n");
                logger.info("第四步-评论同步详情。轮次{}，第{}/{}条-评论id：{}，详情：{}\n", round, i + 1, size, p.ID, log.toString());
                return;
            }

            ReplyInfoNew pNew = JacksonUtil.string2Obj(JacksonUtil.obj2String(p), ReplyInfoNew.class);

            log.append("1.反序列化\n");

            if (StringUtils.hasLength(p.TEXT)) {
                int number = 0;
                //去除话题和用户的UBB标签
                String content = p.TEXT;
                TwoTuple<String, Integer> twoTuple1 = new TwoTuple<>(content, number);
                List<KeyWordModel> keyWordList = processKeyWordList(twoTuple1, keyWordDics, null);
                number = twoTuple1.second;
                content = twoTuple1.first;

                pNew.TEXTEND = content;
                pNew.KEYWORDLIST = JacksonUtil.obj2String(keyWordList);
            }

            log.append("2.生成关键字\n");

            pNew.UPDATETIME = new Date();
            pNew.TIMEPOINT = DateUtil.getTimePoint(p.TIME);
            boolean isSuccess = replyInfoNewDao.syncReply(pNew);

            log.append("3.写tb_replyinfo_new表。结果：" + isSuccess + "\n");
            //将评论保存到Mongodb
            if (isSuccess) {
                try {

                    Map<String, Object> map = new HashMap<>();
                    map.put("_id", String.valueOf(pNew.ID));
                    map.put("ID", pNew.ID);
                    map.put("TOPICID", pNew.TOPICID);
                    map.put("LOUCENG", pNew.LOUCENG);
                    map.put("DEL", pNew.DEL);
                    map.put("TTJJDEL", pNew.TTJJDEL);
                    map.put("CODE", pNew.CODE);
                    map.put("UID", pNew.UID);
                    map.put("NICHENG", pNew.NICHENG == null ? "" : pNew.NICHENG);
                    map.put("TIME", pNew.TIME);
                    map.put("TEXT", pNew.TEXT);
                    map.put("TEXTEND", pNew.TEXTEND);
                    map.put("KEYWORDLIST", pNew.KEYWORDLIST);
                    map.put("IP", pNew.IP == null ? "" : pNew.IP);
                    map.put("HUIFUIDLIST", pNew.HUIFUIDLIST == null ? "" : pNew.HUIFUIDLIST);
                    map.put("PUSHTIME", pNew.PUSHTIME);
                    map.put("PIC", pNew.PIC == null ? "" : pNew.PIC);
                    map.put("ISENABLED", pNew.ISENABLED);
                    map.put("CREATETIME", pNew.CREATETIME);
                    map.put("UPDATETIME", pNew.UPDATETIME);
                    map.put("TIMEPOINT", pNew.TIMEPOINT);
                    boolean flag = replyInfoMongoDao.upsert(map);

                    log.append("4.写mongo。结果：" + flag + "\n");

                    if (!flag) {
                        logger.error("保存评论MongoDb失败ID：" + pNew.ID);
                    }

                    //评论点赞推送
                    pushRobotLike(pNew);

                    log.append("5.评论点赞推送\n");

                } catch (Exception ex) {
                    logger.error(ex.getMessage(), ex);
                }
            }

            logger.info("第四步-评论同步详情。轮次{}，第{}/{}条-评论id：{}，详情：{}\n", round, i + 1, size, p.ID, log.toString());

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        logger.info("第四步-评论同步详情。轮次{}，第{}/{}条-评论id：{}，详情：{}\n", round, i + 1, size, p.ID, log.toString());
    }


    public Map<String, KeywordsEntity> getKeyWordDic() {
        Map<String, KeywordsEntity> result = null;
        String cacheKey = "Fund_JJB_Service_KeyWordsDic";
        result = CacheHelper.get(cacheKey);
        if (result == null) {
            result = keywordDao.getKeywordsDic();
            if (!CollectionUtils.isEmpty(result)) {
                CacheHelper.put(cacheKey, result, 1000 * 60 * 30L);
            }
        }
        if (result == null) {
            result = new HashMap<>();
        }
        return result;
    }

    void pushRobotLike(ReplyInfoNew replyInfo) {
        try {
            //  删除的帖子，不可用的帖子，非普通的帖子,更新时间和创建时间超过40分钟 则不推送
            if (replyInfo == null || replyInfo.DEL != 0 ||
                    replyInfo.TTJJDEL != 0 || replyInfo.ISENABLED == 0 || StringUtils.hasLength(replyInfo.HUIFUIDLIST)
                    || replyInfo.UPDATETIME.compareTo(DateUtil.calendarDateByMinute(replyInfo.CREATETIME, 30)) > 0) {
                return;
            }
            //评论列表
            if (replyPushList.contains(replyInfo.ID)) return;

            //异步post数据
            threadPoolExecutor.execute(() -> {
                try {
                    RobotLikeEntity obj = new RobotLikeEntity();
                    obj.ID = String.valueOf(replyInfo.ID);
                    obj.PTYPE = 2;
                    obj.TID = String.valueOf(replyInfo.TOPICID);
                    obj.TIME = replyInfo.TIME;
                    obj.UID = replyInfo.UID;
                    obj.PUSHTYPE = 0;
                    obj.TEXTLENGTH = getTextLength(replyInfo.TEXTEND);

                    String html = HttpHelper.requestPostJson(MessageFormat.format(appConstant.robotLikePushUrl, obj.ID), JacksonUtil.obj2String(Arrays.asList(obj)), true);
                    PushResultEntity result = null;
                    if (StringUtils.hasLength(html)) {
                        result = JacksonUtil.string2Obj(html, PushResultEntity.class);
                    }
                    if (result == null || !result.isSuccess()) {
                        logger.error("评论点赞PushRobotLike推送失败。数据：{}，结果：{}", JSON.toJSONString(obj), JSON.toJSONString(result));
                    } else {
                        if (replyPushList.size() > 1000) {
                            replyPushList.clear();
                        }
                        replyPushList.add(replyInfo.ID);
                    }
                } catch (Exception ex) {
                    logger.error(ex.getMessage(), ex);
                }
            });
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 获取文本的长度（剔除html,空格，制表符，换行符后）
     */
    public int getTextLength(String html) {
        int length = 0;
        try {
            if (!StringUtils.hasLength(html)) length = 0;
            html = removeHtmlTag(html);
            if (StringUtils.hasLength(html)) {
                html = html.replace(" ", "").replace("\n", "").replace("\r", "").replace("\t", " ");
                length = html.length();
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return length;
    }

    /**
     * 移除html标签
     */
    public String removeHtmlTag(String strhtml) {
        if (!StringUtils.hasLength(strhtml)) {
            return strhtml;
        }
        String stroutput = strhtml;
        stroutput = StringEscapeUtils.unescapeHtml4(strhtml);

        Matcher matcher = HTML_TAB_REGEX_PATTERN.matcher(stroutput);
        while (matcher.find()) {
            stroutput = stroutput.replace(matcher.group(0), "");
        }
        return stroutput;
    }

    private List<KeyWordModel> processKeyWordList(TwoTuple<String, Integer> twoTuple, Map<String, KeywordsEntity> keyWordDics, String extend) {
        String label = "";
        String holdPlace = HOLDPLACE;
        //处理链接替换任务
        ThreeTuple<String, List<KeyWordModel>, Integer> threeTuple = new ThreeTuple<>(twoTuple.first, new ArrayList<>(), twoTuple.second);
        handleAllLinks(threeTuple);
        twoTuple.first = threeTuple.first;
        twoTuple.second = threeTuple.third;

        Matcher matcher = A_TAB_REGEX_PATTERN.matcher(threeTuple.first);
        while (matcher.find()) {
            //处理链接
            threeTuple.first = threeTuple.first.replace(matcher.group(0), matcher.group(2));
        }
        //处理图片
        Pattern pattern1 = IMG_REGEX_PATTERN;
        Matcher matcher1 = pattern1.matcher(threeTuple.first);
        while (matcher1.find()) {
            threeTuple.first = threeTuple.first.replace(matcher1.group(0), "");
        }

        //匹配用户ID关键字
        //[at=6487093977122828]@rzb1016[/at] 招呼您
        Pattern pattern2 = USER_REGEX_PATTERN;
        Matcher matcher2 = pattern2.matcher(threeTuple.first);
        for (int i = 0; matcher2.find(); i++, threeTuple.third++) {
            if (StringUtils.hasLength(matcher2.group(1))) {
                label = MessageFormat.format(holdPlace, threeTuple.third);
                KeyWordModel keyWordModel = new KeyWordModel();
                keyWordModel.Code = matcher2.group(1);
                keyWordModel.Text = matcher2.group(2);
                keyWordModel.Type = 19;
                keyWordModel.Lable = label;
                threeTuple.second.add(keyWordModel);
                threeTuple.first = threeTuple.first.replace(matcher2.group(0), label);
            } else {
                threeTuple.first = threeTuple.first.replace(matcher2.group(0), matcher2.group(2));
            }
        }


        //解析关联基金标签
        String code = null;
        String text = null;
        String subStr = null;
        String temp = null;
        Matcher otcMatcher1 = OTC_FUND_1_REGEX_PATTERN.matcher(threeTuple.first);
        while (otcMatcher1.find()) {
            code = otcMatcher1.group(2);
            text = otcMatcher1.group(3);
            subStr = "$" + text + "$";
            temp = MessageFormat.format("(SH{0})", code);
            if (text.endsWith(temp)) {
                text = text.substring(0, text.lastIndexOf(temp));
            }
            temp = MessageFormat.format("(OTCFUND|{0})", code);
            if (text.endsWith(temp)) {
                text = text.substring(0, text.lastIndexOf(temp));
            }
            label = MessageFormat.format(holdPlace, threeTuple.third);
            KeyWordModel keyWordModel = new KeyWordModel();
            keyWordModel.Code = code;
            keyWordModel.Text = "$" + text + "$";
            keyWordModel.Type = 1;
            keyWordModel.Lable = label;
            threeTuple.second.add(keyWordModel);
            threeTuple.first = insertStr(threeTuple.first, label, threeTuple.first.indexOf(subStr), subStr.length());
            threeTuple.third++;
        }
        Matcher otcMatcher2 = OTC_FUND_2_REGEX_PATTERN.matcher(threeTuple.first);
        while (otcMatcher2.find()) {
            code = otcMatcher2.group(2);
            text = otcMatcher2.group(3);
            subStr = "$" + text + "$";
            temp = MessageFormat.format("(SH{0})", code);
            if (text.endsWith(temp)) {
                text = text.substring(0, text.lastIndexOf(temp));
            }
            temp = MessageFormat.format("(OTCFUND|{0})", code);
            if (text.endsWith(temp)) {
                text = text.substring(0, text.lastIndexOf(temp));
            }
            label = MessageFormat.format(holdPlace, threeTuple.third);
            KeyWordModel keyWordModel = new KeyWordModel();
            keyWordModel.Code = code;
            keyWordModel.Text = "$" + text + "$";
            keyWordModel.Type = 1;
            keyWordModel.Lable = label;
            threeTuple.second.add(keyWordModel);
            threeTuple.first = insertStr(threeTuple.first, label, threeTuple.first.indexOf(subStr), subStr.length());
            threeTuple.third++;
        }

        //匹配话题关键字
        // 看看今天走势[topic_name=5G时代渐行渐近##topic_id=53]
        Pattern pattern3 = TOPIC_REGEX_PATTERN;
        Matcher matcher3 = pattern3.matcher(threeTuple.first);
        for (int i = 0; matcher3.find(); i++, threeTuple.third++) {
            if (StringUtils.hasLength(matcher3.group(2))) {
                label = MessageFormat.format(holdPlace, threeTuple.third);
                KeyWordModel keyWordModel = new KeyWordModel();
                keyWordModel.Code = matcher3.group(2);
                keyWordModel.Text = "#" + matcher3.group(1) + "#";
                keyWordModel.Type = 18;
                keyWordModel.Lable = label;
                threeTuple.second.add(keyWordModel);
                threeTuple.first = threeTuple.first.replace(matcher3.group(0), label);
            } else {
                threeTuple.first = threeTuple.first.replace(matcher3.group(0), matcher3.group(1));
            }
        }

        //匹配话题关键字（话题2.0版本）
        String tempContent = threeTuple.first;
        Matcher tabMatcher = TAB_REGEX_PATTERN.matcher(tempContent);
        while (tabMatcher.find()) {
            tempContent = tempContent.replace(tabMatcher.group(0), "");
        }

        List<String> topicNameList = new ArrayList<>();
        Pattern pattern4 = TOPIC_NEW_REGEX_PATTERN;
        Matcher matcher4 = pattern4.matcher(tempContent);
        while (matcher4.find()) {
            temp = matcher4.group(1);
            if (StringUtils.hasLength(temp)) {
                topicNameList.add(temp);
            }
        }

        List<FundTopic> topicList = fundTopicDao.getFundTopicListByNames(topicNameList);
        if (!CollectionUtils.isEmpty(topicList)) {
            for (int i = 0; i < topicList.size(); i++, threeTuple.third++) {
                String topicNameText = "#" + topicList.get(i).name + "#";
                label = MessageFormat.format(holdPlace, threeTuple.third);
                KeyWordModel keyWordModel = new KeyWordModel();
                keyWordModel.Code = String.valueOf(topicList.get(i).htid);
                keyWordModel.Text = topicNameText;
                keyWordModel.Type = 18;
                keyWordModel.Lable = label;
                threeTuple.second.add(keyWordModel);
                threeTuple.first = threeTuple.first.replace(topicNameText, label);
            }
        }

        //处理基金关键字
        handleFundKeywords(threeTuple, keyWordDics);

        twoTuple.first = threeTuple.first;
        twoTuple.second = threeTuple.third;

        return threeTuple.second;
    }

    private void handleFundKeywords(ThreeTuple<String, List<KeyWordModel>, Integer> threeTuple, Map<String, KeywordsEntity> keyWordDics) {

        List<KeywordsEntity> sortedKeywords = new ArrayList<>();
        if (!CollectionUtils.isEmpty(keyWordDics)) {
            sortedKeywords = keyWordDics.values().stream().sorted((o1, o2) ->
                    {
                        int i = Integer.compare(o2.Name.length(), o1.Name.length());
                        if (i == 0) {
                            return o1.Name.compareTo(o2.Name);
                        } else {
                            return i;
                        }
                    }
            ).collect(Collectors.toList());
        }

        getHaveKeyWord(threeTuple, sortedKeywords);
    }

    /**
     * 匹配内容并生成待处理关键字列表
     */
    private void getHaveKeyWord(ThreeTuple<String, List<KeyWordModel>, Integer> threeTuple, List<KeywordsEntity> sortKeywords) {
        if (StringUtils.hasLength(threeTuple.first) && !CollectionUtils.isEmpty(sortKeywords)) {
            String label = "";
            String text = null;
            String subStr = null;
            String temp = null;
            boolean isSpecial;

            Map<String, String> tabMap = new LinkedHashMap<>();
            int i = 0;
            String tabHoldPlace = null;
            Matcher tabMatcher = TAB_REGEX_PATTERN.matcher(threeTuple.first);
            while (tabMatcher.find()) {
                ++i;
                tabHoldPlace = MessageFormat.format(TAB_HOLD_PLACE, i);
                tabMap.put(tabHoldPlace, tabMatcher.group(0));
                threeTuple.first = threeTuple.first.replace(tabMatcher.group(0), tabHoldPlace);
            }

            for (KeywordsEntity item : sortKeywords) {

                if (threeTuple.first.length() < item.Name.length()) {
                    continue;
                }

                if (threeTuple.first.contains(item.Name)) {

                    isSpecial = false;

                    text = item.Name;
                    subStr = item.Name;

                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}[{1}]$", item.Name, item.Code);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}(OTCFUND{1})$", item.Name, item.Code);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}(OTCFUND|{1})$", item.Name, item.Code);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}({1})$", item.Name, item.Code);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }

                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}$", item.Name);
                        if (threeTuple.first.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }

                    if (isSpecial) {
                        label = MessageFormat.format(HOLDPLACE, threeTuple.third);
                        KeyWordModel keyWordModel = new KeyWordModel();
                        keyWordModel.Id = item.Id;
                        keyWordModel.Code = item.Code;
                        keyWordModel.Text = subStr;
                        keyWordModel.Type = item.Type;
                        keyWordModel.Lable = label;
                        threeTuple.second.add(keyWordModel);
                        threeTuple.first = insertStr(threeTuple.first, label, threeTuple.first.indexOf(subStr), subStr.length());
                        threeTuple.third++;
                    }

                }
            }

            if (!CollectionUtils.isEmpty(tabMap)) {
                for (Map.Entry<String, String> entry : tabMap.entrySet()) {
                    threeTuple.first = threeTuple.first.replace(entry.getKey(), entry.getValue());
                }
            }


        }
    }

    /**
     * 获取话题列表
     */
    private List<FundTopic> handleFundTopic(String extend) {
        List<FundTopic> result = null;
        try {
            if (StringUtils.hasLength(extend)) {
                PostExtendInfo postFundTopic = JacksonUtil.string2Obj(extend, PostExtendInfo.class);
                if (postFundTopic != null && !CollectionUtils.isEmpty(postFundTopic.FundTopicPost)) {
                    //获取话题ID 列表
                    List<Integer> htids = postFundTopic.FundTopicPost.stream().map(item -> Integer.parseInt(item.htid)).collect(Collectors.toList());
                    //根据话题ID 列表获取话题信息
                    if (!CollectionUtils.isEmpty(htids)) {
                        result = fundTopicDao.getListByIds(htids);
                    }
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return result;
    }

    /**
     * 处理链接占位
     */
    private void handleAllLinks(ThreeTuple<String, List<KeyWordModel>, Integer> threeTuple) {
        //替换链接---帖子详情页 咨询文章等
        if (!CollectionUtils.isEmpty(urlPositionTypeAndRegexDicInfos)) {
            Set<Map.Entry<String, String>> entrySet = urlPositionTypeAndRegexDicInfos.entrySet();
            for (Map.Entry<String, String> urlPositionTypeAndRegexDicInfo : entrySet) {
                //获取type 和各项值
                String[] keyInfos = urlPositionTypeAndRegexDicInfo.getKey().split("_");
                Pattern pattern = urlPositionTypeAndRegexDicInfosPattern.get(urlPositionTypeAndRegexDicInfo.getKey());
                Matcher matcher = pattern.matcher(threeTuple.first);
                //生成相应的type信息
                for (int i = 0; matcher.find(); i++, threeTuple.third++) {
                    String label = MessageFormat.format(HOLDPLACE, threeTuple.third);
                    String text = removeHtmlLablesExceptImg(matcher.group(Integer.parseInt(keyInfos[2])));

                    Matcher matcher1 = LINK_IMG_PATTERN.matcher(text);
                    if (matcher1.find()) {
                        label = MessageFormat.format(ImgPlace, threeTuple.third);
                    }
                    KeyWordModel keyWordModel = new KeyWordModel();
                    keyWordModel.Code = matcher.group(Integer.parseInt(keyInfos[1]));
                    keyWordModel.Text = text;
                    keyWordModel.Type = Integer.parseInt(keyInfos[0]);
                    keyWordModel.Lable = label;
                    threeTuple.second.add(keyWordModel);
                    threeTuple.first = threeTuple.first.replace(matcher.group(0), label);
                }
            }
        }
    }

    /**
     * 去除文本中除img标签以外的所有的html标签
     */
    public String removeHtmlLablesExceptImg(String content) {
        try {
            Matcher matcher = NOT_IMG_TAB_REGEX_PATTERN.matcher(content);
            while (matcher.find()) {
                content = content.replace(matcher.group(0), "");
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return null;
        }
        return content;
    }


    /**
     * 替换指定区间字符串
     */
    private String insertStr(String originStr, String insertStr, int start, int length) {
        return originStr.substring(0, start) + insertStr + originStr.substring(start + length);
    }

}
