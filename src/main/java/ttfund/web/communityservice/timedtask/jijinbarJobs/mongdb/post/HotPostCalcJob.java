package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.HotPostDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 生成热门帖子
 */
@JobHandler("HotPostCalcJob")
@Component
public class HotPostCalcJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(HotPostCalcJob.class);

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private App app;

    @Autowired
    private HotPostDao hotPostDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            String codes = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                codes = jsonObject.getString("codes");
            }

            if (codes == null) {
                codes = "zf";
            }

            logger.info("第零步，打印参数。codes：{}", codes);

            hotPostCalcProcess(CommonUtils.toList(codes, ","));

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 取一周内创建的帖子
     * 分数 = 评论数*2+点赞数*3+阅读数/5000-发帖距现在小时数-最后更新距现在小时数
     * 每小时运行一次
     */
    private boolean hotPostCalcProcess(List<String> codeList) {
        try {

            logger.info("零.打印配置。hotPostConfig：{}，codeList：{}", appConstant.hotPostConfig, codeList);

            List<Map<String, Object>> postList = postInfoNewDao.getPostNewList(DateUtil.calendarDateByDays(-3), DateUtil.calendarDateByDays(1));

            logger.info("一.读取数据库。数量：{}，头部数据：{}",
                    postList == null ? 0 : postList.size(),
                    postList == null ? null :
                            JSON.toJSONStringWithDateFormat(postList.stream().limit(1).collect(Collectors.toList()),
                                    "yyyy-MM-dd HH:mm:ss.SSS")
            );

            if (!CollectionUtils.isEmpty(postList)) {
                if (!CollectionUtils.isEmpty(codeList)) {
                    postList = postList.stream().filter(a -> !codeList.contains(a.get("CODE"))).collect(Collectors.toList());
                }
            }

            logger.info("二.过滤。数量：{}，头部数据：{}",
                    postList == null ? 0 : postList.size(),
                    postList == null ? null :
                            JSON.toJSONStringWithDateFormat(postList.stream().limit(1).collect(Collectors.toList()),
                                    "yyyy-MM-dd HH:mm:ss.SSS")
            );

            if (!CollectionUtils.isEmpty(postList)) {
                Set<String> newIDs = postList.stream().map(item -> String.valueOf(item.get("ID"))).collect(Collectors.toSet());

                String[] configs = appConstant.hotPostConfig.split(",");
                double score = 0;
                for (Map<String, Object> item : postList) {

                    score = Integer.parseInt(String.valueOf(item.get("PINGLUNNUM"))) * Integer.parseInt(configs[0])
                            + Long.parseLong(String.valueOf(item.get("LIKECOUNT"))) * Integer.parseInt(configs[1])
                            + Long.parseLong(String.valueOf(item.get("CLICKNUM"))) / Integer.parseInt(configs[2])
                            - getIntervalHours(new Date(), (Date) item.get("TIME"))
                            - getIntervalHours(new Date(), (Date) item.get("UPDATETIME"));

                    item.put("SCORE", new Double(score));

                    item.remove("PINGLUNNUM");
                    item.remove("LIKECOUNT");
                    item.remove("CLICKNUM");
                    item.remove("UPDATETIME");
                }

                logger.info("三.计算得分。数量：{}", postList == null ? 0 : postList.size());

                //重新排序
                postList.sort(((o1, o2) -> Double.compare((Double) o2.get("SCORE"), (Double) o1.get("SCORE"))));
                //最大取500条数据
                List<Map<String, Object>> cacheList = postList.subList(0, postList.size() > 500 ? 500 : postList.size());
                String json = JSON.toJSONStringWithDateFormat(cacheList, "yyyy-MM-dd'T'HH:mm:ss");
                JSONArray jsonArray = JSON.parseArray(json);
                int num = 0;
                for (Object o : jsonArray) {
                    num++;
                    ((JSONObject) o).put("_ID", num);
                }
                String cacheListStr = JSON.toJSONString(jsonArray);

                app.barredis.set(BarRedisKey.fund_guba_service_hotpost_week, cacheListStr, 3600 * 24 * 7L);
                logger.info("四.更新缓存的前500热帖。数量：{}，头部数据：{}",
                        jsonArray == null ? 0 : jsonArray.size(),
                        jsonArray == null ? null : JSON.toJSONString(jsonArray.stream().limit(10).collect(Collectors.toList()))
                );

                /*//拿存量的热帖ID
                List<String> existIDs = hotPostDao.getAllPostIds();
                logger.info("五.读取存量热帖。数量：{}，头部数据：{}",
                        existIDs == null ? 0 : existIDs.size(),
                        existIDs == null ? null : existIDs.stream().limit(20).collect(Collectors.toList())
                );

                List<String> deleteIds = null;
                if (!CollectionUtils.isEmpty(existIDs)) {
                    deleteIds = existIDs.stream().filter(o -> !newIDs.contains(o)).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(deleteIds)) {
                    //删掉过时的热帖
                    List<List<String>> batchList = CommonUtils.toSmallList2(deleteIds, 200);
                    for (List<String> batch : batchList) {
                        hotPostDao.deleteByPostIds(batch);
                    }
                }

                logger.info("六.删除过时的热帖。数量：{}，头部数据：{}",
                        deleteIds == null ? 0 : deleteIds.size(),
                        deleteIds == null ? null : deleteIds.stream().limit(20).collect(Collectors.toList())
                );*/


                postList.forEach(a -> a.put("_id", String.valueOf(a.get("ID"))));
                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(postList, 200);
                for (List<Map<String, Object>> batch : batchList) {
                    hotPostDao.upsertMany(batch);
                }

                logger.info("七.写入新的热帖。数量：{}，头部数据：{}",
                        postList == null ? 0 : postList.size(),
                        postList == null ? null : postList.stream().map(a -> (String) a.get("_id")).limit(20).collect(Collectors.toList())
                );

            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return true;
    }

    /**
     * 获取两个日期之间相差的小时数
     */
    private double getIntervalHours(Date date1, Date date2) {
        return (Math.abs(date1.getTime() - date2.getTime()) / (1000.0 * 3600));
    }

    private void del() {

    }

}
