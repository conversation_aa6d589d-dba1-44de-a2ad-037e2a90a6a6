package ttfund.web.communityservice.timedtask.jijinbarJobs.robot;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.JsonUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.robot.RobotConfigModel;
import ttfund.web.communityservice.bean.jijinBar.robot.RobotGlobal;
import ttfund.web.communityservice.bean.jijinBar.robot.RobotPraiseModel;
import ttfund.web.communityservice.bean.jijinBar.robot.RobotSetConfigModel;
import ttfund.web.communityservice.dao.msyql.RobotConfigDao;
import ttfund.web.communityservice.service.LikeServiceImpl;
import ttfund.web.communityservice.service.entity.AddLikeRequest;
import ttfund.web.communityservice.service.entity.GubaBaseResponse;
import ttfund.web.communityservice.utils.JacksonUtil;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Zhuyuang
 * @date : 2024-11-18 10:12
 * @description : 智能机器人自动点赞服务
 */
@JobHandler("RobotLikeJob")
@Component
public class RobotLikeJob extends IJobHandler {

    private static Logger LOGGER = LoggerFactory.getLogger(RobotLikeJob.class);

    @Autowired
    private RobotConfigDao robotConfigDao;

    @Autowired
    private LikeServiceImpl likeService;

    private List<RobotSetConfigModel> robotConfigList = new ArrayList<>();

    @PostConstruct
    public void load() {
        String json = "[{\"Type\":1,\"Length\":300,\"LengthFlag\":true,\"ExtractionTimes\":6,\"ProbabilityValue\":[0,10000,40000,65000,85000,95000,100001]},{\"Type\":1,\"Length\":300,\"LengthFlag\":false,\"ExtractionTimes\":5,\"ProbabilityValue\":[0,35000,65000,85000,95000,100001]},{\"Type\":2,\"Length\":50,\"LengthFlag\":true,\"ExtractionTimes\":4,\"ProbabilityValue\":[0,50000,75000,90000,100001]},{\"Type\":2,\"Length\":50,\"LengthFlag\":false,\"ExtractionTimes\":3,\"ProbabilityValue\":[0,60000,85000,100001]},{\"Type\":3,\"Length\":50,\"LengthFlag\":true,\"ExtractionTimes\":4,\"ProbabilityValue\":[0,50000,75000,90000,100001]},{\"Type\":3,\"Length\":50,\"LengthFlag\":false,\"ExtractionTimes\":3,\"ProbabilityValue\":[0,60000,85000,100001]}]";
        robotConfigList = JsonUtils.toList(json, RobotSetConfigModel.class);
    }

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        logicDeal();
        return ReturnT.SUCCESS;
    }

    private void logicDeal() {
        while (true) {
            try {
                RobotConfigModel configModel = robotConfigDao.getRobotConfigModel();
                if (configModel != null && configModel.getIsOpen() == 1 && StringUtils.isNotBlank(configModel.getPassPortID())) {
                    List<RobotPraiseModel> PostInfoList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(RobotGlobal.list)) {
                        PostInfoList.addAll(RobotGlobal.list);
                    }
                    //如果当前数据集中有数据则进行推算
                    if (CollectionUtils.isNotEmpty(PostInfoList)) {
                        Random random = new Random();
                        for (RobotPraiseModel item : PostInfoList) {
                            RobotSetConfigModel tempRobotConfig = robotConfigList.stream().
                                filter(model -> model.Type == item.Ptype && model.LengthFlag == (item.TEXTLENGTH >= model.Length)).
                                findFirst().orElse(null);
                            JudgePrize(tempRobotConfig, item);
                            if (item.ClickNum > 0 && !item.Flag) {
                                String PassPortID = getPassportID(configModel.getPassPortID(), item.UserList, random);
                                boolean sign = like((item.Ptype == 1 || item.Ptype == 3) ? item.ID : item.TID,
                                    (item.Ptype == 1 || item.Ptype == 3) ? null : item.ID,
                                    PassPortID, item.TYPE);
                                if (!sign) {
                                    LOGGER.error("调用点赞接口失败==>item:" + JsonUtils.toJsonString(item) + "==>PassPortID:" + PassPortID);
                                }
                                item.UserList.add(PassPortID);
                            }
                        }
                        LOGGER.info("PostInfoList==>{}", JacksonUtil.obj2String(PostInfoList));

                        List<RobotPraiseModel> tempArray = PostInfoList.stream().
                            filter(item -> item.Flag).
                            collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(RobotGlobal.list) && CollectionUtils.isNotEmpty(tempArray)) {
                            List<String> idList = tempArray.stream().
                                map(item -> item.ID).
                                collect(Collectors.toList());
                            RobotGlobal.list.removeIf(model -> idList.contains(model.ID));
                        }
                        if (CollectionUtils.isNotEmpty(tempArray)) {
                            RobotGlobal.OldList.addAll(tempArray);
                        }
                        ///设置下一轮的时间
                        int minuteNumber = GetRandom(30, 91);
                        Thread.sleep(minuteNumber * 1000 * 60L);
                    }
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 判断是否点赞
     * @param configModel 配置文件
     * @param praiseModel 待点赞的数据
     */
    private void JudgePrize(RobotSetConfigModel configModel, RobotPraiseModel praiseModel) {
        int ClickNum = praiseModel.ClickNum;
        int length = configModel.ProbabilityValue.size();
        //获取统计到的随机数
        int randomNumber = GetRandom(configModel.ProbabilityValue.get(ClickNum) + 1, configModel.ProbabilityValue.get(length - 1));
        if (randomNumber < configModel.ProbabilityValue.get(ClickNum + 1)) {
            //此时代表当前抽到不点赞的情况
            praiseModel.Flag = true;
        } else {
            praiseModel.ClickNum++;
        }
    }

    /**
     * 调用点赞接口
     * @param postId 帖子id
     * @param replyId 回复id
     * @param userId 用户id
     * @param type 类型
     * @return
     */
    private boolean like(String postId, String replyId, String userId, int type) {
        AddLikeRequest request = new AddLikeRequest();
        request.setId(postId);
        request.setUid(userId);
        GubaBaseResponse response;
        if (StringUtils.isEmpty(replyId)) {
            request.setType(type == 50 ? 0 : type);
            response = likeService.likeArticle(request);
        } else {
            request.setType(type);
            request.setReplyid(replyId);
            response = likeService.likeArticleReply(request);
        }
        return response != null && response.getRc() == 1;
    }

    public String getPassportID(String passPortID, List<String> userList, Random random) {
        List<String> passPortList = Arrays.asList(passPortID.split(","));
        if (CollectionUtils.isNotEmpty(userList)) {
            passPortList.removeAll(userList);
        }
        int length = passPortList.size();
        int number = random.nextInt(length - 1);

        return passPortList.get(number);
    }

    public int GetRandom(int minNumber, int maxNumber) {
        Random random = new Random();
        return random.nextInt(maxNumber - minNumber) + minNumber;
    }
}
