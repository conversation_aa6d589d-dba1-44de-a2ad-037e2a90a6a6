package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.BigVDataUserModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.dao.mongo.VUserPostInfoDao;
import ttfund.web.communityservice.dao.msyql.VUserPostDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 大V相关数据计算保存MySql mongo
 *
 * @author：liyaogang
 * @date：2023/3/17 14:07
 */
@JobHandler("calBigVDataJob")
@Component
public class CalBigVDataJob extends IJobHandler {

    @Autowired
    UserRedisDao userRedisDao;

    @Autowired
    App app;

    @Autowired
    AppConstant appConstant;

    //操作mysql的dao
    @Autowired
    VUserPostDao vUserPostDao;

    //操作mongo的dao
    @Autowired
    VUserPostInfoDao vUserPostInfoDao;

    private static final Logger logger = LoggerFactory.getLogger(CalBigVDataJob.class);

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        String currentMethodName = "businessBigVDataByDayJava";
        //获取断点时间 breakPoint=lastUpdateTime
        Date breakPoint = null;
        breakPoint = StringUtils.hasLength(param) ?
                DateUtil.strToDate(param, DateUtil.datePattern) : userRedisDao.getBreakTime(currentMethodName);

        if (breakPoint == null) {
            breakPoint = DateHelper.stringToDate2("2022-09-01", DateHelper.FORMAT_YYYYMMDD);
        }
        try {
            logger.info("businessBigVDataByDay()开始执行，上次断点：{}", breakPoint);

            List<BigVDataUserModel> userList = vUserPostInfoDao.getCalBigVDataUser();
            List<String> uidList = userList.stream().map(BigVDataUserModel::getUID).distinct().collect(Collectors.toList());
            logger.info("合并去重后的uidList size：{}", uidList.size());
            if (uidList.size() > 0) {
                long start = DateHelper.getNowTimeLong();
                Date calDate = DateUtil.calendarDateByDays(-1);
                for (String uid : uidList) {
                    long swTotal = DateHelper.getNowTimeLong();
                    List<Map<String, Object>> lastDayData = vUserPostDao.getLastDayBigVData(uid, calDate);
                    logger.info("{}获取前一天大V数据，耗时：{}", uid, DateHelper.getNowTimeLong() - swTotal);

                    swTotal = DateHelper.getNowTimeLong();
                    List<Map<String, Object>> bigVData = vUserPostDao.getTotalBigVData(uid);
                    logger.info("{}获取用户最新阅读总数、点赞总数、收到评论总数、帖子总数，耗时：{}", uid, DateHelper.getNowTimeLong() - swTotal);

                    swTotal = DateHelper.getNowTimeLong();
                    List<Map<String, Object>> bigVCommentData = vUserPostDao.getTotalBigVCommentData(uid, calDate);
                    logger.info("{}获取用户最新发送评论数总数、昨日发送评论数、昨日发帖数，耗时：{}", uid, DateHelper.getNowTimeLong() - swTotal);

                    swTotal = DateHelper.getNowTimeLong();
                    List<Map<String, Object>> fansData = vUserPostDao.getUserFansNum(uid, calDate);
                    logger.info("{}，{}获取用户粉丝相关数据，耗时：{}", uid, DateUtil.dateToStr(calDate), DateHelper.getNowTimeLong() - swTotal);

                    int totalclicknum = 0,
                            totallikenum = 0,
                            totalsendcommentnum = 0,
                            totalreceivecommentnum = 0,
                            totalpostnum = 0;

                    Map<String, Object> saveDatas = new HashMap<>();
                    saveDatas.put("uid", uid);
                    saveDatas.put("cdate", DateUtil.getDate(calDate, DateHelper.FORMAT_YYYY_MM_DD));
                    if (bigVData != null && bigVData.size() > 0) {
                        totalclicknum = bigVData.get(0).get("TotalClickNum") != null ? Integer.parseInt(bigVData.get(0).get("TotalClickNum").toString()) : 0;
                        totallikenum = bigVData.get(0).get("TotalLikeNum") != null ? Integer.parseInt(bigVData.get(0).get("TotalLikeNum").toString()) : 0;
                        totalreceivecommentnum = bigVData.get(0).get("TotalReceiveCommentNum") != null ? Integer.parseInt(bigVData.get(0).get("TotalReceiveCommentNum").toString()) : 0;
                        totalpostnum = bigVData.get(0).get("TotalPostNum") != null ? Integer.parseInt(bigVData.get(0).get("TotalPostNum").toString()) : 0;
                        saveDatas.put("totalclicknum", totalclicknum);
                        saveDatas.put("totallikenum", totallikenum);
                        saveDatas.put("totalreceivecommentnum", totalreceivecommentnum);
                        saveDatas.put("totalpostnum", totalpostnum);
                    }
                    if (bigVCommentData != null && bigVCommentData.size() > 0) {
                        totalsendcommentnum = bigVCommentData.get(0).get("TotalSendCommentNum") != null ? Integer.parseInt(bigVCommentData.get(0).get("TotalSendCommentNum").toString()) : 0;
                        saveDatas.put("totalsendcommentnum", totalsendcommentnum);
                        saveDatas.put("postnum", bigVCommentData.get(0).get("PostNum") != null ? Integer.parseInt(bigVCommentData.get(0).get("PostNum").toString()) : 0);
                        saveDatas.put("sendcommentnum", bigVCommentData.get(0).get("SendCommentNum") != null ? Integer.parseInt(bigVCommentData.get(0).get("SendCommentNum").toString()) : 0);
                    }
                    if (lastDayData != null && lastDayData.size() > 0) {
                        saveDatas.put("clicknum", totalclicknum > 0 ? totalclicknum - Integer.parseInt(lastDayData.get(0).get("totalclicknum").toString()) : 0);
                        saveDatas.put("likenum", totallikenum > 0 ? totallikenum - Integer.parseInt(lastDayData.get(0).get("totalLikeNum").toString()) : 0);
                        saveDatas.put("receivecommentnum", totalreceivecommentnum > 0 ? totalreceivecommentnum - (int) lastDayData.get(0).get("totalReceiveCommentNum") : 0);
                    }
                    if (fansData != null && fansData.size() > 0) {
                        saveDatas.put("totalfansnum", fansData.get(0).get("TotalFansNum") != null ? Integer.parseInt(fansData.get(0).get("TotalFansNum").toString()) : 0);
                        saveDatas.put("fansaddnum", fansData.get(0).get("FansAddNum") != null ? Integer.parseInt(fansData.get(0).get("FansAddNum").toString()) : 0);
                        saveDatas.put("fansoffnum", fansData.get(0).get("FansOffNum") != null ? Integer.parseInt(fansData.get(0).get("FansOffNum").toString()) : 0);
                    }

                    swTotal = DateHelper.getNowTimeLong();
                    //存mysql
                    boolean result1 = vUserPostDao.saveCalBigVData(saveDatas);
                    //存mongo
                    boolean result2 = vUserPostInfoDao.saveCalBigVDataMongo(saveDatas);
                    //把粉丝数量数据存到VUserSummaryPostDetailInfos中
                    boolean result3 = vUserPostInfoDao.saveFansNum2VSummary(saveDatas);
                    logger.info("{},保存大V相关数据进mysql：{}，保存进mongo：{}，耗时：{}", uid, result1, result2 & result3, DateHelper.getNowTimeLong() - swTotal);

                    //删掉一年前的数据
                    boolean result4= vUserPostInfoDao.delExpiredData(breakPoint);
                }
                logger.info("总数：{}，CDate：{}，处理所有大V数据，耗时：{}", uidList.size(), calDate, DateHelper.getNowTimeLong() - start);
            }
            breakPoint = DateUtil.getNowDate(DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS_SSS);
            //大V每日数据计算断点
            userRedisDao.setBreakTime(currentMethodName, breakPoint);
            logger.info("{}下次断点：{}", currentMethodName, breakPoint);
        } catch (Exception e) {
            logger.error("{}发生异常{},{}", currentMethodName, e.getMessage(), e);
            return ReturnT.FAIL;
        }
        logger.info("businessBigVDataByDay()结束");
        return ReturnT.SUCCESS;
    }
}
