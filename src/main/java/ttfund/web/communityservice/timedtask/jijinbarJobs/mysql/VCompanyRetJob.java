package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.VCompanyRetModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.VUserPostDao;

import javax.annotation.Resource;
import java.util.List;

/**
 * 大V公司关系表写缓存
 *
 * @author：liyaogang
 * @date：2023/3/22 10:09
 */
@JobHandler("VCompanyRetJob")
@Component
public class VCompanyRetJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(VCompanyRetJob.class);

    @Resource
    App app;

    @Resource
    VUserPostDao vUserPostDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        //查大V公司关系表写缓存
        try {
            String key = BarRedisKey.BIG_V_COMPANY_RELATION;
            List<VCompanyRetModel> vCompanyRetList = vUserPostDao.getVCompanyRet();
            if (vCompanyRetList != null && vCompanyRetList.size() > 0) {
                app.barredis.set(key, JSON.toJSONString(vCompanyRetList), 60L * 60 * 24 * 7);
                logger.info("大V用户与基金公司关系写入缓存成功，数量：{}", vCompanyRetList.size());
            }
        } catch (Exception e) {
            logger.error("大V用户与基金公司关系写入缓存失败，error：{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
