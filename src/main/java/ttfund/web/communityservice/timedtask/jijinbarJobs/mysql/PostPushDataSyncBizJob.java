package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mysql.cj.util.StringUtils;
import com.ttfund.web.base.base.HttpRequestMethod;
import com.ttfund.web.base.helper.CacheHelper;
import com.ttfund.web.base.helper.DateHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.HtmlUtils;
import ttfund.web.communityservice.bean.jijinBar.mongo.FundJBXXModel;
import ttfund.web.communityservice.bean.jijinBar.post.*;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.dao.mongo.FundJbxxDao;
import ttfund.web.communityservice.dao.mongo.MarketMongoFundArchivesDao;
import ttfund.web.communityservice.dao.mongo.PostInfoExtendDao;
import ttfund.web.communityservice.dao.msyql.KeywordDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.msyql.PostInfoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.service.common.CommonDataCache;
import ttfund.web.communityservice.service.hanlp.KeywordHanlpService;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;
import ttfund.web.communityservice.utils.TwoTuple;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 贴子提取关键字、扩展等推送给股吧相关数据信息并入库
 *
 * <AUTHOR>
 * @date 2022/12/21
 */
@JobHandler(value = "postPushDataSyncBizJob")
@Component
public class PostPushDataSyncBizJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PostPushDataSyncBizJob.class);

    //终结符
    private static final List<Character> TERMINATOR_CHAR_LIST = Arrays.asList(
            //中文符号 参考：https://blog.csdn.net/A_Lonely_Smile/article/details/83381392
            '，', '。', '？', '！', '；', '：', '、', '·', '~', '“', '”', '（', '）', '〔', '〕', '’', '‘', '【', '】', '《', '》', '—', '…', '–', '￥',
            //英文符号
            ',', '.', '?', '!', ';', ':', '/', '\'', '\"', '\\', '|', '`', '[', ']', '{', '}', '(', ')', '<', '>',
            '+', '-', '*', '=', '_',
            '@', '#', '$', '%', '^', '&',
            //特殊符号
            '\n', '\r', '\t', ' '
    );

    @Autowired
    UserRedisDao userRedisDao;

    @Autowired
    App app;

    @Resource
    AppConstant appConstant;

    @Autowired
    KeywordDao keywordDao;

    @Autowired
    PostInfoDao postInfoDao;

    @Autowired
    PostInfoExtraDao postInfoExtraDao;

    @Autowired
    CommonDataCache commonDataCache;

    @Autowired
    PostInfoExtendDao postInfoExtendDao;

    @Autowired
    MarketMongoFundArchivesDao marketMongoFundArchivesDao;

    @Autowired
    private FundJbxxDao fundJbxxDao;

    @Autowired
    private KeywordHanlpService keywordHanlpService;

    private List<FundInfoModel> fundInfoList = new ArrayList<>();

    private List<FundJBXXModel> fundJBXXList = new ArrayList<>();

    private static final String REGEX_1 = "<a(.*?)>(.*?)</a>";
    private static final Pattern REGEX_1_PATTERN = Pattern.compile(REGEX_1);

    private static final String REGEX_2 = "\\[img\\](.*?)\\[\\/img\\]";
    private static final Pattern REGEX_2_PATTERN = Pattern.compile(REGEX_2);

    private static final String TAB_REGEX = "<[^>]*?>";
    private static final Pattern TAB_REGEX_PATTERN = Pattern.compile(TAB_REGEX, Pattern.CASE_INSENSITIVE);

    //private static final String TAB_HOLD_PLACE = "♫";

    private static final String TAB_HOLD_PLACE = "♫{0}>";

    private static final String HOLDPLACE = "<!--adr{0}-->";

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        boolean res1 = syncPostInfoPushData(s);
        boolean res2 = syncPostPushDataToGuBa(s);
        if (res1 && res2) {
            return ReturnT.SUCCESS;
        }
        return ReturnT.FAIL;
    }

    /**
     * 贴子提取关键字、扩展等推送给股吧相关数据信息并入库
     */
    private Boolean syncPostInfoPushData(String param) {
        String currentMethodName = "syncPostInfoPushDataJava";
        int batchPostReadCount = appConstant.batchPostReadCount;
        try {
            //获取断点时间
            Date breakPoint;
            breakPoint = org.springframework.util.StringUtils.hasLength(param) ?
                    DateUtil.strToDate(param, DateUtil.datePattern) : userRedisDao.getBreakTime(currentMethodName);

            if (breakPoint == null) {
                breakPoint = DateUtil.calendarDateByDays(new Date(), -7);
            }

            //获取关键字
            Map<String, KeywordsEntity> keyWordDics = getKeyWordDic();

            List<PostInfoForMysqlModel> postList = postInfoDao.getPostListByUpdateTime(breakPoint, batchPostReadCount);
            if (postList != null && postList.size() > 0) {
                //获取关键字类型数据
                Map<Integer, SetKeyUrlModel> keyUrlsMap = keywordDao.getKeyUrlsMap();
                logger.info("获取关键字类型{}条", keyUrlsMap.size());
                for (PostInfoForMysqlModel p : postList) {
                    PostInfoToGuBaModel model = new PostInfoToGuBaModel();
                    model.Id = p.ID;
                    model.UID = p.UID;
                    try {
                        //关键字处理
                        model.ContentTags = handlePostKeywordInfo(p, keyUrlsMap, keyWordDics);

                        //扩展信息处理
                        model.ContentExtTags = handlePostExtend(p, keyUrlsMap);

                        boolean saveResult = postInfoExtraDao.savePostPushData(model);
                        if (!saveResult) {
                            logger.error("{}帖子关键字扩展信息处理，帖子ID：{} 保存失败！", currentMethodName, p.ID);
                        }
                    } catch (Exception e) {
                        logger.error("{}帖子关键字扩展信息处理，帖子ID：{}，发生异常:{}", currentMethodName, p.ID, e.getMessage(), e);
                    }
                }
                List<Integer> ids = postList.stream().map(m -> m.ID).collect(Collectors.toList());
                logger.info("帖子数为:{}，查询断点：{}，帖子id：{}", postList.size(), breakPoint, ids);

                //更新断点
                if (postList.size() > 0) {
                    breakPoint = postList.stream().max(Comparator.comparing(m -> m.UPDATETIME)).get().getUPDATETIME();
                }
                userRedisDao.setBreakTime(currentMethodName, breakPoint);
                logger.info("{}更新断点时间为：{}", currentMethodName, DateHelper.dateToStr(breakPoint, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS_SSS));
            }
        } catch (Exception e) {
            logger.error("{}发生异常，error：{}", currentMethodName, e.getMessage(), e);
            return false;
        }
        logger.info("{}，执行完成", currentMethodName);
        return true;
    }


    private Boolean syncPostPushDataToGuBa(String param) {
        String currentMethodName = "syncPostPushDataToGuBaJava";
        int batchPostReadCount = appConstant.batchPostReadCount;
        try {
            //获取断点时间
            Date breakPoint;
            breakPoint = org.springframework.util.StringUtils.hasLength(param) ?
                    DateUtil.strToDate(param, DateUtil.datePattern) : userRedisDao.getBreakTime(currentMethodName);

            if (breakPoint == null) {
                breakPoint = DateHelper.stringToDate2("2019-10-18", DateHelper.FORMAT_YYYY_MM_DD);
            }
            // 只是设置了断点时间，并未用到，日志记录用
            List<PostInfoPushDataModel> pushData = postInfoExtraDao.getPostPushData(3, batchPostReadCount);
            if (pushData != null && pushData.size() > 0) {
                logger.info("{}推送帖子关键字扩展信息条数:{}，上次推送时间：{}", currentMethodName, pushData.size(), breakPoint);
                for (PostInfoPushDataModel p : pushData) {
                    try {
                        TypeReference<List<PostInfoTagModel>> typeReference = new TypeReference<List<PostInfoTagModel>>() {
                        };
                        List<PostInfoTagModel> contentTags = JacksonUtil.string2Obj(p.ContentTags, typeReference);
                        List<PostInfoTagModel> contentExtTags = JacksonUtil.string2Obj(p.ContentExtTags, typeReference);

                        if (contentTags != null && contentExtTags != null && contentTags.size() == 0 && contentExtTags.size() == 0) {
                            //关键字及扩展信息都为空，状态直接改为以推送
                            boolean updateRes = postInfoExtraDao.updatePostPushInfo("1", String.valueOf(p.ID));
                            if (updateRes) {
                                logger.info("{}推送帖子，关键字及扩展信息都为空。帖子ID：{}", currentMethodName, p.ID);
                            }
                        } else {
                            //pushtype 推送类型 0:全部推送 ，1：基金不推送 2：资讯不推送，3，资讯+基金都不推送,默认1
                            JSONObject contentJo = new JSONObject();
                            contentJo.put("ContentTags", contentTags);
                            contentJo.put("ContentExtTags", contentExtTags);
                            JSONObject fundTagsJo = new JSONObject();
                            fundTagsJo.put("FundTags", contentJo);
                            String paramStr = "pushtype=1&postid=" + p.ID + "&textmodel=" + JSON.toJSONString(fundTagsJo).replace("&", "\\u0026");
                            String html = HttpHelper.request(appConstant.config_gubahostnewserver + "/postopt/api/post/SetTextModel",
                                    paramStr, HttpRequestMethod.FORM, 100, true);
                            HashMap result = new HashMap<>();
                            if (org.springframework.util.StringUtils.hasLength(html)) {
                                result = JacksonUtil.string2Obj(html, HashMap.class);
                            }
                            if (result != null && result.size() > 0) {
                                postInfoExtraDao.updatePostPushInfo(result.get("rc").toString(), String.valueOf(p.ID));
                            }
                            logger.info("{}推送帖子关键字扩展信息 帖子ID：{}，参数：{}，推送结果：{}", currentMethodName, p.ID, paramStr, result);
                            Thread.sleep(100);
                        }
                    } catch (Exception ex) {
                        logger.error("推送帖子关键字扩展信息，帖子ID：{} 发生异常:{}", p.ID, ex.getMessage(), ex);
                    }
                }
                List<Integer> ids = pushData.stream().map(m -> m.ID).collect(Collectors.toList());
                logger.info("推送帖子关键字扩展信息{}条，id为：{}", ids.size(), ids);
                breakPoint = DateHelper.getNowDate();
                userRedisDao.setBreakTime(currentMethodName, breakPoint);
            }
        } catch (Exception ex) {
            logger.error("{}发生异常{}", currentMethodName, ex.getMessage(), ex);
            return false;
        }
        logger.info("{}推送帖子关键字、扩展信息执行完成", currentMethodName);
        return true;
    }


    /**
     * 处理帖子关键字信息
     *
     * @param p
     * @param urlsMap
     * @param keywordMap
     * @return
     */
    private List<PostInfoTagModel> handlePostKeywordInfo(PostInfoForMysqlModel p, Map<Integer, SetKeyUrlModel> urlsMap, Map<String, KeywordsEntity> keywordMap) {
        List<PostInfoTagModel> contentTags = new ArrayList<>();
        String content = null;
        if (p.CONTENT != null) {
            content = HtmlUtils.htmlUnescape(p.CONTENT);
        }
        int number = 0;
        JSONObject jo = new JSONObject();
        jo.put("content", content);
        jo.put("number", number);
        jo.put("list", new ArrayList<>());
        List<KeyWordModel> keywordList;
        if (!StringUtils.isNullOrEmpty(content)) {
            keywordList = (List<KeyWordModel>) processKeyWordListToGuBa(jo.getString("content"), keywordMap, (Integer) jo.get("number"), p.EXTEND).get("list");
        } else {
            keywordList = new ArrayList<>();
        }

        for (KeyWordModel keyword : keywordList) {
            //14行情走势，15排名走势 关键字信息处理跳过
            if (keyword.Type == 14 || keyword.Type == 15) {
                continue;
            }
            //关键字基金经理用新的链接处理
            if (keyword.Type == 17) {
                keyword.Type = 29;
            }
            SetKeyUrlModel keyInfo = urlsMap.getOrDefault(keyword.Type, null);
            if (keyInfo != null) {
                PostInfoTagModel tag = new PostInfoTagModel();
                tag.BusType = keyword.Type;
                tag.BusName = keyInfo.Name;
                tag.MatchIndex = keyword.MatchIndex;
                tag.Code = new ArrayList<>();
                if (!StringUtils.isNullOrEmpty(keyword.Code) && keyword.Code.indexOf(",") != -1) {
                    tag.Code = Arrays.asList(keyword.Code.split(","));
                } else {
                    tag.Code.add(keyword.Code);
                }
                tag.Name = keyword.Text;
                if (keyword.Type > 0 && keyword.Type != 100) {
                    //话题使用最新配置短链接 610以上版本支持
                    if (keyword.Type == 18) {
                        keyInfo.App_Url = keyInfo.SmallApp_Url;
                    }
                    tag.WebUrl = !StringUtils.isNullOrEmpty(keyInfo.Web_Url) ? MessageFormat.format(keyInfo.Web_Url, tag.Code.get(0)) : "";
                    tag.WapUrl = !StringUtils.isNullOrEmpty(keyInfo.Wap_Url) ? MessageFormat.format(keyInfo.Wap_Url, tag.Code.get(0)) : "";

                    if (keyword.Type == 20) {
                        //基金对比链接参数处理
                        String codeTemp = tag.Code.get(0);
                        int fundUrlType = fundUrlType(codeTemp);
                        if (fundUrlType > 0) {
                            tag.AppUrl = getLinkModel(MessageFormat.format(keyInfo.App_Url, keyword.Code), keyInfo.ExtendInfo);
                        }
                    } else {
                        tag.AppUrl = getLinkModel(MessageFormat.format(keyInfo.App_Url, tag.Code.get(0)), keyInfo.ExtendInfo);
                    }
                }
                contentTags.add(tag);
            }
        }
        return contentTags;
    }

    /**
     * 用jsonObject接收 处理ref引用
     */
    private JSONObject processKeyWordListToGuBa(String content, Map<String, KeywordsEntity> keywordsMap, int number, String extend) {
        JSONObject jo = new JSONObject();
        List<KeyWordModel> keywordList = new ArrayList<>();
        int num = 0;
        //处理链接
//        Pattern p = Pattern.compile("<a(.*?)>(.*?)</a>");
//        Matcher m = p.matcher(content);
//        while (m.find()) {
//            content = m.replaceAll("");
//        }
//        content = content.replaceAll(REGEX_1, "");

        Matcher matcher1 = REGEX_1_PATTERN.matcher(content);
        while (matcher1.find()) {
            content = content.replace(matcher1.group(0), "");
        }

        //处理图片
//        Pattern p1 = Pattern.compile("\\[img\\](.*?)\\[\\/img\\]");
//        Matcher m1 = p1.matcher(content);
//        while (m1.find()) {
//            content = m1.replaceAll("");
//        }
//        content = content.replaceAll(REGEX_2, "");

        Matcher matcher2 = REGEX_2_PATTERN.matcher(content);
        while (matcher2.find()) {
            content = content.replace(matcher2.group(0), "");
        }

        //处理基金关键字
        jo = handleFundKeywords(content, keywordList, num, keywordsMap);
        number = num;
        jo.put("content", content);
        jo.put("number", number);
        jo.put("list", keywordList);
        return jo;
    }

    public JSONObject handleFundKeywords(String content, List<KeyWordModel> keywordList, int num, Map<String, KeywordsEntity> keywordsMap) {

        List<KeywordsEntity> sortedKeywords = new ArrayList<>();
        if (!CollectionUtils.isEmpty(keywordsMap)) {
            sortedKeywords = keywordsMap.values().stream().sorted((o1, o2) ->
                    {
                        int i = Integer.compare(o2.Name.length(), o1.Name.length());
                        if (i == 0) {
                            return o1.Name.compareTo(o2.Name);
                        } else {
                            return i;
                        }
                    }
            ).collect(Collectors.toList());
        }

        return getHaveKeyWordWithNlp(content, keywordList, num, sortedKeywords);
    }

    public List<WordLenModel> getWordLenModel() {
        List<WordLenModel> models = new ArrayList<>();
        String cacheKey = "Fund_JJB_Service_KeyWordLenModel";
        models = CacheHelper.get(cacheKey);
        if (models == null || models.size() == 0) {
            models = keywordDao.getKeyWordLen();
            if (models != null && models.size() > 0) {
                CacheHelper.put(cacheKey, models, 10 * 60 * 1000);
            }
        }
        return models;
    }


    /**
     * 处理帖子扩展信息
     *
     * @param p
     * @param dic
     * @return
     */
    private List<PostInfoTagModel> handlePostExtend(PostInfoForMysqlModel p, Map<Integer, SetKeyUrlModel> dic) {
        List<PostInfoTagModel> contentExtTags = new ArrayList<>();
        List<FundPostExtrasProP> extendList = getPostInfoExtend(p.ID);
        if (extendList != null && extendList.size() > 0) {
            for (FundPostExtrasProP ext : extendList) {
                PostInfoTagModel tag = new PostInfoTagModel();
                tag.BusType = ext.TYPE;
                tag.Code = ext.CODE;
                tag.ImgUrl = ext.IMGURL;
                SetKeyUrlModel keyinfo = dic.getOrDefault(ext.TYPE, null);
                if (keyinfo != null) {
                    tag.BusName = keyinfo.Name;
                    tag.WebUrl = !StringUtils.isNullOrEmpty(keyinfo.Web_Url) ? MessageFormat.format(keyinfo.Web_Url, tag.Code.get(0)) : "";
                    tag.WapUrl = !StringUtils.isNullOrEmpty(keyinfo.Wap_Url) ? MessageFormat.format(keyinfo.Wap_Url, tag.Code.get(0)) : "";
                    tag.WapUrl = tag.WapUrl.replace("&", "\\u0026");
                    if (ext.TYPE == 18) {
                        //话题使用最新配置短链接 610以上版本支持
                        keyinfo.App_Url = keyinfo.SmallApp_Url;
                    }
                }

                if (ext.CODE != null && ext.CODE.size() > 0 && ext.TYPE != 100 && ext.TYPE != 200) {
                    if (ext.TYPE == 16) {
                        //重仓股票
                        FundJBXXModel tempfundjbxx = getFundJBXX(tag.Code.get(0));
                        List<FundHoldModel> tempfundhold = getFundHold(tag.Code.get(0));
                        if (tempfundjbxx != null) {
                            tag.Name = tempfundjbxx.SHORTNAME;
                        }
                        if (tempfundhold != null && tempfundhold.size() > 0) {
                            tag.Remark = tempfundhold.stream().map(m -> m.SName).collect(Collectors.joining("、"));
                        }
                    } else if (ext.TYPE == 17) {
                        //基金经理
                        FundJBXXModel tempfundjbxx = getFundJBXX(tag.Code.get(0));
                        if (tempfundjbxx != null) {
                            tag.Name = tempfundjbxx.SHORTNAME;
                            tag.Remark = tempfundjbxx.JJJL.replace(',', '、');
                        }
                    } else if (ext.TYPE == 18) {
                        //话题
                        TopicDetailsForKafka tempinfo = getFundTopic(tag.Code.get(0));
                        if (tempinfo != null) {
                            tag.Name = tempinfo.NAME;
                            tag.ImgUrl = tempinfo.IMG;
                        } else {
                            continue;
                        }
                    } else if (ext.TYPE == 20) {
                        String tempstr = null;
                        for (String code : tag.Code) {
                            FundJBXXModel tempinfo = getFundJBXX(code);
                            if (tempinfo != null) {
                                if (!StringUtils.isNullOrEmpty(tempstr)) {
                                    tempstr += " vs ";
                                }
                                tempstr += tempinfo.SHORTNAME;
                            }
                        }
                        tag.Name = !StringUtils.isNullOrEmpty(tempstr) ? "基金对比：" + tempstr : "";
                        //基金对比链接参数处理
                        String code_tmp = tag.Code.get(0);
                        int fundurltype = fundUrlType(code_tmp);
                        if (fundurltype > 0) {
                            //tag.AppUrl = keyinfo != null ? GetLinkModel(string.Format(keyinfo.App_Url, fundurltype, string.Join(",", tag.Code)), keyinfo.ExtendInfo) : new LinkModel();
                            tag.AppUrl = keyinfo != null ? getLinkModel(MessageFormat.format(keyinfo.App_Url, String.join(",", tag.Code)), keyinfo.ExtendInfo) : new LinkModel();
                        }
                    } else if (ext.TYPE == 60) {//关联组合
                        continue;
                    } else if (ext.TYPE == 70) {//关联IM社群
                        continue;
                    }
                    if (ext.TYPE != 20) {
                        tag.AppUrl = keyinfo != null ? getLinkModel(MessageFormat.format(keyinfo.App_Url, tag.Code.get(0)), keyinfo.ExtendInfo) : new LinkModel();
                    }
                } else if (ext.TYPE == 100) {
                    if (ext.CODE != null && ext.CODE.size() == 1) {
                        tag.Name = ext.CODE.get(0);
                    }
                    tag.AppUrl = getLinkModel(ext.REDIRECTURL, "");
                } else if (ext.TYPE == 200) {
                    if (ext.CODE != null && ext.CODE.size() == 1) {
                        List<String> codeArray = Arrays.stream(ext.CODE.get(0).split(",")).collect(Collectors.toList());
                        if (codeArray != null && codeArray.size() == 2) {
                            if (codeArray.get(0).length() > 3)
                                tag.Code = new ArrayList<String>(Collections.singleton(codeArray.get(0).substring(3)));
                            if (codeArray.get(1).length() > 5)
                                tag.Name = codeArray.get(1).substring(5);
                        }
                    }
                    if (!StringUtils.isEmptyOrWhitespaceOnly(ext.IMGURL)) {
                        List<String> imgurlArray = Arrays.asList(ext.IMGURL.split(","));
                        if (imgurlArray != null && imgurlArray.size() == 2) {
                            if (imgurlArray.get(0).length() > 2)
                                tag.Remark = imgurlArray.get(0).substring(2);
                            if (imgurlArray.get(1).length() > 2)
                                tag.ImgUrl = imgurlArray.get(1).substring(2);
                        }
                    }
                    tag.AppUrl = getLinkModel(ext.REDIRECTURL, "");
                    //小程序特殊处理，链接类型2
                    tag.AppUrl.setLinkType(2);
                }
                contentExtTags.add(tag);
            }
        }
        return contentExtTags;
    }


    /**
     * 获取基金基本信息（行情MongoDB）
     *
     * @param fundCode
     * @return
     */
    private FundJBXXModel getFundJBXX(String fundCode) {
        FundJBXXModel result = null;
        try {
            if (!StringUtils.isNullOrEmpty(fundCode)) {
                if (fundJBXXList == null) {
                    fundJBXXList = new ArrayList<>();
                }
                result = fundJBXXList.stream().filter(p -> Objects.equals(p.FCODE, fundCode)).findFirst().orElse(null);
                if (result == null) {
                    List<FundJBXXModel> list = fundJbxxDao.getFundJBXX(fundCode);
                    if (list != null && list.size() > 0) {
                        result = list.stream().findFirst().orElse(null);
                        if (result != null) {
                            fundJBXXList.add(result);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            logger.error("PostPushDataSyncBiz.GetFundJBXX，基金：{}，发生异常：{}", fundCode, ex.getMessage(), ex);
        }
        return result;
    }

    /**
     * 获取基金持仓股票（Redis缓存）
     *
     * @param fundCode
     * @return
     */
    private List<FundHoldModel> getFundHold(String fundCode) {
        List<FundHoldModel> result = null;
        try {
            if (!StringUtils.isNullOrEmpty(fundCode)) {
                TypeReference<List<FundHoldModel>> typeReference = new TypeReference<List<FundHoldModel>>() {
                };
                result = JacksonUtil.string2Obj(app.hqredis.get("FundHold_" + fundCode), typeReference);
            }
        } catch (Exception ex) {
            logger.error("PostPushDataSyncBiz.GetFundJBXX，基金：{}，发生异常：{}", fundCode, ex.getMessage(), ex);
        }
        return result;
    }

    /**
     * 获取帖子扩展信息
     *
     * @param topicId
     * @return
     */
    private TopicDetailsForKafka getFundTopic(String topicId) {
        try {
            if (!StringUtils.isNullOrEmpty(topicId)) {
                return postInfoExtendDao.findOne(topicId);
            }
        } catch (Exception ex) {
            logger.error("PostPushDataSyncBiz.GetFundTopic，帖子ID：{}，发生异常：{}", topicId, ex.getMessage(), ex);
        }
        return null;
    }


    /**
     * 获取帖子扩展信息
     *
     * @param postId
     * @return
     */
    private List<FundPostExtrasProP> getPostInfoExtend(int postId) {
        try {
            if (postId > 0) {
                FundPostExtras extraInfo = postInfoExtendDao.findOne(postId);
                if (extraInfo != null && extraInfo.PROP != null && extraInfo.PROP.size() > 0) {
                    return extraInfo.PROP;
                }
            }
        } catch (Exception ex) {
            logger.error("PostPushDataSyncBiz.GetPostInfoExtend，帖子ID：{} 发生异常：{}", postId, ex.getMessage(), ex);
        }
        return null;
    }

    /**
     * 匹配内容并生成待处理关键字列表
     */
    private JSONObject getHaveKeyWord(String content, List<KeyWordModel> keywordList, int num, List<KeywordsEntity> sortKeywords) {

        JSONObject jo = new JSONObject();
        if (!StringUtils.isNullOrEmpty(content) && !CollectionUtils.isEmpty(sortKeywords)) {
            String label = "";
            String text = null;
            String subStr = null;
            String temp = null;
            boolean isSpecial;
            boolean isBad;

            Map<String, String> tabMap = new LinkedHashMap<>();
            int i = 0;
            String tabHoldPlace = null;
            Matcher tabMatcher = TAB_REGEX_PATTERN.matcher(content);
            while (tabMatcher.find()) {
                ++i;
                tabHoldPlace = MessageFormat.format(TAB_HOLD_PLACE, i);
                tabMap.put(tabHoldPlace, tabMatcher.group(0));
                content = content.replace(tabMatcher.group(0), tabHoldPlace);
            }

            for (KeywordsEntity item : sortKeywords) {

                if (content.length() < item.Name.length()) {
                    continue;
                }

                if (content.contains(item.Name)) {

                    isSpecial = false;
                    isBad = false;

                    text = item.Name;
                    subStr = item.Name;

                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}[{1}]$", item.Name, item.Code);
                        if (content.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}(OTCFUND{1})$", item.Name, item.Code);
                        if (content.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}(OTCFUND|{1})$", item.Name, item.Code);
                        if (content.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}({1})$", item.Name, item.Code);
                        if (content.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }

                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}$", item.Name);
                        if (content.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }

                    String safeName = item.Name.replaceAll("\\(", "\\\\(").replaceAll("\\)", "\\\\)");
                    if (!isSpecial && !isBad) {
                        temp = MessageFormat.format("\\$([^♫]*?){0}([^♫]*?)\\$", safeName);
                        Pattern pattern = Pattern.compile(temp);
                        Matcher matcher = pattern.matcher(content);
                        if (matcher.find()) {
                            isBad = true;
                        }
                    }

                    if (!isSpecial && !isBad) {
                        temp = MessageFormat.format("#([^♫]*?){0}([^♫]*?)#", safeName);
                        Pattern pattern = Pattern.compile(temp);
                        Matcher matcher = pattern.matcher(content);
                        if (matcher.find()) {
                            isBad = true;
                        }
                    }


                    if (!isSpecial && !isBad) {
                        temp = MessageFormat.format("@([\\u4e00-\\u9fa5|A-Za-z0-9]*?){0}", safeName);
                        Pattern pattern = Pattern.compile(temp);
                        Matcher matcher = pattern.matcher(content);
                        if (matcher.find()) {
                            isBad = true;
                        }
                    }

                    if (!isBad) {
                        label = MessageFormat.format(HOLDPLACE, num);
                        KeyWordModel keyWordModel = new KeyWordModel();
                        keyWordModel.Id = item.Id;
                        keyWordModel.Code = item.Code;
                        keyWordModel.Text = subStr;
                        keyWordModel.Type = item.Type;
                        keyWordModel.Lable = label;
                        keywordList.add(keyWordModel);
                        content = insertStr(content, label, content.indexOf(subStr), subStr.length());
                        num++;
                    }

                }
            }

            if (!CollectionUtils.isEmpty(tabMap)) {
                for (Map.Entry<String, String> entry : tabMap.entrySet()) {
                    content = content.replace(entry.getKey(), entry.getValue());
                }
            }


            jo.put("content", content);
            jo.put("list", keywordList);
            jo.put("number", num);

        }
        return jo;
    }

    /**
     * 替换指定区间字符串
     */
    private String insertStr(String originStr, String insertStr, int start, int length) {
        return originStr.substring(0, start) + insertStr + originStr.substring(start + length);
    }

    /**
     * 匹配内容并生成待处理关键字列表
     */
    public JSONObject getHaveKeyWord(String content, Map<Integer, List<String>> keywordLen2, Map<String, KeywordsEntity> keywordMap, List<KeyWordModel> keywordList, int num) {
        JSONObject jo = new JSONObject();
        if (!StringUtils.isNullOrEmpty(content) && keywordLen2 != null && keywordMap != null) {
            String label = null;
            //替换整型输出
            String holdPlace = "<!--adr%d-->";
            String tempContent = content;
            for (int key : keywordLen2.keySet()) {
                if (tempContent.length() >= key) {
                    List<String> temp1 = keywordLen2.get(key);
                    if (temp1 != null) {
                        for (String checkWord : temp1) {
                            if (tempContent.contains(checkWord) && keywordMap.containsKey(checkWord)) {
                                StringBuilder str = new StringBuilder();
                                for (int i = 0; i < key; i++) {
                                    str.append(" ");
                                }
                                tempContent = tempContent.replaceAll(checkWord, String.valueOf(str));

                                KeywordsEntity temp2 = keywordMap.get(checkWord);
                                if (temp2 != null) {
                                    Pattern p = Pattern.compile(MessageFormat.format("<[^>]*({0})+[^>]*?>", checkWord));
                                    Matcher m = p.matcher(content);
                                    if (!m.find()) {
                                        KeywordsEntity tempKeyword = keywordMap.get(checkWord);
                                        //String temp = MessageFormat.format("${0}[{1}]$", tempKeyword.Name, tempKeyword.Code);
                                        String temp = "$" + tempKeyword.Name + "[" + tempKeyword.Code + "]$";
                                        label = MessageFormat.format(holdPlace, num);
                                        KeyWordModel model = new KeyWordModel();
                                        model.Id = tempKeyword.Id;
                                        model.Code = tempKeyword.Code;
                                        model.Type = tempKeyword.Type;
                                        model.Lable = label;
                                        if (content.contains(temp)) {
                                            model.Text = temp;
                                            keywordList.add(model);
                                            content = content.replace(temp, label);
                                        } else {
                                            model.Text = tempKeyword.Name;
                                            keywordList.add(model);
                                            content = content.replace(checkWord, label);
                                        }
                                        num += 1;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        jo.put("content", content);
        jo.put("list", keywordList);
        jo.put("number", num);
        return jo;
    }


    /**
     * 获取基金类型 货币 3,理财 3,场内 1,高端 2,港基 4,开放 1
     * 仅基金对比时调用
     *
     * @return
     */
    private int fundUrlType(String fCode) {
        int result = 0;
        if (!StringUtils.isNullOrEmpty(fCode)) {
            FundInfoModel tempFundinfo = getFundAPPCode(fCode);
            if (tempFundinfo != null) {
                if (Objects.equals(tempFundinfo.T, "C"))//货币 T=C
                {
                    result = 3;
                } else if (Objects.equals(tempFundinfo.T, "F"))//理财T=F
                {
                    result = 3;
                } else if ((Objects.equals(tempFundinfo.T, "P")) || (Objects.equals(tempFundinfo.F, "E")))//场内T=P或F=E
                {
                    result = 1;
                } else if (Objects.equals(tempFundinfo.P, "G"))//高端P=G
                {
                    result = 2;
                } else if (Objects.equals(tempFundinfo.P, "H"))//港基P=H
                {
                    result = 4;
                } else //开放 非以上五种情况为开放
                {
                    result = 1;
                }
            }
        }
        return result;
    }


    /**
     * 获取基金数据（行情MongoDB）
     *
     * @param fundCode
     * @return
     */
    private FundInfoModel getFundAPPCode(String fundCode) {
        FundInfoModel result = null;
        try {
            if (!StringUtils.isNullOrEmpty(fundCode)) {
                if (fundInfoList == null) {
                    fundInfoList = new ArrayList<>();
                }
//               result=fundInfoList.stream().filter(p-> Objects.equals(p.FCODE, fundCode)).findFirst().get();
                if (fundInfoList.size() > 0) {
                    for (FundInfoModel fund : fundInfoList) {
                        if (Objects.equals(fund.FCODE, fundCode)) {
                            result = fund;
                            break;
                        }
                    }
                }
                if (result == null) {
                    List<FundInfoModel> list = marketMongoFundArchivesDao.getFAppCode(fundCode);
                    if (list != null) {
                        result = list.stream().findFirst().orElse(null);
                        if (result != null) {
                            fundInfoList.add(result);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("PostPushDataSyncBiz.GetFundAPPCode() 基金：{} 发生异常:{}", fundCode, e.getMessage(), e);
        }
        return result;
    }

    public Map<String, KeywordsEntity> getKeyWordDic() {
        Map<String, KeywordsEntity> result = null;
        String cacheKey = "Fund_JJB_Service_KeyWordsDic";
        result = CacheHelper.get(cacheKey);
        if (result == null) {
            result = keywordDao.getKeywordsDic();
            if (!CollectionUtils.isEmpty(result)) {
                CacheHelper.put(cacheKey, result, 1000 * 60 * 30L);
            }
        }
        if (result == null) {
            result = new HashMap<>();
        }
        return result;
    }

    /**
     * 处理跳转链接
     *
     * @param url
     * @param extendInfo
     * @return
     */
    private LinkModel getLinkModel(String url, String extendInfo) {
        //url=url.replace("&","\\u0026");
        LinkModel result = null;
        if (!StringUtils.isNullOrEmpty(extendInfo)) {
            LinkModel linkModel = JSONObject.parseObject(extendInfo, LinkModel.class);
            if (linkModel != null) {
                result = new LinkModel();
                result.setAdId(linkModel.getAdId());
                result.setLinkType(linkModel.getLinkType());
                result.setLinkTo(url);
            }
        }
        if (result == null) {
            result = new LinkModel();
            result.setAdId(0);
            result.setLinkTo(url);
            if (result.LinkTo != null) {
                result.setLinkType(result.getLinkTo().startsWith("http") ? 2 : 1);
            }
        }
        return result;
    }

    public TwoTuple<Integer, Integer> subStrBylength(String content, int fromIndex, int limit) {
        TwoTuple<Integer, Integer> result = new TwoTuple<>(fromIndex, fromIndex);

        int start = fromIndex - limit < 0 ? 0 : fromIndex - limit;
        int end = fromIndex + limit >= content.length() ? content.length() - 1 : fromIndex + limit;

        int i = fromIndex;
        while (true) {
            /*if (TERMINATOR_CHAR_LIST.contains(content.charAt(i))) {
                i++;
                break;
            }*/
            if (i <= start) {
                break;
            }
            i--;
        }
        start = i;

        i = fromIndex + 1;
        while (true) {
            if (i >= end) {
                break;
            }
            /*if (TERMINATOR_CHAR_LIST.contains(content.charAt(i))) {
                break;
            }*/
            i++;
        }
        end = i;

        result.first = start;
        result.second = end;

        return result;
    }

    /**
     * 预处理
     */
    public TwoTuple<String, Integer> preDealBeforeNlp(String content, int fromIndex, List<KeyWordModel> keyWordModelList) {
        TwoTuple<String, Integer> result = new TwoTuple<>(content, fromIndex);
        if (!CollectionUtils.isEmpty(keyWordModelList)) {
            int tempIndex = 0;
            for (KeyWordModel item : keyWordModelList) {
                tempIndex = content.indexOf(item.Lable);
                if (tempIndex > 0) {
                    if (tempIndex < fromIndex) {
                        fromIndex += (item.Text.length() - item.Lable.length());
                    }
                    content = content.replace(item.Lable, item.Text);
                }

            }
        }
        result.first = content;
        result.second = fromIndex;
        return result;
    }


    /**
     * 匹配内容并生成待处理关键字列表
     * 注：引入nlp词性标注
     */
    private JSONObject getHaveKeyWordWithNlp(String content, List<KeyWordModel> keywordList, int num, List<KeywordsEntity> sortKeywords) {

        JSONObject jo = new JSONObject();
        if (!StringUtils.isNullOrEmpty(content) && !CollectionUtils.isEmpty(sortKeywords)) {
            String label = "";
            String text = null;
            String subStr = null;
            String temp = null;
            boolean isSpecial;
            boolean isBad;

            Map<String, String> tabMap = new LinkedHashMap<>();
            int i = 0;
            String tabHoldPlace = null;
            Matcher tabMatcher = TAB_REGEX_PATTERN.matcher(content);
            while (tabMatcher.find()) {
                ++i;
                tabHoldPlace = MessageFormat.format(TAB_HOLD_PLACE, i);
                tabMap.put(tabHoldPlace, tabMatcher.group(0));
                content = content.replace(tabMatcher.group(0), tabHoldPlace);
            }

            int fromIndex = 0;
            int matchIndex = -1;
            boolean isForce = false;
            int fromIndexCandidate = -1;
            int matchIndexCandidate = -1;
            for (KeywordsEntity item : sortKeywords) {

                if (content.length() < item.Name.length()) {
                    continue;
                }

                fromIndex = 0;
                matchIndex = -1;
                isForce = false;
                fromIndexCandidate = -1;
                matchIndexCandidate = -1;

                while (true) {

                    if (fromIndex >= content.length()) {
                        break;
                    }

                    fromIndex = content.indexOf(item.Name, fromIndex);

                    if (fromIndex < 0) {
                        if (fromIndexCandidate != -1) {
                            fromIndex = fromIndexCandidate;
                            matchIndex = matchIndexCandidate - 1;
                            isForce = true;
                        } else {
                            break;
                        }
                    }

                    matchIndex++;

                    isSpecial = false;
                    isBad = false;

                    text = item.Name;
                    subStr = item.Name;

                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}[{1}]$", item.Name, item.Code);
                        if (content.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}(OTCFUND{1})$", item.Name, item.Code);
                        if (content.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}(OTCFUND|{1})$", item.Name, item.Code);
                        if (content.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }
                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}({1})$", item.Name, item.Code);
                        if (content.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }

                    if (!isSpecial) {
                        temp = MessageFormat.format("${0}$", item.Name);
                        if (content.contains(temp)) {
                            isSpecial = true;
                            text = "$" + item.Name + "$";
                            subStr = temp;
                        }
                    }

                    String safeName = item.Name.replaceAll("\\(", "\\\\(").replaceAll("\\)", "\\\\)");
                    if (!isSpecial && !isBad) {
                        temp = MessageFormat.format("\\$([^♫]*?){0}([^♫]*?)\\$", safeName);
                        Pattern pattern = Pattern.compile(temp);
                        Matcher matcher = pattern.matcher(content);
                        if (matcher.find()) {
                            isBad = true;
                        }
                    }

                    if (!isSpecial && !isBad) {
                        temp = MessageFormat.format("#([^♫]*?){0}([^♫]*?)#", safeName);
                        Pattern pattern = Pattern.compile(temp);
                        Matcher matcher = pattern.matcher(content);
                        if (matcher.find()) {
                            isBad = true;
                        }
                    }


                    if (!isSpecial && !isBad) {
                        temp = MessageFormat.format("@([\\u4e00-\\u9fa5|A-Za-z0-9]*?){0}", safeName);
                        Pattern pattern = Pattern.compile(temp);
                        Matcher matcher = pattern.matcher(content);
                        if (matcher.find()) {
                            isBad = true;
                        }
                    }

                    if (!isBad) {

                        if (!isSpecial && item.Type == 17) {

                            if (!isForce) {
                                //nlp词性标注
                                TwoTuple<Integer, Integer> subStrBylength = subStrBylength(content, fromIndex, 50);
                                String subContent = content.substring(subStrBylength.first, subStrBylength.second);
                                TwoTuple<String, Integer> preDealBeforeNlp = preDealBeforeNlp(subContent, fromIndex - subStrBylength.first, keywordList);
                                int analyzeRes = keywordHanlpService.analyze(preDealBeforeNlp.first, preDealBeforeNlp.second, item);

                                logger.info("nlp处理，结果：{}。内容：{}，位置：{}，关键字：{}", analyzeRes, preDealBeforeNlp.first, preDealBeforeNlp.second, JSON.toJSONString(item));
                                if (analyzeRes == 0) {
                                    fromIndex = fromIndex + item.Name.length();
                                    continue;
                                }
                                if (analyzeRes == 2) {
                                    if (fromIndexCandidate != -1) {
                                        fromIndexCandidate = fromIndex;
                                        matchIndexCandidate = matchIndex;
                                        isForce = true;
                                    }
                                    fromIndex = fromIndex + item.Name.length();
                                    continue;
                                }
                            }

                            label = MessageFormat.format(HOLDPLACE, num);
                            KeyWordModel keyWordModel = new KeyWordModel();
                            keyWordModel.Id = item.Id;
                            keyWordModel.Code = item.Code;
                            keyWordModel.Text = subStr;
                            keyWordModel.Type = item.Type;
                            keyWordModel.Lable = label;
                            keyWordModel.MatchIndex = matchIndex;
                            keywordList.add(keyWordModel);
                            content = insertStr(content, label, content.indexOf(subStr, fromIndex), subStr.length());
                            num++;

                        } else {
                            label = MessageFormat.format(HOLDPLACE, num);
                            KeyWordModel keyWordModel = new KeyWordModel();
                            keyWordModel.Id = item.Id;
                            keyWordModel.Code = item.Code;
                            keyWordModel.Text = subStr;
                            keyWordModel.Type = item.Type;
                            keyWordModel.Lable = label;
                            keyWordModel.MatchIndex = matchIndex;
                            keywordList.add(keyWordModel);
                            content = insertStr(content, label, content.indexOf(subStr), subStr.length());
                            num++;
                        }
                    }

                    break;
                }
            }

            if (!CollectionUtils.isEmpty(tabMap)) {
                for (Map.Entry<String, String> entry : tabMap.entrySet()) {
                    content = content.replace(entry.getKey(), entry.getValue());
                }
            }


            jo.put("content", content);
            jo.put("list", keywordList);
            jo.put("number", num);

        }
        return jo;
    }

}
