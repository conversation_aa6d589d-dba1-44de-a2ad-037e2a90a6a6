package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoByDailyCount;
import ttfund.web.communityservice.dao.msyql.BarPostCountDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 帖子数量统计mysql按天-增量
 */
@JobHandler("PostCountByCodeDaliyIncrementJob")
@Component
public class PostCountByCodeDaliyIncrementJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(PostCountByCodeDaliyIncrementJob.class);

    /**
     * 计算时间
     * UpdateTime：    最新计算时间
     * HisPostCount：  0：回溯状态  1：最新状态
     */
    public static final String FIELD_CALCULATETIME = "field_calculate_time";

    /**
     * 合并时间
     * UpdateTime：    最新合并时间 -合并完成
     */
    public static final String FIELD_MERGE_FINISH_TIME = "field_merge_finish_time";

    /**
     * 合并时间
     * UpdateTime：    最新合并时间 -合并未完成
     * HisPostCount：  0：进行中  1：已完成
     */
    public static final String FIELD_MERGE_NOT_FINISH_TIME = "field_merge_not_finish_time";

    @Autowired
    private BarPostCountDao barPostCountDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            Integer backTime = null;//回溯时间        负值
            Integer mergeInterval = null;//合并周期   正值
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                backTime = jsonObject.getInteger("backTime");
                mergeInterval = jsonObject.getInteger("mergeInterval");
            }

            if (backTime == null) {
                backTime = -3 * 60;
            }
            if (mergeInterval == null) {
                mergeInterval = 5 * 24 * 3600;
            }

            logger.info("第零步，打印参数。backTime：{}，mergeInterval：{}",
                backTime,
                mergeInterval
            );

            logicDeal(backTime, mergeInterval);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    public void logicDeal(int backTime, int mergeInterval) {

        Date now = new Date();
        Date end = DateUtil.calendarDateBySecond(now, backTime);

        PostInfoByDailyCount calculateTimeInfo = barPostCountDao.getById(FIELD_CALCULATETIME);

        logger.info("第一步，读取上次计算信息。数据：{}", JSON.toJSONStringWithDateFormat(calculateTimeInfo, DateUtil.datePattern));

        PostInfoByDailyCount mergeFinishTimeInfo = barPostCountDao.getById(FIELD_MERGE_FINISH_TIME);
        Date mergeFinishTime = mergeFinishTimeInfo.UpdateTime;

        logger.info("第二步，读取上次合并完成信息。数据：{}", JSON.toJSONStringWithDateFormat(mergeFinishTimeInfo, DateUtil.datePattern));

        PostInfoByDailyCount mergeNotFinishTimeInfo = barPostCountDao.getById(FIELD_MERGE_NOT_FINISH_TIME);
        Date mergeNotFinishTime = mergeNotFinishTimeInfo.UpdateTime;

        logger.info("第三步，读取上次合并未完成信息。数据：{}", JSON.toJSONStringWithDateFormat(mergeNotFinishTimeInfo, DateUtil.datePattern));

        boolean isLastMergeNotFinish = false;
        if (mergeNotFinishTimeInfo.HisPostCount < 1) {
            isLastMergeNotFinish = true;
        }
        logger.info("第四步-1，判断上次合并是否完成。isLastMergeNotFinish：{}", isLastMergeNotFinish);

        if (isLastMergeNotFinish) {

            int totalCount = 0;
            int updateCount = 0;
            do {
                updateCount = barPostCountDao.mergeByUpdateTimeInterval(mergeFinishTime, mergeNotFinishTime, end, 1000);
                totalCount += updateCount;
            } while (updateCount != 0);

            logger.info("第四步-2，写库继续完成上次合并。mergeFinishTime：{}，mergeNotFinishTime：{}，总数量：{}",
                DateUtil.dateToStr(mergeFinishTime),
                DateUtil.dateToStr(mergeNotFinishTime),
                totalCount
            );

            PostInfoByDailyCount model = new PostInfoByDailyCount();
            model.Id = FIELD_MERGE_FINISH_TIME;
            model.Code = FIELD_MERGE_FINISH_TIME;
            model.PostCount = 0;
            model.HisPostCount = 0;
            model.UpdateTime = mergeNotFinishTime;
            barPostCountDao.upsert(Arrays.asList(model));

            logger.info("第四步-3，写库更新合并完成信息。数据：{}", JSON.toJSONStringWithDateFormat(model, DateUtil.datePattern));

            PostInfoByDailyCount model1 = new PostInfoByDailyCount();
            model1.Id = FIELD_MERGE_NOT_FINISH_TIME;
            model1.Code = FIELD_MERGE_NOT_FINISH_TIME;
            model1.PostCount = 0;
            model1.HisPostCount = 1;
            model1.UpdateTime = mergeNotFinishTime;
            barPostCountDao.upsert(Arrays.asList(model1));

            logger.info("第四步-4，写库更新合并未完成信息。数据：{}", JSON.toJSONStringWithDateFormat(model1, DateUtil.datePattern));

            return;
        }

        boolean onMergeTime = (now.getTime() - mergeFinishTime.getTime()) / 1000 - mergeInterval > 0;

        logger.info("第五步-1，判断是否处在合并时间。onMergeTime：{}", onMergeTime);

        if (onMergeTime) {

            Date newMergeTime = DateUtil.calendarDateBySecond(now, -mergeInterval);
            StringBuilder builder = new StringBuilder();
            try {
                calculatePostCount(builder, mergeFinishTime, newMergeTime, true);
            } catch (Exception ex) {
                logger.error("第五步-2，计算并更新合并区间内帖子数量。详情：{}", builder.toString());
                throw ex;
            }

            logger.info("第五步-2，计算并更新合并区间内帖子数量。详情：{}", builder.toString());

            PostInfoByDailyCount model = new PostInfoByDailyCount();
            model.Id = FIELD_MERGE_NOT_FINISH_TIME;
            model.Code = FIELD_MERGE_NOT_FINISH_TIME;
            model.PostCount = 0;
            model.HisPostCount = 0;
            model.UpdateTime = newMergeTime;
            barPostCountDao.upsert(Arrays.asList(model));

            logger.info("第五步-3，写库更新合并未完成信息。数据：{}", JSON.toJSONStringWithDateFormat(model, DateUtil.datePattern));

            int totalCount = 0;
            int updateCount = 0;
            do {
                updateCount = barPostCountDao.mergeByUpdateTimeInterval(mergeFinishTime, newMergeTime, end, 1000);
                totalCount += updateCount;
            } while (updateCount != 0);

            logger.info("第五步-4，写库合并。start：{}，end：{}，总数量：{}", DateUtil.dateToStr(mergeFinishTime), DateUtil.dateToStr(newMergeTime), totalCount);

            PostInfoByDailyCount model1 = new PostInfoByDailyCount();
            model1.Id = FIELD_MERGE_FINISH_TIME;
            model1.Code = FIELD_MERGE_FINISH_TIME;
            model1.PostCount = 0;
            model1.HisPostCount = 0;
            model1.UpdateTime = newMergeTime;
            barPostCountDao.upsert(Arrays.asList(model1));

            logger.info("第五步-5，写库更新合并完成信息。数据：{}", JSON.toJSONStringWithDateFormat(model1, DateUtil.datePattern));

            PostInfoByDailyCount model2 = new PostInfoByDailyCount();
            model2.Id = FIELD_MERGE_NOT_FINISH_TIME;
            model2.Code = FIELD_MERGE_NOT_FINISH_TIME;
            model2.PostCount = 0;
            model2.HisPostCount = 1;
            model2.UpdateTime = newMergeTime;
            barPostCountDao.upsert(Arrays.asList(model2));

            logger.info("第五步-6，写库更新合并未完成信息。数据：{}", JSON.toJSONStringWithDateFormat(model2, DateUtil.datePattern));

            return;
        }

        {
            StringBuilder builder = new StringBuilder();
            try {
                calculatePostCount(builder, mergeFinishTime, end, false);
            } catch (Exception ex) {
                logger.error("第六步-1，计算并更新合并区间内帖子数量。详情：{}", builder.toString());
                throw ex;
            }

            logger.info("第六步-1，计算并更新合并区间内帖子数量。详情：{}", builder.toString());
        }

    }


    private void calculatePostCount(StringBuilder builder, Date start, Date end, boolean isBack) {
        List<Map<String, Object>> list = postInfoNewDao.getPostCountByTimePeriod(start, end);

        builder.append(String.format("\n1，读取区间吧帖子数量数据。start：%s，end：%s，数量:%s，头部数据列表：%s",
            DateUtil.dateToStr(start),
            DateUtil.dateToStr(end),
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern))
        );

        if (!CollectionUtils.isEmpty(list)) {
            List<PostInfoByDailyCount> upsertList = list
                .stream()
                .map(a -> {
                    PostInfoByDailyCount model = new PostInfoByDailyCount();
                    model.Id = a.get("CODE").toString();
                    model.Code = a.get("CODE").toString();
                    model.PostCount = Long.parseLong(a.get("COUNT").toString());
                    model.HisPostCount = 0;
                    model.UpdateTime = end;

                    return model;
                })
                .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(upsertList)) {
                List<List<PostInfoByDailyCount>> batchList = CommonUtils.toSmallList2(upsertList, 100);
                for (List<PostInfoByDailyCount> batch : batchList) {
                    barPostCountDao.upsertPostCount(batch);
                }
            }
        }

        builder.append(String.format("\n2，写库吧帖子数量数据。数量:%s，头部数据列表：%s",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern))
        );

        PostInfoByDailyCount model = new PostInfoByDailyCount();
        model.Id = FIELD_CALCULATETIME;
        model.Code = FIELD_CALCULATETIME;
        model.PostCount = 0;
        model.HisPostCount = (isBack ? 0 : 1);
        model.UpdateTime = end;
        barPostCountDao.upsert(Arrays.asList(model));

        builder.append(String.format("\n3，写库更新计算时间。数据：%s", JSON.toJSONStringWithDateFormat(model, DateUtil.datePattern)));

    }

}
