package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumPostType;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoModel;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.msyql.ZnhdQaDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 更新历史帖子job
 */
@JobHandler("UpdateHisPostJob")
@Component
public class UpdateHisPostJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(UpdateHisPostJob.class);

    private static final String CACHE_KEY = "UpdateHisPostJob_breakpoint";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private ZnhdQaDao znhdQaDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.isNotEmpty(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                initBreakpoint,
                batchReadCount);

            if (StringUtils.isNotEmpty(initBreakpoint)) {

                userRedisDao.set(CACHE_KEY, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            logicDeal(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    public void logicDeal(int batchReadCount) {
        try {
            /**
             * 帖子删除后基金吧管理后台和财富号后台都需要删除
             */

            String breakpoint = userRedisDao.get(CACHE_KEY);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("第一步，读取断点。断点:{}", breakpoint);

            //获取基金吧提问的删除的帖子，根据帖子ID删除对应数据
            List<PostInfoModel> list = postInfoNewDao.getList(breakpointDate, EnumPostType.QAQuestion.getValue(), 1, batchReadCount);

            logger.info("第二步，读取数据。数量:{}，头部id列表：{}",
                list == null ? 0 : list.size(),
                list == null ? null : list.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));


            if (!CollectionUtils.isEmpty(list)) {

                breakpointDate = list.stream().max((Comparator.comparing(o -> o.UPDATETIME))).get().UPDATETIME;

                List<Integer> postIdList = list.stream().map(a -> a.ID).collect(Collectors.toList());

                List<List<Integer>> batchList = CommonUtils.toSmallList2(postIdList, 200);
                for (List<Integer> batch : batchList) {
                    znhdQaDao.updateDel(batch, 1);
                }

                logger.info("第三步，数据写库。数量:{}，头部id列表：{}",
                    postIdList.size(),
                    postIdList.stream().limit(20).collect(Collectors.toList()));

            }

            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(CACHE_KEY, breakpoint, 60 * 24 * 3600L);

            logger.info("第四步，更新断点。断点：{}", breakpoint);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

}
