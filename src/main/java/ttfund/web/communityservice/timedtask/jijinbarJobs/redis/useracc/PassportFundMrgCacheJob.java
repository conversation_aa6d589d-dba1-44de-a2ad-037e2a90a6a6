package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.useracc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.mongo.PassportFundMrgModel;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.PassportFundMrgDao;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基金经理-通行证关系
 */
@JobHandler(value = "passportFundMrgCacheJob")
@Component
public class PassportFundMrgCacheJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(PassportFundMrgCacheJob.class);

    @Autowired
    private PassportFundMrgDao passportFundMrgDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PassportUserInfoDao passportUserInfoDao;

    @Override
    public ReturnT<String> execute(String s) {

        try {

            String initBreakpoint = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
            }

            logger.info("第零步，打印参数。initBreakpoint：{}", initBreakpoint);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.PASSPORTFUNDMRGCACHEJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            logicDeal();

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        return ReturnT.SUCCESS;
    }

    private void logicDeal() {

        try {

            String breakpointName = UserRedisConfig.PASSPORTFUNDMRGCACHEJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            List<PassportFundMrgModel> list = passportFundMrgDao.getAll();

            logger.info("第一步，读取数据。数量:{}，头部数据：{}",
                    CollectionUtils.isEmpty(list) ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

            if (!CollectionUtils.isEmpty(list)) {

                Date finalLastTime = breakpointDate;
                list = list.stream()
                        .filter(a -> a.UpdateTime.compareTo(finalLastTime) > 0)
                        .collect(Collectors.toList());

                logger.info("第二步，筛选数据。数量:{}，头部数据：{}",
                        CollectionUtils.isEmpty(list) ? 0 : list.size(),
                        CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
                );

                Date tempDate = list.stream().map(o -> o.UpdateTime).max(Date::compareTo).orElse(null);
                if (tempDate != null) {
                    breakpointDate = tempDate;
                }

                //需要删除的数据，删除绑定关系时 删除基金经理的绑定关系
                List<PassportFundMrgModel> delList = list.stream()
                        .filter(a -> a.IsDel == 1)
                        .collect(Collectors.toList());

                logger.info("第三步，筛选删除数据。数量:{}，头部数据：{}",
                        CollectionUtils.isEmpty(delList) ? 0 : delList.size(),
                        CollectionUtils.isEmpty(delList) ? null : JSON.toJSONStringWithDateFormat(delList.get(0), DateUtil.datePattern)
                );

                if (!CollectionUtils.isEmpty(delList)) {
                    Map<String, Object> tempMap = null;
                    int i = 0;
                    for (PassportFundMrgModel item : delList) {
                        i++;
                        boolean mongdbRes = false;

                        PassportUserInfoModelNew oldModel = passportUserInfoDao.getPassportUserInfoById(item.PassportUID);
                        if (oldModel != null) {

                            item.MGRID = "";
                            item.MGRName = "";
                            item.JJGSID = "";

                            oldModel.MGRID = "";
                            oldModel.MGRName = "";
                            oldModel.JJGSID = "";

                            tempMap = new HashMap<>();
                            tempMap.put("_id", item.PassportUID);
                            tempMap.put("MGRID", item.MGRID);
                            tempMap.put("MGRName", item.MGRName);
                            tempMap.put("JJGSID", item.JJGSID);

                            mongdbRes = passportUserInfoDao.upsertMany(Arrays.asList(tempMap), "_id");
                            if (mongdbRes) {
                                // 如果mysql写成功则直接写入redis
                                String cacheKey = String.format(UserRedisConfig.Asp_Net_Fund_Service_Passport_Info_pid, oldModel.PassportID);
                                userRedisDao.set(cacheKey, JacksonUtil.obj2String(oldModel), 720 * 24 * 3600L);
                            }
                        }

                        logger.info("第四步，删除详情。第{}/{}个。id：{}，写mongo：{}，写redis：{}",
                                i,
                                delList.size(),
                                item._id,
                                oldModel != null,
                                mongdbRes
                        );

                    }
                }

                logger.info("第四步，删除完成。数量:{}，头部数据：{}",
                        CollectionUtils.isEmpty(delList) ? 0 : delList.size(),
                        CollectionUtils.isEmpty(delList) ? null : JSON.toJSONStringWithDateFormat(delList.get(0), DateUtil.datePattern)
                );

                //需更更新的数据
                List<PassportFundMrgModel> updateList = list.stream()
                        .filter(a -> a.IsDel == 0)
                        .collect(Collectors.toList());

                logger.info("第五步，筛选更新数据。数量:{}，头部数据：{}",
                        CollectionUtils.isEmpty(updateList) ? 0 : updateList.size(),
                        CollectionUtils.isEmpty(updateList) ? null : JSON.toJSONStringWithDateFormat(updateList.get(0), DateUtil.datePattern)
                );

                if (!CollectionUtils.isEmpty(updateList)) {
                    int i = 0;
                    Map<String, Object> tempMap = null;

                    for (PassportFundMrgModel item : updateList) {
                        i++;

                        boolean mongdbRes = false;

                        PassportUserInfoModelNew oldModel = passportUserInfoDao.getPassportUserInfoById(item.PassportUID);
                        if (oldModel != null) {

                            oldModel.MGRID = item.MGRID;
                            oldModel.MGRName = item.MGRName;
                            oldModel.JJGSID = item.JJGSID;

                            tempMap = new HashMap<>();
                            tempMap.put("_id", item.PassportUID);
                            tempMap.put("MGRID", item.MGRID);
                            tempMap.put("MGRName", item.MGRName);
                            tempMap.put("JJGSID", item.JJGSID);


                            mongdbRes = passportUserInfoDao.upsertMany(Arrays.asList(tempMap), "_id");
                            if (mongdbRes) {
                                //如果mysql写成功则直接写入redis
                                String cacheKey = String.format(UserRedisConfig.Asp_Net_Fund_Service_Passport_Info_pid, oldModel.PassportID);
                                userRedisDao.set(cacheKey, JacksonUtil.obj2String(oldModel), 720 * 24 * 3600L);
                            }
                        }

                        logger.info("第六步，更新详情。第{}/{}个。id：{}，写mongo：{}，写redis：{}",
                                i,
                                updateList.size(),
                                item._id,
                                oldModel != null,
                                mongdbRes
                        );
                    }
                }

                logger.info("第六步，更新完成。数量:{}，头部数据：{}",
                        CollectionUtils.isEmpty(updateList) ? 0 : updateList.size(),
                        CollectionUtils.isEmpty(updateList) ? null : JSON.toJSONStringWithDateFormat(updateList.get(0), DateUtil.datePattern)
                );

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("第七步，更新断点。断点：{}", breakpoint);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

}
