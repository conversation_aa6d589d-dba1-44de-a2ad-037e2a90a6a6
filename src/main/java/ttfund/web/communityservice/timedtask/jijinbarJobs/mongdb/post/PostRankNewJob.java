package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.PostFundRelation;
import ttfund.web.communityservice.bean.jijinBar.post.PostRankNewCountModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostRankNewModel;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.constant.DateConstant;
import ttfund.web.communityservice.dao.mongo.PostRankNewDao;
import ttfund.web.communityservice.dao.msyql.PostFundRelationDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Zhuyuang
 * @date : 2024-11-08 13:56
 * @description : 智能排序帖子列表
 */
@JobHandler("PostRankNewJob")
@Component
public class PostRankNewJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ElitePostJob.class);

    private static final String LOG_PREFIX = "PostRankNewJob[智能排序帖子列表]";

    private static final String CACHE_KEY = "POST_RANK_NEW_JOB_UPDATE_TIME";

    private static final int BATCH_SIZE = 20000;

    private static final String WEIGHT_CONFIG = "1,1.5,50000,0.08,0.04,3,5";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostFundRelationDao postFundRelationDao;

    @Autowired
    private PostInfoExtraDao postInfoExtraDao;

    @Autowired
    private PostRankNewDao postRankNewDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        LogicDeal(s);
        return ReturnT.SUCCESS;
    }

    private void LogicDeal(String s) {
        try {
            String breakpoint = StringUtils.isEmpty(s) ? userRedisDao.get(CACHE_KEY) : s;
            Date dateForBreakpoint = StringUtils.isNotEmpty(breakpoint) ? DateUtil.strToDate(breakpoint, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT) : DateUtil.calendarDateByDays(-1);

            //只更新近一年的发帖
            List<PostRankNewCountModel> list = postInfoExtraDao.getPostExtra2(dateForBreakpoint, DateUtil.calendarDateByYears(-1), BATCH_SIZE);
            LOGGER.info("{}: 本轮读取数量:{}", LOG_PREFIX,  list.size());
            if (CollectionUtils.isNotEmpty(list)) {
                dateForBreakpoint = list.stream().
                    max((Comparator.comparing(PostRankNewCountModel::getUPDATETIME))).
                    get().getUPDATETIME();
                List<PostRankNewModel> postRankList = new ArrayList<>();
                LOGGER.info("{}: 本轮删除数量: {}", LOG_PREFIX,  list.stream().
                    filter(model -> model.getDEL() != 0).
                    count());

                for (PostRankNewCountModel item : list) {
                    if (item.getDEL() != 0 || item.getTTJJDEL() != 0) {
                        //直接删除数据
                        postRankNewDao.removeById(item.get_id(), BarMongodbConfig.TABLE_POSTRANKNEW);
                    } else {
                        //转发吧，财经评论吧 过滤
                        String[] filterCodes = new String[] {"zf", "cjpl"};
                        List<String> listCode = multiBar(item.getCODELISTSTR(), item.getCODE());

                        //判断是否是财富号文章，如果是财富号文章，增加关联基金吧
                        if (StringUtils.isNotEmpty(item.getCODE()) &&
                            (item.getCODE().equalsIgnoreCase("cfhpl") ||
                                item.getCODE().equalsIgnoreCase("jjspzh"))) {
                            List<PostFundRelation> relationFundList = postFundRelationDao.getByPostId(String.valueOf(item.getID()));
                            if (CollectionUtils.isNotEmpty(relationFundList)) {
                                for (PostFundRelation relationItem : relationFundList) {
                                    if (relationItem.FType == 43) {
                                        relationItem.FCode = "43-" + relationItem.FCode;
                                    } else if (relationItem.FType == 1) {
                                        relationItem.FCode = "58-" + relationItem.FCode.toLowerCase();
                                    }
                                    listCode.add(relationItem.FCode);
                                }
                            }
                        }

                        listCode = listCode.stream().
                            filter(code -> !Arrays.asList(filterCodes).contains(code)).
                            collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(listCode)) {
                            item.setCODELIST(listCode);
                            item.setRANKSCORE(CalRankScore(item));

                            PostRankNewModel newModel = ConverEntity(item);
                            if (newModel != null) {
                                postRankList.add(newModel);
                            }
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(postRankList)) {
                    boolean result = postRankNewDao.upsertMany(postRankList, "_id");
                    if (result) {
                        LOGGER.info("{}: 本轮保存数量:{}", LOG_PREFIX, postRankList.size());
                        userRedisDao.setBreakTime(CACHE_KEY, dateForBreakpoint);
                    }
                }
            }
        }
        catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
        }
    }

    private PostRankNewModel ConverEntity(PostRankNewCountModel model) {
        if (model == null) return null;
        PostRankNewModel rankModel = new PostRankNewModel();
        rankModel.setCODE(model.getCODE());
        rankModel.setCODELIST(model.getCODELIST());
        rankModel.setDEL(model.getDEL());
        rankModel.setID(model.getID());
        rankModel.setNEWSID(model.getNEWSID());
        rankModel.setRANKSCORE(model.getRANKSCORE());
        rankModel.setTIME(model.getTIME());
        rankModel.setTIMEPOINT(model.getTIMEPOINT());
        rankModel.setTYPE(model.getTYPE());
        rankModel.setUID(model.getUID());
        rankModel.setUPDATETIME(new Date());
        rankModel.setTTJJDEL(model.getTTJJDEL());
        return rankModel;
    }

    private double CalRankScore(PostRankNewCountModel model) {
        double score = 0;
        long now = new Date().getTime();
        if (model != null) {
            String[] configs = WEIGHT_CONFIG.split(",");

            score = model.getPINGLUNNUM() * Double.parseDouble(configs[0])
                + model.getLIKECOUNT() * Double.parseDouble(configs[1])
                + model.getCLICKNUM() / Double.parseDouble(configs[2])
                - ((double)(now - model.getTIME().getTime())/(3600 * 1000L) * Double.parseDouble(configs[3]))
            - ((double)(now - model.getPOSTUPDATETIME().getTime()) / (3600 * 1000L) * Double.parseDouble(configs[4]));
            //LogHelper.LogInfo("涉及帖子，计算得分:" + score, LogFileName);
        }
        return score;
    }

    private List<String> multiBar(String codeList, String barCode) {
        Set<String> listCode = new HashSet<>();
        if (StringUtils.isNotEmpty(barCode)) {
            listCode.add(barCode);
        }

        if (StringUtils.isNotEmpty(codeList)) {
            String[] codeArr = codeList.split(",");
            for (String code : codeArr) {
                String tempCode = code.toLowerCase();
                String startStr = "of";
                 //判断是否是普通基金代码
                if (tempCode.startsWith(startStr) && !listCode.contains(tempCode)) {
                    listCode.add(tempCode.replace(startStr, ""));
                }
            }
        }
        return new ArrayList<>(listCode);
    }
}
