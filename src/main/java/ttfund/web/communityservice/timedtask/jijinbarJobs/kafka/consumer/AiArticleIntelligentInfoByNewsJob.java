package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.api.TtAgentPostSummaryHighlightResponse;
import ttfund.web.communityservice.bean.news.CmsArticleKafkaRecord;
import ttfund.web.communityservice.bean.news.CmsInfoKafkaModel;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.mongo.ArticleIntelligentInfoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.dao.vertica.UserContentPreferenceTagDao;
import ttfund.web.communityservice.service.TtAgentApiServiceImpl;
import ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post.AiArticleIntelligentInfoByPostJob;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * AI文章智能信息job  资讯
 */
@Slf4j
@Component
public class AiArticleIntelligentInfoByNewsJob {

    public static final String KAFKA_LISTENER_ID = "AiArticleIntelligentInfoByNewsJob";

    public static final List<String> SET_ON_INSERT_FIELDS = Arrays.asList("createTime");

    //html标签
    private static final String TAB_REGEX = "<[^>]*?>";
    private static final Pattern TAB_REGEX_PATTERN = Pattern.compile(TAB_REGEX);

    private static final Long expireTime = 60 * 60 * 24 * 7L;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private TtAgentApiServiceImpl ttAgentApiService;

    @Autowired
    private ArticleIntelligentInfoDao articleIntelligentInfoDao;

    @Autowired
    private AiArticleIntelligentInfoByPostJob aiArticleIntelligentInfoByPostJob;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private UserContentPreferenceTagDao userContentPreferenceTagDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.NP_PUSH_FUND_CMS}
        , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_AIARTICLEINTELLIGENTINFOBYNEWSJOB + "}"
        , containerFactory = KafkaConfig.KAFKA_LISTENER_CONTAINER_FACTORY_CMS)
    private void onListen(ConsumerRecord<String, String> record) {
        deal(record);
    }

    private void deal(ConsumerRecord<String, String> record) {
        try {

            log.info(String.format("1.打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            CmsArticleKafkaRecord kafkaRecord = JacksonUtil.string2Obj(record.value(), CmsArticleKafkaRecord.class);
            CmsInfoKafkaModel cmsInfo = kafkaRecord.getCmsInfo();

            List<String> columns = CommonUtils.toList(cmsInfo.getArtColumnList(), ",");

            String content = null;
            boolean need = true;
            if (need) {
                if (!CollectionUtils.isEmpty(appConstant.articleIntelligentInfoColumn)) {
                    need = false;
                    if (!CollectionUtils.isEmpty(columns)) {
                        for (String column : columns) {
                            if (appConstant.articleIntelligentInfoColumn.contains(column)) {
                                need = true;
                                break;
                            }
                        }
                    }
                }
            }

            if (need) {

                content = cmsInfo.getArtContent();
                Matcher tabMatcher = TAB_REGEX_PATTERN.matcher(content);
                while (tabMatcher.find()) {
                    content = content.replace(tabMatcher.group(0), "");
                }

                if (appConstant.articleIntelligentInfoByNewsCharCount != null
                    && appConstant.articleIntelligentInfoByNewsCharCount > content.length()) {
                    need = false;
                }

            }

            if (need) {
                List<Document> tempList = articleIntelligentInfoDao.getManyByKeyInValue("_id", Arrays.asList(cmsInfo.getArtCode()), Arrays.asList("_id"), Document.class);
                if (!CollectionUtils.isEmpty(tempList)) {
                    need = false;
                }
            }

            //在内容里添加10个用户最关注主题
            String hotTags = userRedisDao.get(UserRedisConfig.USERCONTENTPREFERENCETAG_HOT10TAGS);
            if (!StringUtils.hasLength(hotTags)) {
                List<String> hot10Tags = userContentPreferenceTagDao.getHot10Tags();
                if (!CollectionUtils.isEmpty(hot10Tags)) {
                    hotTags = String.join(",", hot10Tags);
                    userRedisDao.set(UserRedisConfig.USERCONTENTPREFERENCETAG_HOT10TAGS, hotTags, expireTime);
                }
            }

            TtAgentPostSummaryHighlightResponse response = null;
            Map<String, Object> intoDbMap = null;
            if (need) {
                Map<String, Object> paramMap = new HashMap<>();
                Map<String, Object> userInput = new HashMap<>();
                userInput.put("topic", hotTags);
                userInput.put("context", content);

                paramMap.put("customerNo", "postSummary");
                paramMap.put("userInput", JSON.toJSONString(userInput));

                Future<TtAgentPostSummaryHighlightResponse> future = aiArticleIntelligentInfoByPostJob.getExecutor().submit(() -> ttAgentApiService.postSummaryHighlight(paramMap));
                response = future.get(45, TimeUnit.SECONDS);
                if (response != null) {
                    intoDbMap = new HashMap<>();
                    intoDbMap.put("_id", cmsInfo.getArtCode());
                    intoDbMap.put("articleId", cmsInfo.getArtCode());
                    intoDbMap.put("title", cmsInfo.getArtTitle());
                    //0：帖子 1：资讯
                    intoDbMap.put("articleType", 1);
                    intoDbMap.put("aiHighLights", response.getSentences());
                    intoDbMap.put("aiSummary", response.getSummary());
                    intoDbMap.put("aiAsking", response.getQuestion());
                    intoDbMap.put("createTime", new Date());
                    intoDbMap.put("updateTime", new Date());

                    articleIntelligentInfoDao.upsertManyBySetWithSetOnInsertFields(Arrays.asList(intoDbMap), SET_ON_INSERT_FIELDS, "_id");
                }

            }

            log.info("生成及落库详情。满足条件：{}，资讯数据：{}，ai信息：{}",
                need,
                JSON.toJSONStringWithDateFormat(cmsInfo, DateUtil.datePattern),
                JSON.toJSONStringWithDateFormat(response, DateUtil.datePattern)
            );
        } catch (
            Exception ex) {
            log.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));
            log.error(ex.getMessage(), ex);
        }
    }
}
