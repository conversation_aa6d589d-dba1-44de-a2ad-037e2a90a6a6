package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumerDoubleActive;

import com.alibaba.fastjson.JSON;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.guba.ReplyInfoKafka;
import ttfund.web.communityservice.bean.jijinBar.post.guba.ReplyRelation;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.msyql.PostPingLunNumDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoDao;
import ttfund.web.communityservice.dao.msyql.ReplyRelationDao;

import java.util.*;

/**
 * 社区数据源同步-评论
 */
@Component
public class ReplyMsgJobDoubleActive {

    private static Logger logger = LoggerFactory.getLogger(ReplyMsgJobDoubleActive.class);

    private static final String KAFKA_LISTENER_ID = "ReplyMsgJobDoubleActive";

    @Autowired
    private ReplyInfoDao replyInfoDao;

    @Autowired
    private ReplyRelationDao replyRelationDao;

    @Autowired
    private PostPingLunNumDao postpinglunnumDao;

    @Autowired
    private PostInfoExtraDao postInfoExtraDao;


    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.GubaReplyInfo4FundQueue}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_REPLYMSGJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_pj)
    private void onListen(ConsumerRecord<String, String> record) {
        replyMsg(record);
    }

    /**
     * 评论
     */
    private void replyMsg(ConsumerRecord<String, String> record) {
        try {

            logger.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            ReplyInfoKafka reply = JSON.parseObject(record.value(), ReplyInfoKafka.class);
            //处理gd开头的基金和下划线后面带数字的基金code
            reply.Code = filterCode(reply.Code);

            if (reply.UpdateTime == null) {
                reply.UpdateTime = new Date();
            }

            List<ReplyRelation> relation = new ArrayList<>();
            if (reply.HuiFuIDList != null) {
                String[] huifuids = reply.HuiFuIDList.split(",");
                if (huifuids != null && huifuids.length > 0) {
                    for (int i = 0; i < huifuids.length; i++) {
                        if (StringUtils.hasLength(huifuids[i])) {
                            ReplyRelation m = new ReplyRelation();
                            m.TopicID = reply.TopicID;
                            m.ReplyID = reply.ID;
                            m.HuiFuID = Long.parseLong(huifuids[i]);
                            m.IsEnabled = true;
                            relation.add(m);
                        }
                    }
                }
            }

            //ip超长数据处理
            if (StringUtils.hasLength(reply.IP) && reply.IP.length() > 200) {
                reply.IP = reply.IP.substring(0, 200);
            }

            switch (reply.action_type) {
                case "del"://删除
                case "delh":
                    replyInfoDao.disabled(reply.ID, reply.Del != null ? reply.Del : 0);
                    break;
                case "rech"://恢复
                    replyInfoDao.resume(reply);
                    replyRelationDao.insertListIgnore(relation);
                    break;
                case "rep"://回复（增加）
                case "daoru_huifu":
                case "zmtdistribute":
                case "updatecodelist":
                case "set_pinglun_quanxian":
                case "modifypic":
                    replyInfoDao.insertOrResume(reply);
                    replyRelationDao.insertListIgnore(relation);
                    break;
            }

            if (reply.TopicID != 0) {
                Map<String, Object> pingLunNum = new HashMap<>();
                pingLunNum.put("TopicID", reply.TopicID);
                pingLunNum.put("PingLunNum", reply.TopicPingLunNum);
                pingLunNum.put("UserPingLunNum", reply.UserPingLunNum);

                postpinglunnumDao.insertOrUpdate(pingLunNum);

                /**
                 *  更新帖子拓展表-评论数
                 */
                try {
                    Map<String, Object> model = new HashMap<>();
                    model.put("ID", reply.TopicID);
                    model.put("PINGLUNNUM", reply.TopicPingLunNum);

                    postInfoExtraDao.updatePinglunNum(model);
                } catch (Exception ex) {
                    logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                            record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));
                    logger.error(ex.getMessage(), ex);
                }

            }

        } catch (Exception ex) {
            logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 处理特殊的Code
     */
    public String filterCode(String code) {
        String result = code;
        //处理gd开头的基金和下划线后面带数字的基金code
        if (StringUtils.hasLength(result)) {
            if (result.length() > 6) {
                if (result.startsWith("gd")) {
                    result = result.substring(2);
                }
                if (result.contains("_")) {
                    result = result.split("_")[0];
                }
            } else if (result.endsWith("_1")) {
                result = result.substring(0, result.length() - 2);
            }
        }
        return result;
    }

}
