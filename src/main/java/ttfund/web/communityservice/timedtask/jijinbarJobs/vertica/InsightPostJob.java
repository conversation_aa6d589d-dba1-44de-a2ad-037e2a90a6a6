package ttfund.web.communityservice.timedtask.jijinbarJobs.vertica;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.data.TopicWordDo;
import ttfund.web.communityservice.bean.jijinBar.post.FundTopic;
import ttfund.web.communityservice.bean.jijinBar.post.TopicInsightModel;
import ttfund.web.communityservice.dao.mongo.TopicInsightMongoDao;
import ttfund.web.communityservice.dao.mongo.TopicWordMongoDao;
import ttfund.web.communityservice.dao.msyql.FundTopicDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.service.PostOptApiService;
import ttfund.web.communityservice.service.entity.PostArticleRequest;
import ttfund.web.communityservice.service.entity.PostArticleResponse;
import ttfund.web.communityservice.utils.DateUtil;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Zhuyuang
 * @date : 2024-11-18 17:46
 * @description :
 */
@JobHandler("InsightPostJob")
@Component
public class InsightPostJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(InsightPostJob.class);

    private static final String LOG_PREFIX = "InsightPostJob[观点洞见生成帖子服务]";

    private static final String CACHE_KEY = "InsightPostJob_BreakPoint";

    private static final String AUTHOR_ID = "6050037243792252";

    private static final String BAR_ID = "jjdt";

    private static final String TITLE_PREFIX = "【观点洞见】";

    private static final String FILE_NAME = "topicInsightContent.txt";

    private static final String INSIGHT_TEMPLATE = "<div style=\"display: flex; flex-direction: row; align-items: center; margin: 0 0 4px !important;\">" +
        "<span style= \"margin-top: 3px; margin-right: 8.5px; height: 18px !important; width: 18px !important;\" src=\"https://j5.dfcfw.com/imagesystem/material_imgs/1736303775157875.png\"></span>"+
        "<p style=\"font-size: 18px; font-weight: bold; " +
        "margin: 0 !important\">%s</p></div><span style=\"margin: 0 0 0 24px !important; display: inline-block !important\">"+
        "<p style=\"font-size:16px;margin:0 0 12px !important;\" class=\"ai_content\">%s</p></span>";

    private String template = "";

    @Autowired
    private PostOptApiService postOptApiService;

    @Autowired
    private TopicInsightMongoDao topicInsightMongoDao;

    @Autowired
    private TopicWordMongoDao topicWordMongoDao;

    @Autowired
    private FundTopicDao fundTopicDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @PostConstruct
    public void load() {
        InputStream inputStream = this.getClass().getResourceAsStream("/" + FILE_NAME);
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                template = template + line;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date breakTime = StringUtils.isEmpty(s) ? userRedisDao.getBreakTime(CACHE_KEY) : DateUtil.strToDate(s);
        LOGGER.info("{}: break time:{}", LOG_PREFIX, breakTime);
        //获取洞见列表
        List<TopicInsightModel> topicInsightModelList = topicInsightMongoDao.getInsightList(breakTime);
        if (CollectionUtils.isEmpty(topicInsightModelList)) {
            LOGGER.info("{}: insights list is empty", LOG_PREFIX);
            return ReturnT.SUCCESS;
        }
        List<Integer> topicIds = topicInsightModelList.stream().
            map(model -> Integer.valueOf(model.getTopicId())).
            collect(Collectors.toList());

        breakTime = topicInsightModelList.stream().
            max(Comparator.comparing(o -> o.getUpdateTime())).get().getUpdateTime();

        //拉取洞见信息
        List<FundTopic> fundTopicList = fundTopicDao.getListByIds(topicIds);
        //构造洞见名称映射表
        Map<String, String> topicNameMap = new HashMap<>();
        for (FundTopic fundTopic : fundTopicList) {
            topicNameMap.put(String.valueOf(fundTopic.htid), fundTopic.name);
        }

        for (TopicInsightModel insightModel : topicInsightModelList) {
            String topicId = insightModel.getTopicId();
            //获取词云
            List<TopicWordDo> wordCloudList = topicWordMongoDao.getWordCloud(topicId);
            List<String> wordWeightList = wordCloudList.stream().
                map(wordCloud -> wordCloud.getWord() + "-" + wordCloud.getRank()).
                collect(Collectors.toList());
            //匹配话题名称
            String topicName = topicNameMap.get(topicId);
            if (StringUtils.isEmpty(topicName)) {
                continue;
            }
            //构造帖子标题和正文内容
            String title = TITLE_PREFIX + topicName;

            List<String> insightContent = new ArrayList<>();
            for (TopicInsightModel.Insight insight : insightModel.getInsights()) {
                insightContent.add(String.format(INSIGHT_TEMPLATE, insight.getOpinionId() + ": " + insight.getContent(),
                    String.join("", insight.getDetail())));
            }
            String content = String.format(template, topicId, "#" + topicName + "#", String.join(";", wordWeightList), String.join("\t", insightContent));
            LOGGER.info("{}: 话题id:{}, 标题:{}, 正文:{}", LOG_PREFIX, topicId, title, content);
            boolean result = postInsight(title, content);
            LOGGER.info("{}: 话题id:{}, 发帖结果:{}", LOG_PREFIX, topicId, result);
        }
        userRedisDao.setBreakTime(CACHE_KEY, breakTime);
        return ReturnT.SUCCESS;
    }

    private boolean postInsight(String title, String content) {
        PostArticleRequest request = new PostArticleRequest();
        request.setUid(AUTHOR_ID);
        request.setCode(BAR_ID);
        request.setTitle(title);
        request.setText(content);

        request.setApprovalfirst(1);
        request.setBizfrom("ai_fundpost_0");

        PostArticleResponse response = postOptApiService.postArticleReturnResponse(request);
        return response != null && response.rc == 1;
    }
}
