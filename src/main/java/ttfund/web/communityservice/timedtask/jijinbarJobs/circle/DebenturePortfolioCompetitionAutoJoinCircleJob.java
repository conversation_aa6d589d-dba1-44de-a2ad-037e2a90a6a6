package ttfund.web.communityservice.timedtask.jijinbarJobs.circle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.activity.ActivityCompleteTaskEvent;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserBindInfo;
import ttfund.web.communityservice.config.kafka.ActivityKafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.mongo.PassportUserBindInfoDao;
import ttfund.web.communityservice.service.CircleServiceImpl;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;


/**
 * 债基实盘大赛报名成功自动加入圈子job
 */
@Slf4j
@JobHandler("DebenturePortfolioCompetitionAutoJoinCircleJob")
@Component
public class DebenturePortfolioCompetitionAutoJoinCircleJob extends IJobHandler {

    private volatile Thread thread;

    private volatile long timestamp = 0L;

    private volatile Integer heartBeatInterval = 0;

    private volatile Integer batchCount = 200;

    @Autowired
    private ActivityKafkaConfig activityKafkaConfig;

    @Autowired
    private PassportUserBindInfoDao passportUserBindInfoDao;

    @Autowired
    private CircleServiceImpl circleService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            //kafka一次拉取数量
            Integer maxPollRecords = null;
            //kafka一次拉取等待时间
            Integer pollDuration = null;
            //本服务运行心跳时间  单位毫秒
            Integer heartBeatInterval = null;
            //一批积攒数量
            Integer batchCount = null;
            //圈子id
            String circleId = null;
            //源消息标识
            String templateId = null;
            //申请人
            String proposer = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                maxPollRecords = jsonObject.getInteger("maxPollRecords");
                pollDuration = jsonObject.getInteger("pollDuration");
                heartBeatInterval = jsonObject.getInteger("heartBeatInterval");
                batchCount = jsonObject.getInteger("batchCount");
                circleId = jsonObject.getString("circleId");
                templateId = jsonObject.getString("templateId");
                proposer = jsonObject.getString("proposer");
            }

            if (maxPollRecords == null) {
                maxPollRecords = 500;
            }
            if (pollDuration == null) {
                pollDuration = 1000;
            }
            if (heartBeatInterval == null) {
                heartBeatInterval = 0;
            }
            if (batchCount == null) {
                batchCount = 100;
            }
            if (proposer == null) {
                proposer = "债基实盘大赛";
            }

            this.heartBeatInterval = heartBeatInterval;
            this.batchCount = batchCount;
            this.timestamp = System.currentTimeMillis();

            log.info("第零步，打印参数。maxPollRecords：{}，pollDuration：{}，heartBeatInterval：{}，batchCount：{}，timestamp：{}，circleId：{}，templateId：{}，proposer：{}",
                maxPollRecords,
                pollDuration,
                heartBeatInterval,
                batchCount,
                timestamp,
                circleId,
                templateId,
                proposer
            );

            deal(maxPollRecords, pollDuration, circleId, templateId, proposer);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int maxPollRecords, int pollDuration, String circleId, String templateId, String proposer) {
        if (thread == null) {
            thread = new Thread(() -> dealTask(maxPollRecords, pollDuration, circleId, templateId, proposer));
            thread.start();

            log.info("0.检测到线程为null，新建线程并运行。");
        }
    }


    private void dealTask(int maxPollRecords, int pollDuration, String circleId, String templateId, String proposer) {

        KafkaConsumer<String, String> consumer = null;
        try {
            if (!StringUtils.hasLength(circleId) || !StringUtils.hasLength(templateId)) {
                return;
            }

            log.info("1.启动。");

            Properties properties = activityKafkaConfig.consumerConfigsForDebenturePortfolioCompetitionAutoJoinCircleJob();
            properties.put("max.poll.records", maxPollRecords);
            consumer = new KafkaConsumer<>(properties);
            consumer.subscribe(Arrays.asList(KafkaTopicName.TOPIC_COMPLETE_TASK_EVENT));


            log.info("2.订阅kafka，消费组配置：\n{}", JSON.toJSONString(properties));

            List<ActivityCompleteTaskEvent> modelList = null;

            ConsumerRecords<String, String> consumerRecords = null;
            ConsumerRecord<String, String> lastRecord = null;
            while (true) {
                if (heartBeatInterval > 0 && (System.currentTimeMillis() - timestamp > heartBeatInterval)) {

                    log.info("3.业务运行心跳间隔超出限制，停止运行。当前时间：{}，timestamp：{}", System.currentTimeMillis(), timestamp);
                    break;
                }

                consumerRecords = consumer.poll(Duration.ofMillis(pollDuration));

                log.info("3.消费kafka。数量：{}", consumerRecords.count());

                if (consumerRecords.count() > 0) {
                    modelList = new ArrayList<>();

                    for (ConsumerRecord<String, String> record : consumerRecords) {
                        lastRecord = record;

                        List<ActivityCompleteTaskEvent> tempList = JSON.parseArray(record.value(), ActivityCompleteTaskEvent.class);
                        if (!CollectionUtils.isEmpty(tempList)) {
                            for (ActivityCompleteTaskEvent a : tempList) {
                                if (Objects.equals(templateId, a.getTemplateId())) {
                                    modelList.add(a);
                                }
                            }
                        }
                    }

                    log.info("4.处理消息。数量：{}，partition：{}，key：{}，offset：{}，timestamp：{}，value：\n{}",
                        consumerRecords.count(),
                        lastRecord.partition(),
                        lastRecord.key(),
                        lastRecord.offset(),
                        lastRecord.timestamp(),
                        lastRecord.value()
                    );

                    if (!CollectionUtils.isEmpty(modelList)) {

                        Map<String, String> bindMap = new HashMap<>();
                        List<String> customerNos = modelList.stream().map(a -> a.getCustomerNo()).distinct().collect(Collectors.toList());
                        List<List<String>> batchList = CommonUtils.toSmallList2(customerNos, 100);
                        for (List<String> batch : batchList) {
                            List<PassportUserBindInfo> bindList = passportUserBindInfoDao.listByCustomerNos(PassportUserBindInfo.class, null, batch);
                            if (!CollectionUtils.isEmpty(bindList)) {
                                bindList.sort((o1, o2) -> {
                                    int i = o1.CUSTOMERNO.compareTo(o2.CUSTOMERNO);
                                    if (i == 0) {
                                        return o1.UPDATETIME.compareTo(o2.UPDATETIME);
                                    }
                                    return i;
                                });
                                bindList.forEach(a -> bindMap.put(a.CUSTOMERNO, a.UID));
                            }
                        }

                        int i = 0;
                        for (ActivityCompleteTaskEvent a : modelList) {
                            i++;

                            String uid = bindMap.get(a.getCustomerNo());
                            if (!StringUtils.hasLength(uid)) {
                                log.info("5.处理数据详情。第{}/{}个，用户编号：{}，通行证：{}，结果：{}",
                                    i,
                                    modelList.size(),
                                    a.getCustomerNo(),
                                    uid,
                                    "无绑定通行证"
                                );

                                continue;
                            }

                            String dealInfo = circleService.forceJoin(false, circleId, uid, proposer);

                            log.info("5.处理数据详情。第{}/{}个，用户编号：{}，通行证：{}，结果：{}",
                                i,
                                modelList.size(),
                                a.getCustomerNo(),
                                uid,
                                dealInfo
                            );
                        }
                    }

                    log.info("5.处理数据完成。数量：{}，数据：{}",
                        CollectionUtils.isEmpty(modelList) ? 0 : modelList.size(),
                        CollectionUtils.isEmpty(modelList) ? null : JSON.toJSONStringWithDateFormat(modelList.get(modelList.size() - 1), DateUtil.datePattern)
                    );

                    consumer.commitSync();
                }
            }
            log.info("6.正常结束。");
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            log.error("6.异常结束。");
        } finally {
            if (consumer != null) {
                consumer.close();
            }

            thread = null;
        }


    }

}
