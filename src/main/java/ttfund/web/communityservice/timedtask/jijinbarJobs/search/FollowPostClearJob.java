package ttfund.web.communityservice.timedtask.jijinbarJobs.search;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.dao.search.FollowPostDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;

@JobHandler(value = "FollowPostClearJob")
@Component
public class FollowPostClearJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(FollowPostClearJob.class);
    private final String logpre = "[FollowPostClearJob]=> ";

    private final String logpreNew = "[FollowPostClearJob for es8]=> ";

    @Autowired
    UserRedisDao userRedisDao;
    @Autowired
    FollowPostDao followPostDao;

    public ReturnT<String> execute(java.lang.String param) {
        try {
            ClearHisData();
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    public void ClearHisData() {
        logger.info(logpre + "开始执行");

        if (DateUtil.getNowDate().getHours() > 2 && DateUtil.getNowDate().getHours() < 4) {
            //删除es8过期数据
            try {
                String cacheKey = "FollowPostClear_ClearHisData_es8";
                Date lastUpDateTime = userRedisDao.getBreakTime(cacheKey);
                if (lastUpDateTime == null || lastUpDateTime.compareTo(DateUtil.getNowDate("yyyy-MM-dd")) <= 0) {
                    logger.info(logpreNew + "开始清除数据");
                    boolean delResult = followPostDao.deleteByUpdateTimeEs8(DateUtil.calendarDateByDays(DateUtil.getNowDate(), -30));
                    if (!delResult) {
                        delResult = followPostDao.deleteByUpdateTimeEs8(DateUtil.calendarDateByDays(DateUtil.getNowDate(), -30));
                    }
                    logger.info(logpreNew + "清除数据完成，结果：{}", delResult);
                }
            } catch (Exception ex) {
                logger.error(logpreNew + ex.getMessage(), ex);
            }

        } else {
            logger.info(logpre + "不在时间范围内");
        }
        logger.info(logpre + "结束执行");
    }
}
