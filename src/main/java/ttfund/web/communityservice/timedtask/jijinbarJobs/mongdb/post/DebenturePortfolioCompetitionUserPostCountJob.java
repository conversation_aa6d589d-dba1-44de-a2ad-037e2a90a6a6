package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.BigVDataUserModel;
import ttfund.web.communityservice.dao.mongo.CalVCountDataUserDao;
import ttfund.web.communityservice.dao.mongo.PortfolioCompetitionUserInteractDataDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 债基实盘大赛用户帖子数计算job
 */
@Slf4j
@JobHandler("DebenturePortfolioCompetitionUserPostCountJob")
@Component
public class DebenturePortfolioCompetitionUserPostCountJob extends IJobHandler {

    private static final String TIME_FORMAT = "yyyy-MM-dd";

    private static final String SELECT_FIELDS = " UID,TITLE,CONTENT ";

    public static final List<String> SET_ON_INSERT_FIELDS = Arrays.asList("createTime");

    public static final String DOCUMENT_ID_LATEST_COMPUTE_TIME = "document_id_latest_compute_time";

    @Autowired
    private CalVCountDataUserDao calVCountDataUserDao;

    @Autowired
    private PortfolioCompetitionUserInteractDataDao portfolioCompetitionUserInteractDataDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            //起始日期。格式yyyy-MM-dd
            String startDay = null;
            //指定某一天。格式yyyy-MM-dd
            String oneDay = null;
            Integer batchCount = null;
            String keyword = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                startDay = jsonObject.getString("startDay");
                oneDay = jsonObject.getString("oneDay");
                batchCount = jsonObject.getInteger("batchCount");
                keyword = jsonObject.getString("keyword");
            }

            if (batchCount == null) {
                batchCount = 100;
            }
            if (keyword == null) {
                keyword = "债";
            }

            log.info("0，打印参数。startDay：{}，oneDay：{}，batchCount：{}，keyword：{}", startDay, oneDay, batchCount, keyword);

            deal(startDay == null ? null : DateUtil.strToDate(startDay, TIME_FORMAT),
                oneDay == null ? null : DateUtil.strToDate(oneDay, TIME_FORMAT),
                batchCount,
                keyword
            );
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(Date startDay, Date oneDay, Integer batchCount, String keyword) {

        Date start = null;
        Date end = null;

        if (startDay != null) {
            start = startDay;
            end = DateUtil.strToDate(DateUtil.dateToStr(new Date(), TIME_FORMAT), TIME_FORMAT);
        } else if (oneDay != null) {
            start = oneDay;
            end = oneDay;
        } else {
            Document document = portfolioCompetitionUserInteractDataDao.getOneByKeyIsValue("_id", DOCUMENT_ID_LATEST_COMPUTE_TIME, null, Document.class);
            if (document != null) {
                start = DateUtil.calendarDateByDays(document.getDate("computeTime"), 1);
            }
            end = DateUtil.strToDate(DateUtil.dateToStr(new Date(), TIME_FORMAT), TIME_FORMAT);
        }

        log.info("1，计算区间。start：{}，end：{}",
            start == null ? null : DateUtil.dateToStr(start),
            end == null ? null : DateUtil.dateToStr(end)
        );

        if (start == null) {
            return;
        }

        int round = 0;
        String uid = null;
        while (true) {
            round++;

            List<BigVDataUserModel> userList = calVCountDataUserDao.getByUid(uid, batchCount);
            if (!CollectionUtils.isEmpty(userList)) {
                uid = userList.get(userList.size() - 1).getUID();
            }

            log.info("2，读取用户表。第{}轮，数量：{}，头部数据：{}",
                round,
                CollectionUtils.isEmpty(userList) ? 0 : userList.size(),
                CollectionUtils.isEmpty(userList) ? null : JSON.toJSONString(userList.get(0))
            );

            if (CollectionUtils.isEmpty(userList)) {
                break;
            }

            List<String> uidList = userList.stream().map(a -> a.getUID()).collect(Collectors.toList());
            calculate(round, uidList, start, end, keyword);

            if (userList.size() < batchCount) {
                break;
            }

        }

        if (oneDay == null) {
            Date computeTime = DateUtil.calendarDateByDays(end, -1);
            Map<String, Object> map = new HashMap<>();
            map.put("_id", DOCUMENT_ID_LATEST_COMPUTE_TIME);
            map.put("uid", DOCUMENT_ID_LATEST_COMPUTE_TIME);
            map.put("computeTime", computeTime);
            map.put("postCount", 0);
            map.put("createTime", new Date());
            map.put("updateTime", new Date());

            portfolioCompetitionUserInteractDataDao.upsertManyBySetWithSetOnInsertFields(Arrays.asList(map), SET_ON_INSERT_FIELDS, "_id");

            log.info("6，写库-更新最新计算时间。computeTime：{}", DateUtil.dateToStr(computeTime));
        }

    }


    private void calculate(int round, List<String> uids, Date start, Date end, String keyword) {
        List<Date> dayList = new ArrayList<>();

        Date tempDay = start;
        while (true) {
            tempDay = DateUtil.calendarDateByDays(tempDay, 1);

            if (tempDay.compareTo(end) < 0) {
                dayList.add(tempDay);
            } else {
                break;
            }
        }
        dayList.add(end);

        log.info("3，生成待计算日期列表。第{}轮，数量：{}，头部数据：{}",
            round,
            CollectionUtils.isEmpty(dayList) ? 0 : dayList.size(),
            CollectionUtils.isEmpty(dayList) ? null : DateUtil.dateToStr(dayList.get(0))
        );

        int i = 0;
        for (Date day : dayList) {
            i++;

            Map<String, Integer> postCountMap = new HashMap<>();
            List<PostInfoNewModel> list = postInfoNewDao.getPostsByUidsAndTimeInterval(SELECT_FIELDS, uids, null, day);
            if (!CollectionUtils.isEmpty(list)) {
                for (PostInfoNewModel post : list) {
                    boolean match = false;
                    if (!match && StringUtils.hasLength(post.TITLE)) {
                        match = post.TITLE.contains(keyword);
                    }
                    if (!match && StringUtils.hasLength(post.CONTENT)) {
                        match = post.CONTENT.contains(keyword);
                    }

                    if (match) {
                        postCountMap.put(post.UID, postCountMap.getOrDefault(post.UID, 0) + 1);
                    }
                }
            }

            log.info("4，读取单日期内用户发帖数据。第{}轮，第{}个，日期：{}，数量：{}，头部数据：{}",
                round,
                i,
                DateUtil.dateToStr(day),
                CollectionUtils.isEmpty(dayList) ? 0 : dayList.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

            List<Map<String, Object>> upsertList = new ArrayList<>();
            for (String uid : uids) {
                Map<String, Object> map = new HashMap<>();
                map.put("_id", String.format("%s_%s", DateUtil.dateToStr(DateUtil.calendarDateByDays(day, -1), TIME_FORMAT), uid));
                map.put("uid", uid);
                map.put("computeTime", DateUtil.calendarDateByDays(day, -1));
                map.put("postCount", postCountMap.getOrDefault(uid, 0));
                map.put("createTime", new Date());
                map.put("updateTime", new Date());
                upsertList.add(map);
            }

            List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(upsertList, 200);
            for (List<Map<String, Object>> batch : batchList) {
                portfolioCompetitionUserInteractDataDao.upsertManyBySetWithSetOnInsertFields(batch, SET_ON_INSERT_FIELDS, "_id");
            }

            log.info("5，写库-用户发帖数。第{}轮，第{}个，日期：{}，数量：{}，头部数据：{}",
                round,
                i,
                DateUtil.dateToStr(day),
                CollectionUtils.isEmpty(upsertList) ? 0 : upsertList.size(),
                CollectionUtils.isEmpty(upsertList) ? null : JSON.toJSONStringWithDateFormat(upsertList.get(0), DateUtil.datePattern)
            );
        }
    }


}
