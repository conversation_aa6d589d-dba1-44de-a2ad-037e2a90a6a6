package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.eastmoney.particle.common.concurrent.ThreadFactoryImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.api.BigVOpinionBriefingResponse;
import ttfund.web.communityservice.bean.jijinBar.post.AiNewsModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostinfoExtraModel;
import ttfund.web.communityservice.dao.mongo.AiNewsDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.service.TtAgentApiServiceImpl;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * AI关注早报
 */
@JobHandler("AiFollowNewsJob")
@Component
public class AiFollowNewsJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(AiFollowNewsJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private PostInfoExtraDao postInfoExtraDao;

    @Autowired
    private AiNewsDao aiNewsDao;

    @Autowired
    private TtAgentApiServiceImpl ttAgentApiService;

    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(5, 5, 5L, TimeUnit.MINUTES,
        new LinkedBlockingQueue<>(20000), new ThreadFactoryImpl("AiFollowNewsThreadPool"));

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        ReturnT<String> result = ReturnT.SUCCESS;
        LOGGER.info("AI关注早报处理开始");

        try {

            String breakTimeKey = "AiFollowNewsBreakTime";
            Date lastUpdatetime = userRedisDao.getBreakTime(breakTimeKey, DateUtil.calendarDateByHour(-1));

            LOGGER.info("第1步，断点:{}", DateUtil.dateToStr(lastUpdatetime));

            // 获取帖子列表
            List<PostInfoNewModel> list = postInfoNewDao.getPostList(lastUpdatetime, 500, "ID", "UID", "DEL", "TTJJDEL", "CONTENT", "TIME", "UPDATETIME");

            LOGGER.info("获取到{}个帖子", list.size());

            if (CollectionUtils.isEmpty(list)) {
                LOGGER.info("没有新增帖子，处理结束");
                return ReturnT.SUCCESS;
            }
            lastUpdatetime = list.get(list.size() - 1).UPDATETIME;

            Set<String> ids = list.stream()
                .map(w -> String.valueOf(w.ID))
                .collect(Collectors.toSet());

            // 获取已存在的
            List<AiNewsModel> existedList = aiNewsDao.getByIds(new ArrayList<>(ids));

            LOGGER.info("已存在{}个帖子", existedList.size());

            Set<String> existedIds = existedList.stream()
                .map(AiNewsModel::getId)
                .collect(Collectors.toSet());

            Set<String> deletedIds = list.stream()
                .filter(w -> w.DEL != 0 || w.TTJJDEL != 0)
                .map(w -> String.valueOf(w.ID))
                .collect(Collectors.toSet());

            List<String> toDeleteIds = existedIds.stream()
                .filter(deletedIds::contains)
                .collect(Collectors.toList());

            // 删除失效的
            boolean flag = aiNewsDao.removeByIds(toDeleteIds);
            if (flag) {
                LOGGER.info("已删除{}个帖子：{}", toDeleteIds.size(), JacksonUtil.obj2StringNoException(toDeleteIds));
            } else {
                LOGGER.error("删除帖子失败：{}", JacksonUtil.obj2StringNoException(toDeleteIds));
            }

            List<Long> validIds = list.stream()
                .filter(w -> w.TIME.getTime() >= DateUtil.strToDate("2025-05-01 00:00:00.000").getTime()
                    && w.DEL == 0
                    && w.TTJJDEL == 0
                    && !existedIds.contains(String.valueOf(w.ID)))
                .map(w -> (long) w.ID)
                .collect(Collectors.toList());

            LOGGER.info("满足状态要求的共{}个帖子", validIds.size());

            if (CollectionUtils.isEmpty(validIds)) {
                LOGGER.info("没有符合的帖子，处理结束");
                userRedisDao.setBreakTime(breakTimeKey, lastUpdatetime);
                return ReturnT.SUCCESS;
            }

            // 查询满足字数要求的
            List<PostinfoExtraModel> validList = postInfoExtraDao.getByIdsAndLength(validIds, 50);

            LOGGER.info("满足字数要求的共{}个帖子", validList.size());

            if (CollectionUtils.isEmpty(validList)) {
                LOGGER.info("没有符合的帖子，处理结束");
                userRedisDao.setBreakTime(breakTimeKey, lastUpdatetime);
                return ReturnT.SUCCESS;
            }

            Set<Long> toSaveIds = validList.stream()
                .map(w -> w.ID)
                .collect(Collectors.toSet());

            list = list.stream()
                .filter(w -> toSaveIds.contains((long) w.ID))
                .collect(Collectors.toList());

            List<AiNewsModel> aiNewsList = new ArrayList<>();

            Map<Integer, Future<BigVOpinionBriefingResponse>> futureMap = new LinkedHashMap<>();
            Map<Integer, PostInfoNewModel> postMap = new HashMap<>();

            // 获取摘要
            for (PostInfoNewModel model : list) {

                Future<BigVOpinionBriefingResponse> future = EXECUTOR.submit(() -> ttAgentApiService.bigVOpinionBriefing(model.CONTENT));
                futureMap.put(model.ID, future);
                postMap.put(model.ID, model);
            }

            for (Map.Entry<Integer, Future<BigVOpinionBriefingResponse>> entry : futureMap.entrySet()) {

                try {
                    BigVOpinionBriefingResponse response = entry.getValue().get(30, TimeUnit.SECONDS);

                    if (response == null || (StringUtils.isEmpty(response.getSummary()) && CollectionUtils.isEmpty(response.getKeywords()))) {
                        continue;
                    }

                    Integer id = entry.getKey();
                    PostInfoNewModel model = postMap.get(id);

                    AiNewsModel aiNewsModel = new AiNewsModel();
                    aiNewsModel.setId(String.valueOf(model.ID));
                    aiNewsModel.setPostId(String.valueOf(model.ID));
                    aiNewsModel.setUid(model.UID);
                    aiNewsModel.setTime(model.TIME);

                    aiNewsModel.setKeywords(response.getKeywords());
                    aiNewsModel.setSummary(response.getSummary());

                    aiNewsList.add(aiNewsModel);
                } catch (Exception e) {
                    LOGGER.error("执行失败，id：" + entry.getKey() + e.getMessage(), e);
                }
            }

            // 保存
            if (!CollectionUtils.isEmpty(aiNewsList)) {

                aiNewsList.forEach(w -> w.setUpdateTime(new Date()));

                flag = aiNewsDao.save(aiNewsList);

                if (flag) {
                    LOGGER.info("保存成功，数量:{}", aiNewsList.size());
                } else {
                    LOGGER.error("保存失败：{}", JacksonUtil.obj2StringNoException(aiNewsList));
                }
            }

            // 更新断点
            userRedisDao.setBreakTime(breakTimeKey, lastUpdatetime);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }

        LOGGER.info("AI关注早报处理结束");

        return result;
    }
}
