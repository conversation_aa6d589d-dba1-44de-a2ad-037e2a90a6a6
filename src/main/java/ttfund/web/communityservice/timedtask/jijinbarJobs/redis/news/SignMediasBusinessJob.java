package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.news;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ttfund.web.base.helper.CacheHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.news.SignMediaCacheData;
import ttfund.web.communityservice.bean.news.SignMediasApiResult;
import ttfund.web.communityservice.bean.news.SignMediasItem;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.NewsRedisConstantConfig;
import ttfund.web.communityservice.enums.EnumSignMediasType;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.text.MessageFormat;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 媒体来源数据处理
 * 对接人员：150046(刘涛)
 */
@JobHandler(value = "signMediasBusinessJob")
@Component
public class SignMediasBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SignMediasBusinessJob.class);

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String param) throws JsonProcessingException {


        try {

            setDataToCahche();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 数据填充到redis缓存
     */
    private void setDataToCahche() {
        try {
            List<SignMediasItem> list = getList(EnumSignMediasType.ALL);

            if (!CollectionUtils.isEmpty(list)) {

                SignMediaCacheData cacheData = new SignMediaCacheData();

                cacheData.BlackList = list.stream()
                        .filter(a -> a.Sm_Type == EnumSignMediasType.BLACK.getValue())
                        .map(a -> a.Sm_Name)
                        .collect(Collectors.toList());

                cacheData.WhiteList = list.stream()
                        .filter(a -> a.Sm_Type == EnumSignMediasType.WHITE.getValue())
                        .map(a -> a.Sm_Name)
                        .collect(Collectors.toList());

                cacheData.GrayList = list.stream()
                        .filter(a -> a.Sm_Type == EnumSignMediasType.GRAY.getValue())
                        .map(a -> a.Sm_Name)
                        .collect(Collectors.toList());

                //把数据插入缓存
                Boolean flag = app.newsRedis.set(NewsRedisConstantConfig.Asp_Net_Fund_Service_NewsSignMedias_All, JacksonUtil.obj2String(cacheData));
                if (flag) {
                    logger.info("缓存{}插入【成功】数量为:{}", NewsRedisConstantConfig.Asp_Net_Fund_Service_NewsSignMedias_All, list.size());
                } else {
                    logger.error("缓存{}插入【失败】数量为:{}", NewsRedisConstantConfig.Asp_Net_Fund_Service_NewsSignMedias_All, list.size());
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    private List<SignMediasItem> getList(EnumSignMediasType type) {

        SignMediasApiResult result = new SignMediasApiResult();

        try {

            String apiUrl = MessageFormat.format(appConstant.newsSignMediasApiUrl, type.getValue());
            result = CacheHelper.get(apiUrl);
            if (result == null) {
                String content = HttpHelper.requestGet(apiUrl);
                if (StringUtils.hasLength(content)) {
                    result = JacksonUtil.string2Obj(content, SignMediasApiResult.class);
                    if (result != null) {
                        CacheHelper.put(apiUrl, result, 5 * 60 * 1000L);
                    }
                }
            }
            if (result != null && result.Success == 1 && result.TotalCount > 0) {
                return result.Result;
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        if (result == null) {
            result = new SignMediasApiResult();
        }

        return result.Result;
    }
}
