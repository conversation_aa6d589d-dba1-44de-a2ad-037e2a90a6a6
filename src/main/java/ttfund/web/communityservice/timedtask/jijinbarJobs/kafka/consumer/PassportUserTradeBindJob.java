package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.user.KafkaTBUserInfoModel;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserBindInfo;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.mongo.PassportUserBindInfoDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

/**
 * 用户交易绑定Kafa 信息
 */
@Component
public class PassportUserTradeBindJob {

    private static final Logger logger = LoggerFactory.getLogger(PassportUserTradeBindJob.class);

    public static final String KAFKA_LISTENER_ID = "KAFKA_LISTENER_ID_PassportUserTradeBindJob";

    @Autowired
    public PassportUserBindInfoDao passportUserBindInfoDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.Fund_TradeBind}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_PASSPORTUSERTRADEBINDJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_tradeBind)
    private void onListen(ConsumerRecord<String, String> record) {
        hanlderKafkaMsg(record);
    }


    public void hanlderKafkaMsg(ConsumerRecord<String, String> record) {

        StringBuilder builder = new StringBuilder();
        builder.append("详情。").append("\n");

        try {
            builder.append(String.format("1.打印。partition：%s，key：%s，offset：%s，timestamp：%s，数据：%s",
                    record.partition(), record.key(), record.offset(), record.timestamp(), record.value())).append("\n");

            if (StringUtils.isEmpty(record.value())) {
                return;
            }

            KafkaTBUserInfoModel obj = JacksonUtil.string2Obj(record.value(), KafkaTBUserInfoModel.class);
            if (obj == null || StringUtils.isEmpty(obj.C_PASSPORTID)) {
                return;
            }


            PassportUserBindInfo userInfo = new PassportUserBindInfo();
            userInfo._id = obj.C_PASSPORTID;
            userInfo.CREATETIME = DateUtil.strToDate(obj.C_CREATETIME, "yyyy/M/d HH:mm:ss");
            userInfo.CUSTOMERNO = obj.C_CUSTOMERNO;
            userInfo.ISENABLED = obj.C_ISENABLED;
            userInfo.NICHENG = "";
            userInfo.OPENID = obj.C_OPENID;
            userInfo.SEX = "";
            userInfo.UID = obj.C_PASSPORTID;
            userInfo.UPDATETIME = DateUtil.strToDate(obj.C_UPDATETIME, "yyyy/M/d HH:mm:ss");

            passportUserBindInfoDao.upsertOne(CommonUtils.beanToMap(userInfo), "_id");

            builder.append("2.完成").append("\n");

            logger.info(builder.toString());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            logger.error(builder.toString());
        }
    }
}
