package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumerDoubleActive;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.timedtask.messagepush.MessagePushReplyAtMeJob;

/**
 * 评论消息 提到我的
 */
@Slf4j
@Component
public class MessagePushReplyAtMeJobDoubleActive {

    public static final String KAFKA_LISTENER_ID = "MessagePushReplyAtMeDoubleActive";

    @Autowired
    MessagePushReplyAtMeJob messagePushReplyAtMeJob;

    @KafkaListener(
        id = KAFKA_LISTENER_ID,
        topics = {KafkaTopicName.GubaReplyInfo4FundQueue},
        groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_MESSAGEPUSHREPLYATMEJOB + "}",
        containerFactory = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_pj)
    public void replyKafkaListener(ConsumerRecord<String, String> record) {
        messagePushReplyAtMeJob.deal(record, log);
    }
}

