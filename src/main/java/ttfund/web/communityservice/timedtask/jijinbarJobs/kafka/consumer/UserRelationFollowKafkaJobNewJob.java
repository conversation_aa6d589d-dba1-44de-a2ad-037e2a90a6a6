package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.user.FollowInfo;
import ttfund.web.communityservice.bean.jijinBar.user.UserFollowAndFansModelNew;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.config.redis.UserAccRedisKey;
import ttfund.web.communityservice.dao.mongo.PassportUserFollowNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户粉丝列表，用户关注列表缓存
 */
@Component
public class UserRelationFollowKafkaJobNewJob {

    private static Logger logger = LoggerFactory.getLogger(UserRelationFollowKafkaJobNewJob.class);

    public static final String KAFKA_LISTENER_ID = "KAFKA_LISTENER_ID_UserRelationFollowKafkaJobNewJob";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PassportUserFollowNewDao passportUserFollowNewDao;


    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.Newsplatform_follow_guba}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_USERRELATIONFOLLOWKAFKAJOBNEWJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_zp)
    private void onListen(ConsumerRecord<String, String> record) {
        handleOnMessage((record));
    }

    /**
     * 关注列表
     */
    private void handleOnMessage(ConsumerRecord<String, String> record) {

        StringBuilder builder = new StringBuilder();
        builder.append("详情。").append("\n");

        try {

            JSONObject item = JSON.parseObject(record.value());

            builder.append(String.format("1.打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value())).append("\n");

            String userid = item.getString("user_id");
            String objid = item.getString("obj_id");
            int reftype = item.getInteger("ref_type");

            //用户关注列表key
            String userFollowListCacheKey = UserAccRedisKey.FUND_GUBA_SERVICE_USERFOLLOWLISTNEW_PASSPORT + userid;
            //用户关注数key
            String userFollowCountCacheKey = UserAccRedisKey.FUND_GUBA_SERVICE_USERFOLLOW_FOLLOWNEW_PASSPORT + userid;
            //被关注用户的关注列表key
            String targetUserFollowListCacheKey = UserAccRedisKey.FUND_GUBA_SERVICE_USERFOLLOWLISTNEW_PASSPORT + objid;

            //关注信息
            UserFollowAndFansModelNew followInfo = initUserFollowInfo(userid);
            //被关注用户的关注信息
            UserFollowAndFansModelNew targetFollowInfo = initUserFollowInfo(objid);

            int oldFollowCount = followInfo.Count;

            try {

                //判断被关注用户的关注列表是否有该用户
                int index = -1;
                for (int i = 0; i < targetFollowInfo.List.size(); i++) {
                    if (targetFollowInfo.List.get(i).PId.equals(userid)) {
                        index = i;
                        break;
                    }
                }

                switch (reftype) {
                    //关注
                    case 1:
                        //关注列表,如果存在则删除
                        followInfo.List = followInfo.List.stream().filter(a -> !a.PId.equals(objid)).collect(Collectors.toList());

                        //添加关注信息
                        followInfo.List.add(new FollowInfo(objid, index > -1));
                        if (index > -1) {
                            targetFollowInfo.List.get(index).IsMutualFollow = true;
                        }
                        break;
                    //取消关注
                    case 2:
                        followInfo.List = followInfo.List.stream().filter(a -> !a.PId.equals(objid)).collect(Collectors.toList());
                        if (index > -1) {
                            targetFollowInfo.List.get(index).IsMutualFollow = false;
                        }
                        break;
                    default:
                        logger.error(String.format("Ref_Type的值不正确。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                                record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));
                        break;
                }

                /**
                 * 用户关注缓存设置
                 */
                //用户关注列表
                followInfo.Count = followInfo.List.size();

                targetFollowInfo.Count = targetFollowInfo.List.size();

                long expire = 3 * 24 * 3600L;

                //关注用户
                if (followInfo != null) {
                    //用户关注列表
                    userRedisDao.set(userFollowListCacheKey, JSON.toJSONString(followInfo.List), expire);
                    //用户关注缓存数
                    userRedisDao.set(userFollowCountCacheKey, String.valueOf(followInfo.Count), expire);
                }


                //被关注用户
                if (targetFollowInfo != null && targetFollowInfo.Count > 0) {
                    //被关注用户关注列表
                    userRedisDao.set(targetUserFollowListCacheKey, JSON.toJSONString(targetFollowInfo.List), expire);
                }

                builder.append("2.完成").append("\n");

                logger.info(builder.toString());

            } catch (Exception ex) {
                logger.error(ex.getMessage(), ex);

                logger.error(builder.toString());
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);

            logger.error(builder.toString());
        }

    }


    /**
     * 初始化用户关注列表
     */
    private UserFollowAndFansModelNew initUserFollowInfo(String userid) {
        UserFollowAndFansModelNew result = new UserFollowAndFansModelNew();
        result._id = userid;
        result.List = new ArrayList<>();
        result.Count = 0;
        result.UpdateTime = new Date();

        String value = userRedisDao.get(UserAccRedisKey.FUND_GUBA_SERVICE_USERFOLLOWLISTNEW_PASSPORT + userid);
        if (!StringUtils.hasLength(value)) {
            UserFollowAndFansModelNew temp = passportUserFollowNewDao.get(userid, UserFollowAndFansModelNew.class);
            if (temp != null) {
                result.List = temp.List;
                result.Count = temp.Count;
            }
        } else {
            List<FollowInfo> tempList = JSON.parseArray(value, FollowInfo.class);
            result.List = tempList;
            result.Count = tempList.size();
        }

        return result;
    }

}
