package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.data.*;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.HotFundBarDao;
import ttfund.web.communityservice.dao.mongo.HotTopicDao;
import ttfund.web.communityservice.dao.mongo.PopularityRankWDao;
import ttfund.web.communityservice.dao.msyql.SetModuleDao;
import ttfund.web.communityservice.dao.msyql.SetRecommonDao;
import ttfund.web.communityservice.enums.EnumRecommonType;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 推荐信息数据【推荐模块，话题模块，人气用户，热门基金吧，自定义模块，定投页大V】
 */
@JobHandler(value = "recommondRedisBusinessJob")
@Component
public class RecommondRedisBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(RecommondRedisBusinessJob.class);

    @Autowired
    private SetRecommonDao setRecommonDao;

    @Autowired
    private SetModuleDao setModuleDao;

    @Autowired
    private HotTopicDao hotTopicDao;

    @Autowired
    private HotFundBarDao hotFundBarDao;

    @Autowired
    private PopularityRankWDao popularityRankWDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String param) throws JsonProcessingException {

        try {
            // 同步推荐模块到Redis
            syncRecommend();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        try {
            // 热门话题列表同步到Redis
            syncRecommendTopicInfo();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        try {
            // 人气用户列表同步到Redis
            syncRecommendHotuserInfo();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        try {
            // 热门基金吧列表同步到Redis
            syncRecommendHotbarInfo();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        try {
            // 同步自定义模块明细列表
            syncRecommendSelfInfo();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        try {
            // 定投页 大V信息
            syncRecommendDTInfo();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 同步推荐模块到Redis
     */
    private void syncRecommend() throws JsonProcessingException {


        // 推荐模块
        String cacheKey = BarRedisKey.FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND;
        // 自定义模块
        String userDefinedKey = BarRedisKey.FUND_GUBA_SERVICE_COMMUNITY_USERDEFINED;

        List<SetRecommonEntity> list = setRecommonDao.getRecommonList();

        logger.info("syncRecommend-读库，获取推荐模块列表。数量：{}，头部数据：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JacksonUtil.obj2String(list.get(0))
        );

        List<SetModule> locationlist = setModuleDao.getDiaplayLocationList();

        logger.info("syncRecommend-读库，获取模块的位置列表。数量：{}，头部数据：{}",
                CollectionUtils.isEmpty(locationlist) ? 0 : locationlist.size(),
                CollectionUtils.isEmpty(locationlist) ? null : JacksonUtil.obj2String(locationlist.get(0))
        );

        if (list == null) {
            list = new ArrayList<>();
        }
        if (locationlist == null) {
            locationlist = new ArrayList<>();
        }

        //推荐模块redis集合
        List<RecommonRedisEntity> redisEntitys = new ArrayList<>();
        //有效的推荐模块
        List<SetRecommonEntity> recommonlist = new ArrayList<>();
        RecommonRedisEntity redisEntity;
        SetRecommonEntity definedtemp;
        //推荐模块非周期显示
        List<SetRecommonEntity> notPeriods = list.stream()
                .filter(l -> l.IsPeriod == 2
                        && DateHelper.getNowDate().compareTo(l.StartTime) >= 0
                        && DateHelper.getNowDate().compareTo(l.Endtime) < 0
                        && (l.Type == EnumRecommonType.HOT_TOPIC.getValue()
                        || l.Type == EnumRecommonType.HOT_FUND_BAR.getValue()
                        || l.Type == EnumRecommonType.HOT_USER.getValue()))
                .collect(Collectors.toList());

        if (notPeriods.size() > 0) {

            List<SetRecommonEntity> temps = notPeriods.stream()
                    .filter(l -> l.Type == EnumRecommonType.HOT_TOPIC.getValue()
                            || l.Type == EnumRecommonType.HOT_FUND_BAR.getValue()
                            || l.Type == EnumRecommonType.HOT_USER.getValue())
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(temps)) {
                recommonlist.addAll(temps);
            }
        }

        List<SetRecommonEntity> periods = list.stream()
                .filter(l -> l.IsPeriod == 1
                        && l.Week != null
                        && l.Week.contains(String.valueOf(DateHelper.getNow().getDayOfWeek().getValue()))
                        && DateUtil.isNowTimeOfDayBetween(l.StartTime, l.Endtime)
                ).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(periods)) {
            recommonlist.addAll(periods);
        }

        definedtemp = list.stream()
                .filter(l -> DateUtil.getNowDate().compareTo(l.StartTime) >= 0
                        && DateUtil.getNowDate().compareTo(l.Endtime) < 0
                        && l.Type == EnumRecommonType.USER_DEFINED.getValue())
                .findFirst()
                .orElse(null);

        // 推荐模块
        if (!CollectionUtils.isEmpty(recommonlist)) {

            for (SetRecommonEntity temp : recommonlist) {
                redisEntity = new RecommonRedisEntity();
                int moduleType = getModuleType(temp.Type);
                SetModule recommon = locationlist.stream()
                        .filter(l -> l.ModuleType == moduleType && l.State == 1)
                        .findFirst()
                        .orElse(new SetModule());
                redisEntity.DisplayLocation = recommon.DisplayLocation;
                ;
                redisEntity.MName = temp.MName;
                redisEntity.Type = temp.Type;
                redisEntity.RecommonType = temp.RecommonType;
                redisEntity.IsShowMore = temp.IsShowMore;
                redisEntitys.add(redisEntity);
            }

            app.barredis.set(cacheKey, JacksonUtil.obj2String(redisEntitys), 1888 * 60L);
        }

        // 自定义模块
        if (definedtemp != null) {
            redisEntity = new RecommonRedisEntity();
            int moduleType = getModuleType(definedtemp.Type);
            SetModule definedrecommon = locationlist.stream()
                    .filter(l -> l.ModuleType == moduleType && l.State == 1)
                    .findFirst()
                    .orElse(new SetModule());
            redisEntity.DisplayLocation = definedrecommon.DisplayLocation;
            redisEntity.MName = definedtemp.MName;
            redisEntity.Type = definedtemp.Type;
            redisEntity.RecommonType = definedtemp.RecommonType;
            redisEntity.IsShowMore = definedtemp.IsShowMore;

            app.barredis.set(userDefinedKey, JacksonUtil.obj2String(redisEntity), 8 * 60L);
        }

    }

    /**
     * 热门话题列表同步到Redis
     */
    private void syncRecommendTopicInfo() throws JsonProcessingException {

        // 热门话题
        String cacheTopic = BarRedisKey.FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_TOPIC;
        // 热门话题
        String cacheTopicDefault = BarRedisKey.FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_TOPIC_DEFAULT;

        List<SetRecommonInfoEntity> list = setRecommonDao.getRecommonInfoEntities(EnumRecommonType.HOT_TOPIC.getValue());

        logger.info("syncRecommendTopicInfo-读库，根据类型获取配置内容列表。数量：{}，头部数据：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JacksonUtil.obj2String(list.get(0))
        );

        // MongoDb获取热门话题
        List<String> mongoList = hotTopicDao.getHotTopicByScore(100);

        logger.info("syncRecommendTopicInfo-读库，根据得分倒序获得热门话题。数量：{}，头部数据：{}",
                CollectionUtils.isEmpty(mongoList) ? 0 : mongoList.size(),
                CollectionUtils.isEmpty(mongoList) ? null : mongoList.get(0)
        );

        List<String> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(mongoList)) {
            result.addAll(mongoList);
            app.barredis.set(cacheTopicDefault, JacksonUtil.obj2String(result));
        }

        if (!CollectionUtils.isEmpty(list)) {
            result = new ArrayList<>();
            result.addAll(list.stream().map(l -> l.ItemCode).distinct().collect(Collectors.toList()));
            app.barredis.set(cacheTopic, JacksonUtil.obj2String(result));
        }

    }

    /**
     * 人气用户列表同步到Redis
     */
    private void syncRecommendHotuserInfo() throws JsonProcessingException {

        // 人气用户
        String cacheHotuser = BarRedisKey.FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_HOTUSER;
        String cacheHotuserDefault = BarRedisKey.FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_HOTUSER_DEFAULT;

        List<SetRecommonInfoEntity> list = setRecommonDao.getRecommonInfoEntities(EnumRecommonType.HOT_USER.getValue());

        logger.info("syncRecommendHotuserInfo-读库，根据类型获取配置内容列表。数量：{}，头部数据：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JacksonUtil.obj2String(list.get(0))
        );

        // MongoDb获取人气用户
        List<String> mongoList = popularityRankWDao.getPopularityRankWByScore(100);

        logger.info("syncRecommendHotuserInfo-读库，根据得分倒序获得人气用户。数量：{}，头部数据：{}",
                CollectionUtils.isEmpty(mongoList) ? 0 : mongoList.size(),
                CollectionUtils.isEmpty(mongoList) ? null : mongoList.get(0)
        );

        List<String> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(mongoList)) {
            result.addAll(mongoList);
            app.barredis.set(cacheHotuserDefault, JacksonUtil.obj2String(result), 5 * 60L);
        }

        if (!CollectionUtils.isEmpty(list)) {
            result = new ArrayList<>();
            result.addAll(list.stream().map(l -> l.ItemCode).distinct().collect(Collectors.toList()));
            app.barredis.set(cacheHotuser, JacksonUtil.obj2String(result), 5 * 60L);
        }

    }

    /**
     * 热门基金吧列表同步到Redis
     */
    private void syncRecommendHotbarInfo() throws JsonProcessingException {

        // 热门基金吧
        String cacheHotBar = BarRedisKey.FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_HOTBAR;
        // 默认热门基金吧
        String cacheHotBarDefault = BarRedisKey.FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_HOTBAR_DEFAULT;

        List<SetRecommonInfoEntity> list = setRecommonDao.getRecommonInfoEntities(EnumRecommonType.HOT_FUND_BAR.getValue());

        logger.info("syncRecommendHotbarInfo-读库，根据类型获取配置内容列表。数量：{}，头部数据：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JacksonUtil.obj2String(list.get(0))
        );

        // MongoDb获取热门基金吧
        List<String> mongoList = hotFundBarDao.getHotFundBarByScore(100);

        logger.info("syncRecommendHotbarInfo-读库，根据得分倒序获得热门基金吧。数量：{}，头部数据：{}",
                CollectionUtils.isEmpty(mongoList) ? 0 : mongoList.size(),
                CollectionUtils.isEmpty(mongoList) ? null : mongoList.get(0)
        );

        List<String> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(mongoList)) {
            result.addAll(mongoList);
            app.barredis.set(cacheHotBarDefault, JacksonUtil.obj2String(result), 5 * 60L);
        }

        if (!CollectionUtils.isEmpty(list)) {
            result = new ArrayList<>();
            result.addAll(list.stream().map(l -> l.ItemCode).distinct().collect(Collectors.toList()));
            app.barredis.set(cacheHotBar, JacksonUtil.obj2String(result), 5 * 60L);
        }

    }

    /**
     * 同步自定义模块明细列表
     */
    private void syncRecommendSelfInfo() throws JsonProcessingException {

        // 自定义模块明细列表
        String cacheKey = BarRedisKey.FUND_GUBA_SERVICE_COMMUNITY_USERDEFINED_DETAILS;

        List<SetRecommonSelfInfo> list = setRecommonDao.getRecommonSelfInfoEntities();

        logger.info("syncRecommendSelfInfo-读库，根据自定义模块内容列表。数量：{}，头部数据：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JacksonUtil.obj2String(list.get(0))
        );

        if (!CollectionUtils.isEmpty(list)) {
            app.barredis.set(cacheKey, JacksonUtil.obj2String(list));
        }

    }

    /**
     * 定投页 大V信息
     */
    private void syncRecommendDTInfo() throws JsonProcessingException {

        // 定投页大v
        String cacheBigV = BarRedisKey.FUND_GUBA_SERVICE_COMMUNITY_RECOMMEND_BIGV;
        List<SetRecommonInfoEntity> list = setRecommonDao.getRecommonInfoEntities(EnumRecommonType.BIG_V.getValue());

        logger.info("syncRecommendDTInfo-读库，根据类型获取配置内容列表。数量：{}，头部数据：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JacksonUtil.obj2String(list.get(0))
        );

        if (!CollectionUtils.isEmpty(list)) {

            List<BigV> result = list.stream().map(m -> {
                BigV item = new BigV();
                item.ItemCode = m.ItemCode;
                item.ItemName = m.ItemName;
                item.UserProfile = m.UserProfile;
                return item;
            }).collect(Collectors.toList());

            app.barredis.set(cacheBigV, JacksonUtil.obj2String(result));
        }
    }

    /**
     * 根据推荐模块的Type获取位置type
     * 2001 - 用户模块 2002 -基金吧模块  2003 - 话题模块 2004 - 自定义模块
     *
     * @param type
     * @return
     */
    private int getModuleType(int type) {
        int moduleType = 1;
        switch (type) {
            case 2001:
                moduleType = 4;
                break;
            case 2002:
                moduleType = 6;
                break;
            case 2003:
                moduleType = 5;
                break;
            case 2004:
                moduleType = 2;
                break;
        }
        return moduleType;
    }

    private class BigV {
        public String ItemCode;
        public String ItemName;
        public String UserProfile;
    }
}
