package ttfund.web.communityservice.timedtask.jijinbarJobs.repair;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.service.passportApiServiceImpl;
import ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer.PassportUserInfoJob;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.List;

/**
 * 通行证用户信息手动修复job
 */
@Slf4j
@JobHandler("PassportUserInfoRepairByApiJob")
@Component
public class PassportUserInfoRepairByApiJob extends IJobHandler {

    @Autowired
    private PassportUserInfoJob passportUserInfoJob;

    @Autowired
    private passportApiServiceImpl passportApiService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        String ids = null;

        try {
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                ids = jsonObject.getString("ids");
            }


            log.info("第零步，打印参数。ids：{}", ids);

            deal(CommonUtils.toList(ids, ","));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(List<String> ids) throws Exception {


        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        int i = 0;
        for (String id : ids) {
            i++;

            String html = passportApiService.getUserOpenId(id);
            JSONObject jsonObject = null;
            if (StringUtils.hasLength(html)) {
                jsonObject = JSON.parseObject(html);
            }

            log.info("一.查询api。第{}/{}个，数据：{}",
                i,
                ids.size(),
                JSON.toJSONStringWithDateFormat(jsonObject, DateUtil.datePattern)
            );

            StringBuilder builder = new StringBuilder();
            try {
                if (jsonObject != null && jsonObject.getJSONObject("Data") != null) {
                    PassportUserInfoModelNew model = new PassportUserInfoModelNew();
                    model._id = jsonObject.getJSONObject("Data").getString("UID");
                    model.PassportID = jsonObject.getJSONObject("Data").getString("UID");
                    model.NickName = jsonObject.getJSONObject("Data").getString("UAlias");
                    model.Introduce = jsonObject.getJSONObject("Data").getString("UIntroduce");
                    model.Gender = jsonObject.getJSONObject("Data").getString("UGender");
                    model.OpenID = jsonObject.getJSONObject("Data").getString("OpenId");
                    model.Registertime = jsonObject.getJSONObject("Data").getString("URegisterTime");

                    passportUserInfoJob.dealModel(model, builder);

                    log.info("二.操作详情。第{}/{}个，详情：{}",
                        i,
                        ids.size(),
                        builder.toString()
                    );
                }
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
                log.error("二.操作详情。第{}/{}个，详情：{}",
                    i,
                    ids.size(),
                    builder.toString()
                );
            }
        }

    }

}
