package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.ModelPortfolioModel;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.ModelPortfolioMongoDao;
import ttfund.web.communityservice.dao.msyql.ModelPortfolioDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;

/**
 * 模拟组合同步job  mysql->mongo
 * <p>
 * 备注：需求 #596771 【组合3.9】模拟组合重构
 */
@Slf4j
@JobHandler("ModelPortfolioSyncJob")
@Component
public class ModelPortfolioSyncJob extends IJobHandler {

    private static List<String> setOnInsertFields = Arrays.asList("createTime");

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private ModelPortfolioDao modelPortfolioDao;

    @Autowired
    private ModelPortfolioMongoDao modelPortfolioMongoDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            log.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.MODELPORTFOLIOSYNCJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            deal(batchReadCount);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount) throws Exception {

        String breakpointName = UserRedisConfig.MODELPORTFOLIOSYNCJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            log.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("第一步，读取断点。断点:{}", breakpoint);

        List<ModelPortfolioModel> list = modelPortfolioDao.getByUpdateTime(breakpointDate, batchReadCount);

        log.info("第二步，读取数据。数量:{}，头部数据：{}",
                list == null ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = list.get(list.size() - 1).getUpdateTime();

            List<Map<String, Object>> mapList = new ArrayList<>(list.size());
            Map<String, Object> map = null;
            for (ModelPortfolioModel a : list) {
                map = CommonUtils.beanToMap(a);
                map.put("_id", a.getCode());
                map.put("createTime", new Date());
                map.put("updateTime", new Date());

                map.remove("code");

                mapList.add(map);
            }

            if (!CollectionUtils.isEmpty(mapList)) {
                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 200);
                for (List<Map<String, Object>> batch : batchList) {
                    modelPortfolioMongoDao.upsertManyBySetWithSetOnInsertFields(batch, setOnInsertFields, "_id");
                }
            }

            log.info("第三步，读取数据。数量:{}，头部数据：{}",
                    mapList == null ? 0 : mapList.size(),
                    CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONStringWithDateFormat(mapList.get(0), DateUtil.datePattern)
            );
        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        log.info("第四步，更新断点。断点：{}", breakpoint);
    }

}
