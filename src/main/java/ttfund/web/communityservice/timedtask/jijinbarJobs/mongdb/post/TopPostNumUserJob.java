package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfo;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 发帖数最多用户统计
 */
@JobHandler("TopPostNumUserJob")
@Component
public class TopPostNumUserJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(TopPostNumUserJob.class);

    @Autowired
    private PostDao postDao;

    @Autowired
    private App app;

    @Value("${user.toppost.num.threshold:1000}")
    private int topPostNumThreshold;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        LOGGER.info("发帖数最多用户统计开始");

        ReturnT<String> result = ReturnT.SUCCESS;

        try {

            Date begin = DateUtil.calendarDateByMonth(-1);
            int batchCount = 10000;

            Map<String, Integer> userPostNumMap = new ConcurrentHashMap<>();

            for (int i = 0; i < 1000; i++) {

                List<PostInfo> postList = postDao.getPostListByTime(begin, batchCount, "UID", "TIME");

                for (PostInfo postInfo : postList) {
                    Integer num = userPostNumMap.getOrDefault(postInfo.UID, 0);
                    userPostNumMap.put(postInfo.UID, num + 1);
                }

                begin = postList.get(postList.size() - 1).TIME;

                if (postList.size() < batchCount) {
                    break;
                }
            }

            Set<String> topUserSet = userPostNumMap.entrySet()
                .stream().filter(w -> w.getValue() > topPostNumThreshold)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

            app.barredis.set(BarRedisKey.USER_POST_TOP, JacksonUtil.obj2StringNoException(topUserSet), 7 * 24 * 3600L);

        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
        }

        LOGGER.info("发帖数最多用户统计结束");

        return result;
    }
}
