package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.useracc;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.spel.ast.NullLiteral;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.PostAuthorFlag;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@JobHandler(value = "passportUserUpdateJob")
@Component
public class PassportUserUpdateJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(PassportUserUpdateJob.class);
    @Autowired
    UserRedisDao userRedisDao;

    @Autowired
    PassportUserInfoDao passportUserInfoDao;
    @Override
    public ReturnT<String> execute(String param) {
       String logPre="【通信证用户信息缓存更新】redis.useracc.PassportUserUpdateJob: ";

        logger.info(logPre+" 开始执行");
        ReturnT<String> result= ReturnT.SUCCESS;

        Date paramDate=null;
        String paramPid="";

        try {
            logger.info(logPre+" 参数信息:"+param);
            if(param!=null && !param.isEmpty()) {
                try {
                    String[] arr=param.split("\\|");
                    paramDate= DateUtil.strToDate(arr[0]);
                    if(arr.length>1){
                        paramPid=arr[1];
                    }
                }catch (Exception ex){
                    logger.error(logPre+" 参数异常,涉及数据:"+param+",异常信息："+ ex.getMessage(), ex);
                }
            }


            /**
             * 根据mondb 更新时间把mondb数据刷新到redis
             * 1.根据时间断点读取断点时间
             * 2.根据断点时间从mongdb读取数据
             * 3.把数据更新到redis
             */
            //每页大小
            int limit = 5000;
            String breakTimeName = "passportUserUpdateJobUpdateTime";

            Date breakTime = null;
            //获取断点时间
            Date lastUpdateTime =null;
             if(paramDate!=null){
                 lastUpdateTime=paramDate;
             }else {
                 lastUpdateTime= userRedisDao.getBreakTime(breakTimeName);
             }
            if (lastUpdateTime == null) {
                lastUpdateTime = DateUtil.calendarDateByDays(-1);
            }
            logger.info(logPre+" 上次断点："+DateUtil.dateToStr(lastUpdateTime));

            List<PassportUserInfoModelNew> list= null;
            if (paramPid!=null && !paramPid.isEmpty()){
                List<String> ids= Arrays.asList(paramPid);
                list= passportUserInfoDao.getPassportUserInfoByIds(ids);
            }
            else {
                list=   passportUserInfoDao.getPassportUserInfoListByUpdateTime(lastUpdateTime,limit);
            }
            if(!CollectionUtils.isEmpty(list)){
                List<Date> listDate = list.stream().map(a -> a.UpdateTime).distinct().collect(Collectors.toList());
                //获取最大时间做为下次断点
                breakTime = Collections.max(listDate);
                //是否有异常信息
                boolean ishaveErro=false;
                for(PassportUserInfoModelNew item : list){
                    if (item._id!=null&&!item._id.isEmpty()){
                        try{
                           boolean isSuccess=  userRedisDao.setPassportUserInfoNewCache(item);
                           if(!isSuccess){
                                isSuccess=  userRedisDao.setPassportUserInfoNewCache(item);
                           }
                           if (!isSuccess){
                               ishaveErro=true;
                               logger.error(logPre +" 更新缓存失败，涉及通行证ID:"+item._id);
                           }else {
                               logger.info(logPre +" 更新缓存成功，涉及通行证ID:"+item._id);
                           }
                        }
                        catch (Exception e){
                            ishaveErro=true;
                            logger.error(logPre +" 更新缓存失败，涉及通行证ID:"+item._id+" 异常信息："+ e.getMessage(), e);
                        }
                    }
                }
                //中间无异常时，设置新的时间断点
                if (!ishaveErro){
                    //缓存默认为7天
                    userRedisDao.setBreakTime(breakTimeName,breakTime);
                }
            }
            result= ReturnT.SUCCESS;
        }
        catch (Exception e) {
            logger.error(logPre + e.getMessage(), e);
            result= ReturnT.FAIL;
        }
        logger.info(logPre+" 结束执行");
        return  result;
    }
}
