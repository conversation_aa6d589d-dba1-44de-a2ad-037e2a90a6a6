package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.useracc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.core.register.AppCore;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.mongo.PassportFundMrgModel;
import ttfund.web.communityservice.bean.jijinBar.user.BigVUserInfo;
import ttfund.web.communityservice.config.redis.UserAccRedisKey;
import ttfund.web.communityservice.dao.mongo.BigVUserInfoDao;
import ttfund.web.communityservice.dao.mongo.PassportFundMrgDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 大V/基金经理信息
 */
@JobHandler(value = "bigVFundMgrJob")
@Component
public class BigVFundMgrJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(BigVFundMgrJob.class);

    @Autowired
    private AppCore appCore;

    @Autowired
    private PassportFundMrgDao passportFundMrgDao;

    @Autowired
    private BigVUserInfoDao bigVUserInfoDao;

    @Override
    public ReturnT<String> execute(String s) {


        try {

            String bigVConfig = null;
            String fundManagerConfig = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                bigVConfig = jsonObject.getString("bigVConfig");
                fundManagerConfig = jsonObject.getString("fundManagerConfig");
            }

            if (bigVConfig == null) {
                bigVConfig = "5";
            }
            if (fundManagerConfig == null) {
                fundManagerConfig = "10";
            }

            logger.info("第零步，打印参数。bigVConfig：{}，fundManagerConfig：{}",
                    bigVConfig,
                    fundManagerConfig
            );

            logicDeal(Double.parseDouble(bigVConfig), Double.parseDouble(fundManagerConfig));

        } catch (Exception e) {

        }
        return ReturnT.SUCCESS;
    }

    private void logicDeal(double bigVScore, double fundManagerScore) {

        try {

            getSpecialUser(bigVScore, fundManagerScore);


        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    private void getSpecialUser(double bigVScore, double fundManagerScore) throws Exception {

        List<Map<String, Object>> ret = new ArrayList<>();

        // 获取基金经理
        List<PassportFundMrgModel> list = getFundManagerList();

        logger.info("第一步，读取基金经理。数量:{}，头部数据：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        List<Map<String, Object>> mgrList = null;
        if (!CollectionUtils.isEmpty(list)) {
            mgrList = list.stream()
                    .filter(l -> !StringUtils.isEmpty(l.MGRID))
                    .map(l ->
                            {
                                Map<String, Object> map = new HashMap<>();
                                map.put("UID", l.PassportUID);
                                map.put("MGRID", l.MGRID);
                                return map;
                            }
                    )
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(mgrList)) {
                String cacheKey = UserAccRedisKey.FUND_MGR_LIST;
                appCore.redisuserwrite.set(cacheKey, JacksonUtil.obj2StringNoException(mgrList));
            }

        }

        logger.info("第二步，基金经理数据写redis。数量:{}，头部数据：{}",
                CollectionUtils.isEmpty(mgrList) ? 0 : mgrList.size(),
                CollectionUtils.isEmpty(mgrList) ? null : JSON.toJSONStringWithDateFormat(mgrList.get(0), DateUtil.datePattern)
        );


        List<String> uids = new ArrayList<>();

        if (!CollectionUtils.isEmpty(list)) {

            uids.addAll(list.stream().filter(l -> l.IsV > 0).map(s -> s.PassportUID).collect(Collectors.toList()));

            for (PassportFundMrgModel i : list) {

                Map<String, Object> user = new HashMap<>();
                user.put("UID", i.PassportUID);
                user.put("SCORE", 0D);

                if (!StringUtils.isEmpty(i.MGRID)) {
                    user.put("SCORE", fundManagerScore);
                    ret.add(user);
                    continue;
                }

                if (i.IsV > 0) {
                    user.put("SCORE", bigVScore);
                    ret.add(user);
                }
            }
        }

        // 获取大V用户
        List<BigVUserInfo> ulist = bigVUserInfoDao.getBigVList(new String[]{"0", "3"});

        logger.info("第三步，获取大V用户。数量:{}，头部数据：{}",
                CollectionUtils.isEmpty(ulist) ? 0 : ulist.size(),
                CollectionUtils.isEmpty(ulist) ? null : JSON.toJSONStringWithDateFormat(ulist.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(ulist)) {
            //基金经理
            Set<String> mgrs = ret.stream()
                    .map(l -> (String) l.get("UID"))
                    .collect(Collectors.toSet());

            //非基金经理大V
            List<BigVUserInfo> lists = ulist.stream()
                    .filter(l -> !mgrs.contains(l.uid))
                    .collect(Collectors.toList());

            for (BigVUserInfo item : lists) {
                Map<String, Object> user = new HashMap<>();
                user.put("UID", item.uid);
                user.put("SCORE", 0D);

                user.put("SCORE", bigVScore);
                ret.add(user);
            }
            uids.addAll(ulist.stream().map(s -> s.uid).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(uids)) {
            String cacheKey = UserAccRedisKey.BIGV_USER_IDS;
            uids = uids.stream().distinct().collect(Collectors.toList());
            appCore.redisuserwrite.set(cacheKey, JacksonUtil.obj2StringNoException(uids));
        }

        logger.info("第四步，大V用户数据写redis。数量:{}，头部数据：{}",
                CollectionUtils.isEmpty(uids) ? 0 : uids.size(),
                CollectionUtils.isEmpty(uids) ? null : uids.get(0)
        );

        String cacheKey = UserAccRedisKey.BIGV_FUND_MGR_LIST;
        appCore.redisuserwrite.set(cacheKey, JacksonUtil.obj2String(ret));

        logger.info("第五步，大V/基金经理数据写redis。数量:{}，头部数据：{}",
                CollectionUtils.isEmpty(ret) ? 0 : ret.size(),
                CollectionUtils.isEmpty(ret) ? null : JSON.toJSONStringWithDateFormat(ret.get(0), DateUtil.datePattern)
        );

    }

    /**
     * 获取基金经理通行证ID
     *
     * @return
     */
    public List<PassportFundMrgModel> getFundManagerList() {

        List<PassportFundMrgModel> list = passportFundMrgDao.getAll();

        if (!CollectionUtils.isEmpty(list)) {
            list = list.stream()
                    .filter(a -> a.IsDel == 0
                            && !StringUtils.isEmpty(a.PassportUID)
                            && !StringUtils.isEmpty(a.MGRID))
                    .collect(Collectors.toList());
            return list;
        }
        return null;
    }
}
