package ttfund.web.communityservice.timedtask.jijinbarJobs.postOperate;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.base.base.HttpRequestMethod;
import com.ttfund.web.base.helper.HttpHelper;
import com.ttfund.web.base.helper.IPUtils;
import com.ttfund.web.core.model.hqmodel.mongomodel.FundAppCodeMongodb;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.fundManager.FundManagerNewsInitModel;
import ttfund.web.communityservice.bean.jijinBar.post.fundManager.FundManagerNewsModel;
import ttfund.web.communityservice.bean.jijinBar.post.fundManager.FundManagerNewsVerticaModel;
import ttfund.web.communityservice.bean.jijinBar.post.fundManager.ManagerIdAndPassportIdModel;
import ttfund.web.communityservice.bean.jijinBar.post.guba.BarInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.guba.FundUploadPostResponse;
import ttfund.web.communityservice.bean.jijinBar.post.guba.ReqeustCodeDealRes;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.NewsRedisConstantConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.FDBMReportDao;
import ttfund.web.communityservice.dao.mongo.PassportFundMrgDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.dao.vertica.FundManagerNewsVerticaDao;
import ttfund.web.communityservice.enums.BarCodeInnerEnum;
import ttfund.web.communityservice.service.common.CommonDataCache;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.StringUtil;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基金经理动态自动发帖
 */
@JobHandler(value = "fundManagerNewsUploadPostJob")
@Component
public class FundManagerNewsUploadPostJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(FundManagerNewsUploadPostJob.class);

    private static String logPre = "fundManagerNewsUploadPostJob";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    public FDBMReportDao fdbmReportDao;

    @Autowired
    private FundManagerNewsVerticaDao fundManagerNewsVerticaDao;

    @Autowired
    private PassportFundMrgDao passportFundMrgDao;

    @Autowired
    private CommonDataCache commonDataCache;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        StringBuilder logBuilder = null;
        FundManagerNewsInitModel initModel = null;
        try {
            logBuilder = new StringBuilder(2048);
            long start = System.currentTimeMillis();

            //第一步，获取上一次发帖时间
            Date lastTimePoint = null;
            if (StringUtils.hasLength(s)) {
                initModel = JSON.parseObject(s, FundManagerNewsInitModel.class);
                if (initModel != null) {
                    lastTimePoint = initModel.getLastTime();
                }
                if (lastTimePoint != null) {
//                    userRedisDao.setBreakTime(UserRedisConfig.fundManagerNewsUploadPostJob_lastTimePoint, lastTimePoint);
                    userRedisDao.set(UserRedisConfig.fundManagerNewsUploadPostJob_lastTimePoint, DateUtil.dateToStr(lastTimePoint), 360 * 24 * 3600L);
                }
            }

            if (initModel == null || lastTimePoint == null) {
                lastTimePoint = userRedisDao.getBreakTime(UserRedisConfig.fundManagerNewsUploadPostJob_lastTimePoint);
                if (lastTimePoint == null) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.SECOND, 0);
                    calendar.set(Calendar.MILLISECOND, 0);
                    lastTimePoint = calendar.getTime();
                }
            }
            long step1 = System.currentTimeMillis();
            logBuilder.append(logPre);
            logBuilder.append("第一步，获取上一次发帖时间进入，耗时毫秒数：");
            logBuilder.append((step1 - start));
            logBuilder.append("。 上一次发帖时间lastTimePoint:");
            logBuilder.append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(lastTimePoint));
            logBuilder.append("\n");

            //第二步，用上一次发帖时间查询mongo获取基金经理动态
            List<FundManagerNewsModel> mongoBackList = null;
            if (initModel != null && StringUtils.hasLength(initModel.getNewsIdList())) {
                //第一次启动job时，会传入指定的样本进行测试
                mongoBackList = fdbmReportDao.getFundManagerNewsById(CommonUtils.toList(initModel.getNewsIdList(), ","));
            } else {
                mongoBackList = fdbmReportDao.getFundManagerNewsByUpdateTime(lastTimePoint, appConstant.fundManagerNewsUploadPostJob_size_limit);
            }

            long step2 = System.currentTimeMillis();
            logBuilder.append(logPre);
            logBuilder.append("第二步，用上一次发帖时间查询mongo获取基金经理动态进入，耗时毫秒数：");
            logBuilder.append((step2 - step1));
            logBuilder.append("。 获取总条数:");
            logBuilder.append(mongoBackList == null ? 0 : mongoBackList.size());
            logBuilder.append("\n");

            //第三步，去重处理
            List<FundManagerNewsVerticaModel> alreadyPost = fundManagerNewsVerticaDao.getListByNewsUpdateTime(lastTimePoint);
            if (!CollectionUtils.isEmpty(mongoBackList) && !CollectionUtils.isEmpty(alreadyPost)) {
                Set<String> hashSet = new HashSet<>(mongoBackList.size() * 4 / 3 + 1);
                alreadyPost.forEach(item -> {
                    StringBuilder builder = new StringBuilder(25);
                    if (item.getFundCode() != null) {
                        builder.append(item.getFundCode());
                    }
                    if (item.getNewsStyle() != null) {
                        builder.append(item.getNewsStyle());
                    }
                    if (item.getNewsDate() != null) {
                        builder.append(item.getNewsDate());
                    }
                    hashSet.add(builder.toString());
                });
                if (!CollectionUtils.isEmpty(hashSet)) {
                    mongoBackList = mongoBackList.stream().filter(item -> !hashSet.contains(item.getId())).collect(Collectors.toList());
                }
            }
            long step3 = System.currentTimeMillis();
            logBuilder.append(logPre);
            logBuilder.append("第三步，去重处理进入，耗时毫秒数：");
            logBuilder.append((step3 - step2));
            logBuilder.append("。 去重后总条数:");
            logBuilder.append(mongoBackList == null ? 0 : mongoBackList.size());
            logBuilder.append("\n");

            //第四步，读取敏感词并进行敏感词过滤
            Map<String, String> recordIdToStateMap = new HashMap<>();//一条发帖记录到它对应的状态 的映射map
            Map<String, String> recordIdToPostIdMap = new HashMap<>();//一条发帖记录到它对应的帖子id 的映射map
            Map<String, String> managerIdToPassportIdMap = new HashMap<>();//基金经理id到通行证id 的映射map
            List<String> managerIdList = new ArrayList<>();//基金经理id列表
            if (!CollectionUtils.isEmpty(mongoBackList)) {
                for (FundManagerNewsModel item : mongoBackList) {
                    if (StringUtils.hasLength(item.getManagerId())) {
                        item.setManagerIdList(CommonUtils.toList(item.getManagerId(), ","));
                        if (!CollectionUtils.isEmpty(item.getManagerIdList())) {
                            managerIdList.addAll(item.getManagerIdList());
                        }
                    }
                }
                //获取敏感词，及进行敏感词过滤
                List<String> abortWords = getAbortWords();
                if (!CollectionUtils.isEmpty(abortWords)) {
                    for (FundManagerNewsModel item : mongoBackList) {
                        if (StringUtils.hasLength(item.getManagerId()) && StringUtils.hasLength(item.getContent())) {
                            for (String abortWord : abortWords) {
                                if (!CollectionUtils.isEmpty(item.getManagerIdList()) && item.getContent().contains(abortWord)) {
                                    for (String managerId : item.getManagerIdList()) {
                                        //1：发帖成功，2：包含敏感词不发贴，3：股吧发帖失败，4：基金经理未绑定通行证 5.动态内容为空 6.其它原因失败
                                        recordIdToStateMap.put(concat(item.getId(), managerId), "2");
                                    }
                                }
                            }
                        }
                    }
                }
            }
            long step4 = System.currentTimeMillis();
            logBuilder.append(logPre);
            logBuilder.append("第四步，读取敏感词并进行敏感词过滤进入，耗时毫秒数：");
            logBuilder.append((step4 - step3));
            logBuilder.append("\n");

            //第五步，获取基金经理id到通行证id的映射关系
            List<ManagerIdAndPassportIdModel> bindRelationList = null;
            if (!CollectionUtils.isEmpty(managerIdList)) {
                bindRelationList = passportFundMrgDao.getBindRelationByManagerIdList(managerIdList.stream().distinct().collect(Collectors.toList()));
                if (!CollectionUtils.isEmpty(bindRelationList)) {
                    bindRelationList.forEach(item -> managerIdToPassportIdMap.put(item.getManagerId(), item.getPassportId()));
                }
            }
            long step5 = System.currentTimeMillis();
            logBuilder.append(logPre);
            logBuilder.append("第五步，获取基金经理id到通行证id的映射关系进入，耗时毫秒数：");
            logBuilder.append((step5 - step4));
            logBuilder.append("。 获取总条数:");
            logBuilder.append(bindRelationList == null ? 0 : bindRelationList.size());
            logBuilder.append("\n");

            //第六步,调股吧发帖接口发帖并进行结果记录
            if (!CollectionUtils.isEmpty(mongoBackList)) {
                callGubaUploadPostApi(mongoBackList, recordIdToStateMap, recordIdToPostIdMap, managerIdToPassportIdMap);
            }
            long step6 = System.currentTimeMillis();
            logBuilder.append(logPre);
            logBuilder.append("第六步,调股吧发帖接口发帖并进行结果记录进入，耗时毫秒数：");
            logBuilder.append((step6 - step5));
            logBuilder.append("\n");

            //第七步，发帖结果落vertica
            List<FundManagerNewsVerticaModel> verticaModelList = null;
            if (!CollectionUtils.isEmpty(mongoBackList)) {
                verticaModelList = createVerticaModelList(mongoBackList, recordIdToStateMap, recordIdToPostIdMap);
                if (!CollectionUtils.isEmpty(verticaModelList)) {
                    fundManagerNewsVerticaDao.insertList(verticaModelList);
                }
            }
            long step7 = System.currentTimeMillis();
            logBuilder.append(logPre);
            logBuilder.append("第七步，发帖结果落vertica进入，耗时毫秒数：");
            logBuilder.append((step7 - step6));
            logBuilder.append("。 发帖结果总条数:");
            logBuilder.append(verticaModelList == null ? 0 : verticaModelList.size());
            logBuilder.append("\n");

            //第八步，更新缓存中的上一次最近发帖时间
            Date updateTime = null;
            if (!CollectionUtils.isEmpty(mongoBackList) && (initModel == null || StringUtils.isEmpty(initModel.getNewsIdList()))) {
                updateTime = mongoBackList.get(mongoBackList.size() - 1).getUpdateTime();
//                userRedisDao.setBreakTime(UserRedisConfig.fundManagerNewsUploadPostJob_lastTimePoint, updateTime);
                userRedisDao.set(UserRedisConfig.fundManagerNewsUploadPostJob_lastTimePoint, DateUtil.dateToStr(updateTime), 360 * 24 * 3600L);
                lastTimePoint = updateTime;
            }
            long step8 = System.currentTimeMillis();
            logBuilder.append(logPre);
            logBuilder.append("第八步，更新缓存中的上一次最近发帖时间进入，耗时毫秒数：");
            logBuilder.append((step8 - step7));
            logBuilder.append("。 更新缓存中的上一次最近发帖时间lastTimePoint：");
            logBuilder.append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(lastTimePoint));
            logBuilder.append("\n");

            logBuilder.append(logPre);
            logBuilder.append("完成，总耗时毫秒数：");
            logBuilder.append((step8 - start));
        } catch (Exception ex) {
            logBuilder.append(logPre);
            logBuilder.append("失败，捕获到异常ex。ex.getMessage:");
            logBuilder.append(ex.getMessage());
            logBuilder.append("\n");
            logBuilder.append("ex:");
            logBuilder.append(ex);
            logger.error(logBuilder.toString());
            XxlJobLogger.log(logBuilder.toString());
            return ReturnT.FAIL;
        }


        logger.info(logBuilder.toString());
        XxlJobLogger.log(logBuilder.toString());
        return ReturnT.SUCCESS;
    }


    /**
     * 发帖结果转换为落vertica对应表结构中的实体
     * @param newsList 基金经理动态list
     * @param recordIdToStateMap 一条发帖记录到它对应的状态 的映射map
     * @param recordIdToPostIdMap 一条发帖记录到它对应的帖子id 的映射map
     * @return 发帖结果落vertica的数据list
     */
    private List<FundManagerNewsVerticaModel> createVerticaModelList(List<FundManagerNewsModel> newsList,
                                                                     Map<String, String> recordIdToStateMap,
                                                                     Map<String, String> recordIdToPostIdMap) {
        List<FundManagerNewsVerticaModel> result = null;
        if (!CollectionUtils.isEmpty(newsList) && !CollectionUtils.isEmpty(recordIdToStateMap)) {
            result = new ArrayList<>(recordIdToStateMap.size());
            List<String> tempManagerIdList = null;
            String tempRecordId = null;
            FundManagerNewsVerticaModel tempVerticaModel = null;
            for (FundManagerNewsModel item : newsList) {
                tempManagerIdList = item.getManagerIdList();
                if (!CollectionUtils.isEmpty(tempManagerIdList)) {
                    for (String managerId : tempManagerIdList) {
                        tempRecordId = concat(item.getId(), managerId);
                        if (recordIdToStateMap.containsKey(tempRecordId)) {
                            tempVerticaModel = new FundManagerNewsVerticaModel();
                            tempVerticaModel.setFundCode(item.getFundCode());
                            tempVerticaModel.setNewsStyle(item.getStyle());
                            tempVerticaModel.setNewsDate(item.getReportDate());
                            tempVerticaModel.setManagerId(managerId);
                            tempVerticaModel.setPostId(recordIdToPostIdMap.get(tempRecordId));
                            tempVerticaModel.setState(recordIdToStateMap.get(tempRecordId));
                            tempVerticaModel.setNewsUpdateTime(item.getUpdateTime());
                            result.add(tempVerticaModel);
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 落vertica测试数据
     * 备注：仅作为测试用
     */
    private List<FundManagerNewsVerticaModel> createVerticaModelList_test(List<FundManagerNewsModel> newsList) {
        List<FundManagerNewsVerticaModel> result = null;
        if (!CollectionUtils.isEmpty(newsList)) {
            for (FundManagerNewsModel item : newsList) {
                if (StringUtils.hasLength(item.getManagerId())) {
                    item.setManagerIdList(CommonUtils.toList(item.getManagerId(), ","));
                }
            }

            result = new ArrayList<>();
            List<String> tempManagerIdList = null;
            String tempRecordId = null;
            FundManagerNewsVerticaModel tempVerticaModel = null;
            for (FundManagerNewsModel item : newsList) {
                tempManagerIdList = item.getManagerIdList();
                if (!CollectionUtils.isEmpty(tempManagerIdList)) {
                    for (String managerId : tempManagerIdList) {
                        tempRecordId = concat(item.getId(), managerId);

                        tempVerticaModel = new FundManagerNewsVerticaModel();
                        tempVerticaModel.setFundCode(item.getFundCode());
                        tempVerticaModel.setNewsStyle(item.getStyle());
                        tempVerticaModel.setNewsDate(item.getReportDate());
                        tempVerticaModel.setManagerId(managerId);
                        tempVerticaModel.setPostId("123456789");
                        tempVerticaModel.setState("1");
                        tempVerticaModel.setNewsUpdateTime(item.getUpdateTime());
                        result.add(tempVerticaModel);

                    }
                }
            }
        }

        return result;
    }

    /**
     * 拼接mongo的主键_id 和 基金经理id
     * 备注：该方法没有做空值处理，请在方法外做
     */
    private String concat(String _id, String managerId) {
        StringBuilder builder = new StringBuilder(_id.length() + managerId.length());
        builder.append(_id);
        builder.append(managerId);
        return builder.toString();
    }

    /**
     * 调股吧接口发帖,并记录发帖结果
     * 备注：方法内部发帖处理只专门适用于普通基金吧，且有特殊处理
     * @param dataList 基金经理动态list
     * @param recordIdToStateMap 一条发帖记录到它对应的状态 的映射map
     * @param recordIdToPostIdMap 一条发帖记录到它对应的帖子id的映射map
     * @param managerIdToPassportIdMap 基金经理id到通行证id 的映射map
     */
    private void callGubaUploadPostApi(List<FundManagerNewsModel> dataList,
                                       Map<String, String> recordIdToStateMap,
                                       Map<String, String> recordIdToPostIdMap,
                                       Map<String, String> managerIdToPassportIdMap) {

        if (!CollectionUtils.isEmpty(dataList)) {
            String tempPassportId = null;
            String tempRecordId = null;
            for (FundManagerNewsModel item : dataList) {
                FundUploadPostResponse result = new FundUploadPostResponse();
                ReqeustCodeDealRes gubaCodeModel = commonDataCache.reqeustCodeDeal(item.getFundCode());
                if (!CollectionUtils.isEmpty(item.getManagerIdList())) {
                    for (String managerId : item.getManagerIdList()) {
                        tempPassportId = managerIdToPassportIdMap.get(managerId);
                        tempRecordId = concat(item.getId(), managerId);
                        if (StringUtils.hasLength(gubaCodeModel.getGuba_code())
                                && StringUtils.hasLength(item.getContent())
                                && StringUtils.hasLength(managerId)
                                && StringUtils.hasLength(tempPassportId)
                                && StringUtils.isEmpty(recordIdToStateMap.get(tempRecordId))) {
                            String title = generatePostTitle(item.getFundName(), item.getStyle(), item.getReportDate());
                            int capacity = 140 + (!StringUtils.hasLength(item.getContent()) ? 0 : item.getContent().length())
                                    + (!StringUtils.hasLength(title) ? 0 : title.length());
                            StringBuilder builder = new StringBuilder(capacity);
                            builder.append("&deviceid=");
                            builder.append("&version=");
                            builder.append("&product=Fund");
                            builder.append("&plat=");
                            builder.append("&ip=");
                            String ip = IPUtils.getlocalIp();
                            builder.append(ip == null ? "" : ip);
                            builder.append("&port=");

                            builder.append("&text=");
                            builder.append(item.getContent());
                            builder.append("&title=");
                            builder.append(title);

                            builder.append("&uid=");
                            builder.append(tempPassportId);
                            builder.append("&code=");
                            builder.append(gubaCodeModel.getGuba_code());

                            builder.append("&approvalfirst=1");

                            String url = appConstant.config_gubahostnewserver + "/postopt/api/post/postarticle";
                            String html = HttpHelper.request(url, builder.toString(), HttpRequestMethod.FORM, 15000, "utf-8", null, true);
                            if (StringUtils.hasLength(html)) {
                                result = JSON.parseObject(html, FundUploadPostResponse.class);
                            }
                            //1：发帖成功，2：包含敏感词不发贴，3：股吧发帖失败，4：基金经理未绑定通行证 5.动态内容为空 6.其它原因失败
                            if (result != null && result.rc != null && result.rc == 1 && result.main_post_id != 0L) {
                                recordIdToPostIdMap.put(tempRecordId, String.valueOf(result.main_post_id));
                                recordIdToStateMap.put(tempRecordId, "1");
                            } else {
                                recordIdToStateMap.put(tempRecordId, "3");
                            }
                        } else {
                            if (StringUtil.isNull(tempPassportId)) {
                                recordIdToStateMap.put(tempRecordId, "4");
                            } else if (StringUtil.isNull(item.getContent())) {
                                recordIdToStateMap.put(tempRecordId, "5");
                            } else if ("2".equals(recordIdToStateMap.get(tempRecordId))) {

                            } else {
                                recordIdToStateMap.put(tempRecordId, "6");
                            }
                        }
                    }
                }

            }
        }
    }

    /**
     * 根据基金名称 、报告类型 和 报告日期 生成发帖标题
     */
    private String generatePostTitle(String fundName, String style, String reportDate) {
        String title = "报告期内基金投资策略和运作分析";
        int length = title.length();
        if (StringUtil.isNotNull(fundName) && StringUtil.isNotNull(style) && StringUtil.isNotNull(reportDate)) {
            length = fundName.length() + 6 + 6 + 1 + title.length() + 2;
            StringBuilder builder = new StringBuilder(length);
            builder.append(fundName);
            String year = "";
            try {
                year = reportDate.split("-")[0];
            } catch (Exception ex) {

            }
            if (StringUtil.isNotNull(year)) {
                builder.append(year);
                builder.append("年度");
            }
            //01 一季报 02 中报 03 三季报 04 年报 05 二季报 06 四季报 07 其他
            switch (style) {
                case "04":
                    builder.append("年报-");
                    break;
                case "02":
                    builder.append("中报-");
                    break;
                case "01":
                    builder.append("第一季度报告-");
                    break;
                case "05":
                    builder.append("第二季度报告-");
                    break;
                case "03":
                    builder.append("第三季度报告-");
                    break;
                case "06":
                    builder.append("第四季度报告-");
                    break;
            }
            builder.append(title);
            title = builder.toString();
        }
        return title;
    }

    /**
     * 读取敏感词
     */
    public List<String> getAbortWords() {
        List<String> result = null;
        String value = app.newsRedis.get(NewsRedisConstantConfig.ASP_NET_FUND_SERVICE_NEWSSEARCHWORD_BLACK);
        if (StringUtils.hasLength(value)) {
            result = JSON.parseArray(value, String.class);
        }
        return result;
    }

}
