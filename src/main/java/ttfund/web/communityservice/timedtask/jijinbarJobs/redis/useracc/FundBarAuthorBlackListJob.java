package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.useracc;

import com.ttfund.web.core.register.AppCore;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.config.FundBarAuthorBlackListModel;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.redis.UserAccRedisKey;
import ttfund.web.communityservice.dao.mongo.FundBarAuthorBlackListDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.timedtask.jijinbarJobs.PostAuthorFlagJob;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@JobHandler(value = "FundBarBlackUserListJob")
@Component
public class FundBarAuthorBlackListJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(FundBarAuthorBlackListJob.class);
    private String logpre = "[FundBarAuthorBlackListJob]=>";
    @Autowired
    FundBarAuthorBlackListDao authorBlackListDao;

    @Autowired
    UserRedisDao userRedisDao;


    @Override
    public ReturnT<String> execute(String param) {
        try {
            logger.info(logpre + "FundBarAuthorBlackListJobStart");
            List<FundBarAuthorBlackListModel> list = authorBlackListDao.getAll();
            if (!CollectionUtils.isEmpty(list)) {
                List<String> uidList = list.stream().map(FundBarAuthorBlackListModel::getUID).collect(Collectors.toList());
                boolean result = userRedisDao.set(UserAccRedisKey.FUNDBAR_AUTHOR_BLACKLIST, uidList, UserAccRedisKey.REDIS_EXPIRE);
                if (!result) {
                    result = userRedisDao.set(UserAccRedisKey.FUNDBAR_AUTHOR_BLACKLIST, uidList, UserAccRedisKey.REDIS_EXPIRE);
                }
            }
            logger.info(logpre + "FundBarAuthorBlackListJobSEnd=>dataCount=>" + (list == null ? 0 : list.size()));
        } catch (Exception ex) {
            logger.error(logpre + ex.getMessage(), ex);
        }
        return ReturnT.SUCCESS;
    }
}
