package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.useracc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.core.register.AppCore;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.user.BlackListEntity;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.BlackListDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.redis.RedisUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户黑名单缓存
 */
@JobHandler(value = "blacklistRedisBusinessJob")
@Component
public class BlacklistRedisBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(BlacklistRedisBusinessJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private BlackListDao blackListDao;

    @Autowired
    private AppCore appCore;

    @Override
    public ReturnT<String> execute(String s) {

        String initBreakpoint = null;
        Integer batchReadCount = null;
        //修复时间  修复时间之前的数据的拉黑准确但取消拉黑不准确，修复时间之后的数据全都准确
        String repairTime = null;
        if (StringUtils.hasLength(s)) {
            JSONObject jsonObject = JSON.parseObject(s);
            initBreakpoint = jsonObject.getString("initBreakpoint");
            batchReadCount = jsonObject.getInteger("batchReadCount");
            repairTime = jsonObject.getString("repairTime");
        }

        if (batchReadCount == null) {
            batchReadCount = 5000;
        }

        logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，repairTime：{}",
                initBreakpoint,
                batchReadCount,
                repairTime
        );

        if (StringUtils.hasLength(initBreakpoint)) {
            if (StringUtils.hasLength(initBreakpoint)) {
                userRedisDao.set(UserRedisConfig.BLACKLISTREDISBUSINESSJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
            }

            logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

            return ReturnT.SUCCESS;
        }

        Date date = null;
        if (repairTime != null) {
            date = DateUtil.strToDate(repairTime);
        }
        syncBlacklist(batchReadCount, date);

        return ReturnT.SUCCESS;

    }

    /**
     * 将黑名单同步到Redis
     */
    private void syncBlacklist(int batchReadCount, Date repairTime) {

        try {

            String breakpointName = UserRedisConfig.BLACKLISTREDISBUSINESSJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("第一步，读取断点。断点:{}", breakpoint);

            int round = 0;

            while (true) {

                round++;

                List<BlackListEntity> list = blackListDao.getBlackListByUpdateTime(breakpointDate, batchReadCount);

                logger.info("第二步，读取黑名单数据-第{}轮。数量:{}，头部数据：{}",
                        round,
                        list == null ? 0 : list.size(),
                        CollectionUtils.isEmpty(list) ? null : JSON.toJSONString(list.get(0)));

                if (!CollectionUtils.isEmpty(list)) {

                    breakpointDate = list.stream().map(l -> l.UpdateTime).max(Comparator.naturalOrder()).get();

                    List<String> uidList = list.stream().map(a -> a.Uid).distinct().collect(Collectors.toList());

                    logger.info("第三步，提取用户-第{}轮。数量:{}，头部数据：{}",
                            round,
                            uidList == null ? 0 : uidList.size(),
                            CollectionUtils.isEmpty(uidList) ? null : uidList.get(0));

                    List<BlackListEntity> blackList = new ArrayList<>();
                    List<List<String>> batchList = CommonUtils.toSmallList2(uidList, 50);
                    for (List<String> batch : batchList) {
                        List<BlackListEntity> tempBlackList = blackListDao.getBlackListRecordByUids(batch);
                        blackList.addAll(tempBlackList);
                    }

                    logger.info("第四步，取提取用户所有黑名单-第{}轮。数量:{}，头部数据：{}",
                            round,
                            blackList == null ? 0 : blackList.size(),
                            CollectionUtils.isEmpty(blackList) ? null : JSON.toJSONString(blackList.get(0)));

                    LinkedMultiValueMap<String, BlackListEntity> multiValueMap = new LinkedMultiValueMap<>();
                    for (BlackListEntity a : blackList) {
                        multiValueMap.add(a.Uid, a);
                    }

                    logger.info("第五步，按用户分组-第{}轮。数量:{}",
                            round,
                            multiValueMap == null ? 0 : multiValueMap.size());

                    String cacheKey = null;
                    Set<Map.Entry<String, List<BlackListEntity>>> entrySet = multiValueMap.entrySet();
                    int i = 0;
                    for (Map.Entry<String, List<BlackListEntity>> entry : entrySet) {
                        i++;

                        cacheKey = String.format(BarRedisKey.FUND_GUBA_SERVICE_USERBLACKLIST_KAFKA, entry.getKey());

                        List<String> cacheValue = appCore.redisuserwrite.lrange(cacheKey, 0L, -1L);
                        if (cacheValue == null) {
                            cacheValue = new ArrayList<>();
                        }

                        Map<String, Date> sortMap = new HashMap<>();
                        Set<String> blackSet = cacheValue.stream().collect(Collectors.toSet());
                        for (BlackListEntity a : entry.getValue()) {
                            sortMap.put(a.TargetUid, a.UpdateTime);

                            if (repairTime != null) {
                                if (a.IsEnabled == 0) {
                                    if (a.UpdateTime.compareTo(repairTime) > 0) {
                                        blackSet.remove(a.TargetUid);
                                    }
                                } else {
                                    blackSet.add(a.TargetUid);
                                }
                            } else {
                                if (a.IsEnabled == 0) {
                                    blackSet.remove(a.TargetUid);
                                } else {
                                    blackSet.add(a.TargetUid);
                                }
                            }
                        }

                        //更新时间为null的排最前
                        cacheValue = blackSet.stream().sorted((o1, o2) -> {
                            Date date1 = sortMap.get(o1);
                            Date date2 = sortMap.get(o2);
                            if (date1 == null && date2 == null) {
                                return 0;
                            } else if (date1 != null && date2 == null) {
                                return 1;
                            } else if (date1 == null && date2 != null) {
                                return -1;
                            } else {
                                return date1.compareTo(date2);
                            }
                        }).collect(Collectors.toList());

                        appCore.redisuserwrite.del(cacheKey);
                        if (!CollectionUtils.isEmpty(cacheValue)) {
                            RedisUtils.rpush(appCore.redisuserwrite, cacheKey, cacheValue.toArray(new String[]{}));
                        }

                        logger.info("第五步，写redis详情-第{}轮。第{}/{}个，uid：{}，黑名单数量:{}，尾部数据：{}，记录数量：{}，尾部数据：{}",
                                round,
                                i,
                                entrySet.size(),
                                entry.getKey(),
                                cacheValue == null ? 0 : cacheValue.size(),
                                CollectionUtils.isEmpty(cacheValue) ? null : JSON.toJSONString(cacheValue.get(cacheValue.size() - 1)),
                                entry.getValue() == null ? 0 : entry.getValue().size(),
                                CollectionUtils.isEmpty(entry.getValue()) ? null : JSON.toJSONString(entry.getValue().get(entry.getValue().size() - 1))
                        );

                    }

                    logger.info("第五步，写redis完成-第{}轮。数量:{}",
                            round,
                            multiValueMap == null ? 0 : multiValueMap.size());

                }

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("第六步，更新断点-第{}轮。断点：{}", round, breakpoint);

                if (list == null || list.size() < batchReadCount) {
                    break;
                }

            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }

}
