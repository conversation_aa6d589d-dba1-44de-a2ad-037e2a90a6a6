package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.config.BigDataConfigEntity;
import ttfund.web.communityservice.bean.jijinBar.post.config.BigDataConfigResult;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.HWCardConfigDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 策略互动-华为卡片
 */
@JobHandler("BigDataConfigBizJob")
@Component
public class BigDataConfigBizJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(BigDataConfigBizJob.class);

    private static final String REGEX = "/Date\\(([\\d]+)\\)/";

    private static Pattern pattern = Pattern.compile(REGEX);

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private HWCardConfigDao hwCardConfigDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
            }

            logger.info("第零步，打印参数。initBreakpoint：{}", initBreakpoint);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.BIGDATACONFIGBIZJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);

                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);
                return ReturnT.SUCCESS;
            }

            syncBigDataConfig();

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 华为负一屏基金卡片
     */
    private void syncBigDataConfig() {

        try {
            String breakpointName = UserRedisConfig.BIGDATACONFIGBIZJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = "";

                logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("第一步，读取断点。断点:{}", breakpoint);

            String url = appConstant.webconfigApiAddr + "/webconfig/GetBigDataConfigList";
            if (StringUtils.hasLength(breakpoint)) {
                url = url + "?updateDate=" + URLEncoder.encode(breakpoint, "utf-8");
            }

            String html = HttpHelper.requestGet(url);
            BigDataConfigResult result = null;
            List<BigDataConfigEntity> bigDataList = null;
            if (StringUtils.hasLength(html)) {
                result = JSON.parseObject(html, BigDataConfigResult.class);
            }
            if (result != null && result.resultCode == 0) {
                bigDataList = result.datas;
            }

            logger.info("第二步，读取数据。数量:{}，头部id列表：{}",
                    bigDataList == null ? 0 : bigDataList.size(),
                    bigDataList == null ? null : bigDataList.stream().map(a -> a.id).limit(20).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(bigDataList)) {
                for (BigDataConfigEntity a : bigDataList) {
                    if (StringUtils.hasLength(a.UpdateDateTemp)) {
                        Matcher matcher = pattern.matcher(a.UpdateDateTemp);
                        while (matcher.find()) {
                            String group = matcher.group(1);
                            if (StringUtils.hasLength(group)) {
                                a.UpdateDate = new Date(Long.parseLong(group));
                            }
                            break;
                        }
                    }
                }

                breakpointDate = bigDataList.stream().max((Comparator.comparing(o -> o.UpdateDate))).get().UpdateDate;
                breakpointDate = DateUtil.calendarDateByHour(breakpointDate, -1);

                List<Map<String, Object>> mapList = new ArrayList<>(bigDataList.size());
                Map<String, Object> map = null;
                for (BigDataConfigEntity a : bigDataList) {
                    map = CommonUtils.beanToMap(a);
                    map.remove("id");
                    map.remove("UpdateDateTemp");
                    map.put("_id", a.id);
                    mapList.add(map);
                }
                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 200);
                for (List<Map<String, Object>> batch : batchList) {
                    hwCardConfigDao.upsertMany(batch);
                }
            }

            logger.info("第三步，数据写库。数量:{}，头部id列表：{}",
                    bigDataList == null ? 0 : bigDataList.size(),
                    bigDataList == null ? null : bigDataList.stream().map(a -> a.id).limit(20).collect(Collectors.toList()));

            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

            logger.info("第四步，更新断点。断点：{}", breakpoint);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }

}
