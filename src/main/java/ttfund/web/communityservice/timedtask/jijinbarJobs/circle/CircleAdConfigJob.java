package ttfund.web.communityservice.timedtask.jijinbarJobs.circle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.CircleConfigItem;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.CircleAdConfigDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 圈子配置job
 */
@Slf4j
@JobHandler(value = "CircleAdConfigJob")
@Component
public class CircleAdConfigJob extends IJobHandler {

    @Autowired
    private App app;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private CircleAdConfigDao circleAdConfigDao;

    @Override
    public ReturnT<String> execute(String s) throws JsonProcessingException {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            Long expireForAd = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                expireForAd = jsonObject.getLong("expireForAd");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (expireForAd == null) {
                expireForAd = 30 * 24 * 3600L;
            }

            log.info("0.打印参数。initBreakpoint：{}，batchReadCount：{}，expireForAd：{}",
                initBreakpoint,
                batchReadCount,
                expireForAd
            );

            if (StringUtils.hasLength(initBreakpoint)) {
                userRedisDao.set(UserRedisConfig.CIRCLEADCONFIGJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("0.初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            deal(expireForAd, batchReadCount);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(long expire, int batchReadCount) {
        String breakpointName = UserRedisConfig.CIRCLEADCONFIGJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);
        if (!StringUtils.hasLength(breakpoint)) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-30));

            log.error("0.读取断点为空，使用默认断点。断点：{}", breakpoint);
        }

        log.info("1.读取断点。断点：{}", breakpoint);

        Date breakpointDate = DateUtil.strToDate(breakpoint);
        List<CircleConfigItem> list = circleAdConfigDao.getList(CircleConfigItem.class, null, breakpointDate, batchReadCount);

        log.info("2.读取数据库。数量：{}，头部列表：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {
            breakpointDate = list.get(list.size() - 1).getUpdateTime();
        }

        List<CircleConfigItem> newCacheList = null;
        List<CircleConfigItem> cacheList = null;
        Map<String, CircleConfigItem> map = new HashMap<>();


        String key = BarRedisKey.CIRCLE_CONFIG;
        String value = app.barredis.get(key);
        cacheList = JSON.parseArray(value, CircleConfigItem.class);

        log.info("3.记录旧缓存数据。数量：{}，头部列表：{}",
            CollectionUtils.isEmpty(cacheList) ? 0 : cacheList.size(),
            CollectionUtils.isEmpty(cacheList) ? null : JSON.toJSONStringWithDateFormat(cacheList.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(cacheList)) {
            for (CircleConfigItem item : cacheList) {
                map.put(item.get_id(), item);
            }
        }

        if (!CollectionUtils.isEmpty(list)) {
            for (CircleConfigItem item : list) {
                map.put(item.get_id(), item);
            }
        }

        Date now = new Date();
        newCacheList = map.values().stream()
            .filter(a -> a.getIsDel() == 0
                && now.compareTo(a.getEndTime()) < 0)
            .sorted(((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime())))
            .collect(Collectors.toList());


        log.info("4.记录新缓存数据。数量：{}，头部列表：{}",
            CollectionUtils.isEmpty(newCacheList) ? 0 : newCacheList.size(),
            CollectionUtils.isEmpty(newCacheList) ? null : JSON.toJSONStringWithDateFormat(newCacheList.get(0), DateUtil.datePattern)
        );

        if (newCacheList == null) {
            newCacheList = new ArrayList<>();
        }
        app.barredis.set(key, JSON.toJSONString(newCacheList), expire);

        log.info("5.更新缓存");

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

        log.info("6.更新断点。断点：{}", breakpoint);
    }

}

