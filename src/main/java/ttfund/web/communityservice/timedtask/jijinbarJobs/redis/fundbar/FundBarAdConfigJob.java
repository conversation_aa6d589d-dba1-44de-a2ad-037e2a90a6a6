package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.config.ActivityCardConfig;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.FundBarAdConfigDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基金吧活动卡片和底部公告位配置job
 */
@JobHandler("FundBarAdConfigJob")
@Component
public class FundBarAdConfigJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(FundBarAdConfigJob.class);

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private FundBarAdConfigDao fundBarAdConfigDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {
            String initBreakpoint = null;
            Integer batchReadCount = null;
            Integer expire = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                expire = jsonObject.getInteger("expire");
            }

            if (batchReadCount == null) {
                batchReadCount = 10000;
            }
            if (expire == null) {
                expire = 30 * 24 * 3600;
            }

            logger.info("0.打印参数。initBreakpoint：{}，batchReadCount：{}，expire：{}", initBreakpoint, batchReadCount, expire);

            if (StringUtils.hasLength(initBreakpoint)) {
                userRedisDao.set(UserRedisConfig.FUNDBARADCONFIGJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("0.初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            deal(batchReadCount, expire);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void deal(Integer batchReadCount, Integer expire) {

        String breakpointName = UserRedisConfig.FUNDBARADCONFIGJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);
        if (!StringUtils.hasLength(breakpoint)) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-30), DATE_FORMAT);
        }

        logger.info("1.读取断点。断点：{}", breakpoint);

        Date breakpointDate = DateUtil.strToDate(breakpoint, DATE_FORMAT);
        List<ActivityCardConfig> list = fundBarAdConfigDao.getList(breakpointDate, batchReadCount, false);
        if (!CollectionUtils.isEmpty(list)) {
            breakpointDate = list.stream().max((Comparator.comparing(ActivityCardConfig::getUpdateTime))).get().getUpdateTime();

            list.forEach(a -> {
                if (!CollectionUtils.isEmpty(a.getCodeList())) {
                    a.setCodeListUse(a.getCodeList().stream().map(item -> (String) item.get("Code")).collect(Collectors.toList()));
                }
            });
        }

        logger.info("2.读取数据库。数量：{}，头部列表：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        List<ActivityCardConfig> barActivityCardList = new ArrayList<>();
        List<ActivityCardConfig> announcementList = new ArrayList<>();
        List<ActivityCardConfig> circleActivityCardList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {

            //LinkType转换为前端理解的语义           后端（1 帖子、2 话题、3 专题、4 小程序）  -> 前端（1:原生类型 2:h5类型 0:专题类型）
            list.forEach(a -> a.setLinkType(1));

            list.forEach(a -> {
                if (a.getShowPosition() == null || a.getShowPosition() == 0) {
                    barActivityCardList.add(a);
                } else if (a.getShowPosition() == 1) {
                    announcementList.add(a);
                } else if (a.getShowPosition() == 2) {
                    circleActivityCardList.add(a);
                }
            });
        }

        //处理吧活动卡片
        dealAdList("dealBarActivityCard", BarRedisKey.FUND_BAR_ACTIVITY_CARD_CONFIG, barActivityCardList, expire);
        //处理吧公告位
        dealAdList("dealBarAnnouncement", BarRedisKey.FUND_BAR_ANNOUNCEMENT_CONFIG, announcementList, expire);
        //处理吧圈子活动卡片
        dealAdList("dealCircleActivityCard", BarRedisKey.CIRCLE_ACTIVITY_CARD_CONFIG, circleActivityCardList, expire);

        breakpoint = DateUtil.dateToStr(breakpointDate, DATE_FORMAT);
        userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

        logger.info("5.更新断点。断点：{}", breakpoint);
    }


    private void dealAdList(String logPre, String key, List<ActivityCardConfig> list, long expire) {
        List<ActivityCardConfig> newCacheList = null;
        List<ActivityCardConfig> cacheList = null;
        Map<String, ActivityCardConfig> map = new HashMap<>();

        logger.info("3-1.记录新数据-{}。数量：{}，头部列表：{}",
                logPre,
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );


        String value = app.barredis.get(key);
        cacheList = JSON.parseArray(value, ActivityCardConfig.class);

        logger.info("3-2.记录缓存数据-{}。数量：{}，头部列表：{}",
                logPre,
                CollectionUtils.isEmpty(cacheList) ? 0 : cacheList.size(),
                CollectionUtils.isEmpty(cacheList) ? null : JSON.toJSONStringWithDateFormat(cacheList.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(cacheList)) {
            for (ActivityCardConfig item : cacheList) {
                map.put(item.get_id(), item);
            }
        }

        if (!CollectionUtils.isEmpty(list)) {
            for (ActivityCardConfig item : list) {
                map.put(item.get_id(), item);
            }
        }

        Date now = new Date();
        newCacheList = map.values().stream()
                .filter(a -> a.getIsDel() != null && a.getIsDel() == 0
                        && now.compareTo(a.getEndTime()) < 0)
                .sorted(((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime())))
                .collect(Collectors.toList());


        logger.info("3-3.记录新缓存数据-{}。数量：{}，头部列表：{}",
                logPre,
                CollectionUtils.isEmpty(newCacheList) ? 0 : newCacheList.size(),
                CollectionUtils.isEmpty(newCacheList) ? null : JSON.toJSONStringWithDateFormat(newCacheList.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(newCacheList)) {
            app.barredis.set(key, JSON.toJSONString(newCacheList), expire);
        } else {
            app.barredis.del(key);
        }

        logger.info("4.更新缓存-{}", logPre);
    }


}
