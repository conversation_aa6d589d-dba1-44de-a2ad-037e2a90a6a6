package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.CacheHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.PostAuthorFlag;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.msyql.PostAuthorFlagDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 潜在优质贴同步
 * 源：社区  ->  目：研究(亢林春)
 */
@JobHandler("PotentialHighQualityPostSyncJob")
@Component
public class PotentialHighQualityPostSyncJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(PotentialHighQualityPostSyncJob.class);

    //帖子底池新增cfhpl、jjdt
    private static final List<String> BAR_CONFIG = Arrays.asList("xstl", "jjmnds", "jjdss", "zf", "jjdp", "jjxx", "jjxt", "jjxcx",
            "yllc", "ztmb", "tgcp", "jjjlyjxz", "jjzs", "jjry", "jjft", "sxdt", "jtzh", "jjcl", "jjspzh", "zsjj",
            "zqjj", "hqb", "jjzh", "jmyhs");

    private static final List<Integer> POST_TYPE = Arrays.asList(0, 20, 43);

    private static final String VERTICA_SQL_QUERY = "select POSTID from  %s where POSTID in ";
    private static final String VERTICA_SQL_INSERT = "insert into %s " +
            "(POSTID, POST_DATE, USERID, POST_WORDCNT , PICNUM, EITIME) values ";

    private String verticaTableName = "CONTENT.JJB_TB_POST_QUILITY_BASIC_APP_MED";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private App app;

    @Autowired
    private PostAuthorFlagDao postAuthorFlagDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {
            String initBreakpoint = null;
            Integer batchReadCount = null;
            Integer interval = null;
            List<String> barConfig = null;
            List<Integer> postType = null;
            Integer charCount = null;
            Boolean filterJPAuthor = null;
            String verticaTableName = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                interval = jsonObject.getInteger("interval");
                String temp = jsonObject.getString("barConfig");
                barConfig = CommonUtils.toList(temp, ",");

                temp = jsonObject.getString("postType");
                postType = CommonUtils.toList(temp, ",").stream().map(a -> Integer.parseInt(a)).collect(Collectors.toList());

                charCount = jsonObject.getInteger("charCount");
                filterJPAuthor = jsonObject.getBoolean("filterJPAuthor");

                verticaTableName = jsonObject.getString("verticaTableName");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (interval == null) {
                interval = -300;
            }
            if (CollectionUtils.isEmpty(barConfig)) {
                barConfig = BAR_CONFIG;
            }
            if (CollectionUtils.isEmpty(postType)) {
                postType = POST_TYPE;
            }
            if (charCount == null) {
                charCount = 400;
            }
            if (filterJPAuthor == null) {
                filterJPAuthor = false;
            }
            if (!StringUtils.hasLength(verticaTableName)) {
                verticaTableName = "CONTENT.JJB_TB_POST_QUILITY_BASIC_APP_MED";
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，interval：{}，barConfig：{}，postType：{}，charCount：{}，filterJPAuthor：{}，verticaTableName：{}",
                    initBreakpoint,
                    batchReadCount,
                    interval,
                    barConfig,
                    postType,
                    charCount,
                    filterJPAuthor,
                    verticaTableName);

            if (StringUtils.hasLength(verticaTableName)) {
                this.verticaTableName = verticaTableName;
            }

            if (StringUtils.hasLength(initBreakpoint)) {
                DateUtil.strToDate(initBreakpoint);
                userRedisDao.set(UserRedisConfig.POTENTIALHIGHQUALITYPOSTSYNCJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);

                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);
                return ReturnT.SUCCESS;
            }

            deal(barConfig, postType, charCount, batchReadCount, interval, filterJPAuthor);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(List<String> barConfig, List<Integer> postType, int charCount, int batchReadCount, int interval, boolean filterJPAuthor) throws Exception {

        String breakpointName = UserRedisConfig.POTENTIALHIGHQUALITYPOSTSYNCJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);
        if (!StringUtils.hasLength(breakpoint)) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-60));

            logger.error("第一步，获取断点为空，使用默认断点。断点：{}", breakpoint);
        }

        logger.info("第一步，打印断点。断点：{}", breakpoint);

        Date breakpointDate = DateUtil.strToDate(breakpoint);
        Date minTime = DateUtil.calendarDateByDays(-1);
        if (breakpointDate.compareTo(minTime) < 0) {

            String tempBreakpoint = breakpoint;
            breakpointDate = minTime;
            breakpoint = DateUtil.dateToStr(breakpointDate);

            logger.error("第一步，断点修正。修正前：{}，修正后：{}", tempBreakpoint, breakpoint);
        }

        Set<String> jpAuthorUids = null;
        if (filterJPAuthor) {
            jpAuthorUids = getJPAuthorUids();
        }

        logger.info("第二步，获取解盘作者集合。是否获取：{}，数量：{}，头部id列表：{}",
                filterJPAuthor,
                jpAuthorUids == null ? 0 : jpAuthorUids.size(),
                jpAuthorUids == null ? null : jpAuthorUids.stream().limit(20).collect(Collectors.toList())
        );

        int round = 0;
        boolean isRun = true;
        while (isRun) {
            round++;

            List<Map<String, Object>> list = postInfoNewDao.getPotentialHighQualityPost(breakpointDate, DateUtil.calendarDateBySecond(interval), barConfig, postType, charCount, batchReadCount);

            logger.info("第三步，获取帖子-第{}轮。数量：{}，头部id列表：{}",
                    round,
                    list == null ? 0 : list.size(),
                    list == null ? null : list.stream().map(a -> (Integer) a.get("ID")).limit(50).collect(Collectors.toList())
            );

            if (filterJPAuthor && !CollectionUtils.isEmpty(list) && !CollectionUtils.isEmpty(jpAuthorUids)) {
                final Set<String> jpAuthorUidsFinal = jpAuthorUids;
                list = list.stream().filter(a -> !jpAuthorUidsFinal.contains(a.get("UID"))).collect(Collectors.toList());
            }
            logger.info("第四步，过滤解盘作者帖子-第{}轮。是否过滤：{}，数量：{}，头部id列表：{}",
                    round,
                    filterJPAuthor,
                    list == null ? 0 : list.size(),
                    list == null ? null : list.stream().map(a -> (Integer) a.get("ID")).limit(50).collect(Collectors.toList())
            );

            if (!CollectionUtils.isEmpty(list)) {
                breakpointDate = (Date) list.get(list.size() - 1).get("TIME");

                List<String> postidAll = list.stream().map(a -> String.valueOf(a.get("ID"))).collect(Collectors.toList());
                List<String> idsInDB = getPostIdInVertica(postidAll);
                Set<String> idInDbSet = new HashSet<>();
                if (!CollectionUtils.isEmpty(idsInDB)) {
                    idsInDB.forEach(a -> idInDbSet.add(a));
                }
                list = list.stream().filter(a -> !idInDbSet.contains(String.valueOf(a.get("ID")))).collect(Collectors.toList());
            }

            logger.info("第五步，去重帖子-第{}轮。数量：{}，头部id列表：{}",
                    round,
                    list == null ? 0 : list.size(),
                    list == null ? null : list.stream().map(a -> (Integer) a.get("ID")).limit(50).collect(Collectors.toList())
            );

            if (!CollectionUtils.isEmpty(list)) {
                List<List<Object>> insertList = new ArrayList<>(list.size());
                List<Object> tempList = null;
                Object temp = null;
                int picNum = 0;
                for (Map<String, Object> item : list) {
                    tempList = new ArrayList<>(6);
                    temp = item.get("ID");
                    if (temp != null) {
                        tempList.add(String.valueOf(temp));
                        //a.ID, a.UID, a.TIME,  c.CONTENTCOUNT, a.ALLPIC
                        //(POSTID, POST_DATE, USERID, POST_WORDCNT , PICNUM, EITIME)
                        temp = item.get("TIME");
                        tempList.add(temp);

                        temp = item.get("UID");
                        tempList.add(temp);

                        temp = item.get("CONTENTCOUNT");
                        tempList.add(temp);

                        temp = item.get("ALLPIC");
                        picNum = 0;
                        if (StringUtils.hasLength((String) temp)) {
                            List<String> picList = CommonUtils.toList((String) temp, ",");
                            picNum = picList.size();
                        }
                        tempList.add(picNum);

                        tempList.add(new Date());

                        insertList.add(tempList);
                    }
                }

                logger.info("第六步，构造实体-第{}轮。数量：{}，头部id列表：{}",
                        round,
                        insertList == null ? 0 : insertList.size(),
                        insertList == null ? null : insertList.stream().map(a -> (String) a.get(0)).limit(50).collect(Collectors.toList())
                );

                insertIntoVertica(insertList);

                logger.info("第七步，写入vertica-第{}轮。数量：{}，头部id列表：{}",
                        round,
                        insertList == null ? 0 : insertList.size(),
                        insertList == null ? null : insertList.stream().map(a -> (String) a.get(0)).limit(50).collect(Collectors.toList())
                );

            }

            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);
            logger.info("第八步，更新断点-第{}轮。断点：{}", round, breakpoint);

            if (CollectionUtils.isEmpty(list) || list.size() < batchReadCount) {
                isRun = false;
            }
        }

    }

    private List<String> getPostIdInVertica(List<String> postIds) throws Exception {
        List<String> result = new ArrayList<>();

        if (!CollectionUtils.isEmpty(postIds)) {
            List<List<String>> batchList = CommonUtils.toSmallList2(postIds, 200);
            Connection conn = app.bigdataVertica.getconn();
            PreparedStatement preparedStatement = null;
            try {
                for (List<String> batch : batchList) {
                    String preparedSql = getPreparedSql(batch.size());
                    preparedStatement = conn.prepareStatement(preparedSql);
                    for (int i = 1; i <= batch.size(); i++) {
                        preparedStatement.setObject(i, batch.get(i - 1));
                    }
                    String postIdInDb = null;
                    ResultSet resultSet = preparedStatement.executeQuery();
                    while (resultSet.next()) {
                        postIdInDb = resultSet.getString(1);
                        result.add(postIdInDb);
                    }
                }
            } catch (Exception ex) {
                logger.error(ex.getMessage(), ex);
                throw ex;
            } finally {
                try {
                    if (preparedStatement != null) {
                        preparedStatement.close();
                    }
                    if (conn != null) {
                        conn.close();
                    }
                } catch (Exception ex) {
                    logger.error(ex.getMessage(), ex);
                }
            }
        }
        return result;
    }

    private String getPreparedSql(int count) {
        StringBuilder builder = new StringBuilder();
        builder.append(String.format(VERTICA_SQL_QUERY, verticaTableName));
        builder.append("(");
        for (int i = 0; i < count; i++) {
            if (i == count - 1) {
                builder.append("?");
            } else {
                builder.append("?,");
            }
        }
        builder.append(")");
        return builder.toString();
    }

    private void insertIntoVertica(List<List<Object>> parameters) throws Exception {

        Connection conn = null;
        PreparedStatement ps = null;
        String sql = null;
        try {
            conn = app.bigdataVertica.getconn();
            // 关闭自动提交，即开启事务
            conn.setAutoCommit(false);
            StringBuilder builder = new StringBuilder();
            builder.append(String.format(VERTICA_SQL_INSERT, verticaTableName));
            builder.append("(");
            for (int i2 = 0; i2 < parameters.get(0).size(); i2++) {
                if (i2 == parameters.get(0).size() - 1) {
                    builder.append("?");
                } else {
                    builder.append("?,");
                }

            }
            builder.append(")");
            sql = builder.toString();
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < parameters.size(); i++) {

                List<Object> paramlist = parameters.get(i);
                for (int i2 = 0; i2 < paramlist.size(); i2++) {
                    ps.setObject(i2 + 1, paramlist.get(i2));
                }
                // 添加批处理SQL
                ps.addBatch();
                // 每200条执行一次，避免内存不够的情况
                if (i > 0 && i % 200 == 0) {
                    ps.executeBatch();
                }
            }
            // 最后执行剩余不足200条的
            ps.executeBatch();
            // 执行完后，手动提交事务
            conn.commit();
            ps.close();
            // 在把自动提交打开
            conn.setAutoCommit(true);
        } catch (Exception ex) {
            logger.error(sql, ex);
            throw ex;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (conn != null) {
                    conn.close();
                }
            } catch (Exception ex) {
                logger.error(ex.getMessage(), ex);
            }
        }
    }

    private Set<String> getJPAuthorUids() {
        Set<String> result = null;
        String key = "getJPAuthorUids";
        result = CacheHelper.get(key);
        if (result == null) {
            List<PostAuthorFlag> jpAuthorList = postAuthorFlagDao.getJPAuthor();
            if (!CollectionUtils.isEmpty(jpAuthorList)) {
                result = jpAuthorList.stream().filter(a -> a.del == 0).map(a -> a.uid).collect(Collectors.toSet());
            }
            if (result == null) {
                result = new HashSet<>();
            }

            CacheHelper.put(key, result, 10 * 60 * 1000L);
        }

        return result;
    }

}
