package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.JsonUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.ttfund.web.base.helper.CacheHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.FundbarCountModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostinfoExtraModel;
import ttfund.web.communityservice.bean.jijinBar.post.config.FundBarAuthorBlackListModel;
import ttfund.web.communityservice.bean.jijinBar.post.config.HotPostConfigModel;
import ttfund.web.communityservice.bean.jijinBar.post.config.HotpostWeightConfigModel;
import ttfund.web.communityservice.bean.jijinBar.post.elitepost.ElitePostModel;
import ttfund.web.communityservice.bean.jijinBar.post.elitepost.ElitePostModelOld;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.constant.DateConstant;
import ttfund.web.communityservice.dao.mongo.ElitePostDao;
import ttfund.web.communityservice.dao.mongo.FundBarAuthorBlackListDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.msyql.FundBarCountDao;
import ttfund.web.communityservice.dao.msyql.HotPostConfigDao;
import ttfund.web.communityservice.dao.msyql.HotPostWeightConfigDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.enums.ElitePostConfigWeightKey;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 精华帖
 */
@JobHandler("ElitePostJob")
@Component
public class ElitePostJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ElitePostJob.class);

    private static final String LOG_PREFIX = "ElitePostJob[精华帖同步服务]";

    /**
     * 需要对已标记的刷屏作者的所有帖子评分-300
     */
    private static final int BLACK_USER_PENALTY = 1500;

    private static final String ELITE_WEIGHT_KEY = "EliteGetWeightConfig";

    /**
     * 临时变量帖子计算分数存储
     */
    private Map<String, Double> dicScore = null;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private HotPostConfigDao hotPostConfigDao;

    @Autowired
    private PostDao postDao;

    @Autowired
    private PostInfoExtraDao postExtraDao;

    @Autowired
    private HotPostWeightConfigDao hotPostWeightConfigDao;

    @Autowired
    private ElitePostDao elitePostDao;

    @Autowired
    private FundBarAuthorBlackListDao fundBarAuthorBlackListDao;

    @Autowired
    private FundBarCountDao fundBarCountDao;


    @Override
    public ReturnT<String> execute(String s) throws Exception {

        //凌晨4：000-5：00 不执行
        LocalTime now = LocalTime.now();
        if (now.compareTo(LocalTime.of(4, 0)) > 0 && now.compareTo(LocalTime.of(5, 0)) < 0) {
            return ReturnT.SUCCESS;
        } else {
            logicDeal();
        }
        return ReturnT.SUCCESS;
    }


    public void logicDeal() {
        try {
            /*1.历史入选社区热点weex的优质帖
             *2.帖子评分>准入门槛的高分贴
             *3.需要对已标记的刷屏作者的帖子评分-300
             *4.需要开放运营配置帖子评分各个数据权重的入口
             *5.黑名单用户新增加的话需要 回刷近一个月该用户发帖评分数据
             *
             */

            dicScore = new HashMap<>();

            //1.历史入选社区热点weex的优质帖
            hotPostConfigSetElite();

            //2.根据帖子的点击量 点赞数 评论数 计算精华帖
            calElitePost();
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
        }
    }


    /**
     * 社区热点小程序的配置贴设置为精华帖
     */
    public void hotPostConfigSetElite() {
        try {

            //历史入选社区热点weex的优质帖

            //上次更新时间
            String breakpointName = UserRedisConfig.ELITE_POST_JOB_HOT_POST_CONFIG_SET_ELITE_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);
            Date dateForBreakpoint = StringUtils.isNotEmpty(breakpoint) ? DateUtil.strToDate(breakpoint, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT) : DateUtil.calendarDateByYears(-1);
            LOGGER.info("{}: {} == {}", LOG_PREFIX, breakpointName, dateForBreakpoint);

            //需要更新的帖子ID
            List<String> postIds;
            //需要删除的帖子ID
            List<String> delPostIds;

            List<HotPostConfigModel> list = hotPostConfigDao.getListByUpdateTime(dateForBreakpoint);
            LOGGER.info("{}: hotPostConfigDao result:{}", LOG_PREFIX, JsonUtils.toJsonString(list));

            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            dateForBreakpoint = list.stream().max((Comparator.comparing(o -> o.UPDATETIME))).get().UPDATETIME;
            LOGGER.info("{}: dateForBreakpoint result:{}", LOG_PREFIX, dateForBreakpoint);

            postIds = list.stream().filter(a -> a.ISDEL == 0).map(a -> a.TID).collect(Collectors.toList());
            delPostIds = list.stream().filter(a -> a.ISDEL != 0).map(a -> a.TID).collect(Collectors.toList());


            //帖子列表
            if (!CollectionUtils.isEmpty(postIds)) {
                List<String> fields = Arrays.asList("_id", "ID", "UID", "CODE", "TIME", "UPDATETIME", "TIMEPOINT", "YUANID", "TYPE", "YUANTYPE", "DEL");
                List<PostInfoModel> postList = postDao.getFromMong(postIds, fields, PostInfoModel.class);
                LOGGER.info("{}: postDao.getFromMong result:{}", LOG_PREFIX, JsonUtils.toJsonString(postList));
                if (!CollectionUtils.isEmpty(postList)) {

                    //需要删除的帖子ID
                    List<String> delPosts = postList.stream().filter(a -> a.DEL == 1).map(a -> String.valueOf(a.ID)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(delPostIds)) {
                        delPostIds.addAll(delPosts);
                    }

                    //需要更新的帖子
                    List<PostInfoModel> posts = postList.stream().filter(a -> a.DEL == 0).collect(Collectors.toList());
                    LOGGER.info("{}: need update post list:{}", LOG_PREFIX, JsonUtils.toJsonString(posts));
                    if (!CollectionUtils.isEmpty(posts)) {
                        for (PostInfoModel item : posts) {
                            PostinfoExtraModel postEtra = postExtraDao.getByID(String.valueOf(item.ID));
                            ElitePostModelOld model = new ElitePostModelOld();
                            model.CODE = item.CODE;
                            model.ID = item.ID;
                            model.TIME = item.TIME;
                            model.TIMEPOINT = item.TIMEPOINT;
                            model.TYPE = item.TYPE;
                            model.UID = item.UID;
                            model.UPDATETIME = new Date();
                            model.YUANID = item.YUANID;
                            model.YUANTYPE = (item.YUANTYPE == null ? 0 : item.YUANTYPE);
                            model.SCORE = getOldScore(postEtra, null);
                            model.IsNew = 1;
                            model.Source = 1;
                            model._id = String.valueOf(item.ID);

                        }
                    }
                }
            }

            //需要删除的帖子
            if (!CollectionUtils.isEmpty(delPostIds)) {
                LOGGER.info("{}: delete post ids:{}", LOG_PREFIX, delPostIds);
                elitePostDao.removeByIds(delPostIds, elitePostDao.getCollectionName());
            }

            userRedisDao.set(breakpointName, DateUtil.dateToStr(dateForBreakpoint, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT), 60 * 24 * 3600L);

        } catch (Exception ex) {

        }
    }

    /**
     * 根据既定规则 计算精华贴
     */
    public void calElitePost() throws Exception {
        //获取黑名单信息，新加的黑名单用户热门帖子需要重刷近一月的帖子数据
        List<PostinfoExtraModel> extraPostList = new ArrayList<>();

        //黑名单用户
        List<FundBarAuthorBlackListModel> blackList = fundBarAuthorBlackListDao.getAll();
        LOGGER.info("{}: blackList:{}", LOG_PREFIX, blackList);
        //新增黑名单用户的近一个月的帖子
        if (!CollectionUtils.isEmpty(blackList)) {
            //需要重刷的黑名单用户帖子
            extraPostList.addAll(getBlackUserExtraPost(blackList));
        }

        //根据帖子拓展信息的更新时间
        List<PostinfoExtraModel> lastUpdateExtraPost = getLastUpdateExtraPost();
        LOGGER.info("{}: lastUpdateExtraPost:{}", LOG_PREFIX, lastUpdateExtraPost);
        if (!CollectionUtils.isEmpty(lastUpdateExtraPost)) {
            extraPostList.addAll(lastUpdateExtraPost);
        }

        //计算帖子得分，获取精华贴池
        List<String> postIds = calElitePost(extraPostList, blackList);
        LOGGER.info("{}: cal elite post id:{}", LOG_PREFIX, postIds);
        if (!CollectionUtils.isEmpty(postIds)) {
            saveElitePost(postIds);
        }
    }

    public boolean saveElitePost(List<String> postIds) {
        if (CollectionUtils.isEmpty(postIds)) {
            return true;
        }

        List<String> fields = Arrays.asList("_id", "ID", "UID", "CODE", "TIME", "UPDATETIME", "TIMEPOINT", "YUANID", "TYPE", "YUANTYPE", "DEL");
        List<PostInfoModel> postList = postDao.getFromMong(postIds, fields, PostInfoModel.class);

        if (CollectionUtils.isEmpty(postList)) {
            return true;
        }

        //删除帖子
        List<String> delIds = postList.stream().
            filter(a -> a.DEL != null && a.DEL != 0).
            map(a -> String.valueOf(a.ID)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(delIds)) {
            List<List<String>> batches = CommonUtils.toSmallList2(delIds, 100);
            for (List<String> batch : batches) {
                elitePostDao.removeByIds(batch, elitePostDao.getCollectionName());
            }
        }

        //更新帖子
        List<PostInfoModel> upsertPostList = postList.stream().
            filter(a -> a.DEL != null && a.DEL == 0).
            collect(Collectors.toList());
        if (CollectionUtils.isEmpty(upsertPostList)) {
            return true;
        }

        List<ElitePostModel> upsertElitePostList = new ArrayList<>();
        for (PostInfoModel post : upsertPostList) {
            double score = 0;
            String postId = String.valueOf(post.ID);
            if (dicScore.containsKey(postId)) {
                score = dicScore.get(postId);
            }
            ElitePostModel model = new ElitePostModel();
            model.CODE = post.CODE;
            model.ID = post.ID;
            model.TIME = post.TIME;
            model.TIMEPOINT = (post.TIMEPOINT == null ? 0L : post.TIMEPOINT);
            model.TYPE = (post.TYPE == null ? 0 : post.TYPE);
            model.UID = post.UID;
            model.UPDATETIME = new Date();
            model.YUANID = (post.YUANID == null ? 0 : post.YUANID);
            model.YUANTYPE = (int)(post.YUANTYPE == null ? 0 : post.YUANTYPE);
            model.SCORE = score;
            model.IsNew = 1;
            model.Source = 2;
            model._id = String.valueOf(post.ID);
            upsertElitePostList.add(model);
        }

        List<List<ElitePostModel>> batches = CommonUtils.toSmallList2(upsertElitePostList, 100);
        for (List<ElitePostModel> batch : batches) {
            elitePostDao.upsertBulk(batch, ElitePostModel.class, elitePostDao.getCollectionName(), elitePostDao.getBarMongoTemplate());
        }
        return true;
    }

    /**
     * 计算精华贴，返回精华贴的帖子ID
     */
    public List<String> calElitePost(List<PostinfoExtraModel> list, List<FundBarAuthorBlackListModel> blackList) {
        List<String> result = new ArrayList<>();

        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        //黑名单用户id列表
        List<String> blackUserIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(blackList)) {
            blackUserIds = blackList.stream().
                filter(a -> a.Del != null && a.Del == 0).
                map(a -> a.UID).
                collect(Collectors.toList());
        }

        for (PostinfoExtraModel post : list) {
            //老的精华帖算分方式
            double score = setPostScore(post, blackUserIds);

            FundbarCountModel barPVCount = fundBarCountDao.get(post.CODE);
            LOGGER.info("{}: postId: {} PV count:{}", LOG_PREFIX, post.CODE, JsonUtils.toJsonString(barPVCount));
            if (barPVCount != null && barPVCount.jh_minscore != null && score >= barPVCount.jh_minscore.doubleValue()) {
                result.add(String.valueOf(post.ID));
            }

        }
        return result;
    }

    /**
     * 老的精华帖分数计算方式
     */
    public double setPostScore(PostinfoExtraModel model, List<String> blackUserIds) {
        double result = 0;

        if (dicScore == null) {
            dicScore = new HashMap<>();
        }
        if (model != null) {
            String postId = String.valueOf(model.ID);
            if (!dicScore.containsKey(postId)) {
                double score = getOldScore(model, blackUserIds);
                dicScore.put(postId, score);
                result = score;
            }
        }
        return result;
    }

    /**
     * 获取最近更新的帖子数据
     */
    public List<PostinfoExtraModel> getLastUpdateExtraPost() {
        List<PostinfoExtraModel> result;

        int pageSize = 100000;
        //上次更新时间
        String breakpointName = UserRedisConfig.ELITE_POST_JOB_GET_BLACK_USER_EXTRA_POST_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);
        Date dateForBreakpoint = StringUtils.isNotEmpty(breakpoint) ?
            DateUtil.strToDate(breakpoint, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT) : DateUtil.calendarDateByYears(-50);

        //最新更新的帖子
        result = postExtraDao.getByUpdateTime(dateForBreakpoint, DateUtil.calendarDateByMonth(-1), pageSize);
        if (!CollectionUtils.isEmpty(result)) {
            dateForBreakpoint = result.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;
            userRedisDao.set(breakpointName, DateUtil.dateToStr(dateForBreakpoint, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT), 60 * 24 * 3600L);
        }

        return result;
    }

    /**
     * 黑名单用户帖子列表
     */
    public List<PostinfoExtraModel> getBlackUserExtraPost(List<FundBarAuthorBlackListModel> blackList) {
        List<PostinfoExtraModel> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(blackList)) {

            //上次更新时间
            String breakpointName = UserRedisConfig.ELITE_POST_JOB_GET_BLACK_USER_EXTRA_POST_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);
            Date dateForBreakpoint = StringUtils.isNotEmpty(breakpoint) ?
                DateUtil.strToDate(breakpoint, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT) : DateUtil.calendarDateByYears(-50);

            //更新的黑名单用户数据
            Date curMaxUpdateTime = blackList.stream().max(Comparator.comparing(o -> o.UpdateTime)).get().UpdateTime;
            if (curMaxUpdateTime.compareTo(dateForBreakpoint) > 0) {
                final Date tempDate = dateForBreakpoint;
                List<FundBarAuthorBlackListModel> updateBlackList = blackList.stream().
                    filter(a -> a.UpdateTime.compareTo(tempDate) > 0).
                    collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(updateBlackList)) {
                    for (FundBarAuthorBlackListModel item : updateBlackList) {
                        try {
                            List<PostinfoExtraModel> tempList = postExtraDao.getByTime(DateUtil.calendarDateByMonth(-1), item.UID);
                            if (!CollectionUtils.isEmpty(tempList)) {
                                result.addAll(tempList);
                            }
                        } catch (Exception ex) {
                        }
                    }
                }
            }


            if (curMaxUpdateTime.compareTo(dateForBreakpoint) >= 0) {
                dateForBreakpoint = curMaxUpdateTime;
                userRedisDao.set(breakpointName, DateUtil.dateToStr(dateForBreakpoint, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT), 60 * 24 * 3600L);
            }

        }
        return result;
    }

    /**
     * 计算帖子得分
     */
    public double getOldScore(PostinfoExtraModel model, List<String> blackUserIds) {
        //基金吧配置的权重
        Map<String, Integer> weightConfig = getWeightConfig();
        int wCLICK = weightConfig.get(ElitePostConfigWeightKey.CLICK.getType()); //点击权重
        int wCOMMENT = weightConfig.get(ElitePostConfigWeightKey.COMMENT.getType()); //评论权重
        int wLIKE = weightConfig.get(ElitePostConfigWeightKey.LIKE.getType()); //点赞权重

        double score = (Optional.ofNullable(model.CLICKNUM).orElse(0L) / (double) 50) * wCLICK +
            (Optional.ofNullable(model.PINGLUNNUM).orElse(0L) + Optional.ofNullable(model.SUBPINGLUNNUM).orElse(0L)) * wCOMMENT +
            Optional.ofNullable(model.LIKECOUNT).orElse(0L) * wLIKE;

        if (blackUserIds != null && blackUserIds.contains(model.UID)) {
            score = score - BLACK_USER_PENALTY;
        }
        LOGGER.info("{}, uid:{}, score:{}", LOG_PREFIX, model.UID, score);
        return score;
    }

    /**
     * 获取权重信息
     */
    private Map<String, Integer> getWeightConfig() {
        Map<String, Integer> result = new HashMap<>();
        //精华帖配置
        List<HotpostWeightConfigModel> weightConfigList = CacheHelper.get(ELITE_WEIGHT_KEY);
        if (weightConfigList == null) {
            weightConfigList = hotPostWeightConfigDao.getAll();
            if (weightConfigList != null) {
                CacheHelper.put(ELITE_WEIGHT_KEY, weightConfigList, 10 * 60 * 1000L);
            }
        }

        if (weightConfigList == null) {
            return result;
        }
        result = weightConfigList.stream()
            .collect(Collectors.toMap(config -> config.WEIGHTKEY, config -> config.WEIGHTVALUE));

        return result;
    }

}
