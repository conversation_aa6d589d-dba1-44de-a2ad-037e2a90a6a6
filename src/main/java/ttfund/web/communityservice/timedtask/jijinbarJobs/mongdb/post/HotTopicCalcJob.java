package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.dao.mongo.HotTopicDao;
import ttfund.web.communityservice.dao.msyql.TopicSyncDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 生成热门话题
 */
@JobHandler("HotTopicCalcJob")
@Component
public class HotTopicCalcJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(HotTopicCalcJob.class);

    private static String DATE_FORMAT = "yyyy-MM-dd";

    @Autowired
    private TopicSyncDao topicSyncDao;

    @Autowired
    private HotTopicDao hotTopicDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        hotTopicCalcProcess(s);
        return ReturnT.SUCCESS;
    }

    /**
     * 分数 = 帖子数量/200-话题创建天数*20
     */
    private void hotTopicCalcProcess(String s) {

        try {
            String config = null;
            String htIds = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                config = jsonObject.getString("config");
                htIds = jsonObject.getString("htIds");
            }

            if (!StringUtils.hasLength(config)) {
                config = "200,20";
            }
            if (htIds == null) {
                htIds = "";
            }

            logger.info("第零步，打印参数。config：{}，htIds：{}", config, htIds);

            List<Map<String, Object>> table = topicSyncDao.getTopicDetail();

            logger.info("第一步，获取数据。数量：{}", table == null ? 0 : table.size());

            if (!CollectionUtils.isEmpty(table)) {
                Double score = null;
                String[] configs = config.split(",");
                long i1 = 0;
                long i2 = 0;
                String id = null;
                Iterator<Map<String, Object>> iterator = table.iterator();
                Map<String, Object> item = null;
                while (iterator.hasNext()) {
                    item = iterator.next();
                    try {
                        i1 = (Integer) item.get("PARTICIPANTCOUNT") / Integer.parseInt(configs[0]);
                        i2 = (DateUtil.strToDate(DateUtil.dateToStr(new Date(), DATE_FORMAT), DATE_FORMAT).getTime() -
                                DateUtil.strToDate(DateUtil.dateToStr((Date) item.get("SHOWTIME"), DATE_FORMAT), DATE_FORMAT).getTime())
                                / (24 * 3600 * 1000)
                                * Integer.parseInt(configs[1]);
                    } catch (Exception ex) {

                        logger.error("异常item：{}", JSON.toJSONString(item));

                        iterator.remove();
                        continue;
                    }

                    score = (double) (i1 - i2);
                    item.put("SCORE", score);
                    if ("0".equals(item.get("ISENABLED").toString()) || htIds.contains(item.get("HTID").toString())) {
                        id = item.get("HTID").toString();
                        //从PostRank表中移除
                        hotTopicDao.removeById(id);
                        iterator.remove();
                        continue;
                    }

                    item.remove("SHOWTIME");
                    item.remove("ISENABLED");

                    item.put("_id", String.valueOf(item.get("HTID")));
                }

                logger.info("第二步，处理数据。数量：{}", table == null ? 0 : table.size());

                hotTopicDao.saveMany(table);

                logger.info("第三步，写数据。数量：{}", table == null ? 0 : table.size());
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

}
