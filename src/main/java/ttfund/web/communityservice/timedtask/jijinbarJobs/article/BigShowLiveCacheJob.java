package ttfund.web.communityservice.timedtask.jijinbarJobs.article;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.JsonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.VideoArticleBaseModel;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.VideoArticleModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.VideoArticleDao;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 直播中的视频文章job
 */
@JobHandler("BigShowLiveCacheJob")
@Component
public class BigShowLiveCacheJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(BigShowLiveCacheJob.class);

    private static final long EXPIRE_TIME = 300;

    @Autowired
    private VideoArticleDao videoArticleDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            logicDeal();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }

    public void logicDeal() {
        //直播中大咖秀列表
        List<VideoArticleModel> liveList = videoArticleDao.getLiveList();
        if (CollectionUtils.isEmpty(liveList)) {
            //如果数据不存在，则删除缓存
            app.barredis.del(BarRedisKey.VIDEO_ARTICLE_LIVE_LIST);
            LOGGER.info("BigShowLiveCacheJob[直播中的视频文章]:无直播中的大咖秀,删除缓存");
            return;
        }

        //浪客ID或房间号不为空的才展示
        List<String> listIds = liveList.stream().
                filter(a -> !StringUtils.isEmpty(a.LankeId) || !StringUtils.isEmpty(a.RoomNumber)).
                map(VideoArticleBaseModel::get_id).
                collect(Collectors.toList());

        Boolean result = app.barredis.set(BarRedisKey.VIDEO_ARTICLE_LIVE_LIST, JsonUtils.toJsonString(listIds), EXPIRE_TIME);
        LOGGER.info("BigShowLiveCacheJob[直播中的视频文章]:直播中的大咖秀id缓存结果:{}", result);
    }
}
