package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostCountModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.PostClickNumDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.redis.RedisUtils;

import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 帖子点击数缓存设置
 */
@JobHandler("PostClickNumJob")
@Component
public class PostClickNumJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(PostClickNumJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostClickNumDao postClickNumDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            //忽略处理数据的时间界限  负值
            Integer ignoreTime = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                ignoreTime = jsonObject.getInteger("ignoreTime");
            }

            if (batchReadCount == null) {
                batchReadCount = 10000;
            }

            if (ignoreTime == null) {
                ignoreTime = -365 * 24 * 3600;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，ignoreTime：{}",
                initBreakpoint,
                batchReadCount,
                ignoreTime
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.POSTCLICKNUMJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            logicDeal(batchReadCount, ignoreTime);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    public void logicDeal(int batchReadCount, Integer ignoreTime) {
        try {
            Date date = DateUtil.calendarDateBySecond(ignoreTime);

            String breakpointName = UserRedisConfig.POSTCLICKNUMJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("0.读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("1.读取断点。断点:{}", breakpoint);

            List<PostCountModel> list = postClickNumDao.getList(breakpointDate, batchReadCount);
            logger.info("2.读取数据。数量：{}", CollectionUtils.isEmpty(list) ? 0 : list.size());

            if (!CollectionUtils.isEmpty(list)) {
                breakpointDate = list.stream().max(Comparator.comparing(o -> o.UpdateTime)).get().UpdateTime;

                list = list.stream().filter(a -> a.CreateTime != null && a.CreateTime.compareTo(date) > 0).collect(Collectors.toList());
            }

            logger.info("3.过滤。数量：{}", CollectionUtils.isEmpty(list) ? 0 : list.size());

            if (!CollectionUtils.isEmpty(list)) {
                for (PostCountModel item : list) {
                    String cacheKey = String.format(BarRedisKey.FUND_POST_COUNT_INFO, String.valueOf(item.ID));
                    Map<String, String> hash = new HashMap<>();
                    hash.put("ClickNum", String.valueOf(item.ClickNum));
                    RedisUtils.hset(app.barredis, cacheKey, hash);
                    app.barredis.expire(cacheKey, 6 * 30 * 24 * 3600L);
                }
            }
            logger.info("4.设置缓存。数量：{}", CollectionUtils.isEmpty(list) ? 0 : list.size());

            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);
            logger.info("5.更新断点。breakpoint：{}", breakpoint);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }

}
