package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.mongo.QualityPostTopicRelation;
import ttfund.web.communityservice.bean.jijinBar.post.PostinfoExtraModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.dao.mongo.QualityPostTopicRelationDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 优质帖录入对应话题-同步至MongoDB
 */
@JobHandler("qualityPostIntoTopicJob")
@Component
public class QualityPostIntoTopicJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(QualityPostIntoTopicJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private QualityPostTopicRelationDao qualityPostTopicRelationDao;

    @Autowired
    private PostInfoExtraDao postInfoExtraDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        int size = 0;

        try {

            String breakTimeName = "qualityPostIntoTopicJob_breakTime";
            Date breakTime = userRedisDao.getBreakTime(breakTimeName, DateUtil.calendarDateByYears(-1));

            logger.info("优质帖录入对应话题任务开始，时间断点：" + DateUtil.dateToStr(breakTime, DateUtil.dateTimeDefaultPattern));

            String sql = " select HTID,POSTID,SIMI,updatetime from CONTENT.TOPIC_POST_QUALITY_SIMI_DRCT_ALL " +
                " where updatetime >= ? order by updatetime limit 5000";

            for (int i = 0; i < 100; i++) {

                List<Object> params = new ArrayList<>();
                params.add(breakTime);

                List<Map> result = app.bigdataVertica.executeQuery(sql, params);

                if (CollectionUtils.isEmpty(result)) {
                    break;
                }

                List<QualityPostTopicRelation> list = new ArrayList<>();

                for (Map map : result) {

                    QualityPostTopicRelation bean = new QualityPostTopicRelation();
                    bean.setTopicId((String) map.get("HTID"));
                    bean.setPostId((String) map.get("POSTID"));
                    bean.setSimi(((BigDecimal) map.get("SIMI")).doubleValue());
                    bean.setCreateTime(new Date());
                    bean.setUpdateTime(new Date());
                    bean.setIsDel(0);
                    bean.setState(0);
                    bean.set_id(bean.getTopicId() + "_" + bean.getPostId());

                    list.add(bean);
                }

                Map<Long, QualityPostTopicRelation> idMap = list.stream()
                    .collect(Collectors.toMap(w -> Long.parseLong(w.getPostId()), w -> w, (w1, w2) -> w2));

                // 获取帖子时间和长度
                List<PostinfoExtraModel> extraList = postInfoExtraDao.getPostExtraForQuality(new ArrayList<>(idMap.keySet()));

                for (PostinfoExtraModel model : extraList) {
                    QualityPostTopicRelation relation = idMap.get(model.ID);
                    relation.setTime(model.TIME);
                    relation.setLength(model.CONTENTCOUNT);
                    relation.setIsDel(model.DEL);
                }

                // 保存
                qualityPostTopicRelationDao.saveAll(list);

                size += list.size();

                logger.info("已更新{}条数据：" + size);

                breakTime = (Date) result.get(result.size() - 1).get("updatetime");

                if (result.size() < 5000) {
                    break;
                }
            }

            userRedisDao.setBreakTime(breakTimeName, breakTime);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        logger.info("优质帖录入对应话题任务结束，共更新{}条数据", size);

        return ReturnT.SUCCESS;
    }
}
