package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.mysql.cj.util.StringUtils;
import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumCFHArticle;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumPostType;
import ttfund.web.communityservice.bean.jijinBar.post.*;
import ttfund.web.communityservice.bean.jijinBar.post.QA.AnswerExtensionModel;
import ttfund.web.communityservice.bean.jijinBar.post.caifuhao.PostCFHModel;
import ttfund.web.communityservice.bean.messagepush.FundQuestionInfo;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.dao.mongo.FundUserProfitDao;
import ttfund.web.communityservice.dao.mongo.PassportFundMrgDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.msyql.JjbconfigDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.msyql.PostInfoDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步主帖原贴信息，附加评论 mongo
 *
 * @author：liyaogang
 * @date：2023/3/27 17:30
 */
@JobHandler(value = "postSyncBizMongoJob")
@Component
public class PostSyncBizMongoJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PostSyncBizMongoJob.class);

    @Autowired
    UserRedisDao userRedisDao;

    @Autowired
    PostInfoDao postInfoDao;

    @Autowired
    PostInfoExtraDao postExtraDao;

    @Autowired
    PostDao postDao;

    @Autowired
    JjbconfigDao jjbconfigDao;

    @Autowired
    PassportFundMrgDao passportFundMrgDao;

    @Autowired
    AppConstant appConstant;

    @Autowired
    ReplyInfoNewDao replyInfoNewDao;

    @Autowired
    FundUserProfitDao fundUserProfitDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        logger.info("mongo同步主帖原贴信息，附加评论开始执行");
        boolean res1 = cacheYuanDetail();
        boolean res2 = cacheFindScore();
        boolean res3 = replyRankCalcProcess();
        boolean res4 = removePostFindScore();
        if (res1 && res2 && res3 && res4) {
            return ReturnT.SUCCESS;
        }
        return ReturnT.FAIL;

    }

    /**
     * YuanDetail同步到MongoDb
     */
    public boolean cacheYuanDetail() {
        String currentMethodName = "cacheYuanDetailJava";
        try {
            String param = "";
            int limit = appConstant.batchPostReadCount;
            //获取断点时间 breakPoint=lastUpdateTime
            String breakPoint = null;
            breakPoint = org.springframework.util.StringUtils.hasLength(param) ?
                    param : userRedisDao.get(currentMethodName);

            if (breakPoint == null) {
                breakPoint = DateUtil.dateToStr(DateUtil.calendarDateByDays(-1));
            }
            logger.info("cacheYuanDetail() 上次断点：{}", breakPoint);
            //String[] configs = appConstant.rankPostConfig.split(",");
            boolean isRunning = true;
            while (isRunning) {
                String[] points = breakPoint.split("\\|");
                String point = "";
                if (points.length > 1) {
                    point = points[1];
                }
                List<PostInfoNewModel> postNewInfos = postInfoDao.getPostInfoNew(DateUtil.strToDate(points[0]), point, limit);
                // 保留近一年
                if (postNewInfos != null && postNewInfos.size() > 0) {
                    postNewInfos = postNewInfos.stream()
                            .filter(t -> (t.TIME != null && t.TIME.getTime() > DateUtil.calendarDateByMonth(-13).getTime()))
                            .collect(Collectors.toList());
                }
                if (postNewInfos != null && postNewInfos.size() > 0) {
                    //初始化数据，执行mongodb入库
                    int count = postNewInfos.size();
                    for (PostInfoNewModel postNewInfo : postNewInfos) {
                        int yuanID = postNewInfo.YUANID;
                        String yuanDetail = "";
                        if (yuanID != 0) {
                            yuanDetail = JSON.toJSONString(getYuanDetailList(yuanID));
                        }
                        //YUANDETAIL保存到Mongodb(改为update)
                        postDao.saveYuanDetailToMongo(yuanDetail, postNewInfo.ID);
                    }
                    String maxUpdate = DateUtil.dateToStr(postNewInfos.get(count - 1).UPDATETIME, DateUtil.datePattern);

                    if (breakPoint.equals(maxUpdate) && count == limit) {
                        breakPoint = maxUpdate + "|" + postNewInfos.get(count - 1).TIMEPOINT;
                        logger.info("处理{}条数据完成，时间断点{}->同一秒大量数据发生", count, breakPoint);
                    } else {
                        breakPoint = maxUpdate;
                        logger.info("处理{}条数据完成，时间断点{}", count, breakPoint);
                    }
                    if (breakPoint.equals(maxUpdate) && count < limit) {
                        breakPoint = DateUtil.dateToStr(DateUtil.calendarDateBySecond(postNewInfos.get(count - 1).UPDATETIME, 1));
                    }
                    userRedisDao.set(currentMethodName, breakPoint, 60 * 60 * 24 * 7L);
                    if (count < limit) {
                        isRunning = false;
                    }
                } else {
                    logger.info("本次更新无数据");
                    isRunning = false;
                }
                if (!appConstant.cyclicInterruptSwitch) {
                    logger.info("通过apollo中断该循环，方法名：{}", currentMethodName);
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("cacheYuanDetail()方法发生异常{}", e.getMessage(), e);
            return false;
        }
        return true;
    }


    public boolean cacheFindScore() {
        String currentMethodName = "cacheFindScoreJava";
        try {

            Date breakPoint = null;
            breakPoint = userRedisDao.getBreakTime(currentMethodName);
            if (breakPoint == null) {
                breakPoint = DateUtil.strToDate(
                        DateUtil.dateToStr(DateUtil.calendarDateByDays(-3), DateHelper.FORMAT_YYYY_MM_DD)
                        , DateHelper.FORMAT_YYYY_MM_DD);
            }

            logger.info("发现页开始同步数据，cacheFindScore() 上次断点：" + DateUtil.dateToStr(breakPoint));
            boolean isRunning = true;

            while (isRunning) {
                List<String> codeList = jjbconfigDao.getCodeTypes();
                int batchCount = appConstant.batchPostReadCount;
                List<Map<String, Object>> postExtraNewInfos = postExtraDao.getPostExtraNew(DateUtil.calendarDateByHour(breakPoint, -2), batchCount, codeList, DateUtil.calendarDateByDays(-3));
                //保留近一年
                if (postExtraNewInfos != null && postExtraNewInfos.size() > 0) {
                    postExtraNewInfos = postExtraNewInfos.stream()
                            .filter(t ->
                                    (t.get("TIME") != null && DateUtil.strToDate(t.get("TIME").toString()).getTime() > DateUtil.calendarDateByMonth(-13).getTime()))
                            .collect(Collectors.toList());
                }

                int count = postExtraNewInfos.size();
                if (postExtraNewInfos != null && count > 0) {
                    List<Integer> allIds = new ArrayList<>();
                    for (Map<String, Object> postExtraNewInfo : postExtraNewInfos) {
                        try {
                            int type = Integer.parseInt(postExtraNewInfo.get("TYPE").toString());
                            if (type == 49 || type == 50) {
                                allIds.add(Integer.parseInt(postExtraNewInfo.get("ID").toString()));
                            }
                        } catch (Exception e) {
                            logger.error("发现页----【问答帖数据】读取数据，涉及数据allIdsLong:{},异常:{}", JSON.toJSONString(postExtraNewInfo), e.getMessage());
                        }

                    }
                    List<List<?>> qaLists = new ArrayList<>();
                    if (allIds.size() > 0) {
                        qaLists = postExtraDao.getPostExtras(allIds);
                    }
                    logger.info("发现页----【问答帖数据】读取完成，涉及ID:{}", allIds);
                    Date maxUpdateTime = (Date) postExtraNewInfos.get(count - 1).get("UPDATETIME");
                    logger.info("本次查询起点：{},记录数：{}，最大updateTime：{}", breakPoint, count, maxUpdateTime);
                    //config 回答数系数，评论数系数，点赞数系数，悬赏金额系数，悬赏附加分，悬赏分最高限制
                    String[] configs = StringUtils.isNullOrEmpty(appConstant.findConfig) ? "3,2,3,0.01,10,20".split(",") : appConstant.findConfig.split(",");
                    double score = 0;
                    for (Map<String, Object> postExtraNewInfo : postExtraNewInfos) {
                        //96*2+101*3
                        score = Long.parseLong(postExtraNewInfo.get("PINGLUNNUM").toString()) * Double.parseDouble(configs[1])
                                + Long.parseLong(postExtraNewInfo.get("LIKECOUNT").toString()) * Double.parseDouble(configs[2]);
                        if (postExtraNewInfo.get("TYPE").toString().equals("49")) { //问题
                            if (qaLists != null && qaLists.size() > 0) {
                                List<FundQuestionInfo> questionModels = (List<FundQuestionInfo>) qaLists.get(0);
                                FundQuestionInfo questionModel = questionModels.stream().filter(item -> item.ArticleId == Long.parseLong(postExtraNewInfo.get("ID").toString())).findFirst().orElse(null);
                                if (questionModel != null) {
                                    score = questionModel.AnswerCount * Double.parseDouble(configs[0])
                                            + (Math.abs(questionModel.Amount) < 0.0000000001 ? 0 : Math.min(Double.parseDouble(configs[5]), Double.parseDouble(configs[4]) + questionModel.Amount * Double.parseDouble(configs[3])));
                                    logger.info("问题帖子id：{};分数：{}", postExtraNewInfo.get("ID"), score);
                                }
                            }
                        } else if (postExtraNewInfo.get("TYPE").toString().equals("50")) { //回答
                            if (qaLists != null && qaLists.size() > 0) {
                                List<AnswerExtensionModel> answerModels = (List<AnswerExtensionModel>) qaLists.get(1);
                                AnswerExtensionModel ainfo = answerModels.stream().filter(item -> item.ArticleId == Long.parseLong(postExtraNewInfo.get("ID").toString())).findFirst().orElse(null);
                                String QID = postExtraNewInfo.get("QID") != null ? postExtraNewInfo.get("QID").toString() : "";
                                if (ainfo != null) {
                                    score = ainfo.CommentCount * Double.parseDouble(configs[1])
                                            + ainfo.LikeCount * Double.parseDouble(configs[2])
                                            + (ainfo.IsAdopted == 1 ? Math.min(Double.parseDouble(configs[5]), Double.parseDouble(configs[4]) + ainfo.Amount * Double.parseDouble(configs[3])) : 0);
                                    QID = ainfo.QID;
                                    logger.info("回答帖子id：{};分数：{}", postExtraNewInfo.get("ID"), score);
                                }
                                postExtraNewInfo.put("QID", QID);
                            }
                        }
                        postExtraNewInfo.put("FINDSCORE", score);
                        boolean res1 = fundUserProfitDao.updateFundUserProfit(postExtraNewInfo, score);
                        boolean res2 = passportFundMrgDao.updateFundUserProfit(postExtraNewInfo, score);
                        if (!res1 || !res2) {
                            logger.error("更新表失败!");
                            return false;
                        }

                        postExtraNewInfo.remove("CREATETIME");
                        postExtraNewInfo.remove("CONTENTCOUNT");
                        postExtraNewInfo.remove("PINGLUNNUM");
                        postExtraNewInfo.remove("POSTUPDATETIME");
                    }
                    List<Map<String, Object>> findScoreInfos = getSpecificScore(postExtraNewInfos);
                    if (findScoreInfos != null && findScoreInfos.size() > 0) {
                        postDao.saveMinTableToMongo(findScoreInfos);
                        List<String> idList = findScoreInfos.stream().map(item -> item.get("ID").toString()).collect(Collectors.toList());
                        logger.info("更新的帖子id:{}", String.join(",", idList));
                    } else {
                        logger.info("PostFindScore 无数据更新。");
                    }

                    for (Map<String, Object> postExtraNewInfo : postExtraNewInfos) {

                        postExtraNewInfo.remove("UID");
                        //postExtraNewInfo.remove("LIKECOUNT");
                        //postExtraNewInfo.remove("CLICKNUM");
                        postExtraNewInfo.remove("TIME");
                        postExtraNewInfo.remove("CODE");
                        postExtraNewInfo.remove("DEL");
                        postExtraNewInfo.remove("TTJJDEL");
                        postExtraNewInfo.remove("UPDATETIME");

                        //删除拓展字段
                        postExtraNewInfo.remove("EXTEND");
                        //删除TIMEPOINT 字段
                        postExtraNewInfo.remove("TIMEPOINT");

                        // 重置更新时间 默认为此刻时间
                        postExtraNewInfo.put("UPDATETIME", DateUtil.getNowDate());
                    }

                    if (postExtraNewInfos != null && postExtraNewInfos.size() > 0) {
                        // 总共列数(字段数)
                        int colCount = postExtraNewInfos.get(0).size();
                        for (Map<String, Object> postExtraNewInfo : postExtraNewInfos) {
                            try {
                                // 保存
                                boolean res = postDao.upsertById(postExtraNewInfo);
                            } catch (Exception e) {
                                logger.error("帖子ID:{}，异常信息：{}", postExtraNewInfo.get("ID").toString(), e.getMessage(), e);
                            }
                        }
                        logger.info("【本轮结束】，更新帖子数：{}", postExtraNewInfos.size());
                        logger.info("--------------------------------分隔符--------------------------------");
                    }
                    if (breakPoint == maxUpdateTime && postExtraNewInfos.size() == batchCount) {
                        logger.info("cacheFindScore()同一秒有大量数据，超过阈值！!");
                        break;
                    }
                    if (breakPoint == maxUpdateTime && postExtraNewInfos.size() < batchCount) {
                        // 加1秒 (count>0)
                        maxUpdateTime = DateUtil.calendarDateBySecond(((Date) postExtraNewInfos.get(count - 1).get("UPDATETIME")), 1);
                    }
                    breakPoint = maxUpdateTime;

                    userRedisDao.setBreakTime(currentMethodName, breakPoint);
                    logger.info("{}当前断点：{}", currentMethodName, breakPoint);
                    if (postExtraNewInfos.size() < batchCount) {
                        isRunning = false;
                    }
                } else {
                    isRunning = false;
                }
                if (!appConstant.cyclicInterruptSwitch) {
                    logger.info("通过apollo中断该循环，方法名：{}", currentMethodName);
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("cacheFindScore()发生异常：{}", e.getMessage(), e);
            return false;
        }
        return true;
    }


    /**
     * 主贴信息附加5条评论
     *
     * @return
     * @date 2022/10/28
     */
    public boolean replyRankCalcProcess() {
        String breakPointName = "replyRankCalcProcessJava";
        //获取断点时间
        Date breakPoint = null;
        breakPoint = userRedisDao.getBreakTime(breakPointName);
        if (breakPoint == null) {
            breakPoint = DateHelper.stringToDate2("2023-01-01", DateHelper.FORMAT_YYYY_MM_DD);
        }
        logger.info("replyRankCalcProcess() 上次断点：" + DateUtil.dateToStr(breakPoint));
        try {
            int batchCount = appConstant.batchPostReadCount;
            boolean isRunning = true;
            while (isRunning) {
                //获取更新的帖子
                List<ReplyInfoNewModel> list = replyInfoNewDao.getReplyList(breakPoint, batchCount);
                if (list != null && list.size() > 0) {
                    List<Integer> groupList = list.stream().map(item -> item.TOPICID).distinct().collect(Collectors.toList());
                    Date maxUpdateTime = list.stream().max(Comparator.comparing(item -> item.UPDATETIME)).get().UPDATETIME;
                    logger.info("数量：{}，最大更新时间：{}，当前断点：{}", groupList.size(), DateUtil.dateToStr(maxUpdateTime, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS_SSS), breakPoint);
                    logger.info("涉及帖子为：{}", JSON.toJSONString(groupList));
                    for (int group : groupList) {
                        long start = DateHelper.getNowTimeLong();
                        // 获取包含作者评论的所有顶级父评论
                        //根据帖子ID获取帖子信息
                        List<ReplyInfoNewModel> replys = replyInfoNewDao.getReplyListById(group, false);
                        YuanDetailModel postInfo = postInfoDao.getYuanDetail(group);
                        List<ReplyInfoNewModel> authorReplys = replys.stream()
                                .filter(item -> Objects.equals(item.UID, postInfo.UID))
                                .filter(item -> item.DEL == 0).collect(Collectors.toList());
                        //包含作者评论的父评论ID集合
                        List<Long> replyIds = new ArrayList<>();
                        if (authorReplys.size() > 0) { //存在楼主的评论
                            //根据帖子ID获取作者的评论
                            List<ReplyInfoNewModel> postReplys = replys.stream()
                                    .filter(a -> a.DEL == 0 && a.TTJJDEL == 0 && a.ISENABLED == 1)
                                    .collect(Collectors.toList());
                            if (postReplys != null && postReplys.size() > 0) {
                                //顶级评论
                                replyIds.addAll(postReplys.stream().filter(l -> StringUtils.isNullOrEmpty(l.HUIFUIDLIST)).map(s -> s.ID).collect(Collectors.toList()));
                                //子评论
                                List<ReplyInfoNewModel> subReplys = postReplys.stream().filter(l -> !StringUtils.isNullOrEmpty(l.HUIFUIDLIST)).collect(Collectors.toList());
                                if (subReplys.size() > 0) {
                                    for (ReplyInfoNewModel item : subReplys) {
                                        long topId = item.ID;
                                        topId = getAllTopReplyNew(replys, item.ID);
                                        replyIds.add(topId);
                                    }
                                }
                            }
                        }

                        List<Map<String, Object>> mapList = new ArrayList<>();
                        List<ReplyInfoViewModel> replyStrList = new ArrayList<>();
                        List<ReplyInfoNewModel> validReplys = replys.stream().filter(l -> l.DEL == 0 && l.ISENABLED == 1).collect(Collectors.toList());

                        //只保留近一年的评论
                        if (validReplys != null && validReplys.size() > 0) {
                            validReplys = validReplys.stream().filter(a -> a.TIME.getTime() > DateUtil.calendarDateByMonth(-13).getTime()).collect(Collectors.toList());
                        }

                        List<ReplyInfoNewModel> topReplys = validReplys.stream().sorted(Comparator.comparing(m -> m.TIME, Comparator.reverseOrder())).collect(Collectors.toList());
                        for (ReplyInfoNewModel topReply : topReplys) {
                            ReplyInfoViewModel rv = new ReplyInfoViewModel();
                            //.net  DataEntityConversion.AutoMapping(i, rv);  相同属性进行匹配、赋值
                            //相同属性进行匹配、赋值
                            rv.ID = topReply.ID;
                            rv.TOPICID = topReply.TOPICID;
                            rv.LOUCENG = topReply.LOUCENG;
                            rv.DEL = topReply.DEL;
                            rv.TTJJDEL = topReply.TTJJDEL;
                            rv.CODE = topReply.CODE;
                            rv.UID = topReply.UID;
                            rv.NICHENG = topReply.NICHENG;
                            rv.TIME = topReply.TIME;
                            rv.TEXT = CommonUtils.handleTextForHtml(topReply.TEXT);
                            rv.TEXTEND = CommonUtils.handleTextForHtml(topReply.TEXTEND);
                            rv.KEYWORDLIST = topReply.KEYWORDLIST;
                            rv.IP = topReply.IP;
                            rv.HUIFUIDLIST = topReply.HUIFUIDLIST;
                            rv.PUSHTIME = topReply.PUSHTIME;
                            rv.PIC = topReply.PIC;
                            rv.ISENABLED = topReply.ISENABLED;
                            rv.CREATETIME = topReply.CREATETIME;
                            rv.UPDATETIME = topReply.UPDATETIME;

                            //若子评论不存在，则不显示子子评论
                            if (!StringUtils.isNullOrEmpty(topReply.HUIFUIDLIST)) {
                                List<HuifuDetailModel> huifuDetail = replyInfoNewDao.getHuifuDetail(topReply.HUIFUIDLIST);
                                rv.HuifuDetails = huifuDetail;
                            }
                            List<KeyWordModel> keywordList = JacksonUtil.deserialize(rv.KEYWORDLIST, List.class, KeyWordModel.class);
                            //回复列表 pic字段的图片，加到keywordlist中，接口用

                            //#region  基金吧600 需求暂时不上线
                            int keywordCount = keywordList == null ? 0 : keywordList.size();
                            if (!StringUtils.isNullOrEmpty(rv.PIC)) {
                                for (String item : rv.PIC.split(",")) {
                                    KeyWordModel innerModel = new KeyWordModel();
                                    innerModel.Text = item;
                                    innerModel.Type = 100;
                                    innerModel.Lable = String.format("<!--adr%s-->", keywordCount);
                                    rv.TEXTEND += innerModel.Lable;
                                    keywordList.add(innerModel);
                                    keywordCount++;
                                }
                                rv.KEYWORDLIST = JacksonUtil.obj2String(keywordList);
                            }
                            //#endregion
                            mapList.add(CommonUtils.beanToMap(rv));
                            replyStrList.add(rv);
                        }
                        //取前5条
                        mapList = mapList.stream().limit(5).collect(Collectors.toList());
                        replyStrList = replyStrList.stream().limit(5).collect(Collectors.toList());
                        //更新mongo
                        boolean updateResult = postDao.updateReply(group, replyIds, mapList, replyStrList, validReplys, replys, start);
                        logger.info("帖子：{}的更新mongo结果为updateResult:{}", group, updateResult);
                    }
                    if (breakPoint == maxUpdateTime && list.size() == batchCount) {
                        //数据警告
                        logger.info("replyRankCalcProcess() 同一秒有大量数据，超过阈值！!");
                        break;
                    }
                    if (breakPoint == maxUpdateTime && list.size() < batchCount) {
                        //加一毫秒
                        breakPoint = new Date(DateHelper.dateToUnixTimeMillisecond(breakPoint) + 1L);
                    } else {
                        breakPoint = maxUpdateTime;
                    }
                    //断点
                    userRedisDao.setBreakTime(breakPointName, breakPoint);
                    logger.info("replyRankCalcProcess() 当前断点：{}", breakPoint);

                    if (list.size() < batchCount) {
                        isRunning = false;
                    }
                } else {
                    logger.info("本次同步无数据");
                    isRunning = false;
                }
                if (!appConstant.cyclicInterruptSwitch) {
                    logger.info("通过apollo中断该循环，方法名：{}", breakPointName);
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("replyRankCalcProcess() 发生异常：{}", e.getMessage(), e);
            return false;
        }
        return true;
    }

    /**
     * 获取所有顶级父评论
     *
     * @param list
     * @param id
     */
    private Long getAllTopReply(List<ReplyInfoNewModel> list, long id, long topId) {
        try {
            if (list.size() == 0) return topId;
            ReplyInfoNewModel tempInfo = new ReplyInfoNewModel();
            long parentId = 0;
            if (id > 0) {
                //如果是顶级评论，HUIFUIDLIST为null
                tempInfo = list.stream().filter(t -> t.ID == id).findFirst().get();
                if (tempInfo != null && StringUtils.isNullOrEmpty(tempInfo.HUIFUIDLIST)) {
                    topId = tempInfo.ID;
                    return topId;
                }
                long temp = 0;
                if (tempInfo != null) {
                    temp = Long.parseLong(tempInfo.HUIFUIDLIST.replace(",", ""));
                    parentId = temp;
                }
            }
            if (tempInfo != null && tempInfo.ID == 0) {
                return topId;
            } else {
                topId = parentId;
                topId = getAllTopReply(list, parentId, topId);
            }
        } catch (Exception e) {
            logger.error("获取所有顶级父评论报错，error：{}", e.toString());
        }
        return topId;
    }

    /**
     * 拿2层原帖
     */
    private List<YuanDetailModel> getYuanDetailList(int yuanID) {
        List<YuanDetailModel> res = new ArrayList<>();
        YuanDetailModel d = postInfoDao.getYuanDetail(yuanID);
        if (d != null) {
            res.add(d);
            if (d.YUANID != 0) {
                res.add(postInfoDao.getYuanDetail(d.YUANID));
            }
        }
        return res;
    }


    /**
     * 过滤小于5分的行
     */
    public List<Map<String, Object>> getSpecificScore(List<Map<String, Object>> postExtraNewInfos) throws JsonProcessingException {
        List<Map<String, Object>> dtNew = postExtraNewInfos;
        List<Map<String, Object>> res = new ArrayList<>();

        if (dtNew.size() < 1) return dtNew;
        for (int i = dtNew.size() - 1; i >= 0; i--) {
            Map<String, Object> info = postExtraNewInfos.get(i);
            //帖子类型
            int type = Integer.parseInt(info.get("TYPE").toString());
            //帖子扩展字段
            String extend = info.get("EXTEND") != null ? info.get("EXTEND").toString() : "";
            if (Double.parseDouble(info.get("FINDSCORE").toString()) < 1) {
                //postExtraNewInfos.remove(i);
                continue;
            }
            //如果不是基金栏目下的财富号文章剔除
            if (type == EnumPostType.WeMedia.getValue() && !StringUtils.isNullOrEmpty(extend)) {
                try {
                    PostCFHModel extendModel = JSONObject.parseObject(extend, PostCFHModel.class);
                    if (extendModel == null || !Objects.equals(extendModel.ColumnIds, String.valueOf(EnumCFHArticle.Fund.hashCode()))) {
                        String postId = info.get("ID").toString();
                        logger.info("基金发现页_删除非基金财富号文章:{}", postId);
                        //postExtraNewInfos.remove(i);
                        continue;
                    }
                } catch (Exception e) {
                    logger.error("发现分更新PostFindScore_拓展字段反序列化{}", e.getMessage());
                }
            }
            if ((DateHelper.getNowTimeLong() - ((Date) info.get("TIME")).getTime()) / (1000 * 3600 * 24) > 90) {
                //postExtraNewInfos.remove(i);
                continue;
            } else {
                int id = Integer.parseInt(info.get("ID").toString());
                int del = Integer.parseInt(info.get("DEL").toString());
                int ttjjdel = Integer.parseInt(info.get("TTJJDEL").toString());
                if (del == 1 || ttjjdel == 1) {
                    //postExtraNewInfos.remove(i);
                    postDao.removeFromPostFindScore(id);
                    continue;
                }
            }
            //符合条件的存到新list
            res.add(info);
        }
        for (Map<String, Object> map : res) {
            map.remove("LIKECOUNT");
            map.remove("CLICKNUM");
            map.remove("UPDATETIME");
            map.remove("DEL");
            map.remove("TTJJDEL");
        }
        return res;
    }

    /**
     * 保持3个月内的帖子
     * 每天执行一次
     */
    public boolean removePostFindScore() {
        try {
            String breakPointName = "removePostFindScoreJava";
            //获取断点时间 breakTime=lastUpdateTime
            Date breakPoint = null;
            breakPoint = userRedisDao.getBreakTime(breakPointName);
            if (breakPoint == null) {
                breakPoint = DateHelper.stringToDate2("2023-01-01", DateHelper.FORMAT_YYYY_MM_DD);
            }
            logger.info("removePostFindScore() 上次断点：" + DateUtil.dateToStr(breakPoint));
            if (breakPoint.getTime() < DateUtil.getNowDate().getTime()) {
                Date date = DateUtil.calendarDateByDays(-90);
                //删除三个月前的帖子
                boolean result = postDao.removeFromPostFindScore(date);
                if (result) {
                    userRedisDao.setBreakTime(breakPointName, DateUtil.getNowDate(DateHelper.FORMAT_YYYY_MM_DD));
                }
            }
        } catch (Exception e) {
            logger.error("removePostFindScore()发生异常，error:{}", e.getMessage(), e);
            return false;
        }
        return true;
    }

    /**
     * 获取所有顶级父评论
     *
     * @param list
     * @param id
     */
    private Long getAllTopReplyNew(List<ReplyInfoNewModel> list, long id) {
        Long temp = id;
        try {

            if (list.size() == 0) {
                return temp;
            }

            ReplyInfoNewModel tempInfo = new ReplyInfoNewModel();

            if (id > 0) {
                //如果是顶级评论，HUIFUIDLIST为null
                tempInfo = list.stream().filter(t -> t.ID == id).findFirst().orElse(null);
                //子评论有可能比父评论同步过来早
                if (tempInfo == null) {
                    return temp;
                }

                if (tempInfo != null && StringUtils.isNullOrEmpty(tempInfo.HUIFUIDLIST)) {
                    temp = tempInfo.ID;
                    return temp;
                }

                if (tempInfo != null) {
                    temp = Long.parseLong(tempInfo.HUIFUIDLIST.replace(",", ""));
                }
            }

            if (tempInfo != null && tempInfo.ID == 0) {
                return temp;
            }

            temp = getAllTopReplyNew(list, temp);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return temp;
    }

}
