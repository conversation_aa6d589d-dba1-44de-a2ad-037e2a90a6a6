package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoByDailyCount;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.constant.DateConstant;
import ttfund.web.communityservice.dao.msyql.BarPostCountDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 单品吧帖子数量统计
 */
@JobHandler("PostTotalCountJob")
@Component
public class PostTotalCountJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(PostTotalCountJob.class);

    private static final String LOG_PREFIX = "PostTotalCountJob[单品吧帖子数量统计]";

    private static final int BATCH_SIZE = 100000;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private BarPostCountDao barPostCountDao;

    @Autowired
    private App app;


    @Override
    public ReturnT<String> execute(String s) {
        try {
            doExecute();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }

    private void doExecute() {
        //上次更新时间
        String breakpoint = userRedisDao.get(UserRedisConfig.POST_TOTAL_COUNT_JOB_BREAKPOINT);
        Date breakpointDate = StringUtils.isEmpty(breakpoint) ? DateUtil.calendarDateByDays(-15) :
            DateUtil.strToDate(breakpoint, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT);
        LOGGER.info("{}: 当前断点时间为: {}", LOG_PREFIX, breakpointDate);
        //获取需要重新统计的数据
        List<PostInfoByDailyCount> list = barPostCountDao.getCountList(breakpointDate, BATCH_SIZE);
        LOGGER.info("{}: 拉取列表长度: {}", LOG_PREFIX, list == null ? -1 : list.size());
        if (CollectionUtils.isNotEmpty(list)) {
            for (PostInfoByDailyCount item : list) {
                long totalCount = item.PostCount + item.HisPostCount;
                if (totalCount > 0) {
                    String cacheKey = String.format(BarRedisKey.FUND_GUBA_SERVICE_POST_COUNT, item.Code);
                    app.barredis.set(cacheKey, String.valueOf(totalCount), 180 * DateConstant.ONE_DAY);
                }
            }

            breakpointDate = list.stream().max(Comparator.comparing(o -> o.UpdateTime)).get().UpdateTime;
        }
        boolean result = userRedisDao.set(UserRedisConfig.POST_TOTAL_COUNT_JOB_BREAKPOINT,
            DateUtil.dateToStr(breakpointDate, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT), 90 * DateConstant.ONE_DAY);
        LOGGER.info("{}: 存入缓存结果: {}", LOG_PREFIX, result);
    }
}
