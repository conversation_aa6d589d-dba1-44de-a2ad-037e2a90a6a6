package ttfund.web.communityservice.timedtask.jijinbarJobs.robot;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumPostType;
import ttfund.web.communityservice.bean.jijinBar.post.PushResultEntity;
import ttfund.web.communityservice.bean.jijinBar.post.RobotLikeEntity;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.ApiRedisConstantConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * 机器人点赞帖子推送  3 天前的帖子推送
 */
@Slf4j
@JobHandler("RobotPostJob")
@Component
public class RobotPostJob extends IJobHandler {

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private App app;

    @Autowired
    private AppConstant appConstant;

    @Qualifier("pushRobotLikeExecutor")
    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            //负值
            Integer backTime = null;
            String type = null;
            Long expire = null;
            Boolean force = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                backTime = jsonObject.getInteger("backTime");
                type = jsonObject.getString("type");
                expire = jsonObject.getLong("expire");
                force = jsonObject.getBoolean("force");
            }

            if (batchReadCount == null) {
                batchReadCount = 10000;
            }

            if (backTime == null) {
                backTime = -3 * 24 * 3600;
            }

            if (type == null) {
                type = "0";
            }

            if (expire == null) {
                expire = 2 * 3600L;
            }

            if (force == null) {
                force = false;
            }

            log.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，backTime：{}，type：{}，expire：{}，force：{}",
                initBreakpoint,
                batchReadCount,
                backTime,
                type,
                expire,
                force
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.ROBOTPOSTJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            pushData(batchReadCount,
                backTime,
                CommonUtils.toList(type, ",").stream().map(a -> Integer.parseInt(a)).collect(Collectors.toList()),
                expire,
                force
            );

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void pushData(int batchReadCount, Integer backTime, List<Integer> typeList, Long expire, Boolean force) throws Exception {

        Date end = DateUtil.calendarDateBySecond(backTime);

        String breakpointName = UserRedisConfig.ROBOTPOSTJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByDays(-4));

            log.error("0.读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("1.读取断点。断点:{}", breakpoint);

        List<Map<String, Object>> list = postInfoNewDao.getPostWithExtraInfoByTime(
            "a.ID,a.TITLE,a.UID,a.TYPE,a.TIME,a.DEL,a.TTJJDEL,b.CLICKNUM,b.LIKECOUNT,b.PINGLUNNUM,b.CONTENTCOUNT",
            breakpointDate,
            end,
            batchReadCount
        );

        log.info("2.读取数据。数量：{}，数据：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {
            breakpointDate = (Date)list.get(list.size() - 1).get("TIME");

            list = list.stream().
                filter(a -> Objects.equals(a.get("DEL"), 0) &&
                    Objects.equals(a.get("TTJJDEL"), 0) &&
                    (CollectionUtils.isEmpty(typeList) || typeList.contains((Integer)a.get("TYPE")))
                ).
                collect(Collectors.toList());
        }

        log.info("3.过滤。数量：{}，数据：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {
            for (Map<String, Object> a : list) {
                String key = String.format(ApiRedisConstantConfig.ROBOT_LIKE_POST, String.valueOf(a.get("ID")));
                if (!force) {
                    String value = app.redisapiread.get(key);
                    if (StringUtils.hasLength(value)) {
                        continue;
                    }
                }

                dealItem(a);

                app.redisapiwrite.set(key, "1", expire);
            }
        }

        log.info("4.点赞推送。数量：{}，数据：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);
        log.info("5.更新断点。breakpoint：{}", breakpoint);

    }

    private void dealItem(Map<String, Object> map) throws Exception {

        //异步post数据
        threadPoolExecutor.execute(() -> {
            try {
                RobotLikeEntity obj = new RobotLikeEntity();
                obj.ID = String.valueOf(map.get("ID"));
                obj.PTYPE = Objects.equals((Integer)map.get("TYPE"), EnumPostType.QAAnswer.getValue()) ? 3 : 1;
                obj.TID = "";
                obj.TIME = (Date)map.get("TIME");
                obj.TYPE = (Integer)map.get("TYPE");
                obj.UID = map.get("UID").toString();
                obj.PUSHTYPE = 1;
                obj.TEXTLENGTH = map.get("CONTENTCOUNT") == null ? 0 : Integer.parseInt(map.get("CONTENTCOUNT").toString());
                obj.LIKECOUNT = map.get("LIKECOUNT") == null ? 0 : Integer.parseInt(map.get("LIKECOUNT").toString());
                obj.REPLYCOUNT = map.get("PINGLUNNUM") == null ? 0 : Integer.parseInt(map.get("PINGLUNNUM").toString());

                String html = HttpHelper.requestPostJson(MessageFormat.format(appConstant.robotLikePushUrl, String.valueOf(map.get("ID"))),
                    JacksonUtil.obj2String(Arrays.asList(obj)),
                    true
                );
                PushResultEntity result = null;
                if (StringUtils.hasLength(html)) {
                    result = JacksonUtil.string2Obj(html, PushResultEntity.class);
                }
                if (result == null || !result.isSuccess()) {
                    log.error("帖子点赞推送失败。id：{}，数据：{}，结果：{}", String.valueOf(map.get("ID")), JacksonUtil.obj2String(obj), JacksonUtil.obj2String(result));
                }
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        });
    }

}
