package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.handler.IJobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfo;
import ttfund.web.communityservice.bean.jijinBar.post.ai.AiMultiSummaryModel;
import ttfund.web.communityservice.bean.jijinBar.post.ai.BigVAiTaskResponse;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.CalBigVDataUserModel;
import ttfund.web.communityservice.dao.mongo.AiMultiSummariesDao;
import ttfund.web.communityservice.dao.mongo.PostInfoMongoDao;
import ttfund.web.communityservice.dao.msyql.CalBigVDataUserDao;
import ttfund.web.communityservice.service.BigVAiSummaryService;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;
import java.util.Calendar;

/**
 * 大V多机构帖子AI总结定时任务基类
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
public abstract class BigVMultiPostAiSummaryBaseJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(BigVMultiPostAiSummaryBaseJob.class);

    @Autowired
    protected CalBigVDataUserDao calBigVDataUserDao;

    @Autowired
    protected PostInfoMongoDao postInfoMongoDao;

    @Autowired
    protected AiMultiSummariesDao aiMultiSummariesDao;

    @Autowired
    protected BigVAiSummaryService bigVAiSummaryService;

    private static final int MAX_POLL_ATTEMPTS = 60; // 最大轮询次数（60次 * 5秒 = 300秒）
    private static final int POLL_INTERVAL_SECONDS = 5; // 轮询间隔（秒）

    /**
     * 执行多机构总结任务
     */
    protected String executeMultiSummaryTask(String param, Integer timeSlot, int startHour, int startMinute, int endHour, int endMinute) {
        try {
            logger.info("开始执行大V多机构帖子AI总结任务，时间段：{}，参数：{}", timeSlot, param);

            // 解析参数
            Date targetDate = parseTargetDate(param);
            
            // 检查是否已经处理过
            if (isAlreadyProcessed(targetDate, timeSlot)) {
                logger.info("时间段{}的任务已经处理过，跳过执行", timeSlot);
                return "SUCCESS";
            }

            // 获取所有有效的大V用户
            List<CalBigVDataUserModel> bigVUsers = calBigVDataUserDao.getAllActiveBigVUsers();
            if (CollectionUtils.isEmpty(bigVUsers)) {
                logger.warn("未找到有效的大V用户");
                return "SUCCESS";
            }

            logger.info("找到{}个大V用户", bigVUsers.size());

            // 获取时间范围内的所有帖子
            Date startTime = getTimeInDay(targetDate, startHour, startMinute);
            Date endTime = getTimeInDay(targetDate, endHour, endMinute);

            logger.info("时间段{}查询范围：{} 到 {}", timeSlot, startTime, endTime);
            logger.info("时间段{}查询范围（时间戳）：{} 到 {}", timeSlot, startTime.getTime(), endTime.getTime());

            List<BigVAiSummaryService.PostData> allPostData = new ArrayList<>();
            List<String> allPostIds = new ArrayList<>();
            List<String> allUids = new ArrayList<>();
            List<String> allNickNames = new ArrayList<>();

            for (CalBigVDataUserModel bigVUser : bigVUsers) {
                List<PostInfo> posts = postInfoMongoDao.getPostsByUidAndTimeRange(
                        bigVUser.getUID(), startTime, endTime);
                
                if (!CollectionUtils.isEmpty(posts)) {
                    logger.info("大V用户 {} 在时间段{}内找到{}个帖子", bigVUser.getNickName(), timeSlot, posts.size());
                    for (PostInfo post : posts) {
                        logger.info("帖子详情 - PostId: {}, Time: {}, 时间戳: {}, Title: {}",
                                post._id, post.TIME, post.TIME != null ? post.TIME.getTime() : "null", post.TITLE);

                        allPostData.add(new BigVAiSummaryService.PostData(
                                bigVUser.getNickName(),
                                post.TITLE,
                                post.CONTENTEND,
                                post._id,
                                bigVUser.getUID()
                        ));
                        allPostIds.add(post._id);
                        if (!allUids.contains(bigVUser.getUID())) {
                            allUids.add(bigVUser.getUID());
                            allNickNames.add(bigVUser.getNickName());
                        }
                    }
                } else {
                    logger.info("大V用户 {} 在时间段{}内没有找到帖子", bigVUser.getNickName(), timeSlot);
                }
            }

            if (CollectionUtils.isEmpty(allPostData)) {
                logger.info("时间段{}内没有找到帖子", timeSlot);
                return "SUCCESS";
            }

            logger.info("时间段{}内共找到{}个帖子", timeSlot, allPostData.size());

            // 处理多机构总结
            return processMultiSummary(allPostData, allPostIds, allUids, allNickNames, timeSlot, targetDate);

        } catch (Exception e) {
            logger.error("大V多机构帖子AI总结任务执行异常", e);
            return "FAIL";
        }
    }

    /**
     * 处理多机构总结
     */
    private String processMultiSummary(List<BigVAiSummaryService.PostData> postDataList,
                                     List<String> postIds, List<String> uids, List<String> nickNames,
                                     Integer timeSlot, Date targetDate) {
        try {
            // 格式化消息
            String message = bigVAiSummaryService.formatMultiMessage(postDataList);
            
            logger.info("提交多机构总结任务，时间段：{}, 帖子数量：{}", timeSlot, postDataList.size());
            
            // 提交AI任务
            BigVAiTaskResponse taskResponse = bigVAiSummaryService.submitMultiSummaryTask(message);
            if (taskResponse == null || !StringUtils.hasLength(taskResponse.getTaskId())) {
                logger.error("提交AI多机构任务失败，时间段：{}", timeSlot);
                return "FAIL";
            }

            String taskId = taskResponse.getTaskId();
            logger.info("AI多机构任务提交成功，TaskId: {}, 时间段：{}", taskId, timeSlot);

            // 轮询获取结果
            BigVAiTaskResponse result = pollForMultiResult(taskId);
            if (result == null) {
                logger.error("轮询AI多机构任务结果失败，TaskId: {}, 时间段：{}", taskId, timeSlot);
                return "FAIL";
            }

            // 保存结果
            return saveMultiAiSummary(postIds, uids, nickNames, taskId, result, timeSlot, targetDate);

        } catch (Exception e) {
            logger.error("处理多机构总结异常，时间段：{}", timeSlot, e);
            return "FAIL";
        }
    }

    /**
     * 轮询获取AI多机构任务结果
     */
    private BigVAiTaskResponse pollForMultiResult(String taskId) {
        for (int attempt = 1; attempt <= MAX_POLL_ATTEMPTS; attempt++) {
            try {
                Thread.sleep(POLL_INTERVAL_SECONDS * 1000);
                
                BigVAiTaskResponse result = bigVAiSummaryService.pollMultiTaskResult(taskId);
                if (result != null) {
                    String status = result.getStatus();
                    if ("COMPLETED".equals(status)) {
                        logger.info("AI多机构任务完成，TaskId: {}, 耗时: {}ms", taskId, result.getProcessingTimeMs());
                        return result;
                    } else if ("PROCESSING".equals(status)) {
                        logger.info("AI多机构任务处理中，TaskId: {}, 第{}次轮询", taskId, attempt);
                        continue;
                    } else {
                        logger.error("AI多机构任务状态异常，TaskId: {}, Status: {}", taskId, status);
                        return null;
                    }
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("轮询被中断，TaskId: {}", taskId);
                return null;
            } catch (Exception e) {
                logger.error("轮询异常，TaskId: {}, 第{}次轮询", taskId, attempt, e);
            }
        }
        
        logger.error("轮询超时，TaskId: {}", taskId);
        return null;
    }

    /**
     * 保存AI多机构总结结果
     */
    private String saveMultiAiSummary(List<String> postIds, List<String> uids, List<String> nickNames,
                                    String taskId, BigVAiTaskResponse result, Integer timeSlot, Date targetDate) {
        try {
            AiMultiSummaryModel summary = new AiMultiSummaryModel();
            summary.setPostIds(String.join(",", postIds));
            summary.setUids(String.join(",", uids));
            summary.setNickNames(String.join(",", nickNames));
            summary.setSummary(result.getData());
            summary.setTaskId(taskId);
            summary.setStatus(result.getStatus());
            summary.setTimeSlot(timeSlot);
            summary.setProcessDate(targetDate);
            summary.setCreateTime(new Date());
            summary.setUpdateTime(new Date());

            aiMultiSummariesDao.save(summary);
            
            logger.info("AI多机构总结保存成功，TaskId: {}, 时间段：{}", taskId, timeSlot);
            return "SUCCESS";
            
        } catch (Exception e) {
            logger.error("保存AI多机构总结失败，TaskId: {}, 时间段：{}", taskId, timeSlot, e);
            return "FAIL";
        }
    }

    /**
     * 检查是否已经处理过
     */
    private boolean isAlreadyProcessed(Date targetDate, Integer timeSlot) {
        AiMultiSummaryModel existing = aiMultiSummariesDao.findByDateAndTimeSlot(targetDate, timeSlot);
        return existing != null;
    }

    /**
     * 解析目标日期参数
     */
    private Date parseTargetDate(String param) {
        Date targetDate = new Date(); // 默认当天
        
        if (StringUtils.hasLength(param)) {
            try {
                JSONObject jsonParam = JSON.parseObject(param);
                String dateStr = jsonParam.getString("date");
                if (StringUtils.hasLength(dateStr)) {
                    targetDate = DateUtil.strToDate(dateStr, "yyyy-MM-dd");
                }
            } catch (Exception e) {
                logger.warn("解析日期参数失败，使用默认日期：{}", param, e);
            }
        }
        
        logger.info("目标处理日期：{}", DateUtil.dateToStr(targetDate, "yyyy-MM-dd"));
        return targetDate;
    }

    /**
     * 获取指定日期的指定时间
     */
    private Date getTimeInDay(Date date, int hour, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        Date result = calendar.getTime();
        logger.info("生成时间 {}:{} -> {}, 时间戳: {}", hour, minute, result, result.getTime());
        return result;
    }
}
