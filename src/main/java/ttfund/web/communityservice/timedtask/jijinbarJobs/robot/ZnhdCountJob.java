package ttfund.web.communityservice.timedtask.jijinbarJobs.robot;

import com.eastmoney.particle.common.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.QA.ZnhdCountModel;
import ttfund.web.communityservice.bean.jijinBar.post.caifuhao.CaiFuHaoInfo;
import ttfund.web.communityservice.constant.DateConstant;
import ttfund.web.communityservice.dao.mongo.CaiFuHaoDao;
import ttfund.web.communityservice.dao.msyql.ZnhdQaDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.StringUtil;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Zhuyuang
 * @date : 2024-11-28 13:27
 * @description :
 */
@JobHandler("ZnhdCountJob")
@Component
public class ZnhdCountJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(ZnhdCountJob.class);

    private static final int LAST_DAY = 1;

    private static final int LAST_SEVEN_DAY = 7;

    private static final int LAST_MONTH = 30;

    private static final String CACHE_KEY = "ZnhdCountJob_breakpoint";

    @Autowired
    private ZnhdQaDao znhdQaDao;

    @Autowired
    private CaiFuHaoDao caiFuHaoDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        logicDeal();
        return ReturnT.SUCCESS;
    }

    private void logicDeal() {
        try {
            /*上线时需配置 凌晨0-1点钟执行*/
            //近一月
            count(LAST_MONTH);
            //近7天
            count(LAST_SEVEN_DAY);
            //昨日
            count(LAST_DAY);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    private void count(int day) {
        try {
            //上次更新时间
            Date breakTime = userRedisDao.getBreakTime(CACHE_KEY, DateUtil.calendarDateByMonth(-1));

            Date nowDate = new Date();
            //开始时间
            Date startDate = DateUtil.calendarDateByDays(-1 * day);
            //结束时间
            Date endDate = nowDate;
            //数据日期
            int dataDate = Integer.parseInt(DateUtil.dateToStr(nowDate, "yyyyMMdd"));

            List<ZnhdCountModel> totalList = znhdQaDao.GetTotalCount(startDate, endDate);

            if (totalList != null && totalList.size() > 0) {

                List<CaiFuHaoInfo> caifuhaoList = caiFuHaoDao.GetList();
                if (caifuhaoList != null && caifuhaoList.size() > 0) {
                    //填充财富号信息
                    totalList.forEach(item -> {
                    CaiFuHaoInfo cfhUser = caifuhaoList.stream().
                        filter(o -> o.RelatedUid.equals(item.PassportId)).
                        findFirst().orElse(null);
                    item.DataDate = dataDate;
                    item.CountType = day;
                    item.State = 1;
                    item.CreatTime = nowDate;
                    item.ID = item.DataDate + day + item.PassportId;
                    if (cfhUser != null) {
                        item.CFHID = cfhUser.CFHID == null ? "" : cfhUser.CFHID;
                        item.CFHName = cfhUser.CFHName == null ? "" : cfhUser.CFHName;
                    }});

                    //回答数
                    List<ZnhdCountModel> listAnsweredCount = znhdQaDao.GetAnswerCount(startDate, endDate);
                    if (listAnsweredCount != null && listAnsweredCount.size() > 0) {
                        List<ZnhdCountModel> finalTotalList = totalList;
                        listAnsweredCount.forEach(item -> {
                            ZnhdCountModel obj = finalTotalList.stream().
                                filter(o -> o.getPassportId().equals(item.PassportId)).
                                findFirst().orElse(null);
                            if (obj != null) {
                                obj.AnsweredCount = item.AnsweredCount;
                            }
                        });
                    }

                    //无效提问数
                    List<ZnhdCountModel> listInvalidQuCount = znhdQaDao.GetInvalidQuCount(startDate, endDate);
                    if (listInvalidQuCount != null && listInvalidQuCount.size() > 0) {
                        List<ZnhdCountModel> finalTotalList1 = totalList;
                        listInvalidQuCount.forEach(item -> {
                            ZnhdCountModel obj = finalTotalList1.stream().
                                filter(o -> o.getPassportId().equals(item.PassportId)).
                                findFirst().orElse(null);
                            if (obj != null) {
                                obj.InvalidQuCount = item.InvalidQuCount;
                            }
                        });
                    }

                     //转天天客服数
                    List<ZnhdCountModel> listTransferTTCount = znhdQaDao.GetTransferTTCount(startDate, endDate);
                    if (listTransferTTCount != null && listTransferTTCount.size() > 0) {
                        List<ZnhdCountModel> finalTotalList2 = totalList;
                        listTransferTTCount.forEach(item -> {
                            ZnhdCountModel obj = finalTotalList2.stream().
                                filter(o -> o.getPassportId().equals(item.PassportId)).
                                findFirst().orElse(null);
                            if (obj != null) {
                                obj.TransferTTCount = item.TransferTTCount;
                            }
                        });
                    }

                    //智能回答的匹配数量
                    List<ZnhdCountModel> listZnhdMatchCount = znhdQaDao.GetZnhdMatchCount(startDate, endDate);
                    if (listZnhdMatchCount != null && listZnhdMatchCount.size() > 0) {
                        List<ZnhdCountModel> finalTotalList3 = totalList;
                        listZnhdMatchCount.forEach(item -> {
                            ZnhdCountModel obj = finalTotalList3.stream().
                                filter(o -> o.getPassportId().equals(item.PassportId)).
                                findFirst().orElse(null);
                            if (obj != null) {
                                obj.ZnhdMatchCount = item.ZnhdMatchCount;
                            }
                        });
                    }

                    //计算排名
                    totalList = totalList.stream()
                        .sorted(Comparator.comparing(ZnhdCountModel::getAnswerPercent).reversed())
                        .collect(Collectors.toList());
                    final int[] rank = {1};
                    //上一个用户的回答率百分比
                    final BigDecimal[] lastAnswerPercent = {BigDecimal.ONE};
                    totalList.forEach(item -> {
                        if (StringUtils.isNotEmpty(item.CFHID)) {
                            item.AnswerRank = item.AnswerPercent.compareTo(lastAnswerPercent[0]) < 0 ? ++rank[0] : rank[0];
                            lastAnswerPercent[0] = item.AnswerPercent;
                        } else {
                            item.AnswerRank = 0;
                        }});

                    //数据更新入库
                    int result = znhdQaDao.Add(totalList);
                    if (result == 1) {
                        userRedisDao.setBreakTime(CACHE_KEY,  nowDate);
                    }

                    logger.info("本轮处理类型:" + day + "，本轮处理数据数量:" + totalList.size());
                }
            }
            else {
                logger.info("本次暂无统计数据");
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }
}
