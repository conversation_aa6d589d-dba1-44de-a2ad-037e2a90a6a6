package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.barrage.PassportUserInfoModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.dao.mongo.FundUserMonthRankDao;
import ttfund.web.communityservice.dao.mongo.FundUserWeekRankDao;
import ttfund.web.communityservice.dao.mongo.JJGSFundManagerRelationDao;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.msyql.JjbconfigDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.msyql.UserRelationCountDao;
import ttfund.web.communityservice.service.redis.UserRedisService;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 生成人气榜 周榜和月榜
 */
@JobHandler("PopularityRankCalcNewJob")
@Component
public class PopularityRankCalcNewJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PopularityRankCalcNewJob.class);

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private JjbconfigDao jjbconfigDao;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private JJGSFundManagerRelationDao jjgsFundManagerRelationDao;

    @Autowired
    private UserRelationCountDao userRelationCountDao;

    @Autowired
    private PassportUserInfoDao passportUserInfoDao;

    @Autowired
    private FundUserWeekRankDao fundUserWeekRankDao;

    @Autowired
    private FundUserMonthRankDao fundUserMonthRankDao;

    @Autowired
    private UserRedisService userRedisService;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            //基金吧类型配置
            String codeType = null;
            Boolean tradeTimeRun = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                codeType = jsonObject.getString("codeType");
                tradeTimeRun = jsonObject.getBoolean("tradeTimeRun");
            }

            if (tradeTimeRun == null) {
                tradeTimeRun = false;
            }

            logger.info("零，打印参数。codeType：{}，tradeTimeRun：{}", codeType, tradeTimeRun);

            popularityRankWCalc(codeType, tradeTimeRun);
            popularityRankMCalc(codeType, tradeTimeRun);
            delHisData(tradeTimeRun);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 人气周榜排行计算
     * 分数=赞*3+评论*2+粉丝*0.2
     * 分数=赞*3+总评论*2+粉丝*0.01-自己评论数*1.5   [新版]
     */
    private boolean popularityRankWCalc(String codeType, boolean tradeTimeRun) {
        try {

            if (!tradeTimeRun) {
                /*高峰时间段不执行*/
                if (!isShouldRun()) {

                    logger.info("周榜-0.高峰时间段不执行");
                    return false;
                }
            }

            //以用户为组获取全量评论点选数
            int wday = Calendar.getInstance().get(Calendar.DAY_OF_WEEK) - 1;
            int day = 0;
            day = wday == 0 ? -6 : 1 - wday;

            Date start = DateUtil.strToDate(DateUtil.dateToStr(DateUtil.calendarDateByDays(day), "yyyy-MM-dd"), "yyyy-MM-dd");
            List<String> codeTypes = getCodeTypesSpecial(codeType);

            logger.info("周榜-1.打印参数。configs：{}，start：{}，codeTypes：{}",
                appConstant.popularityRankConfig,
                DateUtil.dateToStr(start),
                codeTypes
            );

            List<Map<String, Object>> postList = postInfoNewDao.getPopularityInfoTable(start, codeTypes);

            logger.info("周榜-2.读取数据。数量：{}，头部数据：{}",
                postList == null ? 0 : postList.size(),
                postList == null ? null : JSON.toJSONStringWithDateFormat(postList.stream().limit(1).collect(Collectors.toList()), "yyyy-MM-dd HH:mm:ss.SSS")
            );

            List<Map<String, Object>> userList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(postList)) {
                Map<String, List<Map<String, Object>>> groupList = postList.stream().collect(Collectors.groupingBy(a -> (String)a.get("UID")));
                for (Map.Entry<String, List<Map<String, Object>>> entry : groupList.entrySet()) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("UID", entry.getKey());
                    map.put("PINGLUNNUM", entry.getValue().stream().map(a -> Integer.parseInt(String.valueOf(a.get("PINGLUNNUM")))).reduce(0, Integer::sum));
                    map.put("UserPingLunNum", entry.getValue().stream().map(a -> Integer.parseInt(String.valueOf(a.get("UserPingLunNum")))).reduce(0, Integer::sum));
                    map.put("LIKECOUNT", entry.getValue().stream().map(a -> Integer.parseInt(String.valueOf(a.get("LIKECOUNT")))).reduce(0, Integer::sum));

                    userList.add(map);
                }
            }

            logger.info("周榜-3.用户分组。数量：{}，头部数据：{}",
                userList == null ? 0 : userList.size(),
                userList == null ? null : JSON.toJSONString(userList.stream().limit(1).collect(Collectors.toList()))
            );

            String[] configs = appConstant.popularityRankConfig.split(",");
            List<String> totalCompanyUids = jjgsFundManagerRelationDao.getCompanyUserIds();
            List<String> tempCompanyUids = new ArrayList<>();
            if (totalCompanyUids == null) {
                totalCompanyUids = new ArrayList<>();
            }

            logger.info("周榜-4.读取基金公司数据。数量：{}，头部数据：{}",
                totalCompanyUids == null ? 0 : totalCompanyUids.size(),
                totalCompanyUids == null ? null : totalCompanyUids.stream().limit(20).collect(Collectors.toList())
            );

            List<String> uidList = null;
            Map<String, Integer> fansMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(userList)) {
                uidList = userList.stream().map(a -> (String)a.get("UID")).collect(Collectors.toList());
            }

            if (!CollectionUtils.isEmpty(uidList)) {
                List<List<String>> batchList = CommonUtils.toSmallList2(uidList, 100);
                for (List<String> batch : batchList) {
                    //获取用户粉丝数
                    List<Map<String, Object>> tempList = userRelationCountDao.getByUids(batch);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        tempList.forEach(a -> fansMap.put((String)a.get("uid"), Integer.parseInt(a.get("TotalFansNum").toString())));
                    }
                }

            }

            logger.info("周榜-5.读取用户粉丝数据。数量：{}，头部数据：{}",
                fansMap == null ? 0 : fansMap.size(),
                fansMap == null ? null : JSON.toJSONString(fansMap.entrySet().stream().limit(1).collect(Collectors.toList()))
            );

            if (!CollectionUtils.isEmpty(userList)) {
                double score = 0;
                Integer fansCount = 0;
                for (Map<String, Object> item : userList) {
                    //获取用户粉丝数
                    fansCount = fansMap.get(item.get("UID"));
                    fansCount = fansCount == null ? 0 : fansCount;
                    score = 0;
                    score += Integer.parseInt(String.valueOf(item.get("PINGLUNNUM"))) * Double.parseDouble(configs[1]);
                    score += Long.parseLong(String.valueOf(item.get("LIKECOUNT"))) * Double.parseDouble(configs[0]);
                    score += fansCount * Double.parseDouble(configs[2]);
                    score -= Long.parseLong(String.valueOf(item.get("UserPingLunNum"))) * Double.parseDouble(configs[3]);

                    item.put("FANSCOUNT", fansCount);
                    item.put("SCORE", score);
                    item.put("TIMEPOINT", DateUtil.getTimePoint(start));

                    //这里判断是否是基金公司用户
                    if (totalCompanyUids.contains(item.get("UID"))) {
                        tempCompanyUids.add((String)item.get("UID"));
                        item.put("RANKTYPE", 1);//基金公司榜单
                    } else {
                        item.put("RANKTYPE", 2);//个人榜单
                    }

                }
            }

            List<String> otherCompanyUids = totalCompanyUids.stream().filter(item -> !tempCompanyUids.contains(item)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(otherCompanyUids)) {

                Integer fansCount = 0;
                double score = 0;
                for (String uid : otherCompanyUids) {

                    fansCount = fansMap.get(uid);
                    fansCount = fansCount == null ? 0 : fansCount;

                    score = fansCount * Double.parseDouble(configs[2]);

                    Map<String, Object> tempMap = new HashMap<>();
                    tempMap.put("UID", uid);
                    tempMap.put("PINGLUNNUM", 0);
                    tempMap.put("LIKECOUNT", 0);
                    tempMap.put("FANSCOUNT", fansCount);
                    tempMap.put("SCORE", score);
                    tempMap.put("TIMEPOINT", DateUtil.getTimePoint(start));
                    tempMap.put("RANKTYPE", 1);//基金公司榜单

                    userList.add(tempMap);
                }
            }

            Map<String, String> passportInfoMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(uidList)) {
                List<List<String>> batchList = CommonUtils.toSmallList2(uidList, 100);
                for (List<String> batch : batchList) {
                    //获取用户粉丝数
                    List<PassportUserInfoModel> tempList = passportUserInfoDao.getPassportUserInfoListById(batch);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        tempList.forEach(a -> passportInfoMap.put(a.PassportID, a.NickName));
                    }
                }

            }

            logger.info("周榜-6.读取用户昵称数据。数量：{}，头部数据：{}",
                passportInfoMap == null ? 0 : passportInfoMap.size(),
                passportInfoMap == null ? null : JSON.toJSONString(passportInfoMap.entrySet().stream().limit(1).collect(Collectors.toList()))
            );

            if (!CollectionUtils.isEmpty(userList)) {
                for (Map<String, Object> item : userList) {
                    item.put("_id", item.get("UID"));
                    item.put("NICHENG", passportInfoMap.getOrDefault(item.get("UID"), ""));

                    item.remove("UserPingLunNum");
                }
            }

            if (!CollectionUtils.isEmpty(userList)) {
                userList.sort((o1, o2) -> Double.compare((Double)o2.get("SCORE"), (Double)o1.get("SCORE")));
            }

            logger.info("周榜-7.计算分数。数量：{}，头部数据：{}",
                userList == null ? 0 : userList.size(),
                userList == null ? null : JSON.toJSONString(userList.stream().limit(1).collect(Collectors.toList()))
            );

            if (!CollectionUtils.isEmpty(userList)) {
                //每周一得分初始化为0
                if (DayOfWeek.MONDAY.equals(LocalDate.now().getDayOfWeek()) && LocalTime.now().getHour() > 0 && LocalTime.now().getHour() < 2) {
                    //先设置分数为0
                    fundUserWeekRankDao.setScoreToZero();

                    logger.info("周榜-8-0.初始化分数写库。");
                }

                //再更新分数
                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(userList, 200);
                for (List<Map<String, Object>> batch : batchList) {
                    fundUserWeekRankDao.upsertMany(batch);
                }

                logger.info("周榜-8-1.数据写库。");
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return true;
    }

    /**
     * 人气月榜排行数计算
     */
    private boolean popularityRankMCalc(String codeType, boolean tradeTimeRun) {
        try {

            if (!tradeTimeRun) {
                /*高峰时间段不执行*/
                if (!isShouldRun()) {
                    logger.info("月榜-0.高峰时间段不执行");
                    return false;
                }
            }

            int days = 1 - Calendar.getInstance().get(Calendar.DAY_OF_MONTH);
            Date start = DateUtil.strToDate(DateUtil.dateToStr(DateUtil.calendarDateByDays(days), "yyyy-MM-dd"), "yyyy-MM-dd");
            List<String> codeTypes = getCodeTypesSpecial(codeType);

            logger.info("月榜-1.打印参数。configs：{}，start：{}，codeTypes：{}",
                appConstant.popularityRankConfig,
                DateUtil.dateToStr(start),
                codeTypes
            );

            List<Map<String, Object>> postList = postInfoNewDao.getPopularityInfoTable(DateUtil.calendarDateByDays(days), codeTypes);

            logger.info("月榜-2.读取数据。数量：{}，头部数据：{}",
                postList == null ? 0 : postList.size(),
                postList == null ? null : JSON.toJSONStringWithDateFormat(postList.stream().limit(1).collect(Collectors.toList()), "yyyy-MM-dd HH:mm:ss.SSS")
            );

            List<Map<String, Object>> userList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(postList)) {
                Map<String, List<Map<String, Object>>> groupList = postList.stream().collect(Collectors.groupingBy(a -> (String)a.get("UID")));
                for (Map.Entry<String, List<Map<String, Object>>> entry : groupList.entrySet()) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("UID", entry.getKey());
                    map.put("PINGLUNNUM", entry.getValue().stream().map(a -> Integer.parseInt(String.valueOf(a.get("PINGLUNNUM")))).reduce(0, Integer::sum));
                    map.put("UserPingLunNum", entry.getValue().stream().map(a -> Integer.parseInt(String.valueOf(a.get("UserPingLunNum")))).reduce(0, Integer::sum));
                    map.put("LIKECOUNT", entry.getValue().stream().map(a -> Integer.parseInt(String.valueOf(a.get("LIKECOUNT")))).reduce(0, Integer::sum));

                    userList.add(map);
                }
            }


            logger.info("月榜-3.用户分组。数量：{}，头部数据：{}",
                userList == null ? 0 : userList.size(),
                userList == null ? null : JSON.toJSONString(userList.stream().limit(1).collect(Collectors.toList()))
            );

            String[] configs = appConstant.popularityRankConfig.split(",");
            List<String> totalCompanyUids = jjgsFundManagerRelationDao.getCompanyUserIds();
            List<String> tempCompanyUids = new ArrayList<>();
            if (totalCompanyUids == null) {
                totalCompanyUids = new ArrayList<>();
            }

            logger.info("月榜-4.读取基金公司数据。数量：{}，头部数据：{}",
                totalCompanyUids == null ? 0 : totalCompanyUids.size(),
                totalCompanyUids == null ? null : totalCompanyUids.stream().limit(20).collect(Collectors.toList())
            );


            List<String> uidList = null;
            Map<String, Integer> fansMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(userList)) {
                uidList = userList.stream().map(a -> (String)a.get("UID")).collect(Collectors.toList());
            }

            if (!CollectionUtils.isEmpty(uidList)) {
                List<List<String>> batchList = CommonUtils.toSmallList2(uidList, 100);
                for (List<String> batch : batchList) {
                    //获取用户粉丝数
                    List<Map<String, Object>> tempList = userRelationCountDao.getByUids(batch);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        tempList.forEach(a -> fansMap.put((String)a.get("uid"), Integer.parseInt(a.get("TotalFansNum").toString())));
                    }
                }

            }

            logger.info("月榜-5.读取用户粉丝数据。数量：{}，头部数据：{}",
                fansMap == null ? 0 : fansMap.size(),
                fansMap == null ? null : JSON.toJSONString(fansMap.entrySet().stream().limit(1).collect(Collectors.toList()))
            );


            if (!CollectionUtils.isEmpty(userList)) {
                double score = 0;
                Integer fansCount = 0;
                for (Map<String, Object> item : userList) {
                    //获取用户粉丝数
                    fansCount = fansMap.get(item.get("UID"));
                    fansCount = fansCount == null ? 0 : fansCount;
                    score = 0;
                    score += Integer.parseInt(String.valueOf(item.get("PINGLUNNUM"))) * Double.parseDouble(configs[1]);
                    score += Long.parseLong(String.valueOf(item.get("LIKECOUNT"))) * Double.parseDouble(configs[0]);
                    score += fansCount * Double.parseDouble(configs[2]);
                    score -= Long.parseLong(String.valueOf(item.get("UserPingLunNum"))) * Double.parseDouble(configs[3]);

                    item.put("FANSCOUNT", fansCount);
                    item.put("SCORE", score);
                    item.put("TIMEPOINT", DateUtil.getTimePoint(start));

                    //这里判断是否是基金公司用户
                    if (totalCompanyUids.contains(item.get("UID"))) {
                        tempCompanyUids.add((String)item.get("UID"));
                        item.put("RANKTYPE", 1);//基金公司榜单
                    } else {
                        item.put("RANKTYPE", 2);//个人榜单
                    }

                }
            }

            List<String> otherCompanyUids = totalCompanyUids.stream().filter(item -> !tempCompanyUids.contains(item)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(otherCompanyUids)) {
                Integer fansCount = 0;
                double score = 0;
                for (String uid : otherCompanyUids) {

                    fansCount = fansMap.get(uid);
                    fansCount = fansCount == null ? 0 : fansCount;

                    score = fansCount * Double.parseDouble(configs[2]);

                    Map<String, Object> tempMap = new HashMap<>();
                    tempMap.put("UID", uid);
                    tempMap.put("PINGLUNNUM", 0);
                    tempMap.put("LIKECOUNT", 0);
                    tempMap.put("FANSCOUNT", fansCount);
                    tempMap.put("SCORE", score);
                    tempMap.put("TIMEPOINT", DateUtil.getTimePoint(start));
                    tempMap.put("RANKTYPE", 1);//基金公司榜单

                    userList.add(tempMap);

                }
            }


            Map<String, String> passportInfoMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(uidList)) {
                List<List<String>> batchList = CommonUtils.toSmallList2(uidList, 100);
                for (List<String> batch : batchList) {
                    //获取用户粉丝数
                    List<PassportUserInfoModel> tempList = passportUserInfoDao.getPassportUserInfoListById(batch);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        tempList.forEach(a -> passportInfoMap.put(a.PassportID, a.NickName));
                    }
                }

            }

            logger.info("月榜-6.读取用户昵称数据。数量：{}，头部数据：{}",
                passportInfoMap == null ? 0 : passportInfoMap.size(),
                passportInfoMap == null ? null : JSON.toJSONString(passportInfoMap.entrySet().stream().limit(1).collect(Collectors.toList()))
            );

            if (!CollectionUtils.isEmpty(userList)) {
                for (Map<String, Object> item : userList) {
                    item.put("_id", item.get("UID"));
                    item.put("NICHENG", passportInfoMap.getOrDefault(item.get("UID"), ""));

                    item.remove("UserPingLunNum");
                }
            }

            if (!CollectionUtils.isEmpty(userList)) {
                userList.sort((o1, o2) -> Double.compare((Double)o2.get("SCORE"), (Double)o1.get("SCORE")));
            }

            logger.info("月榜-7.计算分数。数量：{}，头部数据：{}",
                userList == null ? 0 : userList.size(),
                userList == null ? null : JSON.toJSONString(userList.stream().limit(1).collect(Collectors.toList()))
            );

            if (!CollectionUtils.isEmpty(userList)) {
                //每个月第一天凌晨得分初始化为0
                if (1 == LocalDate.now().getDayOfMonth() && LocalTime.now().getHour() > 0 && LocalTime.now().getHour() < 3) {
                    //先设置分数为0
                    fundUserMonthRankDao.setScoreToZero();

                    logger.info("月榜-8-0.初始化分数写库。");
                }

                //再更新分数
                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(userList, 200);
                for (List<Map<String, Object>> batch : batchList) {
                    fundUserMonthRankDao.upsertMany(batch);
                }

                logger.info("月榜-8-1.数据写库。");

            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return true;
    }

    /**
     * 清除历史数据，保留近2个月的数据
     */
    public boolean delHisData(boolean tradeTimeRun) {

        if (!tradeTimeRun) {
            /*高峰时间段不执行*/
            if (!isShouldRun()) {

                logger.info("清除-0.高峰时间段不执行。");
                return false;
            }
        }

        boolean b1 = false;
        boolean b2 = false;


        //历史数据保留近两个月
        Date startDate = DateUtil.calendarDateByMonth(-2);

        String cacheKey_w = "DelHisData_W_Flag";

        //每周一清除历史
        if (true || DayOfWeek.MONDAY.equals(LocalDate.now().getDayOfWeek()) && LocalTime.now().getHour() > 3 && LocalTime.now().getHour() < 5) {
            String cacheResult = app.barredis.get(cacheKey_w);
            if (!StringUtils.hasLength(cacheResult)) {
                long timePoint = DateUtil.getTimePoint(startDate);
                fundUserWeekRankDao.delByTimepoint(timePoint);
                app.barredis.set(cacheKey_w, "1", 3600 * 3L);

                b1 = true;
                logger.info("清除-1.清除周榜。");
            }
        }


        String cacheKey_m = "DelHisData_M_Flag";

        if (true || LocalDate.now().getDayOfMonth() == 1 && LocalTime.now().getHour() > 3 && LocalTime.now().getHour() < 5) {
            String cacheResult = app.barredis.get(cacheKey_m);
            if (!StringUtils.hasLength(cacheResult)) {
                long timePoint = DateUtil.getTimePoint(startDate);
                fundUserMonthRankDao.delByTimepoint(timePoint);
                app.barredis.set(cacheKey_m, "1", 3600 * 3L);

                b2 = true;
                logger.info("清除-2.清除月榜。");
            }
        }

        logger.info("清除-3.完成。周榜：{}，月榜：{}", b1, b2);

        return true;
    }

    /**
     * 判断是否高峰期
     */
    private boolean isShouldRun() {
        if ((LocalTime.now().compareTo(LocalTime.of(12, 50)) > 0 && LocalTime.now().compareTo(LocalTime.of(15, 25)) < 0)
            || (LocalTime.now().compareTo(LocalTime.of(9, 30)) > 0 && LocalTime.now().compareTo(LocalTime.of(11, 30)) < 0)) {
            return false;
        }
        return true;
    }


    private List<String> getCodeTypesSpecial(String initConfigs) {
        List<String> result = new ArrayList<>();
        if (StringUtils.hasLength(initConfigs)) {
            result.addAll(CommonUtils.toList(initConfigs, ","));
        }
        List<String> codeTypes = jjbconfigDao.getCodeTypes();
        if (!CollectionUtils.isEmpty(codeTypes)) {
            result.addAll(codeTypes);
        }

        result = result.stream().distinct().collect(Collectors.toList());
        return result;
    }

}
