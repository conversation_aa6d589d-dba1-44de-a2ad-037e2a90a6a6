package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.config.HighQualityPostModel;
import ttfund.web.communityservice.bean.jijinBar.post.config.HighQualityPostModelInCache;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.HighQualityPostDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 优质贴分发job
 */
@JobHandler("HighQualityPostDistributeJob")
@Component
public class HighQualityPostDistributeJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(HighQualityPostDistributeJob.class);

    private static List<String> FIELDS = Arrays.asList("_id", "uid", "time", "state", "tag", "codes", "historycodes", "updateTime");

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private HighQualityPostDao highQualityPostDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {
            String initBreakpoint = null;
            Integer batchReadCount = null;
            Integer interval = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                interval = jsonObject.getInteger("interval");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (interval == null) {
                interval = -30;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，interval：{}",
                    initBreakpoint,
                    batchReadCount,
                    interval);

            if (StringUtils.hasLength(initBreakpoint)) {
                DateUtil.strToDate(initBreakpoint);
                userRedisDao.set(UserRedisConfig.HIGHQUALITYPOSTDISTRIBUTEJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);

                logger.info("第零步，初始化断点。breakpoint：{}", initBreakpoint);
                return ReturnT.SUCCESS;
            }

            syncFromMongoToRedis(batchReadCount, interval);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 同步mongo数据到redis
     * 把优质贴按其配置的分发关系 分发到对应吧
     */
    private void syncFromMongoToRedis(int batchReadCount, int interval) throws Exception {

        String breakpointName = UserRedisConfig.HIGHQUALITYPOSTDISTRIBUTEJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);
        if (!StringUtils.hasLength(breakpoint)) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-60));

            logger.error("第一步，获取断点为空，使用默认断点。断点：{}", breakpoint);
        }

        logger.info("第一步，打印断点。断点：{}", breakpoint);

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        List<HighQualityPostModel> list = highQualityPostDao.getList(breakpointDate, batchReadCount, FIELDS);

        logger.info("第二步，获取数据。数量：{}，头部id列表：{}",
                list == null ? 0 : list.size(),
                list == null ? null : list.stream().map(a -> a.get_id()).limit(50).collect(Collectors.toList())
        );

        int i = 0;
        if (!CollectionUtils.isEmpty(list)) {
            ++i;

            breakpointDate = (list.get(list.size() - 1).getUpdateTime());

            String key = null;
            Date date = DateUtil.calendarDateByDays(interval);
            for (HighQualityPostModel item : list) {
                Integer state = item.getState();
                if (state != null && state == 1) {

                    List<String> historycodes = CommonUtils.toList(item.getHistorycodes(), ",");
                    if (!CollectionUtils.isEmpty(historycodes)) {
                        for (String code : historycodes) {
                            key = String.format(BarRedisKey.HIGH_QUALITY_POST_DISTRIBUTE_BAR, code);
                            String value = app.barredis.get(key);
                            List<HighQualityPostModelInCache> postListInCache = null;
                            if (!StringUtils.hasLength(value)) {
                                continue;
                            }

                            postListInCache = JSON.parseArray(value, HighQualityPostModelInCache.class);
                            if (!CollectionUtils.isEmpty(postListInCache)) {
                                postListInCache = postListInCache.stream()
                                        .filter(a -> a.getTime().compareTo(date) >= 0 &&
                                                !a.get_id().equals(item.get_id())
                                        ).collect(Collectors.toList());
                            }

                            app.barredis.set(key, JSON.toJSONString(postListInCache), 90 * 24 * 3600L);

                        }
                    }

                    logger.info("第三步，处理数据-不分发-第{}/{}个。帖子：{}，historycodes：{}",
                            i,
                            list.size(),
                            item.get_id(),
                            item.getHistorycodes());

                } else if (state != null && state == 2) {
                    List<String> historycodes = CommonUtils.toList(item.getHistorycodes(), ",");
                    List<String> codes = CommonUtils.toList(item.getCodes(), ",");

                    List<String> deleteCodes = historycodes.stream().filter(a -> !codes.contains(a)).collect(Collectors.toList());
                    List<String> insertCodes = codes.stream().filter(a -> !historycodes.contains(a)).collect(Collectors.toList());

                    if (!CollectionUtils.isEmpty(deleteCodes)) {
                        for (String code : deleteCodes) {
                            key = String.format(BarRedisKey.HIGH_QUALITY_POST_DISTRIBUTE_BAR, code);
                            String value = app.barredis.get(key);
                            List<HighQualityPostModelInCache> postListInCache = null;
                            if (!StringUtils.hasLength(value)) {
                                continue;
                            }

                            postListInCache = JSON.parseArray(value, HighQualityPostModelInCache.class);
                            if (!CollectionUtils.isEmpty(postListInCache)) {
                                postListInCache = postListInCache.stream()
                                        .filter(a -> a.getTime().compareTo(date) >= 0 &&
                                                !a.get_id().equals(item.get_id())
                                        ).collect(Collectors.toList());
                            }

                            app.barredis.set(key, JSON.toJSONString(postListInCache), 90 * 24 * 3600L);
                        }
                    }

                    logger.info("第三步，处理数据-分发-删除处理-第{}/{}个。帖子：{}，historycodes：{}",
                            i,
                            list.size(),
                            item.get_id(),
                            item.getHistorycodes());

                    if (!CollectionUtils.isEmpty(insertCodes)) {
                        for (String code : insertCodes) {
                            key = String.format(BarRedisKey.HIGH_QUALITY_POST_DISTRIBUTE_BAR, code);
                            String value = app.barredis.get(key);
                            List<HighQualityPostModelInCache> postListInCache = null;
                            if (StringUtils.hasLength(value)) {
                                postListInCache = JSON.parseArray(value, HighQualityPostModelInCache.class);
                            }

                            if (postListInCache == null) {
                                postListInCache = new ArrayList<>();
                            }
                            HighQualityPostModelInCache modelInCache = postListInCache.stream()
                                    .filter(a -> a.get_id().equals(item.get_id())).findFirst().orElse(null);

                            if (modelInCache == null) {
                                modelInCache = new HighQualityPostModelInCache();
                                modelInCache.set_id(item.get_id());
                                modelInCache.setUid(item.getUid());
                                modelInCache.setTag(item.getTag());
                                modelInCache.setTime(item.getTime());
                                postListInCache.add(modelInCache);
                            }

                            postListInCache = postListInCache.stream()
                                    .filter(a -> a.getTime().compareTo(date) >= 0)
                                    .collect(Collectors.toList());

                            app.barredis.set(key, JSON.toJSONString(postListInCache), 90 * 24 * 3600L);
                        }
                    }

                    logger.info("第三步，处理数据-分发-新增处理-第{}/{}个。帖子：{}，historycodes：{}，codes：{}",
                            i,
                            list.size(),
                            item.get_id(),
                            item.getHistorycodes(),
                            item.getCodes());

                }

                logger.info("第三步，处理数据完成-第{}/{}个。id:{}，数据：{}",
                        i,
                        list.size(),
                        item.get_id(),
                        JSON.toJSONString(item)
                );

            }
        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);
        logger.info("第四步，更新断点。断点：{}", breakpoint);

    }

}

