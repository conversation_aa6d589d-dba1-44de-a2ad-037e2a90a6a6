package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.QA.FundAnswerInfoModel;
import ttfund.web.communityservice.bean.messagepush.FundQuestionInfo;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.FundAnswerInfoDao;
import ttfund.web.communityservice.dao.mongo.FundQuestionInfoDao;
import ttfund.web.communityservice.dao.msyql.AnswerDao;
import ttfund.web.communityservice.dao.msyql.QuestionDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 问答同步业务(基金吧问答数据同步到mongdb)
 */
@JobHandler("QASyncBizJob")
@Component
public class QASyncBizJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(QASyncBizJob.class);

    private static List<String> setFields = Arrays.asList("Modified", "AuditStatusType", "IsAdopted"
        , "AdopedType", "Adoped", "IsBestAnswer", "UpdateTime", "IsEnable");

    private static Date minDate = DateUtil.getMinDate();

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private QuestionDao questionDao;

    @Autowired
    private FundQuestionInfoDao fundQuestionInfoDao;

    @Autowired
    private AnswerDao answerDao;

    @Autowired
    private FundAnswerInfoDao fundAnswerInfoDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Integer batchReadCount = null;
        String initBreakpoint1 = null;
        String initBreakpoint2 = null;
        if (StringUtils.hasLength(s)) {
            JSONObject jsonObject = JSON.parseObject(s);
            batchReadCount = jsonObject.getInteger("batchReadCount");
            initBreakpoint1 = jsonObject.getString("initBreakpoint1");
            initBreakpoint2 = jsonObject.getString("initBreakpoint2");
        }

        if (batchReadCount == null) {
            batchReadCount = 5000;
        }

        logger.info("第零步，打印参数。batchReadCount：{}，initBreakpoint1：{}，initBreakpoint2：{}",
            batchReadCount,
            initBreakpoint1,
            initBreakpoint2);

        if (StringUtils.hasLength(initBreakpoint1) || StringUtils.hasLength(initBreakpoint2)) {
            if (StringUtils.hasLength(initBreakpoint1)) {
                userRedisDao.set(UserRedisConfig.QASYNCBIZJOB_QUESTIONSYNCPROCESS_BREAKPOINT, initBreakpoint1, 30 * 24 * 3600L);
            }
            if (StringUtils.hasLength(initBreakpoint2)) {
                userRedisDao.set(UserRedisConfig.QASYNCBIZJOB_ANSWERSYNCPROCESS_BREAKPOINT, initBreakpoint2, 30 * 24 * 3600L);
            }

            logger.info("第零步，初始化断点。initBreakpoint1：{}，initBreakpoint2：{}",
                initBreakpoint1,
                initBreakpoint2);

            return ReturnT.SUCCESS;
        }

        questionSyncProcess(batchReadCount);
        answerSyncProcess(batchReadCount);

        return ReturnT.SUCCESS;
    }


    /**
     * 问题帖子扩展表同步到Mongodb
     */
    private boolean questionSyncProcess(int batchReadCount) {
        try {
            String breakpointName = UserRedisConfig.QASYNCBIZJOB_QUESTIONSYNCPROCESS_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("questionSyncProcess-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("questionSyncProcess-第一步，读取断点。断点:{}", breakpoint);

            int round = 0;
            boolean isRunning = true;
            while (isRunning) {
                round++;

                List<FundQuestionInfo> dataList = questionDao.getFundQuestionList(breakpointDate, batchReadCount, 1);

                if (CollectionUtils.isEmpty(dataList) || dataList.size() < batchReadCount) {
                    isRunning = false;
                }

                if (!CollectionUtils.isEmpty(dataList)) {

                    breakpointDate = dataList.stream().max(Comparator.comparing(o -> o.getUpdateTime())).get().getUpdateTime();

                    for (FundQuestionInfo item : dataList) {
                        item._id = item.QID;
                        if (item.ArticleId == null) {
                            item.ArticleId = 0;
                        }
                        if (item.PayType == null) {
                            item.PayType = 0;
                        }
                        if (item.AnswerType == null) {
                            item.AnswerType = 0;
                        }
                        if (item.AppType == null) {
                            item.AppType = 0;
                        }
                        if (item.AuditStatusType == null) {
                            item.AuditStatusType = 0;
                        }
                        if (item.IsEnd == null) {
                            item.IsEnd = 0;
                        }
                        if (item.HasBestAnswer == null) {
                            item.HasBestAnswer = 0;
                        }
                        if (item.HasAdoptAnswer == null) {
                            item.HasAdoptAnswer = 0;
                        }
                        if (item.AnswerCount == null) {
                            item.AnswerCount = 0;
                        }
                        if (item.IsEnable == null) {
                            item.IsEnable = 0;
                        }
                        if (item.NoContent == null) {
                            item.NoContent = 0;
                        }
                        if (item.TIMEPOINT == null) {
                            item.TIMEPOINT = 0L;
                        }
                    }

                    logger.info("questionSyncProcess-第二步-读取mysql-轮次{}。数量：{}，头部id列表：{}",
                        round,
                        dataList == null ? 0 : dataList.size(),
                        dataList == null ? null : dataList.stream().map(a -> a.QID).limit(20).collect(Collectors.toList()));

                    Map<String, Object> map = null;
                    List<Map<String, Object>> mapList = null;
                    List<List<FundQuestionInfo>> batchList = CommonUtils.toSmallList2(dataList, 200);
                    for (List<FundQuestionInfo> batch : batchList) {
                        mapList = new ArrayList<>(batch.size());
                        for (FundQuestionInfo a : batch) {
                            map = CommonUtils.beanToMap(a);
                            map.remove("AnswerCount");
                            mapList.add(map);
                        }
                        fundQuestionInfoDao.upsertMany(mapList);
                    }

                    logger.info("questionSyncProcess-第三步-写mongo-轮次{}。数量：{}，头部id列表：{}",
                        round,
                        dataList == null ? 0 : dataList.size(),
                        dataList == null ? null : dataList.stream().map(a -> a.QID).limit(20).collect(Collectors.toList()));

                    breakpoint = DateUtil.dateToStr(breakpointDate);
                    userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

                    logger.info("questionSyncProcess-第四步-更新断点-轮次{}。断点：{}", round, breakpoint);
                }
            }

            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

            logger.info("questionSyncProcess-第四步-更新断点-轮次{}。断点：{}", round, breakpoint);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return false;
        }

        return true;
    }

    /**
     * 回答帖子扩展表同步
     */
    private boolean answerSyncProcess(int batchReadCount) {

        //问题的回答数 fund_guba_service_answercount_{qid}
        String cacheKeyPre = "fund_guba_service_answercount_";

        try {
            String breakpointName = UserRedisConfig.QASYNCBIZJOB_ANSWERSYNCPROCESS_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("answerSyncProcess-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("answerSyncProcess-第一步，读取断点。断点:{}", breakpoint);

            int round = 0;
            boolean isRunning = true;
            List<String> qids = null;
            while (isRunning) {
                round++;

                List<FundAnswerInfoModel> dataList = answerDao.getFundAnswerList(breakpointDate, batchReadCount);

                if (CollectionUtils.isEmpty(dataList) || dataList.size() < batchReadCount) {
                    isRunning = false;
                }

                if (!CollectionUtils.isEmpty(dataList)) {

                    breakpointDate = dataList.stream().max(Comparator.comparing(o -> o.getUpdateTime())).get().getUpdateTime();

                    for (FundAnswerInfoModel item : dataList) {
                        item.set_id(item.QID + "_" + item.AID);
                        if (item.ArticleId == null) {
                            item.ArticleId = 0;
                        }
                        if (item.QuestionArticleId == null) {
                            item.QuestionArticleId = 0;
                        }
                        if (item.AuditStatusType == null) {
                            item.AuditStatusType = 0;
                        }
                        if (item.IsAdopted == null) {
                            item.IsAdopted = 0;
                        }
                        if (item.AdopedType == null) {
                            item.AdopedType = 0;
                        }
                        if (item.AppType == null) {
                            item.AppType = 0;
                        }
                        if (item.IsBestAnswer == null) {
                            item.IsBestAnswer = 0;
                        }
                        if (item.IsEnable == null) {
                            item.IsEnable = 0;
                        }
                        if (item.SendPointResult == null) {
                            item.SendPointResult = 0;
                        }
                        if (item.SendAmount == null) {
                            item.SendAmount = 0;
                        }
                        if (item.Adoped != null && item.Adoped.compareTo(minDate) < 0) {
                            item.Adoped = minDate;
                        }
                    }

                    logger.info("answerSyncProcess-第二步-读取mysql-轮次{}。数量：{}，头部id列表：{}",
                        round,
                        dataList == null ? 0 : dataList.size(),
                        dataList == null ? null : dataList.stream().map(a -> a.AID).limit(20).collect(Collectors.toList()));

                    qids = dataList.stream().map(item -> item.getQID()).distinct().collect(Collectors.toList());

                    Map<String, Object> map = null;
                    List<Map<String, Object>> mapList = null;

                    List<List<FundAnswerInfoModel>> batchList = CommonUtils.toSmallList2(dataList, 200);
                    for (List<FundAnswerInfoModel> batch : batchList) {
                        mapList = new ArrayList<>(batch.size());
                        for (FundAnswerInfoModel a : batch) {
                            map = CommonUtils.beanToMap(a);
                            mapList.add(map);
                        }
                        fundAnswerInfoDao.upsertManyBySetOnInsertWithSetFields(mapList, setFields);
                    }

                    logger.info("answerSyncProcess-第三步-写mongo-轮次{}。数量：{}，头部id列表：{}",
                        round,
                        dataList == null ? 0 : dataList.size(),
                        dataList == null ? null : dataList.stream().map(a -> a.AID).limit(20).collect(Collectors.toList()));

                    Map<String, Integer> answerCountMap = answerDao.getQuestionAnswerCount(qids);
                    Set<Map.Entry<String, Integer>> entrySet = null;
                    if (!CollectionUtils.isEmpty(answerCountMap)) {
                        entrySet = answerCountMap.entrySet();
                        for (Map.Entry<String, Integer> entry : entrySet) {
                            app.barredis.set(cacheKeyPre + entry.getKey(), String.valueOf(entry.getValue()));
                        }
                    }

                    logger.info("answerSyncProcess-第四步-更新redis中提问的回答数-轮次{}。数量：{}，头部id列表：{}",
                        round,
                        entrySet == null ? 0 : entrySet.size(),
                        entrySet == null ? null : entrySet.stream().map(a -> a.getKey()).limit(20).collect(Collectors.toList()));

                    breakpoint = DateUtil.dateToStr(breakpointDate);
                    userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

                    logger.info("answerSyncProcess-第五步-更新断点-轮次{}。断点：{}", round, breakpoint);

                }
            }

            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

            logger.info("answerSyncProcess-第五步-更新断点-轮次{}。断点：{}", round, breakpoint);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return false;
        }

        return true;
    }

}
