package ttfund.web.communityservice.timedtask.jijinbarJobs;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.user.VTagDetailModel;
import ttfund.web.communityservice.bean.jijinBar.user.VTagUserInfoModel;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.mongo.VTagDetailDao;
import ttfund.web.communityservice.dao.mongo.VTagUserInfoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.*;
import java.util.stream.Collectors;

@JobHandler(value = "postAuthorFlagJob")
@Component
public class PostAuthorFlagJob  extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(PostAuthorFlagJob.class);

//    @Autowired
//    PostAuthorFlagDao postAuthorFlagDao;

    @Autowired
    UserRedisDao userRedisDao;


    @Autowired
    PassportUserInfoDao passportUserInfoDao;

    @Autowired
    VTagDetailDao vTagDetailDao;

    @Autowired
    VTagUserInfoDao  vTagUserInfoDao;

    @Override
    public ReturnT<String> execute(String param) {



        Date paramDate=null;
        String paramPid="";
        logger.info("PostAuthorFlagJob 开始执行");
        /**
         * 1.获取时间断点
         * 2.根据时间断点获取指定数量的数据
         * 3.把最大更新时间做为新的断点
         */
        try {
            logger.info("PostAuthorFlagJob 参数信息:"+param);
            //自定义参数设置
            if(param!=null && !param.isEmpty()) {
                try {
                    String[] arr=param.split("\\|");
                    paramDate= DateUtil.strToDate(arr[0]);
                    if(arr.length>1){
                        paramPid=arr[1];
                    }
                }catch (Exception ex){
                    logger.error("PostAuthorFlagJob 参数异常,涉及数据:"+param+",异常信息："+ ex.getMessage(), ex);
                }
            }

            //每页大小
            int limit = 10000;
            //断点名称
            String breakID="";
            String breakTimeName = "postVAuthorFlagDaoLastUpdateTimeNew";
            Date breakTime = null;
            //获取断点时间
            Date lastUpdateTime = null;
            if(paramDate!=null){
                lastUpdateTime=paramDate;
            }else {
                lastUpdateTime=  userRedisDao.getBreakTime(breakTimeName);
            }

            if (lastUpdateTime == null) {
                lastUpdateTime = DateUtil.strToDate("2000-01-01 00:00:01.000");
            }
            logger.info("PostAuthorFlagJob 上次断点："+DateUtil.dateToStr(lastUpdateTime));
            //获取用户标签数据
            List<VTagUserInfoModel> list =null;
            if(paramPid!=null && !paramPid.isEmpty()){
                list=vTagUserInfoDao.getListByPid(paramPid);
            }else {
                list = vTagUserInfoDao.getListByUpdateTime(lastUpdateTime, limit);
            }
            if (!CollectionUtils.isEmpty(list)) {
                /*获取新的断点*/
                //用户通行证ID 数组
                List<String> listNew = list.stream().map(VTagUserInfoModel::getPassportId).distinct().collect(Collectors.toList());
                //时间数组
                List<Date> listDate = list.stream().map(VTagUserInfoModel::getUpdateTime).distinct().collect(Collectors.toList());
                //获取最大时间做为下次断点
                breakTime = Collections.max(listDate);
                for (String uid : listNew) {
                    try{

                        //根据用户通行证ID 获取用户标签列表
                        List<VTagUserInfoModel> templist = vTagUserInfoDao.getListByPid(uid);
                        //List<PostAuthorFlag> templist2= postAuthorFlagDao.getListByPidNew(uid);
                        logger.info("PostAuthorFlagJob 单条更新数据 uid:"+uid+"."+JacksonUtil.obj2String(templist));
                        if (!CollectionUtils.isEmpty(templist)) {

                            //获取用户标签ID列表
                            List<String> listFlagsTIds = templist.stream().map(VTagUserInfoModel::getTID).distinct().collect(Collectors.toList());

                            //根据标签列表获取标签信息
                            List<VTagDetailModel> vTagDetailList = vTagDetailDao.getListByTid(listFlagsTIds);
                            if(!CollectionUtils.isEmpty(vTagDetailList)){
                                List<String> listTName = vTagDetailList.stream().map(VTagDetailModel::getTName).distinct().collect(Collectors.toList());
                                //设置mongdb
                                boolean sucess=  passportUserInfoDao.updateUserTagById(uid, listTName.toArray());
                                if(sucess){
                                    logger.info("PostAuthorFlagJob 单条更新成功 数据 uid:"+uid+"=>"+JacksonUtil.obj2String(listTName.toArray()));
                                }
                                else {
                                    logger.info("PostAuthorFlagJob 单条更新失败 数据 uid:"+uid+"=>"+JacksonUtil.obj2String(listTName.toArray()));
                                }
                            }
                            else {

                                boolean sucess=  passportUserInfoDao.updateUserTagById(uid, null);
                                if(sucess){
                                    logger.info("PostAuthorFlagJob 单条更新成功 数据 uid:"+uid+"=>null");
                                }
                                else {
                                    logger.info("PostAuthorFlagJob 单条更新失败 数据 uid:"+uid+"=>null");
                                }
                            }


                        }
                    }
                    catch (Exception e){
                        logger.error("PostAuthorFlagJob 更新失败,涉及数据:"+uid+",异常信息："+ e.getMessage(), e);
                    }
                }
                //设置断点
                userRedisDao.setBreakTime(breakTimeName,breakTime);
                logger.info("PostAuthorFlagJob 本次断点："+DateUtil.dateToStr(breakTime));
                logger.info("PostAuthorFlagJob 本轮涉及更新数据:"+JacksonUtil.obj2String(listNew));
            }
            logger.info("PostAuthorFlagJob 结束执行");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            logger.error("PostAuthorFlagJob" + e.getMessage(), e);
            return ReturnT.FAIL;
        }

    }
}

