package ttfund.web.communityservice.timedtask.jijinbarJobs.apphome;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.JsonUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.AppHomeHostPostAndUser;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.HostPostModel;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.RecommendHotUserModel;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.UserFollowPostInfo;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.constant.DateConstant;
import ttfund.web.communityservice.dao.mongo.AppHomePostInfoDao;
import ttfund.web.communityservice.dao.mongo.HotPostDao;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * app首页热帖列表
 * 数据源来自榜单前30条数据
 */
@JobHandler("RecommendHotPostJob")
@Component
public class RecommendHotPostJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecommendHotPostJob.class);

    // 注释为前30, 实际取前60, 原因未知
    private static final int DATA_LIMIT = 60;

    @Autowired
    private App app;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private HotPostDao hotPostDao;

    @Autowired
    private AppHomePostInfoDao appHomePostInfoDao;

    @Autowired
    private PassportUserInfoDao passportUserInfoDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            logicDeal();
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
        }
        return ReturnT.SUCCESS;
    }

    private void logicDeal() throws Exception {
        /*
         *1.先获取热门帖子的缓存数据，如果缓存数据不存在则从数据库中获取数据
         *2.再根据帖子数据获取相关用户信息数据
         */
        //从缓存中获取热门帖子
        String valueForRedis = app.barredis.get(BarRedisKey.HOT_POST_WEEK);
        List<HostPostModel> result = null;
        if (StringUtils.isNotEmpty(valueForRedis)) {
            result = JsonUtils.toList(valueForRedis, HostPostModel.class);
        }

        List<Integer> appHomePostType = CommonUtils.toList(appConstant.appHomePostType, ",")
            .stream().map(Integer::parseInt).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(result)) {
            result = hotPostDao.getListTop(DateUtil.calendarDateByDays(-7), DATA_LIMIT, appHomePostType);
        }

        LOGGER.info("RecommendHotPostJob[app首页热帖列表]: 热门帖子数据为:{}", result);
        if (result == null || result.isEmpty()) {
            return;
        }

        result = result.stream().
            filter(post -> appHomePostType.contains(post.TYPE)).
            sorted((o1, o2) -> o2.SCORE.compareTo(o1.SCORE)).
            limit(DATA_LIMIT).collect(Collectors.toList());

        AppHomeHostPostAndUser hotPostUser = new AppHomeHostPostAndUser();
        //由帖子id获取热门帖子信息
        List<String> postIds = result.stream().
            map(a -> String.valueOf(a.ID)).collect(Collectors.toList());
        List<UserFollowPostInfo> postList = appHomePostInfoDao.getListByPost(postIds, 20000, null);

        if (CollectionUtils.isNotEmpty(postList)) {
            for (UserFollowPostInfo post : postList) {
                HostPostModel scorePost = result.stream().filter(a -> a.ID == post.ID).findFirst().orElse(null);
                if (scorePost != null) {
                    post.SCORE = scorePost.SCORE;
                } else {
                    post.SCORE = new BigDecimal(-9999999L);
                }
            }
            //赋值最终获得的帖子信息
            hotPostUser.PostList = postList;

            //获取帖子作者信息
            List<String> userIds = postList.stream().map(a -> a.UID).distinct().collect(Collectors.toList());
            List<PassportUserInfoModelNew> passportUserList = passportUserInfoDao.getPassportUserInfoByIds(userIds);
            if (CollectionUtils.isNotEmpty(passportUserList)) {
                hotPostUser.UserList = JsonUtils.toList(JsonUtils.toJsonString(passportUserList), RecommendHotUserModel.class);
            } else {
                LOGGER.error("RecommendHotPostJob[app首页热帖列表]: 未能获取热门帖子对应作者信息");
            }
        } else {
            LOGGER.error("RecommendHotPostJob[app首页热帖列表]: 未能获取热门帖子详细信息");
        }

        if (CollectionUtils.isNotEmpty(hotPostUser.PostList) && CollectionUtils.isNotEmpty(hotPostUser.UserList)) {
            userRedisDao.set(UserRedisConfig.APP_HOME_HOT_POST_LIST, JsonUtils.toJsonString(hotPostUser), 2 * DateConstant.ONE_WEEK);
        }
    }

}
