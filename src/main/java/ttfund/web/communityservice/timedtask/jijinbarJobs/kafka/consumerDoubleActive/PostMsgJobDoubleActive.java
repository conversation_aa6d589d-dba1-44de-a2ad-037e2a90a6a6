package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumerDoubleActive;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer.PostMsgJob;

/**
 * 社区源数据同步服务-帖子
 */
@Slf4j
@Component
public class PostMsgJobDoubleActive {

    public static final String KAFKA_LISTENER_ID = "PostMsgJobDoubleActive";

    @Autowired
    private PostMsgJob postMsgJob;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.GubaPostInfo4FundQueue}
        , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_POSTMSGJOB + "}"
        , containerFactory = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_pj)
    private void onListen(ConsumerRecord<String, String> record) {
        postMsgJob.postMsg(record, log);
    }

}
