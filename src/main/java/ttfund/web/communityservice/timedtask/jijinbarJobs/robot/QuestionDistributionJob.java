package ttfund.web.communityservice.timedtask.jijinbarJobs.robot;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.mongo.FundJBXXModel;
import ttfund.web.communityservice.bean.jijinBar.post.QA.UserconfigModel;
import ttfund.web.communityservice.bean.jijinBar.post.QA.ZnhdQaModel;
import ttfund.web.communityservice.bean.jijinBar.post.caifuhao.CaiFuHaoInfo;
import ttfund.web.communityservice.bean.jijinBar.post.guba.FundBarModel;
import ttfund.web.communityservice.bean.messagepush.FundQuestionInfoSpecial;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.CaiFuHaoDao;
import ttfund.web.communityservice.dao.mongo.MarketMongoFundArchivesDao;
import ttfund.web.communityservice.dao.msyql.FundBarDao;
import ttfund.web.communityservice.dao.msyql.QuestionDao;
import ttfund.web.communityservice.dao.msyql.ZnhdQaDao;
import ttfund.web.communityservice.dao.msyql.ZnhdUserConfigDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.enums.EQuestionOwnerType;
import ttfund.web.communityservice.enums.EnumUserGroup;
import ttfund.web.communityservice.enums.EnumZnhdState;
import ttfund.web.communityservice.enums.EnumZnhdUserType;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 智能互动问答匹配job
 */
@Slf4j
@JobHandler("QuestionDistributionJob")
@Component
public class QuestionDistributionJob extends IJobHandler {

    private static String REG_A_TAG = "<a(.*?)>(.*?)</a>";

    private static Pattern PATTERN = Pattern.compile(REG_A_TAG);

    private static List<String> FIELDS_FUND = Arrays.asList("FCODE", "SHORTNAME", "JJGSID", "JJGS");


    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private QuestionDao questionDao;

    @Autowired
    private ZnhdUserConfigDao znhdUserConfigDao;

    @Autowired
    private CaiFuHaoDao caiFuHaoDao;

    @Autowired
    private MarketMongoFundArchivesDao marketMongoFundArchivesDao;

    @Autowired
    private FundBarDao fundBarDao;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private ZnhdQaDao znhdQaDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            Double threshold = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                threshold = jsonObject.getDouble("threshold");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            if (threshold == null) {
                threshold = 0.6;
            }

            log.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，threshold：{}",
                initBreakpoint,
                batchReadCount,
                threshold
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.QUESTIONDISTRIBUTIONJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            logicDeal(batchReadCount, threshold);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void logicDeal(int batchReadCount, double threshold) throws Exception {
        try {
            String breakpointName = UserRedisConfig.POSTCOUNTHISJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                log.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            log.info("第一步，读取断点。断点:{}", breakpoint);

            //增量获取问答信息列表
            List<FundQuestionInfoSpecial> list = questionDao.getListOfNotEnd(breakpointDate, batchReadCount);

            log.info("第二步，读取数据。数量:{}，头部列表：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

            if (!CollectionUtils.isEmpty(list)) {

                breakpointDate = list.get(list.size() - 1).UpdateTime;

                //剔除无效数据
                list = filterList(list);

                log.info("第三步，过滤数据。数量:{}，头部列表：{}",
                    CollectionUtils.isEmpty(list) ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
                );

                //初始化问答类型
                initOwnerType(list);

                //数据转换
                List<ZnhdQaModel> dataList = initModel(list, threshold);

                log.info("第四步，匹配。数量:{}，头部列表：{}",
                    CollectionUtils.isEmpty(dataList) ? 0 : dataList.size(),
                    CollectionUtils.isEmpty(dataList) ? null : JSON.toJSONStringWithDateFormat(dataList.get(0), DateUtil.datePattern)
                );


                if (!CollectionUtils.isEmpty(dataList)) {
                    for (ZnhdQaModel a : dataList) {
                        if (a.Score == null) {
                            a.Score = new BigDecimal(0);
                        }
                    }

                    //保存数据
                    List<List<ZnhdQaModel>> batchList = CommonUtils.toSmallList2(dataList, 100);
                    for (List<ZnhdQaModel> batch : batchList) {
                        znhdQaDao.insertMany(batch);
                    }
                }

                log.info("第五步，写库。数量:{}，头部列表：{}",
                    CollectionUtils.isEmpty(dataList) ? 0 : dataList.size(),
                    CollectionUtils.isEmpty(dataList) ? null : JSON.toJSONStringWithDateFormat(dataList.get(0), DateUtil.datePattern)
                );

            }


            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

            log.info("第六步，更新断点。断点：{}", breakpoint);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }


    /**
     * 初始化数据
     */
    private List<ZnhdQaModel> initModel(List<FundQuestionInfoSpecial> list, double threshold) {
        List<ZnhdQaModel> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        List<String> codeList = list.stream().map(a -> a.FCode).collect(Collectors.toList());
        //基金吧信息

        List<FundBarModel> barList = fundBarDao.getListByBarCodes(codeList);
        if (barList == null) {
            barList = new ArrayList<>();
        }

        Map<String, FundBarModel> barInfoMap = new HashMap<>();
        barList.forEach(a -> barInfoMap.put(a.BarCode, a));

        //基金的基本信息
        List<FundJBXXModel> fundList = marketMongoFundArchivesDao.getFundJBXX(FundJBXXModel.class, codeList, FIELDS_FUND);
        Map<String, FundJBXXModel> fundMap = new HashMap<>();
        fundList.forEach(a -> fundMap.put(a.FCODE, a));


        //配置用户列表，随机获取用户
        List<UserconfigModel> userList = znhdUserConfigDao.getAll(EnumUserGroup.HD.getValue());

        for (FundQuestionInfoSpecial item : list) {
            try {
                String fundBarName = "";
                FundBarModel barObj = barInfoMap.get(item.FCode);
                if (barObj != null) {
                    fundBarName = barObj.BarName;
                } else {
                    FundJBXXModel fundInfo = fundMap.get(item.FCode);
                    if (fundInfo != null) {
                        fundBarName = fundInfo.SHORTNAME + "吧";
                    }
                }

                String userId = getRandomUserId(userList);

                ZnhdQaModel model = new ZnhdQaModel();
                model.ID = item.QID;
                model.QID = item.QID;
                model.Question = item.CONTENT;
                model.QPassportId = item.UserId;
                model.Code = item.FCode;
                model.GubaCode = item.StockBarCode;
                model.BarName = fundBarName;
                model.DistributionPassportId = userId;//分配人用户ID
                model.UserType = (item.OwnerType == EQuestionOwnerType.BAR_AND_CFH.getValue() ?
                    EnumZnhdUserType.CAI_FU_HAO_YONG_HU.getValue() : EnumZnhdUserType.JI_JIN_BA_HOU_TAI.getValue());//用户类型
                model.Del = 0;//删除状态
                model.AskTime = item.TIME;
                model.AnPassportId = userId;
                model.PostTitle = item.PostTitle;
                model.PostImg = item.PostImg;
                model.PostPic = item.PostPic;
                model.PostId = item.PostId;

                distributionUser(model, item, userId, threshold);
                result.add(model);

                //如果是财富号用户，则把提问数据分发到基金吧管理后台
                if (model.UserType == EnumZnhdUserType.CAI_FU_HAO_YONG_HU.getValue()) {

                    ZnhdQaModel newModel = JSON.parseObject(JSON.toJSONString(model), ZnhdQaModel.class);
                    newModel.ID = model.ID + "_" + EnumZnhdUserType.CAI_FU_HAO_HE_JI_JIN_BA.getValue();
                    newModel.UserType = EnumZnhdUserType.CAI_FU_HAO_HE_JI_JIN_BA.getValue();
                    newModel.DistributionPassportId = userId;
                    newModel.DataType = 1;

                    if (newModel.State == EnumZnhdState.JI_GOU_DAI_SHEN_HE.getValue()) {
                        newModel.State = EnumZnhdState.JI_JIN_DAI_SHEN_HE.getValue();
                    } else if (newModel.State == EnumZnhdState.JI_GOU_DAI_CHU_LI.getValue()) {
                        newModel.State = EnumZnhdState.JI_JIN_DAI_CHU_LI.getValue();
                    }
                    result.add(newModel);
                }
            } catch (Exception ex) {
                log.info(ex.getMessage(), ex);
            }
        }

        return result;
    }

    /**
     * 分配该问题所属平台
     */
    private void distributionUser(ZnhdQaModel model, FundQuestionInfoSpecial item, String randomUserId, double threshold) {
        if (StringUtils.hasLength(item.CONTENT)) {

            Map<String, Object> answerResult = getAnswerByApi(item.CONTENT, threshold);
            //包含超链接的回答过滤掉
            if (answerResult != null && StringUtils.hasLength((String)answerResult.get("answer"))
                && !PATTERN.matcher((String)answerResult.get("answer")).find()
            ) {
                //匹配成功

                model.Answer = (String)answerResult.get("answer");
                model.Score = new BigDecimal(answerResult.get("similarity").toString());
                model.TopicId = (String)answerResult.get("topicId");
                //判断该问题所在吧是否有财富号
                if (model.UserType == EnumZnhdUserType.CAI_FU_HAO_YONG_HU.getValue()) {
                    model.State = EnumZnhdState.JI_GOU_DAI_SHEN_HE.getValue();
                    model.DistributionPassportId = item.OrgOwnerPassportId;
                } else {
                    model.State = EnumZnhdState.JI_JIN_DAI_SHEN_HE.getValue();
                    model.DistributionPassportId = randomUserId;
                }

            } else {
                if (model.UserType == EnumZnhdUserType.CAI_FU_HAO_YONG_HU.getValue()) {
                    model.State = EnumZnhdState.JI_GOU_DAI_CHU_LI.getValue();
                    model.DistributionPassportId = item.OrgOwnerPassportId;
                } else {
                    model.State = EnumZnhdState.JI_JIN_DAI_CHU_LI.getValue();
                    model.DistributionPassportId = randomUserId;
                }
            }
        }
    }

    /**
     * 根据问题内容 获取回答信息
     */
    private Map<String, Object> getAnswerByApi(String question, double threshold) {
        Map<String, Object> result = null;

        if (StringUtils.hasLength(question)) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("question", question);
            paramMap.put("threshold", threshold);
            paramMap.put("size", 1);
            String url = appConstant.znkfApiAddress + "/bd-dialog-fund/fundBar/qAsk";
            String html = HttpHelper.requestPostJson(url, JSON.toJSONString(paramMap), true);
            if (StringUtils.hasLength(html)) {
                JSONObject jsonObject = JSON.parseObject(html);
                if (jsonObject != null && !CollectionUtils.isEmpty(jsonObject.getJSONArray("data"))) {
                    result = new HashMap<>();
                    JSONObject data = (JSONObject)jsonObject.getJSONArray("data").get(0);
                    result.put("topicId", data.get("topicId") == null ? null : data.get("topicId").toString());
                    result.put("answer", data.get("answer") == null ? null : data.get("answer").toString());
                    result.put("similarity", data.get("similarity") == null ? null : Double.valueOf(data.get("similarity").toString()));
                }
            }
        }
        return result;
    }


    /**
     * 随机获取一个用户
     */
    private String getRandomUserId(List<UserconfigModel> list) {
        String result = "";
        if (!CollectionUtils.isEmpty(list)) {
            Random random = new Random();
            int i = random.nextInt(list.size());
            return list.get(i).PassportId;
        }
        return result;
    }

    /**
     * 设置基金吧类型
     */
    private void initOwnerType(List<FundQuestionInfoSpecial> list) throws Exception {
        if (!CollectionUtils.isEmpty(list)) {
            //所在基金吧
            List<String> arrCodes = list.stream().map(item -> item.FCode).distinct().collect(Collectors.toList());

            //基金的基本信息
            List<FundJBXXModel> fundList = marketMongoFundArchivesDao.getFundJBXX(FundJBXXModel.class, arrCodes, FIELDS_FUND);

            //所有财富号信息
            List<CaiFuHaoInfo> cfhUserList = caiFuHaoDao.GetList();

            if (fundList == null) {
                fundList = new ArrayList<>();
            }
            if (cfhUserList == null) {
                cfhUserList = new ArrayList<>();
            }

            Map<String, FundJBXXModel> fundMap = new HashMap<>();
            Map<String, CaiFuHaoInfo> cfhMap = new HashMap<>();
            fundList.forEach(a -> fundMap.put(a.FCODE, a));
            cfhUserList.forEach(a -> cfhMap.put(a.CommpanyCode, a));

            for (FundQuestionInfoSpecial item : list) {
                FundJBXXModel fundObj = fundMap.get(item.FCode);
                CaiFuHaoInfo cfhObj = null;
                if (fundObj != null) {
                    cfhObj = cfhMap.get(fundObj.JJGSID);
                }

                if (cfhObj != null && StringUtils.hasLength(cfhObj.RelatedUid)) {
                    item.OrgOwnerPassportId = cfhObj.RelatedUid;
                }
                item.OwnerType = getType(cfhObj, fundObj).getValue();
            }

        }
    }

    /**
     * 获取类型
     */
    private EQuestionOwnerType getType(CaiFuHaoInfo cfhUser, FundJBXXModel fundInfo) {
        //这里做强校验 基金公司ID 要一致,且要有财富号用户关联的通行证ID
        if (cfhUser != null && fundInfo != null
            && Objects.equals(cfhUser.CommpanyCode, fundInfo.JJGSID)
            && StringUtils.hasLength(cfhUser.RelatedUid)
        ) {
            return EQuestionOwnerType.BAR_AND_CFH;
        } else if (fundInfo != null) {
            return EQuestionOwnerType.BAR_AND_NO_CFH;
        } else if (cfhUser != null && StringUtils.hasLength(cfhUser.RelatedUid))//要有财富号用户关联的通行证ID
        {
            return EQuestionOwnerType.NO_BAR_AND_CFH;
        }
        return EQuestionOwnerType.NO_BAR_AND_NO_CFH;
    }

    /**
     * 过滤数据
     */
    private List<FundQuestionInfoSpecial> filterList(List<FundQuestionInfoSpecial> list) throws Exception {

        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        /**
         * 1.剔除内部账户，机构账户提问
         */
        Set<String> listPassport = new HashSet<>();
        //内部账户
        List<UserconfigModel> selfUser = znhdUserConfigDao.getAll(EnumUserGroup.INNER_ACC.getValue());
        if (!CollectionUtils.isEmpty(selfUser)) {
            List<String> tempList = selfUser.stream().filter(item -> StringUtils.hasLength(item.PassportId)).map(item -> item.PassportId.trim())
                .collect(Collectors.toList());
            listPassport.addAll(tempList);
        }
        //财富号用户
        List<CaiFuHaoInfo> cfhUserList = caiFuHaoDao.GetList();
        if (!CollectionUtils.isEmpty(cfhUserList)) {
            List<String> tempList = cfhUserList.stream().filter(item -> StringUtils.hasLength(item.RelatedUid)).map(item -> item.RelatedUid.trim()).collect(Collectors.toList());
            listPassport.addAll(tempList);
        }

        //剔除内部账户和财富号用户
        if (!CollectionUtils.isEmpty(listPassport)) {
            list = list.stream().filter(item -> !listPassport.contains(item.UserId)).collect(Collectors.toList());
        }

        return list;
    }

}
