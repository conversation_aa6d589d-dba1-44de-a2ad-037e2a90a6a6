package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.data.SetTips;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.SetTipsDao;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.List;

/**
 * 限时活动同步至Redis
 */
@JobHandler(value = "SetTipsRedisBusinessJob")
@Component
public class SetTipsRedisBusinessJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(SetTipsRedisBusinessJob.class);

    @Autowired
    private SetTipsDao setTipsDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String param) throws JsonProcessingException {

        try {

            syncSetTipsRedis();

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void syncSetTipsRedis() throws Exception {
        List<SetTips> list = setTipsDao.getTipsList();

        logger.info("读库。数量：{}，头部数据：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JacksonUtil.obj2String(list.get(0))
        );

        if (!CollectionUtils.isEmpty(list)) {
            app.barredis.set(BarRedisKey.FUND_GUBA_SERVICE_SETTIPS, JacksonUtil.obj2String(list.get(0)), 600L);
        }
    }

}
