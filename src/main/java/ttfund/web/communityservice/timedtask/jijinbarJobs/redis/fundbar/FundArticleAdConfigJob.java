package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.config.FundArticleAdConfig;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.FundArticleAdConfigDao;
import ttfund.web.communityservice.dao.mongo.HideActivityConfigDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 帖子正文banner广告位
 * 需求：#719406 【基金吧6.17.5】正文页增加广告位
 */
@Slf4j
@JobHandler(value = "FundArticleAdConfigJob")
@Component
public class FundArticleAdConfigJob extends IJobHandler {

    @Autowired
    private App app;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private FundArticleAdConfigDao fundArticleAdConfigDao;

    @Autowired
    private HideActivityConfigDao hideActivityConfigDao;

    @Override
    public ReturnT<String> execute(String s) throws JsonProcessingException {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            Long expireForAd = null;
            Long expireForHide = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                expireForAd = jsonObject.getLong("expireForAd");
                expireForHide = jsonObject.getLong("expireForHide");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (expireForAd == null) {
                expireForAd = 30 * 24 * 3600L;
            }
            if (expireForHide == null) {
                expireForHide = 30 * 24 * 3600L;
            }

            log.info("0.打印参数。initBreakpoint：{}，batchReadCount：{}，expireForAd：{}，expireForHide：{}",
                initBreakpoint,
                batchReadCount,
                expireForAd,
                expireForHide
            );

            if (StringUtils.hasLength(initBreakpoint)) {
                userRedisDao.set(UserRedisConfig.FUNDARTICLEADCONFIGJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("0.初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            dealFundArticleAdConfig(expireForAd, batchReadCount);

            dealHideActivityConfig(expireForHide);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void dealFundArticleAdConfig(long expire, int batchReadCount) {
        String breakpointName = UserRedisConfig.FUNDARTICLEADCONFIGJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);
        if (!StringUtils.hasLength(breakpoint)) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-30));
        }

        log.info("1.读取断点-全部广告。断点：{}", breakpoint);

        Date breakpointDate = DateUtil.strToDate(breakpoint);
        List<FundArticleAdConfig> list = fundArticleAdConfigDao.getList(FundArticleAdConfig.class, null, breakpointDate, batchReadCount);

        log.info("2.读取数据库-全部广告。数量：{}，头部列表：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {
            breakpointDate = list.get(list.size() - 1).UpdateTime;
        }

        List<FundArticleAdConfig> newCacheList = null;
        List<FundArticleAdConfig> cacheList = null;
        Map<String, FundArticleAdConfig> map = new HashMap<>();


        String key = BarRedisKey.FUND_GUBA_SERVICE_ACTIVITYCONFIG_UNIQUE_ALL;
        String value = app.barredis.get(key);
        cacheList = JSON.parseArray(value, FundArticleAdConfig.class);

        log.info("3.记录缓存数据-全部广告。数量：{}，头部列表：{}",
            CollectionUtils.isEmpty(cacheList) ? 0 : cacheList.size(),
            CollectionUtils.isEmpty(cacheList) ? null : JSON.toJSONStringWithDateFormat(cacheList.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(cacheList)) {
            for (FundArticleAdConfig item : cacheList) {
                map.put(item._id, item);
            }
        }

        if (!CollectionUtils.isEmpty(list)) {
            for (FundArticleAdConfig item : list) {
                map.put(item._id, item);
            }
        }

        Date now = new Date();
        newCacheList = map.values().stream()
            .filter(a -> a.State == 1
                && now.compareTo(a.EndTime) < 0)
            .sorted(((o1, o2) -> o2.UpdateTime.compareTo(o1.UpdateTime)))
            .collect(Collectors.toList());


        log.info("4.记录新缓存数据-全部广告。数量：{}，头部列表：{}",
            CollectionUtils.isEmpty(newCacheList) ? 0 : newCacheList.size(),
            CollectionUtils.isEmpty(newCacheList) ? null : JSON.toJSONStringWithDateFormat(newCacheList.get(0), DateUtil.datePattern)
        );

        if (newCacheList == null) {
            newCacheList = new ArrayList<>();
        }
        app.barredis.set(key, JSON.toJSONString(newCacheList), expire);

        log.info("5.更新缓存-全部广告");

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

        log.info("6.更新断点-全部广告，断点：{}", breakpoint);
    }

    /**
     * 帖子正文页/话题banner广告位屏蔽配置  缓存设置
     */
    private void dealHideActivityConfig(Long expireForHide) {
        try {

            List<Document> list = new ArrayList<>();
            List<Document> documents = null;

            documents = hideActivityConfigDao.getHideActivityConfig(Arrays.asList("_id", "code", "type"), Document.class);
            if (!CollectionUtils.isEmpty(documents)) {
                list.addAll(documents);
            }

            log.info("1.读取屏蔽配置。数量：{}，数据：{}",
                CollectionUtils.isEmpty(documents) ? 0 : documents.size(),
                CollectionUtils.isEmpty(documents) ? null : JSON.toJSONStringWithDateFormat(documents.get(0), DateUtil.datePattern)
            );

            app.barredis.set(BarRedisKey.HIDEACTIVITYCONFIG_ALL, JSON.toJSONString(list), expireForHide);

            log.info("2.写缓存。数量：{}，数据：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }


}
