package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostCountModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.PostPingLunNumDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.redis.RedisUtils;

import java.util.*;

/**
 * 帖子评论数缓存设置
 */
@JobHandler("PostPingLunNumJob")
@Component
public class PostPingLunNumJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(PostPingLunNumJob.class);

    private final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostPingLunNumDao postPingLunNumDao;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        logicDeal();
        return ReturnT.SUCCESS;
    }

    public void logicDeal() {
        try {
            String breakpointName = UserRedisConfig.POST_PING_LUN_NUM_JOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);
            if (!StringUtils.hasLength(breakpoint)) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByDays(-15), DATE_FORMAT);
            }
            Date dateForBreakpoint = DateUtil.strToDate(breakpoint, DATE_FORMAT);
            logger.info("1.读取断点。breakpoint：{}", breakpoint);

            List<PostCountModel> list = postPingLunNumDao.getList(dateForBreakpoint, appConstant.batchCount);
            logger.info("2.读取数据。数量：{}", CollectionUtils.isEmpty(list) ? 0 : list.size());

            if (!CollectionUtils.isEmpty(list)) {
                for (PostCountModel item : list) {
                    String cacheKey = String.format(BarRedisKey.FUND_POST_COUNT_INFO, String.valueOf(item.ID));
                    Map<String, String> hash = new HashMap<>();
                    hash.put("ReplyNum", String.valueOf(item.ReplyNum));
                    RedisUtils.hset(app.barredis, cacheKey, hash);
                    app.barredis.expire(cacheKey, 6 * 30 * 24 * 3600L);
                }

                dateForBreakpoint = list.stream().max(Comparator.comparing(o -> o.UpdateTime)).get().UpdateTime;
            }
            logger.info("3.设置缓存。数量：{}", CollectionUtils.isEmpty(list) ? 0 : list.size());

            breakpoint = DateUtil.dateToStr(dateForBreakpoint, DATE_FORMAT);
            userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);
            logger.info("4.更新断点。breakpoint：{}", breakpoint);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }

}
