package ttfund.web.communityservice.timedtask.jijinbarJobs.user;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.CacheHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.mongo.FundUserProfitPostModel;
import ttfund.web.communityservice.bean.jijinBar.mongo.PassportFundMrgModel;
import ttfund.web.communityservice.bean.jijinBar.post.ProfitEntity;
import ttfund.web.communityservice.bean.jijinBar.user.FundUserProfit;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.FundUserProfitDao;
import ttfund.web.communityservice.dao.mongo.PassportFundMrgDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.msyql.ProfitDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户交易持仓数据同步kafka-Mongdb
 */
@JobHandler("FundUserProfitKafkaMongJob")
@Component
public class FundUserProfitKafkaMongJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(FundUserProfitKafkaMongJob.class);

    private static final List<String> FIELDS = Arrays.asList("ID", "FINDSCORE", "TIME", "TIMEPOINT", "TYPE", "YUANID", "UID", "CODE");

    private static final String CACHE_KEY_FUND_MRG = "cache_key_fund_mrg";

    private static final String POSTS_CACHE_KEY = "FundUserProfitKafkaMongJob_posts_%s";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private ProfitDao profitDao;

    @Autowired
    private PassportFundMrgDao passportFundMrgDao;

    @Autowired
    private FundUserProfitDao fundUserProfitDao;

    @Autowired
    private PostDao postDao;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            Integer batchUidCount = null;
            Integer batchWriteCount = null;
            Long expire = null;
            String uids = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                batchUidCount = jsonObject.getInteger("batchUidCount");
                batchWriteCount = jsonObject.getInteger("batchWriteCount");
                expire = jsonObject.getLong("expire");
                uids = jsonObject.getString("uids");
            }

            if (batchReadCount == null) {
                batchReadCount = 10000;
            }
            if (batchUidCount == null) {
                batchUidCount = 50;
            }
            if (batchWriteCount == null) {
                batchWriteCount = 100;
            }
            if (expire == null) {
                expire = 8 * 24 * 3600 * 1000L;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，batchUidCount：{}，batchWriteCount：{}，expire：{}，uids：{}",
                    initBreakpoint,
                    batchReadCount,
                    batchUidCount,
                    batchWriteCount,
                    expire,
                    uids
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.FUNDUSERPROFITKAFKAMONGJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            if (StringUtils.hasLength(uids)) {
                dealByUids(batchUidCount, batchWriteCount, expire, CommonUtils.toList(uids, ","));
            } else {
                deal(batchReadCount, batchUidCount, batchWriteCount, expire);
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void deal(Integer batchReadCount, Integer batchUidCount, Integer batchWriteCount, Long expire) throws Exception {

        String breakpointName = UserRedisConfig.FUNDUSERPROFITKAFKAMONGJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("增量-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("增量-第一步，读取断点。断点:{}", breakpoint);


        List<ProfitEntity> list = profitDao.getListByUpdateTime(breakpointDate, batchReadCount);

        logger.info("增量-第二步，读取用户持仓数据。数量:{}，头部列表：{}",
                list == null ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = list.get(list.size() - 1).UpdateTime;

            Map<String, List<PassportFundMrgModel>> fundMrgPids = getFundMrgFromCache();

            logger.info("增量-第三步，读取基金经理。数量:{}，头部列表：{}",
                    list == null ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

            List<String> totalUids = list.stream().map(a -> a.PId).distinct().collect(Collectors.toList());
            initUserPosts(totalUids, batchUidCount, expire, false);

            logger.info("增量-第四步，初始化获取用户帖子。数量:{}，头部列表：{}",
                    totalUids == null ? 0 : totalUids.size(),
                    CollectionUtils.isEmpty(totalUids) ? null : totalUids.stream().limit(20).collect(Collectors.toList())
            );

            int i = 0;
            int postCount = 0;
            for (ProfitEntity a : list) {
                i++;

                postCount = dealData(a, fundMrgPids, batchWriteCount);

                logger.info("增量-第五步，处理数据详情。第{}/{}个，pid：{}，fcode：{}，更新帖子数量:{}，数据：{}",
                        i,
                        list.size(),
                        a.PId,
                        a.FCode,
                        postCount,
                        JSON.toJSONStringWithDateFormat(a, DateUtil.datePattern)
                );
            }

            logger.info("增量-第五步，处理数据完成。数据：{}",
                    CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );
        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        logger.info("增量-第六步，更新断点。断点：{}", breakpoint);

    }

    private void dealByUids(Integer batchUidCount, Integer batchWriteCount, Long expire, List<String> uids) throws Exception {

        logger.info("指定uid方式-第一步，打印参数。batchUidCount：{}，batchWriteCount：{}，expire：{}，uids：{}",
                batchUidCount,
                batchWriteCount,
                expire,
                uids
        );

        if (CollectionUtils.isEmpty(uids)) {
            return;
        }

        List<ProfitEntity> list = profitDao.getListByUids(uids);

        logger.info("指定uid方式-第二步，读取用户持仓数据。数量:{}，头部列表：{}",
                list == null ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {
            Map<String, List<PassportFundMrgModel>> fundMrgPids = getFundMrgFromCache();

            logger.info("指定uid方式-第三步，读取基金经理。数量:{}，头部列表：{}",
                    list == null ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

            List<String> totalUids = list.stream().map(a -> a.PId).distinct().collect(Collectors.toList());
            initUserPosts(totalUids, batchUidCount, expire, true);

            logger.info("增量-第四步，初始化获取用户帖子。数量:{}，头部列表：{}",
                    totalUids == null ? 0 : totalUids.size(),
                    CollectionUtils.isEmpty(totalUids) ? null : totalUids.stream().limit(20).collect(Collectors.toList())
            );


            int i = 0;
            int postCount = 0;
            for (ProfitEntity a : list) {
                i++;

                postCount = dealData(a, fundMrgPids, batchWriteCount);

                logger.info("指定uid方式-第五步，处理数据详情。第{}/{}个，pid：{}，fcode：{}，更新帖子数量:{}，数据：{}",
                        i,
                        list.size(),
                        a.PId,
                        a.FCode,
                        postCount,
                        JSON.toJSONStringWithDateFormat(a, DateUtil.datePattern)
                );
            }

            logger.info("指定uid方式-第五步，处理数据完成。数据：{}",
                    CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );
        }
    }

    private int dealData(ProfitEntity data, Map<String, List<PassportFundMrgModel>> fundMrgPids, Integer batchWriteCount) throws Exception {
        List<FundUserProfitPostModel> postlist = getUserPostsByCache(data.PId, data.FCode);

        if (CollectionUtils.isEmpty(postlist)) {
            return 0;
        }

        //持有人贴剔除及基金经理贴
        if (fundMrgPids.containsKey(data.PId)) {
            return 0;
        }


        List<FundUserProfit> listProfits = new ArrayList<>();
        for (FundUserProfitPostModel postInfo : postlist) {
            FundUserProfit mongdbProfit = new FundUserProfit();

            mongdbProfit.EUTIME = new Date();
            mongdbProfit.FCODE = data.FCode;
            mongdbProfit.FINDSCORE = postInfo.FINDSCORE;
            mongdbProfit.HOLDMONTH = data.HoldMonth;
            mongdbProfit.PID = data.PId;
            mongdbProfit.POSTID = postInfo.ID;
            mongdbProfit.PROFITSTATE = data.ProfitState;
            mongdbProfit.POSTTIME = postInfo.TIME;
            mongdbProfit.TIMEPOINT = postInfo.TIMEPOINT;
            mongdbProfit.TYPE = postInfo.TYPE;
            mongdbProfit.YUANID = postInfo.YUANID;
            mongdbProfit.ID = 0;
            mongdbProfit._id = mongdbProfit.FCODE + "_" + mongdbProfit.PID + "_" + mongdbProfit.POSTID;

            listProfits.add(mongdbProfit);
        }

        List<List<FundUserProfit>> batchs = CommonUtils.toSmallList2(listProfits, batchWriteCount);
        for (List<FundUserProfit> batch : batchs) {
            List<Map<String, Object>> mapList = new ArrayList<>(batch.size());
            for (FundUserProfit o : batch) {
                mapList.add(CommonUtils.beanToMap(o));
            }
            fundUserProfitDao.upsertManyBySetWithSetOnInsertFields(mapList, null, "_id");
        }

        return listProfits.size();
    }

    public void initUserPosts(List<String> uids, Integer batchUidCount, Long expire, boolean force) {
        if (CollectionUtils.isEmpty(uids)) {
            return;
        }

        List<String> notInUids = new ArrayList<>();
        String cacheKey = null;
        LinkedMultiValueMap<String, FundUserProfitPostModel> userPostMap = null;
        for (String uid : uids) {
            if (!force) {
                cacheKey = String.format(POSTS_CACHE_KEY, uid);
                userPostMap = CacheHelper.get(cacheKey);

                if (userPostMap != null) {
                    continue;
                }
            }

            notInUids.add(uid);
        }

        if (CollectionUtils.isEmpty(notInUids)) {
            return;
        }

        List<FundUserProfitPostModel> posts = null;
        List<List<String>> batchList = CommonUtils.toSmallList2(notInUids, batchUidCount);
        for (List<String> batch : batchList) {
            posts = postDao.getPostsByUids(FIELDS,
                    FundUserProfitPostModel.class,
                    batch
            );

            LinkedMultiValueMap<String, FundUserProfitPostModel> userPostListMap = new LinkedMultiValueMap<>();
            if (!CollectionUtils.isEmpty(posts)) {
                for (FundUserProfitPostModel a : posts) {
                    userPostListMap.add(a.UID, a);
                }
            }

            for (Map.Entry<String, List<FundUserProfitPostModel>> entry : userPostListMap.entrySet()) {

                userPostMap = new LinkedMultiValueMap<>();
                cacheKey = String.format(POSTS_CACHE_KEY, entry.getKey());
                posts = entry.getValue();
                if (!CollectionUtils.isEmpty(posts)) {
                    for (FundUserProfitPostModel a : posts) {
                        userPostMap.add(a.CODE, a);
                    }
                }

                CacheHelper.put(cacheKey, userPostMap, expire);
            }
        }
    }

    private Map<String, List<PassportFundMrgModel>> getFundMrg() {

        Map<String, List<PassportFundMrgModel>> result = new HashMap<>();

        //基金经理绑定关系
        try {
            List<PassportFundMrgModel> fundMrgList = passportFundMrgDao.getAll();
            if (!CollectionUtils.isEmpty(fundMrgList)) {
                result = fundMrgList.stream().filter(item -> StringUtils.hasLength(item.PassportUID) && item.IsDel == 0)
                        .collect(Collectors.groupingBy(item -> item.PassportUID));
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return result;
    }

    private Map<String, List<PassportFundMrgModel>> getFundMrgFromCache() {
        Map<String, List<PassportFundMrgModel>> result = null;
        String cacheKey = CACHE_KEY_FUND_MRG;
        result = CacheHelper.get(cacheKey);
        if (result == null) {
            result = getFundMrg();
            if (result == null) {
                result = new HashMap<>();
            }
            CacheHelper.put(cacheKey, result, 30 * 60 * 1000L);
        }
        return result;
    }

    public List<FundUserProfitPostModel> getUserPostsByCache(String uid, String code) {
        List<FundUserProfitPostModel> result = null;

        String cacheKey = String.format(POSTS_CACHE_KEY, uid);
        LinkedMultiValueMap<String, FundUserProfitPostModel> userPostMap = CacheHelper.get(cacheKey);
        if (userPostMap != null) {
            result = userPostMap.get(code);
        }
        return result;
    }

}
