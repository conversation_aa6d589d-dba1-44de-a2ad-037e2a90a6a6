package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostinfoExtraModel;
import ttfund.web.communityservice.bean.jijinBar.post.guba.PostclickNum;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.PostClickNumDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.utils.CommonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PostClickMsgJob {

    public static final String KAFKA_LISTENER_ID = "PostClickMsgJob";

    @Autowired
    private PostClickNumDao postClickNumDao;

    @Autowired
    private PostInfoExtraDao postInfoExtraDao;

    /*@KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.GubaClickNum4FundQueue}
        , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_POSTCLICKMSGJOB + "}"
        , containerFactory = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_zp_ForPostClickMsgJob)*/
    private void onListen(ConsumerRecord<String, String> record) {
        postClickMsg(log, record);
    }

    public void postClickMsg(Logger logger, ConsumerRecord<String, String> record) {
        try {

            logger.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            List<PostclickNum> clicks = new ArrayList<>();

            String value = record.value();
            String[] clickInfo = value.split(",");
            for (String info : clickInfo) {
                PostclickNum num = new PostclickNum();
                String[] click = info.split("\\|");
                num.ID = Long.parseLong(click[0]);
                num.ClickNum = Long.parseLong(click[1]);
                clicks.add(num);
            }

            if (!CollectionUtils.isEmpty(clicks)) {
                List<List<PostclickNum>> batchList = CommonUtils.toSmallList2(clicks, 1000);
                for (List<PostclickNum> batch : batchList) {
                    postClickNumDao.insertOrUpdateBulk(batch);
                }
            }

            //更新帖子拓展表-点击数
            if (!CollectionUtils.isEmpty(clicks)) {
                List<PostinfoExtraModel> models = clicks.stream().map(a -> {
                    PostinfoExtraModel clickModel = new PostinfoExtraModel();
                    clickModel.CLICKNUM = a.ClickNum;
                    clickModel.ID = a.ID;

                    return clickModel;
                }).collect(Collectors.toList());

                List<List<PostinfoExtraModel>> batchList = CommonUtils.toSmallList2(models, 1000);
                for (List<PostinfoExtraModel> batch : batchList) {
                    try {
                        postInfoExtraDao.updateClicknum(batch);
                    } catch (Exception e) {
                        logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                            record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));
                        logger.error(e.getMessage(), e);
                    }
                }

            }

        } catch (Exception ex) {
            logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));
            logger.error(ex.getMessage(), ex);
        }
    }
}
