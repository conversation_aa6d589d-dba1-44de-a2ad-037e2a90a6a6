package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostinfoExtraModel;
import ttfund.web.communityservice.bean.jijinBar.post.guba.PostclickNum;
import ttfund.web.communityservice.config.kafka.GubaKafkaPostConfigNewZP;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.PostClickNumDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.utils.CommonUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * 帖子点击数同步job-双活-周浦 新
 * 备注：将KafkaListener改成原始Consumer原因，是为了控制启动实例数量为一个，本job内有批量写库操作，如果是多实例运行，有可能多个实例直接因为批量写库互相持有并抢占资源发生死锁
 */
@Slf4j
@JobHandler("PostClickMsgJobNew")
@Component
public class PostClickMsgJobNew extends IJobHandler {

    private volatile Thread thread;

    private volatile long timestamp = 0L;

    private volatile Integer heartBeatInterval = 0;

    private volatile Integer batchCount = 1000;

    @Autowired
    private GubaKafkaPostConfigNewZP gubaKafkaPostConfigNewZP;

    @Autowired
    private PostClickNumDao postClickNumDao;

    @Autowired
    private PostInfoExtraDao postInfoExtraDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            //kafka一次拉取数量
            Integer maxPollRecords = null;
            //kafka一次拉取等待时间
            Integer pollDuration = null;
            //本服务运行心跳时间  单位毫秒
            Integer heartBeatInterval = null;
            //一批积攒数量
            Integer batchCount = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                maxPollRecords = jsonObject.getInteger("maxPollRecords");
                pollDuration = jsonObject.getInteger("pollDuration");
                heartBeatInterval = jsonObject.getInteger("heartBeatInterval");
                batchCount = jsonObject.getInteger("batchCount");
            }

            if (maxPollRecords == null) {
                maxPollRecords = 500;
            }
            if (pollDuration == null) {
                pollDuration = 1000;
            }
            if (heartBeatInterval == null) {
                heartBeatInterval = 0;
            }
            if (batchCount == null) {
                batchCount = 1000;
            }

            this.heartBeatInterval = heartBeatInterval;
            this.batchCount = batchCount;
            this.timestamp = System.currentTimeMillis();

            log.info("第零步，打印参数。maxPollRecords：{}，pollDuration：{}，heartBeatInterval：{}，batchCount：{}，timestamp：{}",
                maxPollRecords,
                pollDuration,
                heartBeatInterval,
                batchCount,
                timestamp
            );

            deal(maxPollRecords, pollDuration);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int maxPollRecords, int pollDuration) {
        if (thread == null) {
            thread = new Thread(() -> dealTask(maxPollRecords, pollDuration));
            thread.start();

            log.info("0.检测到线程为null，新建线程并运行。");
        }
    }


    private void dealTask(int maxPollRecords, int pollDuration) {

        KafkaConsumer<String, String> consumer = null;
        try {
            log.info("1.启动。");

            Properties properties = gubaKafkaPostConfigNewZP.consumerConfigsForPostClickMsgJobNew();
            properties.put("max.poll.records", maxPollRecords);
            consumer = new KafkaConsumer<>(properties);
            consumer.subscribe(Arrays.asList(KafkaTopicName.GubaClickNum4FundQueue));

            log.info("2.订阅kafka，消费组配置：\n{}", JSON.toJSONString(properties));

            ConsumerRecords<String, String> consumerRecords = null;
            ConsumerRecord<String, String> lastRecord = null;
            while (true) {
                if (heartBeatInterval > 0 && (System.currentTimeMillis() - timestamp > heartBeatInterval)) {

                    log.info("3.业务运行心跳间隔超出限制，停止运行。当前时间：{}，timestamp：{}", System.currentTimeMillis(), timestamp);
                    break;
                }

                consumerRecords = consumer.poll(Duration.ofMillis(pollDuration));

                log.info("3.消费kafka。数量：{}", consumerRecords.count());

                if (consumerRecords.count() > 0) {
                    for (ConsumerRecord<String, String> record : consumerRecords) {
                        lastRecord = record;

                        postClickMsg(log, record, batchCount);
                    }

                    log.info("5.处理消息。数量：{}，partition：{}，key：{}，offset：{}，timestamp：{}，value：\n{}",
                        consumerRecords.count(),
                        lastRecord.partition(),
                        lastRecord.key(),
                        lastRecord.offset(),
                        lastRecord.timestamp(),
                        StringUtils.hasLength(lastRecord.value()) && lastRecord.value().length() > 100 ? lastRecord.value().substring(0, 100) : lastRecord.value()
                    );

                    consumer.commitSync();
                }
            }
            log.info("6.正常结束。");
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            log.error("6.异常结束。");
        } finally {
            if (consumer != null) {
                consumer.close();
            }

            thread = null;
        }
    }

    public void postClickMsg(Logger logger, ConsumerRecord<String, String> record, int batchCount) {
        try {

            logger.info("4.打印。partition：{}，offset：{}，timestamp：{}，key：{}，数据：{}",
                record.partition(),
                record.offset(),
                record.timestamp(),
                record.key(),
                StringUtils.hasLength(record.value()) && record.value().length() > 100 ? record.value().substring(0, 100) : record.value()
            );

            List<PostclickNum> clicks = new ArrayList<>();

            String value = record.value();
            String[] clickInfo = value.split(",");
            for (String info : clickInfo) {
                PostclickNum num = new PostclickNum();
                String[] click = info.split("\\|");
                num.ID = Long.parseLong(click[0]);
                num.ClickNum = Long.parseLong(click[1]);
                clicks.add(num);
            }

            if (!CollectionUtils.isEmpty(clicks)) {
                List<List<PostclickNum>> batchList = CommonUtils.toSmallList2(clicks, batchCount);
                for (List<PostclickNum> batch : batchList) {
                    postClickNumDao.insertOrUpdateBulk(batch);
                }
            }

            //更新帖子拓展表-点击数
            if (!CollectionUtils.isEmpty(clicks)) {
                List<PostinfoExtraModel> models = clicks.stream().map(a -> {
                    PostinfoExtraModel clickModel = new PostinfoExtraModel();
                    clickModel.CLICKNUM = a.ClickNum;
                    clickModel.ID = a.ID;

                    return clickModel;
                }).collect(Collectors.toList());

                List<List<PostinfoExtraModel>> batchList = CommonUtils.toSmallList2(models, batchCount);
                for (List<PostinfoExtraModel> batch : batchList) {
                    try {
                        postInfoExtraDao.updateClicknum(batch);
                    } catch (Exception e) {
                        logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                            record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));
                        logger.error(e.getMessage(), e);
                    }
                }

            }

        } catch (Exception ex) {
            logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));
            logger.error(ex.getMessage(), ex);
        }
    }

}
