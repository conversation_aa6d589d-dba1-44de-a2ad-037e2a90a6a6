package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.mongo.PassportFundMrgModel;
import ttfund.web.communityservice.bean.jijinBar.post.fundManager.FundManagerVideoPost;
import ttfund.web.communityservice.dao.mongo.FundManagerVideoPostDao;
import ttfund.web.communityservice.dao.mongo.PassportFundMrgDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基金经理视频贴同步job
 */
@JobHandler("FundManagerVideoPostSyncJob")
@Component
public class FundManagerVideoPostSyncJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(FundManagerVideoPostSyncJob.class);

    private static List<String> FIELDS = Arrays.asList("_id", "TYPE", "UID", "TIME", "TIMEPOINT", "DEL", "TTJJDEL");

    @Autowired
    private PassportFundMrgDao passportFundMrgDao;

    @Autowired
    private PostDao postDao;

    @Autowired
    private FundManagerVideoPostDao fundManagerVideoPostDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            //指定基金经理通行证id集合
            String managerUids = null;
            //每批基金经理数量
            Integer managerBatchCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                managerUids = jsonObject.getString("managerUids");
                managerBatchCount = jsonObject.getInteger("managerBatchCount");
            }

            if (managerBatchCount == null) {
                managerBatchCount = 50;
            }

            logger.info("第零步，打印参数。managerUids：{}，managerBatchCount：{}", managerUids, managerBatchCount);

            syncPost(CommonUtils.toList(managerUids, ","), managerBatchCount);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void syncPost(List<String> managerUids, int managerBatchCount) throws Exception {

        List<String> uids = new ArrayList<>();
        List<PassportFundMrgModel> managerList = passportFundMrgDao.getAll();
        if (!CollectionUtils.isEmpty(managerList)) {
            uids = managerList.stream().filter(a -> StringUtils.hasLength(a.MGRID) && StringUtils.hasLength(a.PassportUID) && a.IsDel == 0)
                    .map(a -> a.PassportUID)
                    .collect(Collectors.toList());
        }

        if (!CollectionUtils.isEmpty(managerUids)) {
            uids = uids.stream().filter(a -> managerUids.contains(a)).collect(Collectors.toList());
        }

        logger.info("第一步，获取基金经理通行证id集合。数量：{}，头部id列表：{}", uids == null ? 0 : uids.size(), uids);
        
        
        

        List<Document> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(uids)) {
            List<List<String>> batchList = CommonUtils.toSmallList2(uids, managerBatchCount);
            for (List<String> batch : batchList) {
                List<Document> tempList = postDao.getVideoPostByUidsAndTime(batch, DateUtil.calendarDateByYears(-1), FIELDS);
                if (!CollectionUtils.isEmpty(tempList)) {
                    list.addAll(tempList);
                }
            }
        }

        logger.info("第二步，获取基金经理视频贴集合。数量：{}", list == null ? 0 : list.size());

        if (!CollectionUtils.isEmpty(list)) {
            List<FundManagerVideoPost> upsertList = new ArrayList<>(list.size());
            for (Document item : list) {
                FundManagerVideoPost model = JSON.parseObject(JSON.toJSONString(item), FundManagerVideoPost.class);
                upsertList.add(model);
            }

            if (!CollectionUtils.isEmpty(upsertList)) {
                List<Map<String, Object>> mapList = new ArrayList<>(upsertList.size());
                for (FundManagerVideoPost item : upsertList) {
                    mapList.add(CommonUtils.beanToMap(item));
                }
                Map<String, List<Map<String, Object>>> groupList = mapList.stream().collect(Collectors.groupingBy(a -> (String) a.get("UID")));
                int i = 0;
                for (Map.Entry<String, List<Map<String, Object>>> entry : groupList.entrySet()) {
                    List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(entry.getValue(), 200);
                    for (List<Map<String, Object>> batch : batchList) {
                        fundManagerVideoPostDao.upsertMany(batch);
                    }

                    logger.info("第三步，数据写库详情-第{}/{}个基金经理。通行证id:{},视频贴数量：{}",
                            ++i,
                            groupList.size(),
                            entry.getKey(),
                            entry.getValue().size());
                }
            }

            logger.info("第四步，数据写库完成。数量：{}", list == null ? 0 : list.size());
        }

    }

}
