package ttfund.web.communityservice.timedtask.jijinbarJobs.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.barrage.PassportUserInfoModel;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserExtendInfo;
import ttfund.web.communityservice.bean.jijinBar.user.UserRankMModel;
import ttfund.web.communityservice.dao.mongo.FundCompanyMonthRankDao;
import ttfund.web.communityservice.dao.mongo.PassportUserExtendInfoDao;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.mongo.PopularityRankMDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.msyql.UserRelationDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Component
@JobHandler(value = "VFansScoreJob")
public class VFansScoreJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(VFansScoreJob.class);

    private static final String TIME_FORMAT = "HH:mm:ss";

    @Autowired
    private PassportUserInfoDao passportUserInfoDao;

    @Autowired
    private UserRelationDao userRelationDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private PopularityRankMDao popularityRankMDao;

    @Autowired
    private FundCompanyMonthRankDao fundCompanyMonthRankDao;

    @Autowired
    private PassportUserExtendInfoDao passportUserExtendInfoDao;

    @Override
    public ReturnT<String> execute(String s) {

        JSONArray intervalList = null;
        Integer count = null;
        if (StringUtils.hasLength(s)) {
            JSONObject jsonObject = JSON.parseObject(s);
            intervalList = jsonObject.getJSONArray("intervalList");
            count = jsonObject.getInteger("count");
        }

        if (count == null) {
            count = 100000;
        }

        List<List<LocalTime>> intervalTimeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(intervalList)) {
            JSONObject temp = null;
            String timeStr = null;
            LocalTime localTime = null;
            List<LocalTime> localTimeList = null;
            for (Object o : intervalList) {
                localTimeList = new ArrayList<>(2);

                temp = (JSONObject) o;
                timeStr = temp.getString("start");
                if (StringUtils.hasLength(timeStr)) {
                    localTime = LocalTime.parse(timeStr, DateTimeFormatter.ofPattern(TIME_FORMAT));
                } else {
                    localTime = null;
                }
                localTimeList.add(localTime);

                timeStr = temp.getString("end");
                if (StringUtils.hasLength(timeStr)) {
                    localTime = LocalTime.parse(timeStr, DateTimeFormatter.ofPattern(TIME_FORMAT));
                } else {
                    localTime = null;
                }
                localTimeList.add(localTime);

                intervalTimeList.add(localTimeList);
            }
        }

        logger.info("第零步，打印参数。intervalList：{}，count：{}", intervalList, count);

        vFansScoreProcess(intervalTimeList, count);

        return ReturnT.SUCCESS;
    }

    private void vFansScoreProcess(List<List<LocalTime>> intervalTimeList, int count) {
        PassportUserInfoModel nowItem = null;

        try {

            LocalTime now = LocalTime.now();
            boolean run = true;
            if (!CollectionUtils.isEmpty(intervalTimeList)) {
                LocalTime start = null;
                LocalTime end = null;
                for (List<LocalTime> timeArr : intervalTimeList) {

                    run = true;

                    start = timeArr.get(0);
                    end = timeArr.get(1);
                    if (start != null && now.compareTo(start) < 0) {
                        run = false;
                    }
                    if (end != null && now.compareTo(end) > 0) {
                        run = false;
                    }

                    if (run == true) {
                        break;
                    }
                }
            }

            logger.info("第一步，判断是否运行。run：{}", run);

            if (!run) {
                return;
            }

            List<PassportUserInfoModel> userList = passportUserInfoDao.getVPassportUserInfo(count);

            logger.info("第二步-读取数据。数量：{}，头部id列表：{}",
                    userList == null ? 0 : userList.size(),
                    userList == null ? null : userList.stream().map(a -> a.PassportID).limit(20).collect(Collectors.toList()));

            int wday = Calendar.getInstance().get(Calendar.DAY_OF_MONTH);
            Date start = DateUtil.calendarDateByDays(1 - wday);
            if (!CollectionUtils.isEmpty(userList)) {

                List<PassportUserExtendInfo> upsertList = new ArrayList<>(userList.size());

                int i = 0;
                for (PassportUserInfoModel user : userList) {
                    nowItem = user;

                    i++;

                    if (StringUtils.hasLength(user.PassportID)) {
                        // 用户粉丝数
                        double fansNum = userRelationDao.getUserFansCountByUserId(user.PassportID);

                        // 用户发帖数
                        int postNum = postInfoNewDao.getPostCountByUid(user.PassportID, DateUtil.calendarDateByMonth(-1));
                        int lastMonthLikeCount = 0;
                        int lastMPingLunCount = 0;
                        //处理批次以日期为时间
                        int pid = Integer.parseInt(new SimpleDateFormat("yyyyMMdd").format(start));
                        //获取排行中用户V的数据
                        UserRankMModel rankModel = popularityRankMDao.getFirst(user.PassportID, pid);
                        if (rankModel == null) {
                            //基金公司V排行
                            rankModel = fundCompanyMonthRankDao.getFirstFromCompany(user.PassportID, pid);
                        }
                        if (rankModel != null) {
                            ////近一个月用户获赞数
                            lastMonthLikeCount = (int) rankModel.LIKECOUNT;

                            ////近一个月用户评论数
                            lastMPingLunCount = (int) rankModel.PINGLUNNUM;
                        }

                        double fansScore = getFansScore(fansNum, postNum);

                        PassportUserExtendInfo updateItem = new PassportUserExtendInfo();
                        updateItem._id = user.PassportID;
                        updateItem.PassportID = user.PassportID;
                        updateItem.Vtype = user.accreditationType;
                        updateItem.FansScore = fansScore;
                        updateItem.FansScoreUpdateTime = new Date();
                        updateItem.UpdateTime = new Date();
                        updateItem.PostCount = postNum;
                        updateItem.LastMGetLikeCount = lastMonthLikeCount;
                        updateItem.LastMPingLunCount = lastMPingLunCount;
                        updateItem.Del = getStatus(user);

                        upsertList.add(updateItem);

                        logger.info("第三步-计算数据-第{}/{}个。id:{}", i, userList.size(), updateItem._id);
                    }
                }

                if (!CollectionUtils.isEmpty(upsertList)) {
                    Map<String, Object> map = null;
                    List<Map<String, Object>> mapList = null;
                    List<List<PassportUserExtendInfo>> batchList = CommonUtils.toSmallList2(upsertList, 200);
                    for (List<PassportUserExtendInfo> batch : batchList) {
                        mapList = new ArrayList<>(batch.size());
                        for (PassportUserExtendInfo a : batch) {
                            map = CommonUtils.beanToMap(a);
                            mapList.add(map);
                        }
                        passportUserExtendInfoDao.upsertMany(mapList);
                    }
                }

                logger.info("第四步-数据写库。数量:{}", upsertList == null ? 0 : upsertList.size());

                Date updateTime = DateUtil.calendarDateByDays(-5);
                passportUserExtendInfoDao.setDelByUpdateTime(updateTime);

                logger.info("第五步-逻辑删除历史数据。时间界限:{}", DateUtil.dateToStr(updateTime));
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            logger.error("捕获异常。正在处理元素：" + nowItem, ex);
        }
    }

    /**
     * 计算分数，根据粉丝数和发帖数
     */
    public static double getFansScore(double fansNum, int postNum) {
        double fansScore;

        if (postNum == 0 || fansNum == 0)
            return 0;

        if (postNum <= 10) {
            fansScore = fansNum / 10;
        } else if (postNum <= 30) {
            fansScore = fansNum / 2;
        } else {
            fansScore = fansNum;
        }

        return fansScore;
    }

    public static int getStatus(PassportUserInfoModel vuserInfo) {
        if ("0".equals(vuserInfo.CFHVSstatus)
                && "0".equals(vuserInfo.abandon) && StringUtils.hasLength(vuserInfo.accreditationType)) {
            return 0;
        } else {
            return 1;
        }
    }


}
