package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.mongo.SubAccountDtoModelPortfolioDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;

/**
 * 子账户动态 模拟组合
 * 备注：需求 #596771 【组合3.9】模拟组合重构
 */
@Component
public class SubAccountBusinessModelPortfolioJob {
    private static Logger logger = LoggerFactory.getLogger(SubAccountBusinessModelPortfolioJob.class);

    public static final String KAFKA_LISTENER_ID = "SubAccountBusinessModelPortfolioJob";

    @Autowired
    private SubAccountDtoModelPortfolioDao subAccountDtoModelPortfolioDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.MODELSUBACCOUNTACTION}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_SUBACCOUNTBUSINESSMODELPORTFOLIOJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_fundproduct)
    private void onListen(ConsumerRecord<String, String> record) {
        handleSubAccountActionMongo((record));
    }


    /**
     * 处理子账户动态
     */
    public void handleSubAccountActionMongo(ConsumerRecord<String, String> record) {

        try {

            logger.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            JSONObject bd = JSON.parseObject(record.value());
            if (bd.get("AppType").toString().toUpperCase().contains("TRADE")) {
                if (bd.containsKey("TradeContent")) {
                    bd.put("TradeContent", bd.get("TradeContent").toString());
                }
            }

            if (bd.get("AppType").toString().toUpperCase().contains("ACC")) {
                if (bd.containsKey("AccMessage")) {
                    bd.put("AccMessage", bd.get("AccMessage").toString());
                }
            }

            Date date = DateUtil.strToDate(bd.get("AppTime").toString().replace("Z", ""), "yyyy-MM-dd HH:mm:ss");
            long timePoint = DateUtil.getTimePoint(date);
            bd.put("TIMEPOINT", timePoint);
            bd.put("_id", bd.get("AppSheetserialNo").toString());
            bd.put("ISTRADEPOST", 0);
            //增加更新时间
            bd.put("UpdateTime", DateUtil.dateToStr(new Date(), "yyyy-MM-dd HH:mm:ss"));

            bd.put("_id", bd.get("AppSheetserialNo").toString());

            subAccountDtoModelPortfolioDao.getTemplate().save(bd, BarMongodbConfig.TABLE_SubAccountDtoModelPortfolio);

        } catch (Exception ex) {
            logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            logger.error(ex.getMessage(), ex);
        }

    }
}
