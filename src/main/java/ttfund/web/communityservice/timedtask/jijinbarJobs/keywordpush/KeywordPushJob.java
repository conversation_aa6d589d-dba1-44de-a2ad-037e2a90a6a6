package ttfund.web.communityservice.timedtask.jijinbarJobs.keywordpush;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.keywordpush.CmsPostModel;
import ttfund.web.communityservice.config.appconfig.keywordpushconfig.CmsKeywordConstant;
import ttfund.web.communityservice.service.keywordpush.KeywordPushService;

import java.util.List;

/**
 * @Author: ShaMo
 * @Date: 2022/11/1
 * @ApiNote:
 */
@Component
@JobHandler("keywordPushJob")
public class KeywordPushJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(KeywordPushJob.class);
    private static final int HELPER_POST_TIMOUT = 10000;
    @Autowired
    private KeywordPushService cmsPushService;

    @Autowired
    private CmsKeywordConstant cmsPostConstant;

    private static final String KEYWORD_PUSH_JOB_TITLE = "[Keyword关键字推送服务]";


    /**
     * 推送关键字信息给kafka
     * @param cmsPostModel
     * @return Http调用结果的jsonObj
     */
    private JSONObject getSendJsonObjRet(CmsPostModel cmsPostModel) {
        JSONObject ret = null;
        String postRetStr = HttpHelper.requestPostJson(cmsPostConstant.cms_post_url, JSON.toJSONString(cmsPostModel), HELPER_POST_TIMOUT);
        if (StringUtils.hasLength(postRetStr)) {
            ret = JSON.parseObject(postRetStr);
        }
        return ret;
    }

    /**
     * 判断调用是否成功
     * @param msg
     * @return
     */
    private boolean sendMsgSuccess(JSONObject msg) {
        return msg != null && msg.getString("code").equals("200");
    }

    @Override
    public ReturnT<String> execute(String s) {
        StringBuilder errLogger = new StringBuilder();
        try {
            List<CmsPostModel> cmsPostModelList = cmsPushService.commonPushList();
            int count = 0;
            boolean failOnce = false;
            errLogger.append(KEYWORD_PUSH_JOB_TITLE).append("本次拆分推送数据长度: ").append(cmsPostModelList.size()).append("\n");
            if (!cmsPostModelList.isEmpty()) { // 结果范围不可能为null
                for (CmsPostModel cmsPostModel : cmsPostModelList) {
                    ++count;
                    JSONObject msg = getSendJsonObjRet(cmsPostModel);
                    if (!sendMsgSuccess(msg)) {
                        // 失败的时候count不能自增
                        errLogger.append(KEYWORD_PUSH_JOB_TITLE).
                                append(String.format("批次%d全量/增量更新 - 推送关键字任务失败！", count)).
                                append("\n").append(msg).append("\n");
                        failOnce = true;
                    }
                }
                if (!failOnce) {
                    logger.info("全部批次 全量/增量更新 - 推送关键字任务成功！");
                    return ReturnT.SUCCESS;
                }
            } else {
                // 拿不到批量的结果在增量更新的时候会出现，不能视为一种错误
                logger.info("未能获取到批量拆分的结果");
                return ReturnT.SUCCESS;
            }
        }catch (Exception e) {
            XxlJobLogger.log(e);
            logger.error(e.getMessage(), e);
        }
        logger.error("{}全量/增量更新 - 推送关键字任务失败！", KEYWORD_PUSH_JOB_TITLE);
        logger.error("{}", errLogger);
        return ReturnT.FAIL;
    }
}
