package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.data.PostTopicRelation;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.FundTopicPostRelationDao;
import ttfund.web.communityservice.dao.msyql.PostTopicRetDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 话题和帖子关联关系job
 */
@JobHandler("FundTopicPostRelationBizJob")
@Component
public class FundTopicPostRelationBizJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(FundTopicPostRelationBizJob.class);

    @Autowired
    private PostTopicRetDao postTopicRetDao;

    @Autowired
    private FundTopicPostRelationDao fundTopicPostRelationDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.FUNDTOPICPOSTRELATIONBIZJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            syncToMongdb(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 数据同步到mongdb
     */
    private void syncToMongdb(int batchReadCount) throws Exception {


        String breakpointName = UserRedisConfig.FUNDTOPICPOSTRELATIONBIZJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("第一步，读取断点。断点:{}", breakpoint);

        int round = 0;
        while (true) {
            round++;

            List<PostTopicRelation> dataList = postTopicRetDao.getList(breakpointDate, batchReadCount);

            logger.info("第二步，读取数据-第{}轮。数量:{}，头部id列表：{}",
                    round,
                    dataList == null ? 0 : dataList.size(),
                    dataList == null ? null : dataList.stream().map(a -> a.id).limit(20).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(dataList)) {

                breakpointDate = dataList.stream().max((Comparator.comparing(PostTopicRelation::getUpdateTime))).get().UpdateTime;

                List<Map<String, Object>> mapList = new ArrayList<>(dataList.size());
                Map<String, Object> map = null;
                for (PostTopicRelation a : dataList) {
                    map = CommonUtils.beanToMap(a);
                    map.remove("id");
                    map.put("_id", a.id);
                    mapList.add(map);
                }

                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 200);
                for (List<Map<String, Object>> batch : batchList) {
                    fundTopicPostRelationDao.upsertMany(batch);
                }

            }

            logger.info("第三步，数据写库-第{}轮。数量:{}，头部id列表：{}",
                    round,
                    dataList == null ? 0 : dataList.size(),
                    dataList == null ? null : dataList.stream().map(a -> a.id).limit(20).collect(Collectors.toList()));

            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

            logger.info("第四步，更新断点-第{}轮。断点：{}", round, breakpoint);

            if (dataList == null || dataList.size() < batchReadCount) {
                break;
            }
        }

    }
}
