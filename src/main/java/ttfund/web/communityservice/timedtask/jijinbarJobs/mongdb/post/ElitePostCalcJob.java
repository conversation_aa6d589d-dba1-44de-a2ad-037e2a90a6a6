package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.post.fundManager.FundManagerPostModel;
import ttfund.web.communityservice.bean.jijinBar.post.fundManager.FundManagerPostModelTemp;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.FundManagerPostDao;
import ttfund.web.communityservice.dao.mongo.PassportFundMrgDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 同步精华帖子(基金经理单品吧帖子)
 */
@JobHandler("ElitePostCalcJob")
@Component
public class ElitePostCalcJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(ElitePostCalcJob.class);

    private static final List<String> SET_ON_INSERT_FIELDS = Arrays.asList("ID", "UID", "CODE", "TIME", "TIMEPOINT",
        "YUANID", "TYPE", "YUANTYPE", "CODELIST");

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PassportFundMrgDao passportFundMrgDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private FundManagerPostDao fundManagerPostDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            String initBreakpoint1 = null;
            String initBreakpoint2 = null;
            Integer batchReadCount = null;
            Integer keepDays = null;
            Integer retryCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint1 = jsonObject.getString("initBreakpoint1");
                initBreakpoint2 = jsonObject.getString("initBreakpoint2");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                keepDays = jsonObject.getInteger("keepDays");
                retryCount = jsonObject.getInteger("retryCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (keepDays == null) {
                keepDays = -90;
            }
            if (retryCount == null) {
                retryCount = 3;
            }

            logger.info("第零步，打印参数。initBreakpoint1：{}，initBreakpoint2：{}，batchReadCount：{}",
                initBreakpoint1,
                initBreakpoint2,
                batchReadCount);

            if (StringUtils.hasLength(initBreakpoint1) || StringUtils.hasLength(initBreakpoint2)) {
                DateUtil.strToDate(initBreakpoint1);
                userRedisDao.set(UserRedisConfig.ELITEPOSTCALCJOB_SYNCFUNDMANAGERPOST_BREAKPOINT, initBreakpoint1, 30 * 24 * 3600L);

                DateUtil.strToDate(initBreakpoint2);
                userRedisDao.set(UserRedisConfig.ELITEPOSTCALCJOB_SYNCFUNDMANAGERPOSTFROMRELATION_BREAKPOINT, initBreakpoint2, 30 * 24 * 3600L);

                logger.info("第零步，初始化断点。initBreakpoint1：{}，initBreakpoint2：{}", initBreakpoint1, initBreakpoint2);
                return ReturnT.SUCCESS;
            }


            syncFundManagerPost(batchReadCount, keepDays, retryCount);

            syncFundManagerPostFromRelation(batchReadCount, retryCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 基金经理单品吧帖子
     */
    private void syncFundManagerPost(int batchReadCount, int keepDays, int retryCount) {
        try {
            String breakpointName = UserRedisConfig.ELITEPOSTCALCJOB_SYNCFUNDMANAGERPOST_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);
            if (!StringUtils.hasLength(breakpoint)) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-60));

                logger.error("syncFundManagerPost-第一步，获取断点为空，使用默认断点。断点：{}", breakpoint);
            }

            logger.info("syncFundManagerPost-第一步，打印断点。断点：{}", breakpoint);

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            //加载一次基金经理列表
            List<String> uids = passportFundMrgDao.getFundManagerIds();
            logger.info("syncFundManagerPost-第二步，加载一次基金经理列表。数量：{}，头部id列表：{}",
                uids == null ? 0 : uids.size(),
                uids == null ? null : uids.stream().limit(50).collect(Collectors.toList())
            );

            boolean isRun = true;
            int round = 0;
            while (isRun) {
                round++;

                List<FundManagerPostModel> upsertList = new ArrayList<>();

                Date time = DateUtil.calendarDateByDays(keepDays);
                //获取近三个月的帖子
                List<PostInfoNewModel> postList = postInfoNewDao.getPostListByTime(time, batchReadCount, breakpointDate, uids);

                logger.info("syncFundManagerPost-第三步，获取基金经理帖子-第{}轮。数量：{}，头部id列表：{}",
                    round,
                    postList == null ? 0 : postList.size(),
                    postList == null ? null : postList.stream().map(a -> a.ID).limit(50).collect(Collectors.toList())
                );

                if (!CollectionUtils.isEmpty(postList)) {
                    breakpointDate = postList.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;

                    upsertList = new ArrayList<>(postList.size());
                    for (PostInfoNewModel item : postList) {
                        {
                            FundManagerPostModel model = new FundManagerPostModel();

                            model._id = String.valueOf(item.ID);
                            model.ID = item.ID;
                            model.UID = item.UID;
                            model.CODE = item.CODE;
                            model.CODELIST = item.CODELIST;
                            model.CODELISTUSE = dealCodeForCodeList(CommonUtils.toList(item.CODELIST, ","));
                            model.TIME = item.TIME;
                            model.TIMEPOINT = item.TIMEPOINT;
                            model.YUANID = item.YUANID;
                            model.TYPE = item.TYPE;
                            model.YUANTYPE = item.YUANTYPE == null ? null : item.YUANTYPE.intValue();
                            model.DEL = item.DEL;
                            model.TTJJDEL = item.TTJJDEL;

                            upsertList.add(model);
                        }
                    }

                    logger.info("syncFundManagerPost-第四步，帖子处理-第{}轮。数量：{}，头部id列表：{}，更新数量：{}，头部更新列表：{}",
                        round,
                        postList == null ? 0 : postList.size(),
                        postList == null ? null : postList.stream().map(a -> a.ID).limit(50).collect(Collectors.toList()),
                        upsertList == null ? 0 : upsertList.size(),
                        upsertList == null ? null : upsertList.stream().map(a -> a.ID).limit(50).collect(Collectors.toList())
                    );

                    if (!CollectionUtils.isEmpty(upsertList)) {
                        for (FundManagerPostModel item : upsertList) {
                            findAndModify(item, retryCount, null);
                        }
                    }

                    logger.info("syncFundManagerPost-第五步，更新数据写库-第{}轮。数量：{}，头部id列表：{}",
                        round,
                        upsertList == null ? 0 : upsertList.size(),
                        upsertList == null ? null : upsertList.stream().map(a -> a.ID).limit(50).collect(Collectors.toList())
                    );

                }

                if (CollectionUtils.isEmpty(postList) || postList.size() < batchReadCount) {
                    isRun = false;
                }

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

                logger.info("syncFundManagerPost-第六步，更新断点-第{}轮。断点：{}", round, breakpoint);
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 基金经理单品吧帖子
     */
    private void syncFundManagerPostFromRelation(int batchReadCount, int retryCount) {
        try {
            String breakpointName = UserRedisConfig.ELITEPOSTCALCJOB_SYNCFUNDMANAGERPOSTFROMRELATION_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);
            if (!StringUtils.hasLength(breakpoint)) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-60));

                logger.error("syncFundManagerPostFromRelation-第一步，获取断点为空，使用默认断点。断点：{}", breakpoint);
            }

            logger.info("syncFundManagerPostFromRelation-第一步，打印断点。断点：{}", breakpoint);

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            //加载一次基金经理列表
            List<String> uids = passportFundMrgDao.getFundManagerIds();
            logger.info("syncFundManagerPostFromRelation-第二步，加载一次基金经理列表。数量：{}，头部id列表：{}",
                uids == null ? 0 : uids.size(),
                uids == null ? null : uids.stream().limit(50).collect(Collectors.toList())
            );

            boolean isRun = true;
            int round = 0;
            while (isRun) {
                round++;

                List<FundManagerPostModelTemp> postList = postInfoNewDao.getPostListFromRelation(batchReadCount, breakpointDate, uids);

                logger.info("syncFundManagerPostFromRelation-第三步，获取基金经理帖子-第{}轮。数量：{}，头部id列表：{}",
                    round,
                    postList == null ? 0 : postList.size(),
                    postList == null ? null : postList.stream().map(a -> a.ID).limit(50).collect(Collectors.toList())
                );

                if (!CollectionUtils.isEmpty(postList)) {
                    breakpointDate = postList.get(postList.size() - 1).UPDATETIME;

                    Map<Integer, FundManagerPostModel> map = new LinkedHashMap<>();
                    FundManagerPostModel model = null;
                    String tempCode = null;
                    for (FundManagerPostModelTemp item : postList) {
                        {
                            if (!map.containsKey(item.ID)) {

                                tempCode = null;
                                if (item.FType != null) {
                                    tempCode = dealCode(item.FType, item.FCode);
                                }
                                if (!StringUtils.hasLength(tempCode)) {
                                    continue;
                                }

                                model = new FundManagerPostModel();
                                model._id = String.valueOf(item.ID);
                                model.ID = item.ID;
                                model.UID = item.UID;
                                model.CODE = item.CODE;
                                model.CODELIST = null;
                                List<String> tempList = new ArrayList<>();
                                tempList.add(tempCode);
                                model.CODELISTUSE = tempList;
                                model.TIME = item.TIME;
                                model.TIMEPOINT = item.TIMEPOINT;
                                model.YUANID = item.YUANID;
                                model.TYPE = item.TYPE;
                                model.YUANTYPE = item.YUANTYPE == null ? null : item.YUANTYPE.intValue();
                                model.DEL = item.DEL;
                                model.TTJJDEL = item.TTJJDEL;

                                map.put(item.ID, model);
                            } else {

                                tempCode = null;
                                if (item.FType != null) {
                                    tempCode = dealCode(item.FType, item.FCode);
                                }
                                if (!StringUtils.hasLength(tempCode)) {
                                    continue;
                                }

                                model = map.get(item.ID);
                                model.CODELISTUSE = union(model.CODELISTUSE, Arrays.asList(tempCode));
                            }

                        }
                    }

                    logger.info("syncFundManagerPostFromRelation-第四步，帖子处理-第{}轮。数量：{}，头部id列表：{}",
                        round,
                        map == null ? 0 : map.size(),
                        map == null ? null : map.keySet().stream().limit(50).collect(Collectors.toList())
                    );

                    if (!CollectionUtils.isEmpty(map)) {
                        for (FundManagerPostModel item : map.values()) {
                            findAndModify(item, retryCount, SET_ON_INSERT_FIELDS);
                        }
                    }

                    logger.info("syncFundManagerPostFromRelation-第五步，更新数据写库-第{}轮。数量：{}，头部id列表：{}",
                        round,
                        map == null ? 0 : map.size(),
                        map == null ? null : map.keySet().stream().limit(50).collect(Collectors.toList())
                    );

                }


                if (CollectionUtils.isEmpty(postList) || postList.size() < batchReadCount) {
                    isRun = false;
                }

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

                logger.info("syncFundManagerPostFromRelation-第六步，更新断点-第{}轮。断点：{}", round, breakpoint);
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    private List<String> dealCodeForCodeList(List<String> codeList) {
        List<String> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(codeList)) {
            String tempCode = null;
            for (String item : codeList) {
                item = item.toLowerCase();

                if (item.startsWith("of")) {
                    tempCode = item.substring(2);
                } else if (item.startsWith("43-")) {
                    tempCode = item;
                } else if (item.startsWith("58-")) {
                    tempCode = item;
                } else {
                    tempCode = null;
                }

                if (StringUtils.hasLength(tempCode)) {
                    result.add(tempCode);
                }
            }

        }
        return result;
    }

    private String dealCode(int fType, String fCode) {
        String result = null;
        //1=投顾产品 43=组合产品 2=高端理财 3=场外基金
        if (StringUtils.hasLength(fCode)) {
            switch (fType) {
                case 1:
                    result = "58-" + fCode.toLowerCase();
                    break;
                case 2:
                    result = fCode.toLowerCase();
                    break;
                case 3:
                    result = fCode.toLowerCase();
                    break;
                case 43:
                    result = "43-" + fCode.toLowerCase();
                    break;
            }
        }
        return result;
    }

    /**
     * 查找再更新 cas方式
     */
    private void findAndModify(FundManagerPostModel model, int retryCount, List<String> setOnInsertFields) {
        int count = 0;
        FundManagerPostModel modelInDb = null;
        while (true) {
            if (modelInDb == null) {
                modelInDb = fundManagerPostDao.getById(model._id);
            }
            if (modelInDb == null || !StringUtils.hasLength(modelInDb._id)) {
                try {
                    modelInDb = fundManagerPostDao.insertOne(model);
                } catch (Exception ex) {
                    modelInDb = null;

                    logger.error(ex.getMessage(), ex);
                }

            } else {

                Query query = new Query();
                query.addCriteria(Criteria.where("_id").is(model._id).and("CODELISTUSE").is(modelInDb.CODELISTUSE));

                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("_id", model._id);
                tempMap.put("ID", model.ID);
                tempMap.put("UID", model.UID);
                tempMap.put("CODE", model.CODE);
                List<String> union = union(modelInDb.CODELISTUSE, model.CODELISTUSE);
                tempMap.put("CODELISTUSE", union);
                tempMap.put("TIME", model.TIME);
                tempMap.put("TIMEPOINT", model.TIMEPOINT);
                tempMap.put("YUANID", model.YUANID);
                tempMap.put("TYPE", model.TYPE);
                tempMap.put("YUANTYPE", model.YUANTYPE);
                tempMap.put("DEL", model.DEL);
                tempMap.put("TTJJDEL", model.TTJJDEL);

                Update update = new Update();
                for (Map.Entry<String, Object> entry : tempMap.entrySet()) {
                    if (!CollectionUtils.isEmpty(setOnInsertFields)) {
                        if (setOnInsertFields.contains(entry.getKey())) {
                            update.setOnInsert(entry.getKey(), entry.getValue());
                        } else {
                            update.set(entry.getKey(), entry.getValue());
                        }
                    } else {
                        update.set(entry.getKey(), entry.getValue());
                    }
                }

                try {
                    modelInDb = fundManagerPostDao.findAndModifyByUpdate(query, update);
                } catch (Exception ex) {
                    modelInDb = null;

                    logger.error(ex.getMessage(), ex);
                }
            }

            //成功
            if (modelInDb != null) {
                return;
            }

            if (modelInDb == null) {
                count++;
            }

            if (count >= retryCount) {
                throw new RuntimeException("ElitePostCalcJob自定义异常。异常信息：写入mongo失败。数据：" + JSON.toJSONString(model));
            }
        }
    }

    private List<String> union(List<String> list1, List<String> list2) {
        List<String> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list1)) {
            result.addAll(list1);
        }
        if (!CollectionUtils.isEmpty(list2)) {
            result.addAll(list2);
        }
        result = result.stream().distinct().collect(Collectors.toList());
        return result;
    }


}
