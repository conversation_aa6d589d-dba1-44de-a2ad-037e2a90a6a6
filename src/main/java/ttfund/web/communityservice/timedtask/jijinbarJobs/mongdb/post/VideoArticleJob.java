package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumLiveBroadcastStatus;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumVideoArtcleApprovalState;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumVideoArticleSource;
import ttfund.web.communityservice.bean.jijinBar.post.caifuhao.CaiFuHaoInfo;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.CFHArticleModel;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.CFHLiveBroadcastConfigModel;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.VideoArticleModel;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.VideoInfo;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.dao.mongo.*;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;
import ttfund.web.communityservice.utils.StringUtil;
import ttfund.web.communityservice.utils.TwoTuple;

import java.util.*;
import java.util.stream.Collectors;

@JobHandler(value = "videoArticleJob")
@Component
public class VideoArticleJob extends IJobHandler {
    int TopOrder = 0;
    private final String logpre = "[videoArticleJob]=>";
    private static final Logger logger = LoggerFactory.getLogger(VideoArticleJob.class);

    /**
     * 视频文章数据
     */
    @Autowired
    VideoArticleDao videoArticleDao;

    @Autowired
    UserRedisDao userRedisDao;
    /**
     * 理财直播配置信息
     */
    @Autowired
    CFHLiveBroadcastDao cfhLiveBroadcastDao;

    /**
     * 财富号 文章
     */
    @Autowired
    CFHArticleDao cfhArticleDao;

    /**
     * 财富号信息
     */
    @Autowired
    CaiFuHaoDao caiFuHaoDao;

    @Autowired
    PassportUserInfoDao passportUserInfoDao;

    @Autowired
    AppConstant appConstant;

    public ReturnT<String> execute(String param) {
        try {

            resetDelete();
            setEndLiveViedoInfo();
            //理财直播数据
            logicDealLCZB();
            //财富号文章数据
            logicDealCFHArticle();

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            logger.error(logpre + "{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 直播结束尚未房间号的视频文章删除（大咖秀新版以后这个逻辑可以拿掉）
     */
    public void resetDelete() {
        try {
            //为了解决直播结束后没有配置房间号的问题，这里先把结束的且没有房间号的置为删除状态
            /*这里需要加endtime为索引*/
            logger.info(logpre + "=>resetDelete=>开始");
            List<VideoArticleModel> list = videoArticleDao.getEndLiveAndNoRoomNumber();
            if (!CollectionUtils.isEmpty(list)) {
                for (VideoArticleModel item : list) {
                    item.setIsDeleted(1);
                }
                videoArticleDao.insertOrUpdate(list);
                logger.info(logpre + "=>resetDelete=>本轮设置删除状态数量:" + list.size());
            } else {
                logger.info(logpre + "=>resetDelete=>本轮设置删除状态数量:0");
            }
            logger.info(logpre + "=>resetDelete=>结束");
        } catch (Exception ex) {
            logger.error(logpre + "=>resetDelete=>{}", ex.getMessage(), ex);
        }
    }


    /**
     * 设置直播结束的大咖秀视频信息
     */
    public void setEndLiveViedoInfo() {

        try {
            logger.info(logpre + "setEndLiveViedoInfo=>设置直播结束的大咖秀视频信息--【开始】");

            String breakPointName = "setEndLiveViedoInfo";
            Date defaultDate = DateUtil.calendarDateByHour(-24);
            Date lastUpdate;
            lastUpdate = userRedisDao.getBreakTime(breakPointName, defaultDate);

            List<VideoArticleModel> articleList = new ArrayList<>();

            int batchCount = 10000;
            List<CFHLiveBroadcastConfigModel> list = cfhLiveBroadcastDao.getLiveEndList(DateUtil.calendarDateByHour(lastUpdate, -48), batchCount);

            if (!CollectionUtils.isEmpty(list)) {

                for (CFHLiveBroadcastConfigModel item : list) {
                    VideoArticleModel model = InitViedoArt(item);
                    if (model != null) {
                        if (StringUtil.isNull(item.RoomNumber)) {
                            item.setIsDelete(1);
                        }
                        articleList.add(model);
                    }
                }
                if (!CollectionUtils.isEmpty(articleList)) {
                    boolean updateResult = videoArticleDao.insertOrUpdate(articleList);
                    if (!updateResult) {
                        updateResult = videoArticleDao.insertOrUpdate(articleList);
                    }
                    if (updateResult) {
                        lastUpdate = list.stream().max(Comparator.comparing(CFHLiveBroadcastConfigModel::getEndTime))
                                .get().getEndTime();

                        userRedisDao.setBreakTime(breakPointName, lastUpdate);
                    }

                    logger.info(logpre + "setEndLiveViedoInfo=>设置直播结束的大咖秀视频信息,更新数量:" + articleList.size() + ",数据为：" + JacksonUtil.obj2String(articleList));
                }
            }
            logger.info(logpre + "setEndLiveViedoInfo=>设置直播结束的大咖秀视频信息--【结束】");
        } catch (Exception ex) {
            logger.error(logpre + "{}", ex.getMessage(), ex);
        }
    }

    /// <summary>
    /// 大咖秀视频文章转换为 视频文章格式
    /// </summary>
    /// <param name="item"></param>
    /// <returns></returns>
    private VideoArticleModel InitViedoArt(CFHLiveBroadcastConfigModel item) {
        try {

            // 过滤黑名单用户
            if (appConstant.blacklist.contains(item.getCompanyUid())) {
                return null;
            }

            //如果是视频的话才提取数据
            if (!StringUtil.isNull(item.LankeId) || !StringUtil.isNull(item.RoomNumber)) {
                VideoArticleModel model = new VideoArticleModel();
                {
                    model.setMy_ID(item.get_id());
                    model.setCFHID(item.getCFHID());
                    model.setCreatTime(item.getCreateTime());
                    model.setIsDeleted(getDelStatus(item.getIsDelete(), item.getIsHide()));
                    model.setLankeId(item.getLankeId());
                    model.setRoomNumber(StringUtil.isNull(item.getRoomNumber()) ? null : item.getRoomNumber());
                    model.setLastUpdateTime(DateUtil.getNowDate());
                    model.setLiveStartTime(item.getStartTime());
                    model.setLiveEndTime(item.getEndTime());
                    model.setPassportId(item.getCompanyUid());
                    model.setPostID(item.getPostId());
                    model.setGubaPostID(item.getPostId());
                    model.setPublisher(item.getCFHName());
                    model.setShowTime(item.getStartTime());//大咖秀对应的开始时间
                    model.setSource(EnumVideoArticleSource.BIGSHOW.getValue());
                    model.setUpDateTime(item.getUpdateTime());
                    model.setTitle(item.getTitle());
                    model.setOrder(TopOrder);
                    model.setFundInfoList(null);
                    model.setIsClosed(0);
                    model.setIsTTOrg(1);
                    model.setTimePoint(videoArticleDao.getTimePoint(TopOrder, item.getStartTime(), item.get_id()));
                }
                //大咖秀直播结束时获取录播视频相关信息,目前只是获取mp4格式的视频
                if (!StringUtil.isNull(item.getRoomNumber())) {
                    //视频封面图片地址
                    String videoPic = "";
                    TwoTuple<List<VideoInfo>, String> viedoList = videoArticleDao.getViedoInfo(model.RoomNumber, null);
                    if (viedoList == null || CollectionUtils.isEmpty(viedoList.getFirst())) {
                        viedoList = videoArticleDao.getViedoInfo(model.RoomNumber, null);
                    }

                    if (viedoList != null && !CollectionUtils.isEmpty(viedoList.getFirst())) {
                        model.setVideos(viedoList.getFirst());
                        videoPic = viedoList.getSecond();
                    } else {
                        //如果没有视频列表不对外展示，标识为删除状态
                        model.setIsDeleted(1);
                    }
                    if (!StringUtil.isNull(videoPic)) {
                        model.setVideoPic(videoPic);
                    }
                } else if (DateUtil.getNowDate().compareTo(item.getEndTime()) > 0 && StringUtil.isNull(item.getRoomNumber())) {
                    //如果直播结束，房间为为空
                    item.setIsDelete(1);
                }
                model.setApprovalState(getApprovalState(item.getStatus()));
                return model;
            }
            return null;

        } catch (Exception ex) {
            logger.error(logpre + "InitVideoArt=>{}", ex.getMessage(), ex);
            return null;
        }
    }

    /// <summary>
    /// 根据配置的删除状态，是否隐藏状态 作为处理
    /// </summary>
    /// <param name="isdelete"></param>
    /// <param name="ishide"></param>
    /// <returns></returns>
    private int getDelStatus(int isdelete, int ishide) {
        if (isdelete == 1) return 1;
        if (ishide == 1) return 1;
        return 0;
    }

    /// <summary>
    /// 根据大咖秀状态转换为视频文章状态
    /// </summary>
    /// <param name="status"></param>
    /// <returns></returns>
    public int getApprovalState(int status) {

        if (status == EnumLiveBroadcastStatus.FWAduit.getValue()) {
            return EnumVideoArtcleApprovalState.AUDIT.getValue();
        } else if (status == EnumLiveBroadcastStatus.SubmitAduit.getValue()
                || status == EnumLiveBroadcastStatus.YYAduitSucess.getValue()
                || status == EnumLiveBroadcastStatus.Draf.getValue()) {
            return EnumVideoArtcleApprovalState.UNAUDIT.getValue();
        } else {
            return EnumVideoArtcleApprovalState.UNPASS.getValue();
        }
    }


    /// <summary>
    /// 处理理财直播的数据
    /// </summary>
    public void logicDealLCZB() {
        try {

            logger.info(logpre + "=>logicDealLCZB=>开始");
            String methodName = "logicDealLCZB10";
            Date lastTime = userRedisDao.getBreakTime(methodName, DateUtil.calendarDateByHour(-24));
            logger.info(logpre + "logicDealLCZB=>时间断点" + DateUtil.dateToStr(lastTime));
            int batchCount = 1000;
            //根据跟新时间获取，获取指定数量的数据
            List<CFHLiveBroadcastConfigModel> list = cfhLiveBroadcastDao.getViedoList(lastTime, batchCount);
            List<VideoArticleModel> articleList = new ArrayList<>();

            if (!CollectionUtils.isEmpty(list)) {
                for (CFHLiveBroadcastConfigModel item : list) {
                    VideoArticleModel model = InitViedoArt(item);
                    if (model != null) {
                        articleList.add(model);
                    }
                }
                if (!CollectionUtils.isEmpty(articleList)) {
                    //有房间号的财富号视频文章
                    List<String> roomNumberList = articleList.stream().filter(a -> !StringUtil.isNull(a.getRoomNumber()))
                            .map(VideoArticleModel::getRoomNumber).distinct().collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(roomNumberList)) {

                        List<VideoArticleModel> haveExsitRoomNumberList = videoArticleDao.getByRoomNumber(roomNumberList);
                        if (!CollectionUtils.isEmpty(haveExsitRoomNumberList)) {
                            //已经存在的财富号视频文章ID
                            List<String> idList = haveExsitRoomNumberList.stream().map(VideoArticleModel::get_id).collect(Collectors.toList());
                            //本轮要更新的视频财富号文章ID
                            List<String> allIdList = articleList.stream().map(VideoArticleModel::get_id).collect(Collectors.toList());

                            //不需要更新的房间号对应的文章ID
                            List<String> notUpdateIdList = idList.stream().filter(a -> !allIdList.contains(a)).collect(Collectors.toList());

                            //不需要更新的房间号列表
                            List<String> notUpdateRoomNumberList = haveExsitRoomNumberList.stream().filter(a -> notUpdateIdList.contains(a.get_id())).map(a -> a.getRoomNumber()).collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(notUpdateRoomNumberList)) {
                                //剔除不需要更新的相关房间号的财富号视频文章
                                logger.info(logpre + "logicDeal=>大咖秀--财富号文章过滤，涉及的房间号为:" + JacksonUtil.obj2String(notUpdateRoomNumberList));
                                articleList = articleList.stream().filter(a -> !notUpdateRoomNumberList.contains(a.getRoomNumber())).collect(Collectors.toList());
                            }
                        }
                    }

                    /*
                    * 按照房间号分组,目的是一个房间号只保留一条数据
                    * */
                    Map<String, List<VideoArticleModel>> mapList = articleList.stream()
                            .filter(a -> !StringUtil.isNull(a.getRoomNumber()))
                            .collect(Collectors.groupingBy(VideoArticleModel::getRoomNumber, Collectors.toList()));

                    List<VideoArticleModel> articleListResult = new ArrayList<>();
                    for(String key:mapList.keySet()){
                        articleListResult.add(mapList.get(key).get(0));
                    }

                    Boolean updateResult = videoArticleDao.insertOrUpdate(articleListResult);
                    if (!updateResult) {
                        updateResult = videoArticleDao.insertOrUpdate(articleListResult);
                    }
                    if (updateResult) {
                        //获取最大更新时间作为断点
                        lastTime = list.stream().max(Comparator.comparing(CFHLiveBroadcastConfigModel::getUpdateTime)).get().getUpdateTime();
                        userRedisDao.setBreakTime(methodName, lastTime);
                    }
                }
            }
            logger.info(logpre + "logicDealLCZB=>本轮同步视频数量：原数量：" + (list == null ? 0 : list.size()) + "，更新数量:" + (articleList == null ? 0 : articleList.size()));
            logger.info(logpre + "=>logicDealLCZB=>结束");
        } catch (Exception ex) {
            logger.error(logger + "logicDeal=>{}", ex.getMessage(), ex);
        }
    }

    public void logicDealCFHArticle() {
        try {
            logger.info(logpre + "logicDealCFHArticleNew=> 方法执行开始");
            try {
                String methodName = "logicDealCFHArticleNew224New";
                Date lastTime = userRedisDao.getBreakTime(methodName, DateUtil.calendarDateByDays(-1));
                if (lastTime == null) {
                    lastTime = DateUtil.calendarDateByDays(-1);
                }
                List<CFHArticleModel> list = cfhArticleDao.getVideoList(lastTime, 3000);
                logger.info(logpre + "logicDealCFHArticleNew=>断点时间 lastTime=> " + DateUtil.dateToStr(lastTime) + "=>Data=>涉及数量:" + (list == null ? 0 : list.size()));


                if (!CollectionUtils.isEmpty(list)) {
                    List<VideoArticleModel> articleList = new ArrayList<>();
                    List<CaiFuHaoInfo> cfhList = caiFuHaoDao.GetList();

                    for (CFHArticleModel item : list) {
                        try {
                            logger.info(logpre + "logicDealCFHArticleNew=>001涉及数据[item]： " + JacksonUtil.obj2String(item));
                            VideoArticleModel article = new VideoArticleModel();
                            article.setCFHID(item.getAuthorId());
                            article.setCreatTime(item.getCreatTime());
                            article.setIsDeleted((item.IsDeleted == null ? 1 : item.getIsDeleted()));
                            article.setLankeId("");
                            article.setLastUpdateTime(DateUtil.getNowDate());
                            article.setPostID(item.getArtCode());
                            //新加
                            article.setGubaPostID(item.getPostId());
                            article.setRoomNumber("");
                            article.setSource(EnumVideoArticleSource.CFHVIDEOART.getValue());
                            article.setTitle(item.getTitle());
                            article.setShowTime(item.getShowtime());
                            article.setVideoPic(item.getVideoPic());
                            article.setMy_ID(item.get_id());
                            article.setApprovalState((item.ApprovalState == null ? 0 : item.getApprovalState()));
                            article.setOrder(TopOrder);
                            article.setFundInfoList(null);
                            article.setIsClosed(0);
                            article.setIsTTOrg(0);
                            article.setTimePoint(videoArticleDao.getTimePoint(TopOrder, item.getShowtime(), item.get_id()));

                            Optional<CaiFuHaoInfo> any = cfhList.stream().filter(a -> !StringUtil.isNull(a.getCFHID()) && a.getCFHID().equals(item.getAuthorId() == null ? "" : item.getAuthorId())).findAny();
                            if (any.isPresent()) {
                                //财富号信息
                                CaiFuHaoInfo cfhInfo = any.get();
                                article.setPassportId((cfhInfo == null ? "" : cfhInfo.getRelatedUid()));//获取财富号关联的财富号ID
                                article.setPublisher((cfhInfo == null ? "" : cfhInfo.getCFHName()));
                                if (item.getIsCfhArticle()) {
                                    article.setIsTTOrg(1);
                                }

                            } else {
                                //不存在财富号信息，获取用户通行证信息填充
                                PassportUserInfoModelNew passportUserInfoModelNew = passportUserInfoDao.getPassportUserInfoById(item.getRelatedUid());
                                if (passportUserInfoModelNew != null) {
                                    article.setPassportId(passportUserInfoModelNew.PassportID);//获取财富号关联的财富号ID
                                    article.setPublisher(passportUserInfoModelNew.NickName);
                                    articleList.add(article);
                                } else {
                                    logger.info(logpre + "logicDealCFHArticleNew=>004涉及数据[item]： " + JacksonUtil.obj2String(item));
                                    continue;
                                }
                            }

                            //视频列表
                            if (!StringUtil.isNull(item.getVideos())) {
                                List<VideoInfo> videoList = JacksonUtil.string2Obj(item.getVideos(), List.class, VideoInfo.class);
                                if (!CollectionUtils.isEmpty(videoList)) {
                                    article.setVideos(videoList);
                                    //财富号文章对应的房间号
                                    article.setRoomNumber(videoList.get(0).getZhiboId().toString());
                                    List<VideoInfo> haveVideoList  = videoList.stream().filter(a->!StringUtil.isNull(a.VideoSrc)).collect(Collectors.toList());
                                    if(haveVideoList!=null && haveVideoList.size()==0){
                                        article.setIsDeleted(1);
                                    }
                                }
                            }
                            //如果财富号视频文章没有视频，则状态标识为删除状态
                            if (CollectionUtils.isEmpty(article.getVideos())) {
                                article.setIsDeleted(1);
                            }

                            //视频封面
                            if (StringUtils.hasLength(article.getRoomNumber())) {
                                TwoTuple<List<VideoInfo>, String> viedoList = videoArticleDao.getViedoInfo(article.getRoomNumber(), null);
                                if (viedoList != null && StringUtils.hasLength(viedoList.getSecond())) {
                                    article.setVideoPic(viedoList.getSecond());
                                }
                            }

                            articleList.add(article);
                        } catch (Exception ex) {
                            logger.error(logpre + "logicDealCFHArticleNew=>涉及数据:" + JacksonUtil.obj2String(item) + ",财富号数据：" + JacksonUtil.obj2String(cfhList) + "  erro:" + ex.getMessage());
                            logger.info(logpre + "logicDealCFHArticleNew=>ERRO涉及数据[item]： " + JacksonUtil.obj2String(item));
                        }
                    }

                    if (!CollectionUtils.isEmpty(articleList)) {

                        articleList = articleList.stream()
                            .filter(w -> !appConstant.blacklist.contains(w.PassportId)
                                && StringUtils.hasLength(w.getVideoPic()))
                            .collect(Collectors.toList());

                        //有房间号的财富号视频文章
                        List<String> roomNumberList = articleList.stream().filter(a -> !StringUtil.isNull(a.getRoomNumber()))
                                .map(VideoArticleModel::getRoomNumber).collect(Collectors.toList());

                        if (!CollectionUtils.isEmpty(roomNumberList)) {
                            List<VideoArticleModel> haveExsitRoomNumberList = videoArticleDao.getByRoomNumber(roomNumberList);
                            if(!CollectionUtils.isEmpty(haveExsitRoomNumberList)){
                                List<String> idList = haveExsitRoomNumberList.stream().map(VideoArticleModel::get_id).collect(Collectors.toList());
                                videoArticleDao.deleteByids(idList);
                            }
                        }
                        /*
                         * 按照房间号分组,目的是一个房间号只保留一条数据
                         * */
                        Map<String, List<VideoArticleModel>> mapList = articleList.stream()
                                .filter(a -> !StringUtil.isNull(a.getRoomNumber()))
                                .collect(Collectors.groupingBy(VideoArticleModel::getRoomNumber, Collectors.toList()));

                        List<VideoArticleModel> articleListResult = new ArrayList<>();
                        for(String key:mapList.keySet()){
                            articleListResult.add(mapList.get(key).get(0));
                        }

                        if (!CollectionUtils.isEmpty(articleListResult)) {
                            //更新财富号视频文章到数据库
                            Boolean updateResult = videoArticleDao.insertOrUpdate(articleListResult);
                            if (!updateResult) {
                                updateResult = videoArticleDao.insertOrUpdate(articleListResult);
                            }
                            if (updateResult) {
                                lastTime = list.stream().max(Comparator.comparing(CFHArticleModel::getInsertTime)).get().getInsertTime();
                                userRedisDao.setBreakTime(methodName, DateUtil.calendarDateBySecond(lastTime, -300));
                                logger.info(logpre + "logicDealCFHArticleNew=>本轮更新数量:" + (articleListResult == null ? 0 : articleListResult.size())+",涉及文章ID:"+ articleListResult.stream().map(VideoArticleModel::getMy_ID).collect(Collectors.toList()));
                            }else {
                                logger.info(logpre + "logicDealCFHArticleNew=>updatefaile:" + (articleListResult == null ? 0 : articleListResult.size()));
                            }
                        } else {
                            logger.info(logpre + "logicDealCFHArticleNew=>本轮更新数量:0");
                        }
                    } else {
                        logger.info(logpre + "logicDealCFHArticleNew=>本轮更新数量:0");
                    }

                }
            } catch (Exception ex) {
                logger.error(logpre + "logicDealCFHArticleNew=>{}", ex.getMessage(), ex);
            }
            logger.info(logpre + "logicDealCFHArticleNew=> 方法执行结束");
        } catch (Exception ex) {
            logger.error(logpre + "logicDealCFHArticleNew=>{}", ex.getMessage(), ex);
        }
    }
}
