package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.eastmoney.particle.common.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.data.AiCommentDo;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.dao.mongo.AiReplyMongoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.service.ReplyServiceImpl;
import ttfund.web.communityservice.service.entity.AddReplyRequest;
import ttfund.web.communityservice.service.entity.AddReplyResponse;
import ttfund.web.communityservice.utils.DateUtil;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Zhuyuang
 * @date : 2024-08-21 14:39
 * @description :
 */
@JobHandler("AiReplyJob")
@Component
public class AiReplyJob extends IJobHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AiReplyJob.class);

    private static final String LOG_PREFIX = "AiReplyJob[气氛组评论Job]";

    @Value("${" + AppConstantConfig.AI_COMMENT_OPEN + ":true}")
    private boolean aiReplyOpen;

    @Value("${" + AppConstantConfig.AI_COMMENT_ID + ":#{null}}")
    private String robotId;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private AiReplyMongoDao aiReplyMongoDao;

    @Autowired
    private ReplyServiceImpl replyService;

    /**
     * 同一个账号发布两条评论的时间间隔大于10min
     * 所有账号不会在凌晨1200-早上700之间发评论
     */
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        // 获取当前时间
        LocalTime now = LocalTime.now();

        // 定义时间范围
        LocalTime start = LocalTime.of(0, 0); // 0点
        LocalTime end = LocalTime.of(7, 0);   // 7点

        // 判断当前时间是否在0点到7点之间
        if (now.isAfter(start) && now.isBefore(end)) {
            LOGGER.info("{}:未到启动时间", LOG_PREFIX);
        } else {
            aiReply();
        }
        return ReturnT.SUCCESS;
    }

    private void aiReply() {
        if (StringUtils.isEmpty(robotId)) {
            LOGGER.error("{}:气氛组账号为空", LOG_PREFIX);
            return;
        }
        List<String> coolDownRobot = new ArrayList<>();
        List<String> robotIdList = Arrays.asList(robotId.split(","));
        // 存在发帖失败的可能 最多十次拉取数据回复
        for (int count = 0; count < 10; count ++) {
            //过滤掉使用过的账号
            robotIdList = robotIdList.stream().
                filter(id -> !coolDownRobot.contains(id)).
                collect(Collectors.toList());
            if (robotIdList.isEmpty()) {
                LOGGER.error("{}:无可用气氛组账号", LOG_PREFIX);
                break;
            }
            int robotSize = robotIdList.size();
            List<AiCommentDo> commentList = aiReplyMongoDao.getList(robotSize, null);
            for (int i = 0; i < commentList.size(); i++) {
                String id = robotIdList.get(i % robotSize);
                AiCommentDo comment = commentList.get(i);
                boolean result = reply(id, comment.getPostId(), comment.getComment());
                if (result) {
                    coolDownRobot.add(id);
                }
                int sendState = comment.getSendState();
                //更新发送状态，若未发送则修改为发送完成和发送失败，若发送失败，则二次失败时触发放弃发送
                if (sendState == 0) {
                    comment.setSendState(result ? 1 : 2);
                } else if (sendState == 2) {
                    comment.setSendState(result ? 1 : 3);
                }
                comment.setUpdateTime(DateUtil.dateToStr(new Date()));
            }
            boolean result = aiReplyMongoDao.upsertMany(commentList, "_id");
            LOGGER.info("{}:第{}轮, 共发帖成功数: {}, 气氛组更新发送状态结果:{}", LOG_PREFIX, count + 1, coolDownRobot.size(), result);
        }
    }

    private boolean reply(String robotId, String postId, String content) {
        AddReplyRequest replyRequest = new AddReplyRequest();
        replyRequest.setId(postId);
        replyRequest.setText(content);
        replyRequest.setUid(robotId);
        replyRequest.setBusiness_type(4);

        AddReplyResponse addReplyResponse = replyService.addReply(replyRequest);
        if (addReplyResponse != null && addReplyResponse.getReplyId() != 0) {
            return true;
        } else {
            String errorMessage = (addReplyResponse != null) ? addReplyResponse.getMe() : "返回体解析失败";
            LOGGER.error("评论错误信息: {}", errorMessage);
            return false;
        }

    }
}
