package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.PostAuthorFlag;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.RecommenderMongoDao;
import ttfund.web.communityservice.dao.msyql.PostAuthorFlagDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基金吧发现页 推荐帖子 个性化部分
 * 1. 解盘作者同步至缓存
 * 2. 近一周有异动（大事件-异动）的基金同步至缓存
 */
@JobHandler(value = "findRecommendPersonalizedJob")
@Component
public class FindRecommendPersonalizedJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(FindRecommendPersonalizedJob.class);

    @Autowired
    private PostAuthorFlagDao postAuthorFlagDao;

    @Autowired
    private App app;

    @Autowired
    private RecommenderMongoDao recommenderMongoDao;

    @Override
    public ReturnT<String> execute(String param) {

        ReturnT result = ReturnT.SUCCESS;

        try {
            // 获取解盘作者
            List<PostAuthorFlag> users = postAuthorFlagDao.getJPAuthor();

            // 保存至缓存
            List<String> ids = users.stream().map(w -> w.uid).collect(Collectors.toList());
            String key = BarRedisKey.ANALYSIS_MARKET_AUTHOR;
            app.barredis.set(key, JacksonUtil.obj2String(ids), 7 * 24 * 3600L);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        try {
            // 获取近一周有异动（大事件-异动）的基金
            Date sevenDaysAgo = DateUtil.calendarDateByDays(-7);
            List<String> codeList = recommenderMongoDao.getAbnormalAction(sevenDaysAgo);
            String key = BarRedisKey.ABNORMAL_ACTION_FUND_LIST;
            app.barredis.set(key, JacksonUtil.obj2String(codeList), 7 * 24 * 3600L);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }

        return result;
    }
}
