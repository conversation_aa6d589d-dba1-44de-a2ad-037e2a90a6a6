package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.ttfund.web.base.helper.CacheHelper;
import com.ttfund.web.core.register.AppCore;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.*;
import ttfund.web.communityservice.bean.jijinBar.post.PostinfoExtraModel;
import ttfund.web.communityservice.bean.jijinBar.post.elitepost.ElitePostModel;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.*;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.VideoArticleModel;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.dao.mongo.*;
import ttfund.web.communityservice.dao.msyql.FaxianWeightDao;
import ttfund.web.communityservice.dao.msyql.PostAuthorFlagDao;
import ttfund.web.communityservice.dao.msyql.PostCountIncrByTimeDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基金吧发现页 推荐帖子
 */
@JobHandler(value = "findRecommendPostJob")
@Component
public class FindRecommendPostJob extends IJobHandler {

    @Autowired
    PostAuthorFlagDao postAuthorFlagDao;

    @Autowired
    UserRedisDao userRedisDao;

    @Autowired
    PostCountIncrByTimeDao postCountIncrByTimeDao;

    @Autowired
    ElitePostDao elitePostDao;

    @Autowired
    PostDao postDao;

    @Autowired
    PassportUserInfoDao passportUserInfoDao;

    @Autowired
    FindRecommendPostDao findRecommendPostDao;

    @Resource
    AppCore appCore;

    @Autowired
    VTagUserInfoDao vTagUserInfoDao;

    @Autowired
    PostInfoExtraDao postInfoExtraDao;

    @Autowired
    AppConstant appConstant;

    /**
     * 理财用户配置白名单信息
     */
    @Autowired
    LCAuthorInfoDao lcAuthorInfoDao;
    private static final Logger logger = LoggerFactory.getLogger(FindRecommendPostJob.class);
    private String logPre = "[findRecommendPostJob]=>";

    /**
     * 作者类型字典
     */
    private Map<String, Integer> DicAuthorType = null;

    /**
     * 增量分
     */
    private Map<String, List<PostCalScore>> DicIncrScoreList = null;

    /**
     * 存量分
     */
    private Map<String, List<PostCalScore>> DicHisScoreList = null;

    /**
     * 分组配置
     */
    private List<FaxianWeightModel> WeightConfigModelGroupList = null;

    private String cacheKeyNew = "fundbar_post_hisscoreratio_group_%s";

    @Autowired
    private FaxianWeightDao faxianWeightDao;


    private Map<String, String> dicVTagUserInfo;

    //理财白名单用户
    private Map<String, String> dicWitheLCAuthors;

    @Autowired
    VideoArticleDao videoArticleDao;

    @Autowired
    FundBarAuthorBlackListDao authorBlackListDao;


    public ReturnT<String> execute(String param) {

        Date startDateTime = new Date();
        try {
            /**
             * “IsDel”:0,//是否删除   1：删除，0：未删除
             * “IsConfigV”:1//是否大V   1:大V, 0 ：普通用户
             * "IsShow":1 //是否在瀑布流展示  1：在瀑布流展示，0：不在瀑布流展示
             */

            DicIncrScoreList = new HashMap<>();
            DicHisScoreList = new HashMap<>();
            dicVTagUserInfo = vTagUserInfoDao.getVUserIds();

            WeightConfigModelGroupList = new ArrayList<>();
            int count = 0;

            /**
             * 1.初始化用户标签数据 解盘作者、优质作者、黑名单=================
             */
            DicAuthorType = postAuthorFlagDao.initUserTypesToDic();
            DicAuthorType = authorBlackListDao.initBlackUserToDic(DicAuthorType);
//            logger.info(logPre+"=>DicAuthorType=>"+JacksonUtil.obj2String(DicAuthorType));

            /**
             * 2.初始化权重系数配置====================================
             */
            WeightConfigModelGroupList = faxianWeightDao.getAll();
//            logger.info(logPre+"WeightConfigModelGroupList=>"+JacksonUtil.obj2String(WeightConfigModelGroupList));
            /**为了可用的配置为空情况，这里增加默认配置*/
            if (CollectionUtils.isEmpty(WeightConfigModelGroupList)) {
                WeightConfigModelGroupList = faxianWeightDao.getDefaultConfig();
            }

            /**
             *理财 配置白名单用户信息
             */
            dicWitheLCAuthors = lcAuthorInfoDao.getALLIds();

            List<String> listPostId = new ArrayList<>();
            /**
             * 近三小时发帖数据
             */
            List<String> last3HPostIds = getlast3HourIncrPost();
            if (!CollectionUtils.isEmpty(last3HPostIds)) {
                listPostId.addAll(last3HPostIds);
            }
            logger.info(logPre + "last3HPostIds:" + (last3HPostIds == null ? 0 : last3HPostIds.size()));

            /**
             * 获取精华帖数据
             * */
            List<ElitePostModel> elitePostModels = getLast1MonthElitePost();
            logger.info(logPre + "elitePostModels:" + (elitePostModels == null ? 0 : elitePostModels.size()));

            /**
             * 来源于配置的精华帖
             * */
            List<String> fromConfigElitePostIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(elitePostModels)) {
                List<String> elitePostIds = elitePostModels.stream().
                        map(a -> String.valueOf(a.getID())).
                        collect(Collectors.toList());
                listPostId.addAll(elitePostIds);


                fromConfigElitePostIds = elitePostModels.stream().
                        filter(a -> a.getSource() != null && a.getSource() == EnumElitePostSource.BackGroundConfig.getValue()).
                        map(a -> String.valueOf(a.getID())).
                        collect(Collectors.toList());

            }

            /**
             * 晒收益话题帖
             */
            List<String> htPostIds = showProfitHTPost();
            if (!CollectionUtils.isEmpty(htPostIds)) {
                listPostId.addAll(htPostIds);
//                logger.info(logPre+"showProfitHTPost=>"+JacksonUtil.obj2String(htPostIds));
            }
            /**
             * 对帖子ID 进行去重
             */
            if (!CollectionUtils.isEmpty(listPostId)) {
                listPostId = listPostId.stream().distinct().collect(Collectors.toList());
            }
            logger.info(logPre + "needcalpostcount:" + (listPostId == null ? 0 : listPostId.size()));


            /**
             * 帖子分数计算
             */
            count = postFindScoreByGroup(listPostId, fromConfigElitePostIds);

            logger.info(logPre + "=>postFindScoreByGroupcount:" + listPostId.size());
            logger.info(logPre + "=>postFindScoreByGroupsavecount:" + count);

            /***
             * 处理视频帖
             */
            videoArtInit();

            //计算存量分系数缓存
            calHisScoreRatio();

        } catch (Exception e) {
            logger.error(logPre + "异常信息=>" + e.getMessage(), e);
        }

        try {
            /**
             * 清除历史数据
             */
            clearHisData();
            /**
             * 近15天数据重新计算
             */
            calHisData();
        } catch (Exception ex) {
            logger.error(logPre + ex.getMessage(), ex);
        }

        Date endDateTime = new Date();
        logger.info(logPre + "本轮执行耗时：" + String.valueOf((endDateTime.getTime() - startDateTime.getTime()) / 1000) + "秒");
        return ReturnT.SUCCESS;

    }

    /**
     * 最近三小时更新的帖子ID 列表
     *
     * @return
     */
    private List<String> getlast3HourIncrPost() {
        try {

            /**
             * 获取断点的更新时间
             */
            String breakTimeName = "findRecommendPostJob_Last3HourIncrPost_new11";
            Date lastUpdateTime = userRedisDao.getBreakTime(breakTimeName);
            if (lastUpdateTime == null) {
                lastUpdateTime = DateUtil.calendarDateByDays(-7);
            }
            logger.info(logPre + "=>" + breakTimeName + "=>获取断点时间为：" + DateUtil.dateToStr(lastUpdateTime));
            /**
             * 文章长度限制
             */
            int contentcount = 50;

            /**获取增量有变化，且字数大于指定数量帖子数据*/
            List<PostRecommendExtendModel> postRecommendExtendModelList =
                    postCountIncrByTimeDao.getListByContentcount(lastUpdateTime, contentcount);
            logger.info(logPre + "近三小时发帖数据:" + JacksonUtil.obj2String(postRecommendExtendModelList));
            if (!CollectionUtils.isEmpty(postRecommendExtendModelList)) {

                logger.info(logPre + "=>" + breakTimeName + "=>最近三小时更新的帖子数据=>" + JacksonUtil.obj2String(postRecommendExtendModelList));
                /**获取最大时间点*/
                lastUpdateTime = postRecommendExtendModelList.stream().
                        max(Comparator.comparing(PostRecommendExtendModel::getUPDATETIME))
                        .get().getUPDATETIME();
                logger.info(logPre + "=>设置断点" + breakTimeName + "=>" + DateUtil.dateToStr(lastUpdateTime));
                userRedisDao.setBreakTime(breakTimeName, lastUpdateTime);
                List<String> postIds = postRecommendExtendModelList.stream().map(a -> String.valueOf(a.getID())).collect(Collectors.toList());
                ;
                return postIds;
            }
            return null;

        } catch (Exception ex) {
            logger.error(logPre + "=>getlast3HourIncrPost=>" + ex.getMessage(), ex);
        }

        return null;
    }

    /// <summary>
    /// 获取近增量精华帖数据
    /// </summary>
    /// <returns></returns>
    public List<ElitePostModel> getLast1MonthElitePost() {
        try {
            //这里需要判断取增量数据还是历史存量数据，需要根据策略去获取数据
            String cacheKey = "cache_getlast1monthelitepost_new88";
            String cacheResult = CacheHelper.get(cacheKey);
            //是否全量获取
            boolean isAll = false;
            if (StringUtil.isNull(cacheResult)) {
                isAll = true;
            }

            /**
             * 断点时间
             */
            String breakTimemethodName = "GetLast1MonthElitePost_new88";
            Date lastTime = null;
            if (isAll) {
                lastTime = DateUtil.calendarDateByMonth(-3);
            } else {
                lastTime = userRedisDao.getBreakTime(breakTimemethodName);
                if (lastTime == null) {
                    lastTime = DateUtil.calendarDateByDays(-7);
                }
            }
            logger.info(logPre + "读取断点：" + breakTimemethodName + "：" + DateUtil.dateToStr(lastTime));

            Long timePoint = DateUtil.getTimePoint(DateUtil.calendarDateByMonth(-3));

            /**
             * 获取精华帖信息
             * */
            List<ElitePostModel> elitePostModels = elitePostDao.getListByUpdateTime(lastTime, timePoint);
            if (!CollectionUtils.isEmpty(elitePostModels)) {
                lastTime = elitePostModels.stream().max(Comparator.comparing(ElitePostModel::getUPDATETIME)).
                        get().getUPDATETIME();

                if (isAll) {
                    CacheHelper.put(cacheKey, "1", 24 * 60 * 60 * 1000);
                }
                elitePostModels = elitePostModels.stream().
                        filter(a -> a.getCODE() != null && !a.getCODE().equals("jjft")).
                        collect(Collectors.toList());
                logger.info(logPre + "设置断点：" + breakTimemethodName + "：" + DateUtil.dateToStr(lastTime));
                userRedisDao.setBreakTime(breakTimemethodName, lastTime);
                return elitePostModels;
            }

        } catch (Exception ex) {
            logger.error(logPre + "=>getLast1MonthElitePost:" + ex.getMessage(), ex);
        }
        return null;
    }

    /**
     * 帖子算分逻辑
     *
     * @param listPostId             需要计算的帖子信息
     * @param fromConfigElitePostIds 后台配置的帖子id
     * @return
     */
    public int postFindScoreByGroup(List<String> listPostId, List<String> fromConfigElitePostIds) {
        int count = 0;
        try {
            String[] fileds = new String[]{"_id", "ID", "UID", "CODE", "TIME", "TIMEPOINT", "YUANID", "QID", "TYPE", "YUANTYPE", "DEL", "CODELIST", "TTJJDEL"};
            for (String postid : listPostId) {
                //帖子信息
                PostRecommendModelExt postInfo = postDao.getFromMong(postid, fileds, PostRecommendModelExt.class);
                //logger.info(logPre+"=>postFindScoreByGroup单个帖子信息:"+JacksonUtil.obj2String(postInfo));
                /**
                 * 1.帖子存在，且该用户不是黑名单用户（如果是黑名单用户）
                 * */
                if (postInfo != null && !isBlackUser(postInfo.getUID())) {

                    if (postInfo.getDEL() == 0 && postInfo.getTTJJDEL() == 0) {

                        //获取通行证信息
                    /*2.1.2 code='cfhpl'的帖子中，仅保留 作者为 基金经理/基金公司/社区运营标记的
                    财富号作者（需要在基金吧后台新增类型）或者 导入到jjdp或单品吧 的帖子
                    */
                        if (postInfo.getCODE().equals("cfhpl") || postInfo.getTYPE() == EnumPostType.WeMedia.getValue()) {

                            /** 判断是否有关联基金*/
                            if (StringUtil.isNull(postInfo.getCODELIST())) {
                                continue;
                            }

                            /**
                             * 关联的吧是否包含基金点评吧或者基金单品吧，如果都不包含则不该条数据忽略
                             */

                            String[] arrCodeList = postInfo.getCODELIST().split("\\,");
                            if (arrCodeList != null) {
                                List<String> listCodes = Arrays.asList(arrCodeList);
                                //&& (!arrCodeList.Contains("jjdp") && arrCodeList.Where(a => a.StartsWith("of")).Count() == 0)
                                if (!CollectionUtils.isEmpty(listCodes) && !listCodes.contains("jjdp")
                                        && listCodes.stream().filter(a -> a.startsWith("of")).count() == 0) {
                                    continue;
                                }
                            }


                            /**
                             * 判断该用户类型，是否是橙V 或者 蓝V 用户
                             */
                            PassportUserInfoModelNew passportUserInfoModelNew = passportUserInfoDao.getPassportUserInfoById(postInfo.getUID());
                            if (passportUserInfoModelNew != null) {
                                EnumPassportUserVType userVType = passportUserInfoDao.getUserVType(passportUserInfoModelNew);
                                /**
                                 只保留非大v 白名单用户
                                 */
                                if (userVType.getValue() != EnumPassportUserVType.OrangeV.getValue() &&
                                        !dicWitheLCAuthors.containsKey(postInfo.getUID())) {
                                        continue;
                                }
                            } else {
                                continue;
                            }
                        }

                        postInfo.setUPDATETIME(DateUtil.getNowDate());

                        //精华帖来源
                        EnumElitePostSource elitPostSource = EnumElitePostSource.Default;
                        if (fromConfigElitePostIds != null && fromConfigElitePostIds.contains(postid)) {
                            elitPostSource = EnumElitePostSource.BackGroundConfig;
                        }

                        //初始化帖子信息
                        Map<String, Object> dicPostInfo = mapPostInfo(postInfo, elitPostSource, 0);
                        //保存数据
                        logger.info(logPre + "=>postFindScoreByGroup=>savedatas:" + JacksonUtil.obj2String(dicPostInfo));
                        boolean result = findRecommendPostDao.upsert(dicPostInfo);
                        if (!result) {
                            findRecommendPostDao.upsert(dicPostInfo);
                        }
                        count++;
                    } else {
                        //删除数据
                        boolean result = findRecommendPostDao.remove(String.valueOf(postInfo.ID));
                        if (!result) {
                            findRecommendPostDao.remove(String.valueOf(postInfo.ID));
                        }
                    }

                }
            }
        } catch (Exception ex) {
            logger.error(logPre + "=>postFindScoreByGroup=>" + ex.getMessage(), ex);
        }
        return count;
    }

    /**
     * 发现页帖子分数计算
     *
     * @param post
     * @param uid
     * @param postTime
     * @param WeightConfigModel
     * @param elitePostSource
     * @return
     */
    private double calScore(PostRecommendExtendModel post, String uid, Date postTime, FaxianWeightModel WeightConfigModel, EnumElitePostSource elitePostSource) {


            /*排序分=（近3h点击量/50*权重2+近3h评论量*权重4+近3h主贴点赞量*权重2+作者增量调整参数）*新帖调整乘数（新帖2，其他1）
+（累计点击量/50*权重4+累计评论量*权重3+累计主贴点赞量*权重2+作者存量调整参数）*时间衰减系数（权重运营可调）*存量分量级调整系数
                *1.帖子范围：近3h有增量的字数超过200的帖子+近1个月发帖的精华帖
                *2.作者存量调整参数	刷屏等需降权作者-1500
                *3.新帖调整乘数	新帖定义：没有经历过完整的访问峰值（14点/21点，自然小时） 新帖2，其他1
                *
                */

        /*
         *    /*Mongdb 中有帖子的原贴ID 和 类型*/

        //增量分（近3h点击量 / 50 * 权重2 + 近3h评论量 * 权重4 + 近3h主贴点赞量 * 权重2 + 作者增量调整参数）*新帖调整乘数（新帖2，其他1）

        //存量分：（累计点击量/50*权重4+累计评论量*权重3+累计主贴点赞量*权重2+作者存量调整参数）*时间衰减系数（权重运营可调）


            /*
             *when (b.tockentype in ('3','4','6') or b.tockentype is null) and ffn<=6 then  power(power(0.6,1/6),ffn)				--优质帖作者/普通作者，3天衰减到0.6
        when (b.tockentype in ('3','4','6')or b.tockentype is null) and ffn<=14 then 0.6*power(power(0.4/0.6,1/8),ffn-6)		--优质帖作者/普通作者，3-7天衰减从0.6到0.4
        when (b.tockentype in ('3','4','6')or b.tockentype is null) and ffn>14 then 0.4*power(0.9,ffn-14)					--优质帖作者/普通作者，7天后每次峰值*0.9
        when b.tockentype='5' then power(least(replyaddscore,10)+1,1/3)*power(0.5,ffn)	end	tamn	ffn：经历高峰期次数
             *
             */


        //  存量分量级调整系数： 增量分求和 / 存量分求和

        int maxClick = 5000;
        int maxPinglun = 200;
        int maxLikeCount = 500;


        if (post != null) {
            String groupName = StringUtil.isNull(WeightConfigModel.getGroupName()) ? "A" : WeightConfigModel.getGroupName().toUpperCase();
            /**
             * 增量分
             */
            PostCalScore incrScoreItem = new PostCalScore();
            //新帖调整乘数
            double newPostRatio = PostRatio(postTime, WeightConfigModel);

            //作者增量调整参数
            double userIncrScroe = getIncrUserScore(uid, WeightConfigModel);

//            logger.info(logPre + "新帖调整乘数计算01:" + post.getID() + ",发帖时间:" + DateUtil.dateToStr(postTime) + ",新帖调整乘数:" + newPostRatio + ",uid=>" + uid + ",userIncrScroe=>" + userIncrScroe);

            //增量分
            double incrScore = ((double) post.CLICKNUM / 50 * WeightConfigModel.ClickNum_Last3h +
                    post.PINGLUNNUM * WeightConfigModel.ReplyNum_Last3h +
                    post.LIKECOUNT * WeightConfigModel.LikeNum_Last3h + userIncrScroe) * newPostRatio;

//            logger.info(logPre + "=>帖子ID:{" + post.ID + "},增量分:(" + post.CLICKNUM + " / 50 * " + WeightConfigModel.ClickNum_Last3h + " +" + post.PINGLUNNUM + " * " + WeightConfigModel.ReplyNum_Last3h + " +" + post.LIKECOUNT + " * " + WeightConfigModel.LikeNum_Last3h + " + " + userIncrScroe + ") * " + newPostRatio + ",增量分:" + incrScore);

            incrScoreItem.ID = post.ID;
            incrScoreItem.Score = incrScore;

            if (DicIncrScoreList.containsKey(groupName)) {
                List<PostCalScore> postCalScoreList = DicIncrScoreList.get(groupName);
                postCalScoreList.add(incrScoreItem);
            } else {
                //增量分
                List<PostCalScore> postCalScoreList = new ArrayList<>();
                postCalScoreList.add(incrScoreItem);

                DicIncrScoreList.put(groupName, postCalScoreList);
            }


            /**
             * 存量分计算=========================
             */

            //作者存量调整参数
            double userHisScore = getHisUserScore(uid, WeightConfigModel);

            //帖子经历高峰期次数
            int ffn = FFN(postTime);
            //时间衰减系数
            double _TimeDecayRatio = timeDecayRatio(uid, post.PINGLUNNUM, ffn, WeightConfigModel);

//            logger.info(logPre + "=>时间衰减系数:" + post.ID + ",发帖时间：" + DateUtil.dateToStr(postTime) + ",帖子评论数增量:{" + post.PINGLUNNUM + "},经历高峰期次数:{" + ffn + "},时间衰减系数:{" + _TimeDecayRatio + "}");
            //存量分
            Double hisScroere = (Math.min(post.TotalCLICKNUM, maxClick) / 50 * WeightConfigModel.ClickNum_Total +
                    Math.min(post.PINGLUNNUM, maxPinglun) * WeightConfigModel.ReplyNum_Total +
                    Math.min(post.TotalLIKECOUNT, maxLikeCount) * WeightConfigModel.LikeNum_Total + userHisScore) * _TimeDecayRatio;

//            logger.info(logPre+"帖子ID:{" + post.ID + "},存量分：(" + post.TotalCLICKNUM + " / 50 * " + WeightConfigModel.ClickNum_Total + " +" + post.PINGLUNNUM + "* " + WeightConfigModel.ReplyNum_Total + " +" + post.TotalLIKECOUNT + " * " + WeightConfigModel.LikeNum_Total + " + " + userHisScore + ") *" + _TimeDecayRatio + "，得分：" + hisScroere);
            PostCalScore hisScoreItem = new PostCalScore();
            hisScoreItem.ID = post.ID;
            hisScoreItem.Score = hisScroere;
            //存量分

            if (DicHisScoreList.containsKey(groupName)) {
                DicHisScoreList.get(groupName).add(incrScoreItem);
            } else {
                List<PostCalScore> templist = new ArrayList<>();
                templist.add(incrScoreItem);
                //增量分
                DicHisScoreList.put(groupName, templist);
            }


            double _HisScoreRatio = getHisScoreRatio(WeightConfigModel);
            double total = incrScore + hisScroere * _HisScoreRatio;
            //来源于配置后台的精华帖 增加分数
            if (elitePostSource.getValue() == EnumElitePostSource.BackGroundConfig.getValue()) {
                total = total + WeightConfigModel.ElitePostScore;
            }

//            logger.info(logPre+"帖子ID:" + post.ID + ",增量分:" + incrScore + ",存量分:" + hisScroere + ",存量分系数:" + _HisScoreRatio + "，总分:" + total);

            return total;
        }
        return 0;
    }

    /// <summary>
    /// 是否是新帖
    /// </summary>
    /// <param name="postTime"></param>
    /// <returns></returns>
    private double PostRatio(Date postTime, FaxianWeightModel WeightConfigModel) {

        boolean isNewPost = findRecommendPostDao.isNewPost(postTime);
        if (isNewPost) {
            return WeightConfigModel.NewPost;
        }
        return WeightConfigModel.OldPost;
    }

    /**
     * 作者增量调整参数
     *
     * @param uid
     * @param WeightConfigModel
     * @return
     */
    private double getIncrUserScore(String uid, FaxianWeightModel WeightConfigModel) {
        if (StringUtil.isNull(uid)) return 0;
            /*
             如果是标记过的优质作者+10，刷屏等需降权作者-10
             */
        boolean isBlackUser = isBlackUser(uid);
        if (isBlackUser) {
            return WeightConfigModel.BlackAuthor;
        }

        boolean isFineAuthor = isFineAuthor(uid);
        return isFineAuthor ? WeightConfigModel.FineAuthor : 0;
    }

    /**
     * 判断用户是否是黑名单用户
     *
     * @param uid
     * @return
     */
    private boolean isBlackUser(String uid) {
        String key = uid + "_" + EnumAuthorType.BlackUser.getValue();
        boolean isblackuser = DicAuthorType != null && DicAuthorType.containsKey(key);
        return isblackuser;
    }

    /**
     * 是否是优秀作者(优质帖获奖作者,回答获奖作者)
     *
     * @param uid
     * @return
     */
    private Boolean isFineAuthor(String uid) {
        String key1 = uid + "_" + EnumAuthorType.YZTHJAuthor.getValue();
        String key2 = uid + "_" + EnumAuthorType.AnswerHJAuthor.getValue();

        if (DicAuthorType != null && (DicAuthorType.containsKey(key1) || DicAuthorType.containsKey(key2))) {
            return true;
        }
        return false;
    }

    /**
     * 是否是解盘作者
     *
     * @param uid
     * @return
     */
    private boolean isJiePanAuthor(String uid) {
            /*
             /*
             1	财富号引导成功
             2	潜质作者
             3	优质帖获奖作者
             4	回答获奖作者
             5	解盘贴作者
             6	刷屏的作者
            */
        String key = uid + "_" + EnumAuthorType.JPAuthor.getValue();
        if (DicAuthorType != null && DicAuthorType.containsKey(key)) {
            return true;
        }
        return false;

    }

    /**
     * 作者存量调整参数
     *
     * @param uid
     * @param WeightConfigModel
     * @return
     */
    private double getHisUserScore(String uid, FaxianWeightModel WeightConfigModel) {
        if (StringUtil.isNull(uid)) return 0;
        boolean isBlackUser = isBlackUser(uid);
        return isBlackUser ? WeightConfigModel.HisBlackAuthor : 0;
    }

    /**
     * 计算从发帖到目前经过多少次高峰时间段
     * 每天有两个高峰时间段
     *
     * @param postTime 发帖时间
     * @return
     */
    public int FFN(Date postTime) {

        /*
         * 1.发帖当日经历高峰期时间段
         * 2.当前日期经历过高峰时间段
         * 3.（当前-发帖日）*2
         */
        int postDateCount = 0;
        int curentDateCount = 0;
        //相隔整数天数的高峰时间段次数
        int durDateCount = 0;

        /*1.发帖当日经历高峰期时间段*/
        postDateCount = getPostDateGFTimes(postTime);

        /*2.当前日期经历过高峰时间段*/
        curentDateCount = getCurentDateGFTimes(postTime);

        /*3.（当前-发帖日）*2*/
        double subDays = DateUtil.getDistanceHourOfTwoDate(DateUtil.getNowDate(), postTime);
        durDateCount = (int) (Math.abs(subDays) - 1) * 2;


        return postDateCount + curentDateCount + durDateCount;

    }

    private int getPostDateGFTimes(Date postTime) {

        //当前日期
        //如果在当天14点前发帖 经历2次高峰
        Date curDate = DateUtil.getNowDate("yyyy-MM-dd");
        Date tempDate1 = DateUtil.calendarDateByHour(curDate, 14);
        if (postTime.compareTo(tempDate1) < 1) {
            return 2;
        }

        //如果在 14-21之间发帖 经历过一次高峰
        Date tempDate2 = DateUtil.calendarDateByHour(curDate, 21);
        if (postTime.compareTo(tempDate1) < 1) {
            return 1;
        }
        return 0;
//        //如果在当天14点前发帖 经历2次高峰
//        if (postTime <= postTime.Date.AddHours(14))
//        {
//            return 2;
//        }
//        //如果在 14-21之间发帖 经历过一次高峰
//        if (postTime <= postTime.Date.AddHours(21))
//        {
//            return 1;
//        }
//        return 0;
    }

    private int getCurentDateGFTimes(Date postTime) {

        Date curDate = DateUtil.getNowDate("yyyy-MM-dd");
        Date postDate = DateUtil.getDate(postTime, "yyyy-MM-dd");
        if (postDate.compareTo(curDate) < 0) {
            return 0;
        }

        /**
         * 当前时间
         */
        Date curTime = DateUtil.getNowDate();
        int hour = DateUtil.getHour(curTime);
        if (hour >= 22) {
            return 2;
        }
        if (hour >= 15) {
            return 1;
        }
        return 0;
//
//        if (DateTime.Now.Hour >= 22)
//        {
//            return 2;
//        }
//        if (DateTime.Now.Hour >= 15)
//        {
//            return 1;
//        }
//        return 0;
    }

    /**
     * 时间衰减系数
     *
     * @param uid
     * @param incryReplyCount
     * @param ffn
     * @param WeightConfigModel
     * @return
     */
    public double timeDecayRatio(String uid, int incryReplyCount, int ffn, FaxianWeightModel WeightConfigModel) {
            /*
             1	财富号引导成功
             2	潜质作者
             3	优质帖获奖作者
             4	回答获奖作者
             5	解盘贴作者
             6	刷屏的作者
            */
            /*
             * 衰减系数：
               解盘作者：power(least(评论增量+1,10)+1,1/3)*power(0.5,ffn)
               统计区间内有讨论的乘数：min(评论增量,10)+1 开3次方（1.26~2.22）
               衰减乘数0.5
               其他作者：开方衰减 3天衰减到0.6，7天衰减到0.4，以后每次峰值*0.9
             */
        Boolean isJiePanAuthor = isJiePanAuthor(uid);
        double _TimeDecayRatio = 0;
        /*
         * 解盘贴作者
         */
        if (isJiePanAuthor) {
            if (WeightConfigModel.TimeDecayJiePanAuthorDec < 1 && ffn > 20) {
                return _TimeDecayRatio;
            }

            _TimeDecayRatio = Math.pow(Math.min(incryReplyCount + 1, 10) + 1, WeightConfigModel.TimeDecayJiePanAuthorIncr) * Math.pow(WeightConfigModel.TimeDecayJiePanAuthorDec, ffn);
        } else {
            //衰减超过20次 直接返回0，因为通过计算会返回一个趋近于0的数，计算没有意义
            if (ffn >= 20) {
                return _TimeDecayRatio;
            }
            /*其他作者*/
            if (ffn <= WeightConfigModel.TimeDecayOtherAuthorFFN) {

                _TimeDecayRatio = Math.pow(Math.pow(WeightConfigModel.TimeDecayOtherAuthorFFNVal, (double) 1 / WeightConfigModel.TimeDecayOtherAuthorFFN), ffn);
            } else if (ffn <= WeightConfigModel.TimeDecayOtherAuthorFFN2) {
                _TimeDecayRatio = WeightConfigModel.TimeDecayOtherAuthorFFNVal * Math.pow(Math.pow(WeightConfigModel.TimeDecayOtherAuthorFFN2Val / WeightConfigModel.TimeDecayOtherAuthorFFNVal, (double) 1 / (WeightConfigModel.TimeDecayOtherAuthorFFN2 - WeightConfigModel.TimeDecayOtherAuthorFFN)), ffn);
            } else if (ffn > WeightConfigModel.TimeDecayOtherAuthorFFN2) {
                _TimeDecayRatio = WeightConfigModel.TimeDecayOtherAuthorFFN2Val * Math.pow(WeightConfigModel.TimeDecayOtherAuthorChenShu, ffn - WeightConfigModel.TimeDecayOtherAuthorFFN2);
            }
        }

        return _TimeDecayRatio;

    }

    /**
     * 获取存量分系数
     *
     * @param weightConfigModel
     * @return
     */
    private double getHisScoreRatio(FaxianWeightModel weightConfigModel) {
        //存量分系数
        double _HisScoreRatio = 1;
        if (weightConfigModel == null) return _HisScoreRatio;
        try {
            String groupName = StringUtil.isNull(weightConfigModel.GroupName) ? "A" : weightConfigModel.GroupName.toUpperCase();
            String cacheKey = String.format(cacheKeyNew, groupName);
            String value = appCore.redisuserread.get(cacheKey);
            if (!StringUtil.isNull(value)) {
                _HisScoreRatio = Double.parseDouble(value);
            }

//            var value = redisHelper.Get<string>(cacheKey);
//            if (!String.IsNullOrEmpty(value))
//            {
//                _HisScoreRatio = Convert.ToDouble(value);
//            }

        } catch (Exception ex) {
            logger.error(logPre + "=>getHisScoreRatio=>" + ex.getMessage(), ex);
        }

        return _HisScoreRatio;
    }

    /**
     * 计算存量分系数
     */
    private void calHisScoreRatio() {
        try {


            //当前时间
            Date curDateTime = DateUtil.getNowDate();
            //当前日期
            Date curDate = DateUtil.getDate(curDateTime, "yyyy-MM-dd");
            //当前小时
            int hour = DateUtil.getHour(curDateTime);

            if (hour > 15 && hour < 23) {

                String methodName = "CalHisScoreRatio";
                Date lastTime = userRedisDao.getBreakTime(methodName);
                if (lastTime == null) {
                    lastTime = DateUtil.calendarDateByHour(-48);
                }
                //每日更新一次，如果当日更新过无需再次更新
                if (lastTime.compareTo(curDate) > 0) {
                    return;
                }
                lastTime = curDateTime;

                if (!CollectionUtils.isEmpty(WeightConfigModelGroupList)) {
                    for (FaxianWeightModel item : WeightConfigModelGroupList) {
                        if (!StringUtil.isNull(item.getGroupName())) {
                            String groupName = item.getGroupName();
                            List<PostCalScore> incrScoreList = new ArrayList<>();
                            if (DicIncrScoreList.containsKey(groupName)) {
                                incrScoreList = DicIncrScoreList.get(groupName);
                            }

                            List<PostCalScore> hisScoreList = new ArrayList<>();
                            if (DicHisScoreList.containsKey(groupName)) {
                                hisScoreList = DicHisScoreList.get(groupName);
                            }

                            if (!CollectionUtils.isEmpty(incrScoreList) && !CollectionUtils.isEmpty(hisScoreList)) {
                                //增量得分大于0的帖子列表
                                List<PostCalScore> tempListIncr = incrScoreList.stream().filter(a -> a.getScore() > 0).
                                        sorted(Comparator.comparing(PostCalScore::getScore)).collect(Collectors.toList());
                                //存量得分大于0的帖子列表
                                List<PostCalScore> tempListHis = hisScoreList.stream().filter(a -> a.getScore() > 0).
                                        sorted(Comparator.comparing(PostCalScore::getScore)).collect(Collectors.toList());


                                int incrIndex1 = (int) Math.ceil(tempListIncr.size() * 0.3);
                                int incrIndex2 = (int) Math.ceil(tempListIncr.size() * 0.5);


                                int hisIndex1 = (int) Math.ceil(tempListHis.size() * 0.3);
                                int hisIndex2 = (int) Math.ceil(tempListHis.size() * 0.5);


                                List<PostCalScore> incrList1 = tempListIncr.subList(incrIndex1, incrIndex2);
                                List<PostCalScore> hisList1 = tempListHis.subList(hisIndex1, hisIndex2);
                                // LogHelper.LogInfo("CalHisScoreRatio=>incrList1:" + (incrList1 == null ? 0 : incrList1.Count) + ",hisList1:" + (hisList1 == null ? 0 : hisList1.Count), LogFileName);
                                if (!CollectionUtils.isEmpty(incrList1) && !CollectionUtils.isEmpty(hisList1)) {
                                    //ist.stream().mapToDouble(User::getHeight).sum();
                                    Double incrTotalScore = incrList1.stream().mapToDouble(PostCalScore::getScore).sum();
                                    Double hisTotalScore = hisList1.stream().mapToDouble(PostCalScore::getScore).sum();

                                    if (incrTotalScore > 0 && hisTotalScore > 0) {
                                        //
                                        String result = String.format("%.3f", (incrTotalScore / hisTotalScore));

                                        String cacheKey = String.format(cacheKeyNew, groupName);
                                        Long seconds = 60 * 60 * 24L;
                                        appCore.redisuserwrite.set(cacheKey, result, seconds);
                                        CacheHelper.put(cacheKey, result, seconds * 1000);
                                        userRedisDao.setBreakTime(methodName, lastTime);

//                                        logger.info(logPre + "=>CalHisScoreRatio=>计算存量分系数=>" + incrTotalScore + "/" + hisTotalScore + "=" + result);
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                logger.info(logPre + "calHisScoreRatio=>不在执行时间范围，暂不执行");
            }
        } catch (Exception e) {
            logger.error(logPre + "calHisScoreRatio=>异常:" + e.getMessage(), e);
        }
    }


    /**
     * 清除历史数据
     */
    public void clearHisData() {
        try {
            String breakTimeName = "findrecommendClearHisData";
            Date lastUpdateTime = userRedisDao.getBreakTime(breakTimeName);
            if (lastUpdateTime == null) {
                lastUpdateTime = DateUtil.calendarDateByDays(-1);
            }


            if (DateUtil.getNowDate().getHours() >= 2
                    && DateUtil.getNowDate().getHours() <= 5
                    && lastUpdateTime.compareTo(DateUtil.getNowDate("yyyy-MM-dd")) <= 0) {

                logger.info(logPre + "ClearHisData=>清除历史数据=>开始");
                Date startDate = DateUtil.calendarDateByMonth(-12);
                long timepoint = DateUtil.getTimePoint(startDate);
                //清除一年前的数据
                boolean result = findRecommendPostDao.remove(timepoint);
                if (!result) {
                    result = findRecommendPostDao.remove(timepoint);
                }
                userRedisDao.setBreakTime(breakTimeName, DateUtil.getNowDate());
                logger.info(logPre + "ClearHisData=>清除历史数据=>完成");
            } else {
                logger.info(logPre + "ClearHisData=>不在执行时间范围，暂不执行");
            }
        } catch (Exception ex) {
            logger.error(logPre + "ClearHisData=>" + ex.getMessage());
        }
    }

    /**
     * 计算历史数据
     */
    public void calHisData() {
        try {
            logger.info(logPre + "CalHisData=>-----------【历史数据重新计算开始】-----------------------");

            String breakPointName = "findrecommendCalHisDataNew4";
            Date lastUpdatetime = userRedisDao.getBreakTime(breakPointName);
            if (lastUpdatetime == null) {
                lastUpdatetime = DateUtil.calendarDateByDays(-1);
            }

            logger.info(logPre + "CalHisData=>上次断点：" + DateUtil.dateToStr(lastUpdatetime));
            int startHour = 1, endHour = 4;

            if (DateUtil.getNowDate().getHours() >= startHour
                    && DateUtil.getNowDate().getHours() <= endHour
                    && lastUpdatetime.compareTo(DateUtil.getNowDate("yyyy-MM-dd")) <= 0) {
                Date time = DateUtil.calendarDateByDays(-15);
                long startTimePoint = DateUtil.getTimePoint(time);

                long endTimePoint = DateUtil.getTimePoint(DateUtil.getNowDate("yyyy-MM-dd"));
                int pageSize = 1000;
                //清除0分的或者一年前的帖子数据
                logger.info(logPre + "CalHisData=>[000]=>历史数据重新计算开始=>时间断点：开始时间：" + startTimePoint + ",结束时间：" + endTimePoint);
                while (true) {

                    if (DateUtil.getNowDate().getHours() < startHour || DateUtil.getNowDate().getHours() > endHour) {
                        break;
                    }
                    List<PostRecommendModel> list = findRecommendPostDao.get(startTimePoint, endTimePoint, pageSize);
                    if (CollectionUtils.isEmpty(list)) {
                        break;
                    }
                    //记录最大时间
                    startTimePoint = list.stream().max(Comparator.comparing(PostRecommendModel::getTIMEPOINT))
                            .get().getTIMEPOINT();
                    logger.info(logPre + "CalHisData=>[003]=>本轮获取帖子，涉及数量:" + list.size() + ",断点:" + startTimePoint);
                    //重新计算分数
                    List<String> listPostIds = list.stream().map(a -> a.getID().toString()).collect(Collectors.toList());
                    //来源数据为人工筛选的帖子
                    List<PostRecommendModel> configPostList = list.stream().filter(a -> a.SOURCE != null && a.SOURCE == EnumElitePostSource.BackGroundConfig.getValue()).collect(Collectors.toList());
                    List<String> configPostIds = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(configPostList)) {
                        configPostIds = configPostList.stream().map(a -> a.getID().toString()).collect(Collectors.toList());
                    }
                    int count = postFindScoreByGroup(listPostIds, configPostIds);
                    logger.info(logPre + "CalHisData=>[004]=>历史数据重新计算，涉及数量:" + count);

                    if (list.size() < pageSize) {
                        break;
                    }
                    logger.info(logPre + "CalHisData=>[1007]");
                }
                //设置当前时间为断点
                lastUpdatetime = DateUtil.getNowDate();
                userRedisDao.setBreakTime(breakPointName, lastUpdatetime);
                logger.info(logPre + "CalHisData=>下次断点：" + DateUtil.dateToStr(lastUpdatetime));
            } else {
                logger.info(logPre + "CalHisData=>不在执行时间范围");
            }
            logger.info(logPre + "CalHisData=>-----------【历史数据重新计算完成】--------------");

        } catch (Exception ex) {
            logger.error(logPre + "CalHisData=>" + ex.getMessage());
        }
    }

    private void videoArtInit() {
        try {

            logger.info(logPre + "videoArtInit=>视频文章开始同步");

            int limit = 1000;
            List<String> newsids = new ArrayList<>();
            List<String> delnewsids = new ArrayList<>();
            String breakTime = "videoArtbreaktimeNew";
            Date lastUpdatetime = userRedisDao.getBreakTime(breakTime);
            if (lastUpdatetime == null) {
                lastUpdatetime = DateUtil.calendarDateByDays(-3);
            }
            List<VideoArticleModel> list = videoArticleDao.getByLastUpdateTime(lastUpdatetime, EnumVideoArticleSource.CFHVIDEOART.getValue(), limit);
            if (list != null && list.size() > 0) {
                lastUpdatetime = list.stream().filter(a -> a.getLastUpdateTime() != null).max(Comparator.comparing(VideoArticleModel::getLastUpdateTime))
                        .get().getLastUpdateTime();
                for (VideoArticleModel item : list) {
                    if (item.getIsClosed() == 0 && item.getIsDeleted() == 0) {
                        newsids.add(item.getPostID());
                    } else {
                        delnewsids.add(item.getPostID());
                    }
                }

                String[] fileds = new String[]{"_id", "ID", "UID", "CODE", "TIME", "TIMEPOINT", "YUANID", "QID", "TYPE", "YUANTYPE", "DEL", "CODELIST", "TTJJDEL"};
                /**
                 * 需要删除的帖子
                 */
                if (!CollectionUtils.isEmpty(delnewsids)) {
                    List<PostRecommendModelExt> delpostInfos = postDao.getListFromMongByNewsids(delnewsids, fileds, PostRecommendModelExt.class);
                    for (PostRecommendModelExt item : delpostInfos) {
                        boolean result = findRecommendPostDao.remove(item._id);
                        if (!result) {
                            findRecommendPostDao.remove(item._id);
                        }
                    }
                }

                //有效的帖子信息
                if (!CollectionUtils.isEmpty(newsids)) {
                    List<PostRecommendModelExt> postInfos = postDao.getListFromMongByNewsids(newsids, fileds, PostRecommendModelExt.class);
                    for (PostRecommendModelExt item : postInfos) {
                        /**
                         * 黑明单用户过滤
                         */
                        if (!isBlackUser(item.getUID())) {
                            Map<String, Object> map = mapPostInfo(item, EnumElitePostSource.Default, 1);
                            boolean rsutlt = findRecommendPostDao.upsert(map);
                            if (!rsutlt) {
                                rsutlt = findRecommendPostDao.upsert(map);
                            }
                        }
                    }
                    logger.info(logPre + "=>videoArtInit=>videoArtInitsavedata:" + JacksonUtil.obj2String(postInfos));
                }
                //设置断点
                userRedisDao.setBreakTime(breakTime, lastUpdatetime);
            }
            logger.info(logPre + "videoArtInit=>finishjobvideoArtInit=>resultdata：" + newsids.size() + ",删除数量：" + delnewsids.size());
        } catch (Exception ex) {
            logger.error(logPre + "videoArtInit=>videoArtInitERROR" + ex.getMessage(), ex);
        }
    }

    /**
     * @param postInfo       帖子信息
     * @param elitPostSource 精华帖来源
     * @param isVideoArt     是否是视频文章  1： 是
     * @return
     */
    private Map<String, Object> mapPostInfo(PostRecommendModelExt postInfo, EnumElitePostSource elitPostSource, int isVideoArt) {

        Map<String, Object> dicPostInfo = new HashMap<>();

        dicPostInfo.put("CODE", postInfo.getCODE());
        dicPostInfo.put("DEL", postInfo.getDEL());
        dicPostInfo.put("ID", postInfo.getID());
        dicPostInfo.put("QID", postInfo.getQID());
        dicPostInfo.put("TIME", postInfo.getTIME());
        dicPostInfo.put("TIMEPOINT", postInfo.getTIMEPOINT());
        dicPostInfo.put("TYPE", postInfo.getTYPE());
        dicPostInfo.put("UID", postInfo.getUID());
        dicPostInfo.put("UPDATETIME", DateUtil.getNowDate());
        dicPostInfo.put("YUANID", postInfo.getYUANID());
        dicPostInfo.put("YUANTYPE", postInfo.getYUANTYPE());
        dicPostInfo.put("_id", postInfo._id);
        //这里标记是否是视频文章
        dicPostInfo.put("IsVideoArt", isVideoArt);

        //精华帖来源
        dicPostInfo.put("SOURCE", elitPostSource.getValue());

        //设置大V用户
        if (dicVTagUserInfo != null) {
            String postUid = dicVTagUserInfo.get(postInfo.getUID());
            int isConfigV = StringUtil.isNotNull(postUid) ? 1 : 0;
            dicPostInfo.put("IsConfigV", isConfigV);
        }

        if (!CollectionUtils.isEmpty(WeightConfigModelGroupList)) {
            //帖子统计信息
            PostRecommendExtendModel postCountInfo = postCountIncrByTimeDao.getById(postInfo._id);
            //根据分组计算分数
            dicPostInfo.put("FINDSCORE", Long.parseLong(DateUtil.dateToStr(postInfo.getTIME(), "yyyyMMddHHmm")));
            for (FaxianWeightModel item : WeightConfigModelGroupList) {
                if (StringUtil.isNotNull(item.GroupName)) {
                    String _groupName = item.getGroupName().toUpperCase();
                    String mapKeyName = findRecommendPostDao.getFindScoreFiledName(_groupName);
                    if (postCountInfo != null) {
                        dicPostInfo.put(mapKeyName, (int) calScore(postCountInfo, postInfo.getUID(), postInfo.getTIME(), item, elitPostSource));
                    } else {
                        dicPostInfo.put(mapKeyName, -99);
                    }
                }
            }
        } else {
            dicPostInfo.put(findRecommendPostDao.getFindScoreFiledName("A"), -88);
        }
        return dicPostInfo;
    }


    /**
     * 晒收益话题下的帖子
     *
     * @return
     */
    private List<String> showProfitHTPost() {

        try {
            logger.info(logPre + "showProfitHTPost=>开始");
            String breakTime = "showProfitHTPostnew";
            Date lastUpDate = userRedisDao.getBreakTime(breakTime);
            if (lastUpDate == null) {
                lastUpDate = DateUtil.calendarDateByDays(-3);
            }
            String htid = appConstant.recommendpost_htid;
            if (!NullUtil.isNull(htid)) {
                int clickNum = 500, limit = 10000;
                if (!NullUtil.isNull(appConstant.recommendpost_htid_postcliknum)) {
                    clickNum = Integer.parseInt(appConstant.recommendpost_htid_postcliknum);
                }
                //lastUpDate=DateUtil.calendarDateByDays(-30);
                List<PostinfoExtraModel> list = postInfoExtraDao.selectByHtidandClickNum(htid, lastUpDate, clickNum, limit);
                if (!CollectionUtils.isEmpty(list)) {
                    lastUpDate = list.stream().max(Comparator.comparing(PostinfoExtraModel::getUPDATETIME))
                            .get().getUPDATETIME();
                    List<String> postIds = list.stream().map(a -> String.valueOf(a.getID())).collect(Collectors.toList());
//                    logger.info(logPre + "showProfitHTPost=>涉及数量:" + postIds.size() + ",涉及数据：" + postIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
                    return postIds;
                }
            }
        } catch (Exception e) {
            logger.error(logPre + "showProfitHTPost=>" + e.getMessage(), e);

        }
        logger.info(logPre + "showProfitHTPost=>结束");
        return null;
    }

}
