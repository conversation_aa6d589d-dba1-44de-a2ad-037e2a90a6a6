package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfo;
import ttfund.web.communityservice.bean.jijinBar.post.ai.AiSingleSummaryModel;
import ttfund.web.communityservice.bean.jijinBar.post.ai.BigVAiTaskResponse;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.CalBigVDataUserModel;
import ttfund.web.communityservice.dao.mongo.AiSingleSummariesDao;
import ttfund.web.communityservice.dao.mongo.PostInfoMongoDao;
import ttfund.web.communityservice.dao.msyql.CalBigVDataUserDao;
import ttfund.web.communityservice.service.BigVAiSummaryService;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.Calendar;

/**
 * 大V帖子内容AI总结定时任务
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@JobHandler("BigVPostAiSummaryJob")
@Component
public class BigVPostAiSummaryJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(BigVPostAiSummaryJob.class);

    @Autowired
    private CalBigVDataUserDao calBigVDataUserDao;

    @Autowired
    private PostInfoMongoDao postInfoMongoDao;

    @Autowired
    private AiSingleSummariesDao aiSingleSummariesDao;

    @Autowired
    private BigVAiSummaryService bigVAiSummaryService;

    private static final int MAX_POLL_ATTEMPTS = 60; // 最大轮询次数（60次 * 5秒 = 300秒）
    private static final int POLL_INTERVAL_SECONDS = 5; // 轮询间隔（秒）

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            logger.info("开始执行大V帖子AI总结任务，参数：{}", param);

            // 解析参数
            Date targetDate = parseTargetDate(param);
            
            // 获取所有有效的大V用户
            List<CalBigVDataUserModel> bigVUsers = calBigVDataUserDao.getAllActiveBigVUsers();
            if (CollectionUtils.isEmpty(bigVUsers)) {
                logger.warn("未找到有效的大V用户");
                return ReturnT.SUCCESS;
            }

            logger.info("找到{}个大V用户", bigVUsers.size());

            int processedCount = 0;
            int successCount = 0;

            for (CalBigVDataUserModel bigVUser : bigVUsers) {
                try {
                    int userProcessedCount = processBigVUser(bigVUser, targetDate);
                    processedCount += userProcessedCount;
                    if (userProcessedCount > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    logger.error("处理大V用户失败，UID: {}, NickName: {}", 
                            bigVUser.getUID(), bigVUser.getNickName(), e);
                }
            }

            logger.info("大V帖子AI总结任务执行完成，处理用户数：{}/{}, 总处理帖子数：{}", 
                    successCount, bigVUsers.size(), processedCount);

            return ReturnT.SUCCESS;

        } catch (Exception e) {
            logger.error("大V帖子AI总结任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 处理单个大V用户
     */
    private int processBigVUser(CalBigVDataUserModel bigVUser, Date targetDate) {
        String uid = bigVUser.getUID();
        String nickName = bigVUser.getNickName();
        
        logger.info("开始处理大V用户：{} ({})", nickName, uid);

        // 获取当天的帖子
        Date startTime = getDayStart(targetDate);
        Date endTime = getDayEnd(targetDate);
        
        List<PostInfo> posts = postInfoMongoDao.getPostsByUidAndTimeRange(uid, startTime, endTime);
        if (CollectionUtils.isEmpty(posts)) {
            logger.info("大V用户 {} 当天无帖子", nickName);
            return 0;
        }

        logger.info("大V用户 {} 当天共有{}个帖子", nickName, posts.size());

        // 过滤已处理的帖子
        List<String> postIds = posts.stream().map(post -> post._id).collect(Collectors.toList());
        List<AiSingleSummaryModel> existingSummaries = aiSingleSummariesDao.findByPostIds(postIds);
        Set<String> processedPostIds = existingSummaries.stream()
                .map(AiSingleSummaryModel::getPostId)
                .collect(Collectors.toSet());

        List<PostInfo> unprocessedPosts = posts.stream()
                .filter(post -> !processedPostIds.contains(post._id))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(unprocessedPosts)) {
            logger.info("大V用户 {} 当天所有帖子都已处理", nickName);
            return 0;
        }

        logger.info("大V用户 {} 需要处理{}个未处理的帖子", nickName, unprocessedPosts.size());

        int processedCount = 0;
        for (PostInfo post : unprocessedPosts) {
            try {
                if (processPost(bigVUser, post)) {
                    processedCount++;
                }
                
                // 避免请求过于频繁
                Thread.sleep(1000);

            } catch (Exception e) {
                logger.error("处理帖子失败，PostId: {}, UID: {}", post._id, uid, e);
            }
        }

        logger.info("大V用户 {} 处理完成，成功处理{}个帖子", nickName, processedCount);
        return processedCount;
    }

    /**
     * 处理单个帖子
     */
    private boolean processPost(CalBigVDataUserModel bigVUser, PostInfo post) {
        String postId = post._id;
        String uid = bigVUser.getUID();
        String nickName = bigVUser.getNickName();
        String title = post.TITLE;
        String content = post.CONTENTEND;

        logger.info("开始处理帖子：{}, 标题：{}", postId, title);

        // 格式化消息
        String message = bigVAiSummaryService.formatMessage(nickName, title, content);
        
        // 提交AI任务
        BigVAiTaskResponse taskResponse = bigVAiSummaryService.submitSingleSummaryTask(message);
        if (taskResponse == null || !StringUtils.hasLength(taskResponse.getTaskId())) {
            logger.error("提交AI任务失败，PostId: {}", postId);
            return false;
        }

        String taskId = taskResponse.getTaskId();
        logger.info("AI任务提交成功，TaskId: {}, PostId: {}", taskId, postId);

        // 轮询获取结果
        BigVAiTaskResponse result = pollForResult(taskId);
        if (result == null) {
            logger.error("轮询AI任务结果失败，TaskId: {}, PostId: {}", taskId, postId);
            return false;
        }

        // 保存结果
        return saveAiSummary(bigVUser, post, taskId, result);
    }

    /**
     * 轮询获取AI任务结果
     */
    private BigVAiTaskResponse pollForResult(String taskId) {
        for (int attempt = 1; attempt <= MAX_POLL_ATTEMPTS; attempt++) {
            try {
                Thread.sleep(POLL_INTERVAL_SECONDS * 1000);
                
                BigVAiTaskResponse result = bigVAiSummaryService.pollTaskResult(taskId);
                if (result != null) {
                    String status = result.getStatus();
                    if ("COMPLETED".equals(status)) {
                        logger.info("AI任务完成，TaskId: {}, 耗时: {}ms", taskId, result.getProcessingTimeMs());
                        return result;
                    } else if ("PROCESSING".equals(status)) {
                        logger.info("AI任务处理中，TaskId: {}, 第{}次轮询", taskId, attempt);
                        continue;
                    } else {
                        logger.error("AI任务状态异常，TaskId: {}, Status: {}", taskId, status);
                        return null;
                    }
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("轮询被中断，TaskId: {}", taskId);
                return null;
            } catch (Exception e) {
                logger.error("轮询异常，TaskId: {}, 第{}次轮询", taskId, attempt, e);
            }
        }
        
        logger.error("轮询超时，TaskId: {}", taskId);
        return null;
    }

    /**
     * 保存AI总结结果
     */
    private boolean saveAiSummary(CalBigVDataUserModel bigVUser, PostInfo post, String taskId, BigVAiTaskResponse result) {
        try {
            AiSingleSummaryModel summary = new AiSingleSummaryModel();
            summary.setPostId(post._id);
            summary.setUid(bigVUser.getUID());
            summary.setNickName(bigVUser.getNickName());
            summary.setSummary(result.getData());
            summary.setTitle(post.TITLE);
            summary.setContent(post.CONTENTEND);
            summary.setTaskId(taskId);
            summary.setStatus(result.getStatus());
            summary.setCreateTime(new Date());
            summary.setUpdateTime(new Date());
            summary.setCREATETIME(new Date()); // 设置CREATETIME字段

            aiSingleSummariesDao.save(summary);
            
            logger.info("AI总结保存成功，PostId: {}, TaskId: {}", post._id, taskId);
            return true;

        } catch (Exception e) {
            logger.error("保存AI总结失败，PostId: {}, TaskId: {}", post._id, taskId, e);
            return false;
        }
    }

    /**
     * 解析目标日期参数
     */
    private Date parseTargetDate(String param) {
        Date targetDate = new Date(); // 默认当天
        
        if (StringUtils.hasLength(param)) {
            try {
                JSONObject jsonParam = JSON.parseObject(param);
                String dateStr = jsonParam.getString("date");
                if (StringUtils.hasLength(dateStr)) {
                    targetDate = DateUtil.strToDate(dateStr, "yyyy-MM-dd");
                }
            } catch (Exception e) {
                logger.warn("解析日期参数失败，使用默认日期：{}", param, e);
            }
        }
        
        logger.info("目标处理日期：{}", DateUtil.dateToStr(targetDate, "yyyy-MM-dd"));
        return targetDate;
    }

    /**
     * 获取一天的开始时间（00:00:00.000）
     */
    private Date getDayStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取一天的结束时间（23:59:59.999）
     */
    private Date getDayEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }
}
