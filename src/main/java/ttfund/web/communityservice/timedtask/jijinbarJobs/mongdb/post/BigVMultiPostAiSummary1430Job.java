package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 大V多机构帖子AI总结定时任务 - 14:30执行
 * 处理当个交易日10:30-14:30之间的发帖文章
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@JobHandler("BigVMultiPostAiSummary1430Job")
@Component
public class BigVMultiPostAiSummary1430Job extends BigVMultiPostAiSummaryBaseJob {

    private static final Logger logger = LoggerFactory.getLogger(BigVMultiPostAiSummary1430Job.class);

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        logger.info("开始执行14:30大V多机构帖子AI总结任务");
        
        // 时间段3：10:30-14:30
        String result = executeMultiSummaryTask(param, 3, 10, 30, 14, 30);
        
        if ("SUCCESS".equals(result)) {
            logger.info("14:30大V多机构帖子AI总结任务执行成功");
            return ReturnT.SUCCESS;
        } else {
            logger.error("14:30大V多机构帖子AI总结任务执行失败");
            return ReturnT.FAIL;
        }
    }
}
