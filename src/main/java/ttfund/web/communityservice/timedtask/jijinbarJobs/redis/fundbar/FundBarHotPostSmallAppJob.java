package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.JsonUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostinfoExtraModel;
import ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.config.HotPostConfigModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.HotPostConfigDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 社区热点小程序服务
 */
@JobHandler("FundBarHotPostSmallAppJob")
@Component
public class FundBarHotPostSmallAppJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(FundBarHotPostSmallAppJob.class);

    @Autowired
    private HotPostConfigDao hotPostConfigDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private PostInfoExtraDao postExtraDao;

    @Autowired
    private ReplyInfoDao replyDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            logicDeal();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }

    public void logicDeal() {
        Date dateTime = DateUtil.calendarDateByMonth(-3);
        //获取帖子列表
        List<HotPostConfigModel> postList = hotPostConfigDao.getList(dateTime);
        LOGGER.error("FundBarHotPostSmallAppJob[社区热点小程序服务]:帖子列表为{}", postList);
        if (CollectionUtils.isEmpty(postList)) {
            return;
        }
        Map<Integer, HotPostConfigModel> postMap = postList.stream().
            collect(Collectors.toMap(model -> Integer.valueOf(model.TID), Function.identity()));
        List<Integer> idList = new ArrayList<>(postMap.keySet());
        //获取帖子ID对应的帖子信息、帖子补充信息、最新回复
        List<PostInfoNewModel> postInfoList = postInfoNewDao.getPostInfoNewByIds(idList, null);
        List<PostinfoExtraModel> postExtraList = postExtraDao.getByIds(idList, null);
        List<ReplyInfoModel> latestReplyList = replyDao.getLatestReply(idList);
        LOGGER.info("帖子详细信息列表:{}, 帖子补充信息列表:{}, 最新回复:{}",
            JsonUtils.toJsonString(postInfoList), JsonUtils.toJsonString(postExtraList), JsonUtils.toJsonString(latestReplyList));

        //均根据帖子ID分组
        Map<Integer, PostInfoNewModel> postInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(postExtraList)) {
            postInfoMap = postInfoList.stream().collect(Collectors.toMap(model -> model.ID, Function.identity()));
        }
        Map<Integer, PostinfoExtraModel> postExtraMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(postExtraList)) {
            postExtraMap = postExtraList.stream().collect(Collectors.toMap(model -> model.ID.intValue(), Function.identity()));
        }
        Map<Integer, ReplyInfoModel> replyInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(latestReplyList)) {
            replyInfoMap = latestReplyList.stream().collect(Collectors.toMap(model -> model.TOPICID, Function.identity()));
        }

        //遍历帖子, 根据帖子详细信息和补充信息进行帖子信息的赋值
        for (Map.Entry<Integer, HotPostConfigModel> entry : postMap.entrySet()) {
            int postId = entry.getKey();
            HotPostConfigModel post = entry.getValue();
            PostInfoNewModel postInfo = postInfoMap.get(postId);
            if (postInfo != null) {
                if (postInfo.DEL == 1) {
                    continue;
                }
                post.TITLE = postInfo.TITLE;
                post.SUMMARY = StringUtils.isEmpty(post.NEWSUMMARY) ? postInfo.SUMMARY : post.NEWSUMMARY;
                post.NEWSUMMARY = "";
                if (StringUtils.isNotEmpty(post.SUMMARY)) {
                    post.SUMMARY = post.SUMMARY.replace("&nbsp;", "");
                }
                ReplyInfoModel reply = replyInfoMap.get(postId);
                post.UPDATETIME = (reply != null && postInfo.HUIFUTIME.compareTo(reply.TIME) < 0) ? reply.TIME : postInfo.HUIFUTIME;
            }
            PostinfoExtraModel postExtra = postExtraMap.get(postId);
            if (postExtra != null) {
                post.CLICKNUM = postExtra.CLICKNUM == null ? 0 : postExtra.CLICKNUM;
                post.LIKECOUNT = postExtra.LIKECOUNT == null ? 0 : postExtra.LIKECOUNT;
                post.PINGLUNNUM = postExtra.PINGLUNNUM == null ? 0 : postExtra.PINGLUNNUM;
            }
        }
        //存入缓存
        Boolean result = app.barredis.set(BarRedisKey.HOT_POST_SMALL_APP, JsonUtils.toJsonString(postList), 120 * 24 * 3600L);
        LOGGER.info("缓存存入结果:{}", result);
    }

}
