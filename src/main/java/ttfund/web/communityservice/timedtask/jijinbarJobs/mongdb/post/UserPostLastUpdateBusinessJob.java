package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.mongo.PassportFundMrgModel;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.UserPostUpdateModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.PassportFundMrgDao;
import ttfund.web.communityservice.dao.mongo.UserPostUpdateInfoDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户帖子更新红点提示job
 */
@JobHandler("UserPostLastUpdateBusinessJob")
@Component
public class UserPostLastUpdateBusinessJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(UserPostLastUpdateBusinessJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private UserPostUpdateInfoDao userPostUpdateInfoDao;

    @Autowired
    private PassportFundMrgDao passportFundMrgDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            String initBreakpoint1 = null;
            String initBreakpoint2 = null;
            Integer batchReadCount = null;
            String fundManagerWhiteList = null;
            Boolean isTradeTimeRun = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint1 = jsonObject.getString("initBreakpoint1");
                initBreakpoint2 = jsonObject.getString("initBreakpoint2");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                fundManagerWhiteList = jsonObject.getString("fundManagerWhiteList");
                isTradeTimeRun = jsonObject.getBoolean("isTradeTimeRun");

            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (isTradeTimeRun == null) {
                isTradeTimeRun = false;
            }

            logger.info("第零步，打印参数。batchReadCount：{}，initBreakpoint1：{}，initBreakpoint2：{}，fundManagerWhiteList：{}，isTradeTimeRun：{}",
                    batchReadCount,
                    initBreakpoint1,
                    initBreakpoint2,
                    fundManagerWhiteList,
                    isTradeTimeRun
            );

            if (StringUtils.hasLength(initBreakpoint1) || StringUtils.hasLength(initBreakpoint2)) {
                if (StringUtils.hasLength(initBreakpoint1)) {
                    userRedisDao.set(UserRedisConfig.USERPOSTLASTUPDATEJOB_SYNCTOMONG_BREAKPOINT, initBreakpoint1, 30 * 24 * 3600L);
                }
                if (StringUtils.hasLength(initBreakpoint2)) {
                    userRedisDao.set(UserRedisConfig.USERPOSTLASTUPDATEJOB_SYNCTOREDIS_BREAKPOINT, initBreakpoint2, 30 * 24 * 3600L);
                }

                logger.info("第零步，初始化断点。initBreakpoint1：{}，initBreakpoint2：{}", initBreakpoint1, initBreakpoint2);

                return ReturnT.SUCCESS;
            }

            syncToMong(batchReadCount, isTradeTimeRun);
            syncToRedis(batchReadCount, CommonUtils.toList(fundManagerWhiteList, ","));

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private boolean syncToMong(int batchReadCount, boolean isTradeTimeRun) {
        try {
            if (!isTradeTimeRun) {
                if (LocalTime.now().compareTo(LocalTime.of(13, 0, 0)) > 0
                        && LocalTime.now().compareTo(LocalTime.of(15, 20, 0)) < 0) {

                    logger.info("syncToMong-第零步，高峰期停止更新。当前时间：{}", LocalTime.now());

                    //高峰停止更新
                    return true;
                }
            }

            String breakpointName = UserRedisConfig.USERPOSTLASTUPDATEJOB_SYNCTOMONG_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-2));

                logger.error("syncToMong-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);

            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            boolean isRunning = true;

            logger.info("syncToMong-第一步，读取断点。断点：{}", breakpoint);

            List<UserPostUpdateModel> dataList = null;
            List<UserPostUpdateModel> insertMongoList = null;
            UserPostUpdateModel temp = null;
            int round = 0;
            while (isRunning) {
                round++;

                dataList = postInfoNewDao.getPostNewUpdate(breakpointDate, batchReadCount);
                if (!CollectionUtils.isEmpty(dataList)) {
                    dataList.forEach(a -> a._id = a.UID);
                }

                logger.info("syncToMong-第二步-读取mysql-轮次{}。数量：{}，头部id列表：{}",
                        round,
                        dataList == null ? 0 : dataList.size(),
                        dataList == null ? null : dataList.stream().map(a -> a._id).limit(20).collect(Collectors.toList()));

                if (!CollectionUtils.isEmpty(dataList)) {
                    //更新数据到mongdb
                    Map<String, List<UserPostUpdateModel>> groupMap = dataList.stream().collect(Collectors.groupingBy(item -> item.UID));

                    insertMongoList = new ArrayList<>(groupMap.size());
                    Collection<List<UserPostUpdateModel>> values = groupMap.values();
                    for (List<UserPostUpdateModel> item : values) {
                        if (!CollectionUtils.isEmpty(item)) {
                            temp = item.stream().max((Comparator.comparing(o -> o.CreatTime))).get();
                            if (temp != null) {
                                temp.RecordUpDateTime = new Date();
                                insertMongoList.add(temp);
                            }
                        }
                    }

                    List<Map<String, Object>> mapList = new ArrayList<>(insertMongoList.size());
                    for (UserPostUpdateModel item : insertMongoList) {
                        mapList.add(CommonUtils.beanToMap(item));
                    }

                    List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 200);
                    for (List<Map<String, Object>> batch : batchList) {
                        userPostUpdateInfoDao.upsertMany(batch);
                    }


                    logger.info("syncToMong-第三步-写mongo-轮次{}。数量：{}，头部id列表：{}",
                            round,
                            dataList == null ? 0 : dataList.size(),
                            dataList == null ? null : dataList.stream().map(a -> a.UID).limit(20).collect(Collectors.toList()));


                    breakpointDate = dataList.stream().max((Comparator.comparing(o -> o.CreatTime))).get().CreatTime;
                    breakpoint = DateUtil.dateToStr(breakpointDate);
                    userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

                    logger.info("syncToMong-第四步-更新断点-轮次{}。断点：{}", round, breakpoint);

                    if (dataList.size() < batchReadCount) {
                        isRunning = false;
                    }

                } else {
                    isRunning = false;
                }

            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return true;
    }

    private boolean syncToRedis(int batchReadCount, List<String> fundManagerWhiteList) {
        try {

            String breakpointName = UserRedisConfig.USERPOSTLASTUPDATEJOB_SYNCTOREDIS_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByDays(-1));

                logger.error("syncToRedis-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);

            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("syncToRedis-第一步，读取断点。断点:{}", breakpoint);

            Date tempDate = null;
            int round = 0;
            boolean isRunning = true;
            while (isRunning) {
                round++;

                List<UserPostUpdateModel> dataList = null;
                List<UserPostUpdateModel> tempList = null;
                dataList = postInfoNewDao.getPostNewUpdate(breakpointDate, batchReadCount);
                if (!CollectionUtils.isEmpty(dataList)) {
                    dataList.forEach(a -> a._id = a.UID);
                }

                logger.info("syncToRedis-第二步-读取帖子-轮次{}。数量：{}，头部id列表：{}",
                        round,
                        dataList == null ? 0 : dataList.size(),
                        dataList == null ? null : dataList.stream().map(a -> a._id).limit(20).collect(Collectors.toList()));

                if (!CollectionUtils.isEmpty(dataList)) {
                    /*
                     *1.基金经理帖子设置更新时间
                     */
                    //获取所有基金经理用户ID
                    List<PassportFundMrgModel> allList = passportFundMrgDao.getAll();

                    Set<String> listFundMgr = new HashSet<>();
                    if (!CollectionUtils.isEmpty(allList)) {
                        for (PassportFundMrgModel a : allList) {
                            if (a.IsDel == 0 && StringUtils.hasLength(a.PassportUID) && StringUtils.hasLength(a.MGRID)) {
                                listFundMgr.add(a.PassportUID);
                            }
                        }
                    }
                    if (!CollectionUtils.isEmpty(fundManagerWhiteList)) {
                        listFundMgr.addAll(fundManagerWhiteList);
                    }

                    logger.info("syncToRedis-第三步-读取基金经理-轮次{}。数量：{}，头部id列表：{}",
                            round,
                            listFundMgr == null ? 0 : listFundMgr.size(),
                            listFundMgr == null ? null : listFundMgr.stream().limit(20).collect(Collectors.toList()));

                    Map<String, List<UserPostUpdateModel>> groupMap = dataList.stream().collect(Collectors.groupingBy(item -> item.Code));
                    Set<Map.Entry<String, List<UserPostUpdateModel>>> entrySet = groupMap.entrySet();
                    for (Map.Entry<String, List<UserPostUpdateModel>> entry : entrySet) {
                        final Set<String> tempListFundMgr = listFundMgr;
                        tempList = dataList.stream().filter(item -> tempListFundMgr.contains(item.UID)).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(tempList)) {
                            tempDate = tempList.stream().max(Comparator.comparing(o -> o.CreatTime)).get().CreatTime;
                            app.barredis.set(String.format(BarRedisKey.ASP_NET_SERVICE_MRGPOSTUPDATETIME_BAR, entry.getKey())
                                    , String.valueOf(DateUtil.getUnixTimeNew(tempDate)),
                                    60 * 60 * 7L);

                            logger.info("syncToRedis-第四步-写redis详情-轮次{}。吧：{}", round, entry.getKey());

                        }
                    }

                    logger.info("syncToRedis-第五步-写redis完成-轮次{}。", round);

                    breakpointDate = dataList.stream().max((Comparator.comparing(o -> o.CreatTime))).get().CreatTime;
                    breakpoint = DateUtil.dateToStr(breakpointDate);
                    userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

                    logger.info("syncToRedis-第六步-更新断点-轮次{}。断点：{}", round, breakpoint);

                    if (dataList.size() < batchReadCount) {
                        isRunning = false;
                    }

                } else {
                    isRunning = false;
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return true;
    }

}
