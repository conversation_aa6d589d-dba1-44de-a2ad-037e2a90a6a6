package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.post.ReplyLikeCountModel;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.msyql.LikeCountDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoExtraDao;
import ttfund.web.communityservice.dao.msyql.ReplyInfoNewDao;
import ttfund.web.communityservice.dao.msyql.ReplyRelationDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@JobHandler("ReplyExtraSyncBizJob")
@Component
public class ReplyExtraSyncBizJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(ReplyExtraSyncBizJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private LikeCountDao likeCountDao;

    @Autowired
    private ReplyInfoExtraDao replyInfoExtraDao;

    @Autowired
    private ReplyInfoNewDao replyInfoNewDao;

    @Autowired
    private ReplyRelationDao replyRelationDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        String initBreakpoint1 = null;
        String initBreakpoint2 = null;
        Integer batchReadCount = null;
        Integer replyBackTime = null;  //取评论回退时间  单位秒
        //服务第一次运行时，初始化断点
        if (StringUtils.hasLength(s)) {
            JSONObject jsonObject = JSON.parseObject(s);
            initBreakpoint1 = jsonObject.getString("initBreakpoint1");
            initBreakpoint2 = jsonObject.getString("initBreakpoint2");
            batchReadCount = jsonObject.getInteger("batchReadCount");
            replyBackTime = jsonObject.getInteger("replyBackTime");
        }

        if (batchReadCount == null) {
            batchReadCount = 5000;
        }

        if (replyBackTime == null) {
            replyBackTime = -30;
        }

        logger.info("打印参数。initBreakpoint1：{}，initBreakpoint2：{}，batchReadCount：{}，replyBackTime：{}",
                initBreakpoint1,
                initBreakpoint2,
                batchReadCount,
                replyBackTime
        );

        if (StringUtils.hasLength(initBreakpoint1) || StringUtils.hasLength(initBreakpoint2)) {
            if (StringUtils.hasLength(initBreakpoint1)) {
                userRedisDao.set(UserRedisConfig.REPLYEXTRASYNCBIZJOB_SYNCREPLYEXTRA1_BREAKPOINT, initBreakpoint1, 30 * 24 * 3600L);

                logger.info("初始化断点。initBreakpoint1：{}", initBreakpoint1);
            }
            if (StringUtils.hasLength(initBreakpoint2)) {
                userRedisDao.set(UserRedisConfig.REPLYEXTRASYNCBIZJOB_SYNCREPLYEXTRA2_BREAKPOINT, initBreakpoint2, 30 * 24 * 3600L);

                logger.info("初始化断点。initBreakpoint2：{}", initBreakpoint2);
            }

            return ReturnT.SUCCESS;
        }

        syncReplyExtra1(batchReadCount);
        syncReplyExtra2(batchReadCount, replyBackTime);

        return ReturnT.SUCCESS;
    }

    /**
     * 同步评论点赞数
     */
    private void syncReplyExtra1(int batchReadCount) {
        try {
            String breakpointName = UserRedisConfig.REPLYEXTRASYNCBIZJOB_SYNCREPLYEXTRA1_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);
            if (!StringUtils.hasLength(breakpoint)) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-30));

                logger.error("syncReplyExtra1-第零步：获取断点为空，走默认断点。断点：{}", breakpoint);

            }

            logger.info("syncReplyExtra1-第零步：获取断点。断点：{}", breakpoint);

            int round = 0;
            while (true) {
                round++;

                Date breakpointDate = DateUtil.strToDate(breakpoint);
                List<ReplyLikeCountModel> list = likeCountDao.getLikeCounTable(breakpointDate, batchReadCount);

                logger.info("syncReplyExtra1-第一步：获取数据-第{}轮。数量：{}，头部id列表：{}",
                        round,
                        list == null ? 0 : list.size(),
                        list == null ? 0 : list.stream().map(a -> a.Id).limit(20).collect(Collectors.toList()));


                if (!CollectionUtils.isEmpty(list)) {
                    for (ReplyLikeCountModel p : list) {
                        try {
                            replyInfoExtraDao.synReplyExtra1(p);
                        } catch (Exception ex) {
                            logger.error(ex.getMessage(), ex);
                        }
                    }

                    logger.info("syncReplyExtra1-第二步：数据写评论扩展表-第{}轮。数量：{}，头部id列表：{}",
                            round,
                            list == null ? 0 : list.size(),
                            list == null ? 0 : list.stream().map(a -> a.Id).limit(20).collect(Collectors.toList()));


                    breakpointDate = list.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;

                    breakpoint = DateUtil.dateToStr(breakpointDate);
                    userRedisDao.set(breakpointName, breakpoint, 3600 * 24 * 90L);

                    logger.info("syncReplyExtra1-第三步：更新断点-第{}轮。断点：{}", round, breakpoint);

                }

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 3600 * 24 * 90L);

                logger.info("syncReplyExtra1-第三步：更新断点-第{}轮。断点：{}", round, breakpoint);

                if (list == null || list.size() < batchReadCount) {
                    break;
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 同步评论回复数
     */
    private void syncReplyExtra2(int batchReadCount, int replyBackTime) {
        try {
            String breakpointName = UserRedisConfig.REPLYEXTRASYNCBIZJOB_SYNCREPLYEXTRA2_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);
            if (!StringUtils.hasLength(breakpoint)) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-30));

                logger.error("syncReplyExtra2-第零步：获取断点为空，走默认断点。断点：{}", breakpoint);

            }

            logger.info("syncReplyExtra2-第零步：获取断点。断点：{}", breakpoint);

            int round = 0;
            while (true) {
                round++;

                Date breakpointDate = DateUtil.strToDate(breakpoint);
                List<ReplyInfoNewModel> list = replyInfoNewDao.getListByUpdateTime(breakpointDate, DateUtil.calendarDateBySecond(replyBackTime), batchReadCount);

                logger.info("syncReplyExtra2-第一步：获取数据-第{}轮。数量：{}，头部id列表：{}",
                        round,
                        list == null ? 0 : list.size(),
                        list == null ? 0 : list.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

                if (!CollectionUtils.isEmpty(list)) {
                    for (ReplyInfoNewModel p : list) {
                        try {
                            //todo：拿回复数
                            long huiFuCount = replyRelationDao.getHuiFuCount(p.ID);
                            replyInfoExtraDao.synReplyExtra2(p.ID, huiFuCount);
                        } catch (Exception ex) {
                            logger.error(ex.getMessage(), ex);
                        }
                    }

                    logger.info("syncReplyExtra2-第二步：数据写评论扩展表-第{}轮。数量：{}，头部id列表：{}",
                            round,
                            list == null ? 0 : list.size(),
                            list == null ? 0 : list.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

                    breakpointDate = list.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;

                    breakpoint = DateUtil.dateToStr(breakpointDate);
                    userRedisDao.set(breakpointName, breakpoint, 3600 * 24 * 90L);

                    logger.info("syncReplyExtra2-第三步：更新断点-第{}轮。断点：{}", round, breakpoint);

                }

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 3600 * 24 * 90L);

                logger.info("syncReplyExtra2-第三步：更新断点-第{}轮。断点：{}", round, breakpoint);

                if (list == null || list.size() < batchReadCount) {
                    break;
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

}
