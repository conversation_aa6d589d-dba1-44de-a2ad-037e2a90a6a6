package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.HtmlUtils;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.ExcellentAnswererRankDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 优秀答主榜单job
 * #656275 【基金吧6.13.1】问答功能优化
 */
@JobHandler("ExcellentAnswererRankJob")
@Component
public class ExcellentAnswererRankJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(ExcellentAnswererRankJob.class);

    private static final String TAB_REGEX = "<[^>]*?>";
    private static final Pattern TAB_REGEX_PATTERN = Pattern.compile(TAB_REGEX);

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private ExcellentAnswererRankDao excellentAnswererRankDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String uidBlackList = null;//过滤用户集合
            String weightConfig = null;//权重配置
            Integer charCountLimit = null;//字数限制
            Integer excellentAnswerLimit = null;//优质回答数限制
            Integer mongoSize = null;//写mongo数量限制
            Integer redisSize = null;//写redis数量限制
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                uidBlackList = jsonObject.getString("uidBlackList");
                weightConfig = jsonObject.getString("weightConfig");
                charCountLimit = jsonObject.getInteger("charCountLimit");
                excellentAnswerLimit = jsonObject.getInteger("excellentAnswerLimit");
                mongoSize = jsonObject.getInteger("mongoSize");
                redisSize = jsonObject.getInteger("redisSize");
            }

            if (uidBlackList == null) {
                uidBlackList = "6359044564018472,6573085420055854,1512067126443212";
            }

            if (weightConfig == null) {
                weightConfig = "0.9,0.1";
            }

            if (charCountLimit == null) {
                charCountLimit = 50;
            }

            if (excellentAnswerLimit == null) {
                excellentAnswerLimit = 6;
            }

            if (mongoSize == null) {
                mongoSize = 30;
            }

            if (redisSize == null) {
                redisSize = 30;
            }

            logger.info("第零步，打印参数。uidBlackList：{}，weightConfig：{}，charCountLimit：{}，excellentAnswerLimit：{}，mongoSize：{}，redisSize：{}",
                    uidBlackList,
                    weightConfig,
                    charCountLimit,
                    excellentAnswerLimit,
                    mongoSize,
                    redisSize
            );

            deal(uidBlackList, weightConfig, charCountLimit, excellentAnswerLimit, mongoSize, redisSize);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void deal(String uidBlackList, String weightConfig, int charCountLimit, int excellentAnswerLimit, int mongoSize, int redisSize) {

        LocalDateTime start = null;
        LocalDateTime end = null;

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastWeekNow = now.plusDays(-7);

        end = now.plusDays(-now.getDayOfWeek().getValue() + 1);
        start = lastWeekNow.plusDays(-lastWeekNow.getDayOfWeek().getValue() + 1);

        Date date1 = DateUtil.strToDate(start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), "yyyy-MM-dd");
        Date date2 = DateUtil.strToDate(end.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), "yyyy-MM-dd");

        logger.info("1.计算起始日期。date1：{}，date2：{}",
                DateUtil.dateToStr(date1),
                DateUtil.dateToStr(date2)
        );

        List<Map<String, Object>> list = postInfoNewDao.getAnswerInfoOverPeriodTime(date1, date2);

        logger.info("2.读库。数量：{}，头部数据：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );


        excellentAnswererRankDao.removeAll();
        app.barredis.del(BarRedisKey.FUND_GUBA_SERVICE_EXCELLENTANSWERERRANK);

        logger.info("3.清空库。");

        if (!CollectionUtils.isEmpty(list)) {

            String[] split = weightConfig.split(",");
            double d0 = Double.parseDouble(split[0]);
            double d1 = Double.parseDouble(split[1]);

            List<Map<String, Object>> calResultList = new ArrayList<>();

            Map<String, List<Map<String, Object>>> userRecordMap = list.stream().collect(Collectors.groupingBy(a -> (String) a.get("UID")));

            logger.info("4.按用户聚合。数量：{}",
                    CollectionUtils.isEmpty(userRecordMap) ? 0 : userRecordMap.size()
            );

            Map<String, Object> tempMap = null;
            int charCount = 0;
            int excellentAnswerCount = 0;
            int answerLikeCount = 0;
            for (Map.Entry<String, List<Map<String, Object>>> entry : userRecordMap.entrySet()) {

                excellentAnswerCount = 0;
                answerLikeCount = 0;

                for (Map<String, Object> a : entry.getValue()) {

                    String content = (String) a.get("CONTENT");
                    if (StringUtils.hasLength(content)) {
                        content = HtmlUtils.htmlUnescape(content);

                        Matcher matcher = TAB_REGEX_PATTERN.matcher(content);
                        while (matcher.find()) {
                            content = content.replace(matcher.group(0), "");
                        }

                        charCount = content.length();
                        if (charCount >= charCountLimit) {
                            excellentAnswerCount++;
                        }
                    }

                    answerLikeCount += Integer.parseInt(a.get("LIKECOUNT").toString());

                }

                if (excellentAnswerCount < excellentAnswerLimit) {
                    continue;
                }

                if (uidBlackList != null && uidBlackList.contains(entry.getKey())) {
                    continue;
                }

                tempMap = new HashMap<>();
                tempMap.put("_id", entry.getKey());
                tempMap.put("passportId", entry.getKey());
                tempMap.put("excellentAnswerCount", excellentAnswerCount);
                tempMap.put("answerLikeCount", answerLikeCount);
                tempMap.put("score", (double) (d0 * excellentAnswerCount + d1 * answerLikeCount));
                tempMap.put("updateTime", new Date());

                calResultList.add(tempMap);

            }

            logger.info("5.计算得分。数量：{}，头部数据：{}",
                    CollectionUtils.isEmpty(calResultList) ? 0 : calResultList.size(),
                    CollectionUtils.isEmpty(calResultList) ? null : JSON.toJSONStringWithDateFormat(calResultList.get(0), DateUtil.datePattern)
            );

            if (!CollectionUtils.isEmpty(calResultList)) {
                calResultList.sort((o1, o2) -> Double.compare((double) o2.get("score"), (double) o1.get("score")));

                List<Map<String, Object>> mongoList = calResultList.stream().limit(mongoSize).collect(Collectors.toList());
                excellentAnswererRankDao.upsertMany(mongoList);

                logger.info("6.写mongo。数量：{}，头部数据：{}",
                        CollectionUtils.isEmpty(mongoList) ? 0 : mongoList.size(),
                        CollectionUtils.isEmpty(mongoList) ? null : JSON.toJSONStringWithDateFormat(mongoList.get(0), DateUtil.datePattern)
                );

                List<Map<String, Object>> redisList = calResultList.stream().limit(redisSize).collect(Collectors.toList());
                app.barredis.set(BarRedisKey.FUND_GUBA_SERVICE_EXCELLENTANSWERERRANK,
                        JSON.toJSONStringWithDateFormat(redisList, DateUtil.datePattern),
                        30 * 24 * 3600L);

                logger.info("7.写redis。数量：{}，头部数据：{}",
                        CollectionUtils.isEmpty(redisList) ? 0 : redisList.size(),
                        CollectionUtils.isEmpty(redisList) ? null : JSON.toJSONStringWithDateFormat(redisList.get(0), DateUtil.datePattern)
                );

            }
        }

    }

}
