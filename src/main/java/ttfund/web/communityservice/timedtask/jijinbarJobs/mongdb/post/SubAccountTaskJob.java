package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.CacheHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.FundFavorSubAccCountListResponse;
import ttfund.web.communityservice.bean.jijinBar.data.PostFindRankModel;
import ttfund.web.communityservice.bean.jijinBar.data.SubAccReplyOrFavorSubAccDto;
import ttfund.web.communityservice.bean.jijinBar.data.SubPushHis;
import ttfund.web.communityservice.bean.jijinBar.data.TopPostApiResponse;
import ttfund.web.communityservice.bean.jijinBar.data.TopPostModel;
import ttfund.web.communityservice.bean.jijinBar.data.TopicTopPostInfo;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumPostType;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.guba.FundBarModel;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.FindPostDao;
import ttfund.web.communityservice.dao.mongo.FindPostNewDao;
import ttfund.web.communityservice.dao.mongo.PassportUserBindInfoDao;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.mongo.PostFindRankDao;
import ttfund.web.communityservice.dao.mongo.ShieldBrowseDao;
import ttfund.web.communityservice.dao.mongo.ShieldUserDao;
import ttfund.web.communityservice.dao.msyql.FundBarDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.msyql.SensitiveWordConfigDao;
import ttfund.web.communityservice.dao.msyql.SubpushHisDao;
import ttfund.web.communityservice.dao.msyql.UserRelationDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.enums.EnumSubPushHisType;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 子账户任务
 * 1.msyql 优化 涉及方法  GetPostListForSubPush  时间:2022-01-13
 */
@JobHandler("SubAccountTaskJob")
@Component
public class SubAccountTaskJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SubAccountTaskJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private FindPostDao findPostDao;

    @Autowired
    private PostFindRankDao postFindRankDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private FindPostNewDao findPostNewDao;

    @Autowired
    private SensitiveWordConfigDao sensitiveWordConfigDao;

    @Autowired
    private PassportUserInfoDao passportUserInfoDao;

    @Autowired
    private UserRelationDao userRelationDao;

    @Autowired
    private PassportUserBindInfoDao passportUserBindInfoDao;

    @Autowired
    private FundBarDao fundBarDao;

    @Autowired
    @Qualifier(KafkaConfig.fundbar_kafka_temp_beanname)
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private SubpushHisDao subpushHisDao;

    @Autowired
    private App app;

    @Autowired
    private ShieldBrowseDao shieldBrowseDao;

    @Autowired
    private ShieldUserDao shieldUserDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        String initBreakpoint1 = null;
        String initBreakpoint2 = null;
        String initBreakpoint3 = null;
        Integer batchReadCount = null;
        if (StringUtils.hasLength(s)) {
            JSONObject jsonObject = JSON.parseObject(s);
            initBreakpoint1 = jsonObject.getString("initBreakpoint1");
            initBreakpoint2 = jsonObject.getString("initBreakpoint2");
            initBreakpoint3 = jsonObject.getString("initBreakpoint3");
            batchReadCount = jsonObject.getInteger("batchReadCount");
        }

        if (batchReadCount == null) {
            batchReadCount = 5000;
        }

        logger.info("第零步，打印参数。initBreakpoint1：{}，initBreakpoint2：{}，initBreakpoint3：{}，batchReadCount：{}",
            initBreakpoint1,
            initBreakpoint2,
            initBreakpoint3,
            batchReadCount);

        if (StringUtils.hasLength(initBreakpoint1) || StringUtils.hasLength(initBreakpoint2) || StringUtils.hasLength(initBreakpoint3)) {

            if (StringUtils.hasLength(initBreakpoint1)) {
                userRedisDao.set(UserRedisConfig.SUBACCOUNTTASKJOB_FUNDBARHOTPOST_BREAKPOINT, initBreakpoint1, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint1：{}", initBreakpoint1);
            }

            if (StringUtils.hasLength(initBreakpoint2)) {
                userRedisDao.set(UserRedisConfig.SUBACCOUNTTASKJOB_SUBACCOUNTBARPUSHMSG_BREAKPOINT, initBreakpoint2, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint2：{}", initBreakpoint2);
            }

            if (StringUtils.hasLength(initBreakpoint3)) {
                userRedisDao.set(UserRedisConfig.SUBACCOUNTTASKJOB_SUBACCOUNTFAVORDTOPUSHMSG_BREAKPOINT, initBreakpoint3, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint3：{}", initBreakpoint3);
            }

            return ReturnT.SUCCESS;
        }

        //2025-02-14 看了线上表，发现线上表没有数据  说明当时近段时间，在.net服务在运行期间，这张表就没有数据
        //然后又搜了下是否有业务用到这张表，发现没有  所以这张表没数据且没有业务用到
        //所以在本java服务中，直接以下方法  handleFindPostHistory()、userRelatedPost()、fundBarNicePost()
        //handleFindPostHistory();

        //userRelatedPost();

        //fundBarNicePost();

        fundBarHotPost(batchReadCount);

        //subAccountBarPushMsg(batchReadCount);

        //subAccountFavorDtoPushMsg(batchReadCount);

        subAccountTopPost();

        delHistoryShieldBrowseAndUser();

        return ReturnT.SUCCESS;
    }

    /**
     * 处理FindPost历史记录，
     * 删除帖子发表时间是一个月之前的，
     * 一天删一次，新方法，前提是FindPost实时插入
     */
    private void handleFindPostHistory() {
        try {

            //删除一个月前的历史记录
            findPostDao.delByTime(DateUtil.calendarDateByMonth(-1));

            logger.info("handleFindPostHistory-执行成功");

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 用户相关基金吧帖子
     * PostFindRank 按照分数排序，从14天前的贴子同步到FindPost
     * 然后删除 FindPost 表中的前一天的记录
     * FINDTYPE = 2;
     */
    private boolean userRelatedPost() {
        try {

            List<PostFindRankModel> dataList = postFindRankDao.getListForUserRelatedPost();

            logger.info("userRelatedPost-1.读取数据。数量:{}，头部id列表：{}",
                dataList == null ? 0 : dataList.size(),
                dataList == null ? null : dataList.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(dataList)) {
                for (PostFindRankModel item : dataList) {
                    item.FINDTYPE = 2;
                }

                List<Map<String, Object>> mapList = new ArrayList<>(dataList.size());
                Map<String, Object> map = null;
                for (PostFindRankModel a : dataList) {
                    map = CommonUtils.beanToMap(a);
                    map.put("_id", String.valueOf(a.ID));
                    mapList.add(map);
                }
                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 200);
                for (List<Map<String, Object>> batch : batchList) {
                    findPostDao.upsertMany(batch);
                }

                logger.info("userRelatedPost-2.写库。数量:{}，头部id列表：{}",
                    mapList == null ? 0 : mapList.size(),
                    mapList == null ? null : mapList.stream().map(a -> (String)a.get("_id")).limit(20).collect(Collectors.toList()));

                findPostDao.delByRankDateAndFindType(DateUtil.dateToStr(DateUtil.calendarDateByDays(-1), "yyyyMMdd"), 2);

                logger.info("userRelatedPost-3.删除数据。");
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return true;
    }

    /**
     * 基金吧优质内容推荐
     * 从PostFindRank 分数降序,筛选内容长度大于50的帖子且最近14天的帖子
     * 写入到FindPost 表中
     * FINDTYPE = 3
     * 然后删除前一天的帖子
     */
    private boolean fundBarNicePost() {
        try {

            List<PostFindRankModel> dataList = postFindRankDao.getListForFundBarNicePost();

            logger.info("fundBarNicePost-1.读取数据。数量:{}，头部id列表：{}",
                dataList == null ? 0 : dataList.size(),
                dataList == null ? null : dataList.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(dataList)) {
                for (PostFindRankModel item : dataList) {
                    item.FINDTYPE = 3;
                }

                List<Map<String, Object>> mapList = new ArrayList<>(dataList.size());
                Map<String, Object> map = null;
                for (PostFindRankModel a : dataList) {
                    map = CommonUtils.beanToMap(a);
                    map.put("_id", String.valueOf(a.ID));
                    mapList.add(map);
                }
                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 200);
                for (List<Map<String, Object>> batch : batchList) {
                    findPostDao.upsertMany(batch);
                }

                logger.info("fundBarNicePost-2.写库。数量:{}，头部id列表：{}",
                    mapList == null ? 0 : mapList.size(),
                    mapList == null ? null : mapList.stream().map(a -> (String)a.get("_id")).limit(20).collect(Collectors.toList()));

                findPostDao.delByRankDateAndFindType(DateUtil.dateToStr(DateUtil.calendarDateByDays(-1), "yyyyMMdd"), 3);

                logger.info("fundBarNicePost-3.删除数据。");
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return true;
    }

    /**
     * 基金吧热门帖 FINDTYPE =7
     */
    private boolean fundBarHotPost(int batchReadCount) {
        try {

            String breakpointName = UserRedisConfig.SUBACCOUNTTASKJOB_FUNDBARHOTPOST_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = String.valueOf(DateUtil.getTimePoint(DateUtil.calendarDateByHour(-1)));

                logger.error("fundBarHotPost-0.读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            logger.info("fundBarHotPost-1.读取断点。断点:{}", breakpoint);

            int round = 0;

            boolean run = true;
            //循环执行
            while (run) {

                round++;

                long timePoint = Long.parseLong(breakpoint);
                //取备库postinfo_new表半个月帖子数据
                List<Map<String, Object>> table = postInfoNewDao.getPostNewListForRecommend(timePoint, batchReadCount);

                logger.info("fundBarHotPost-2.读取数据-第{}轮。数量:{}，头部id列表：{}",
                    round,
                    table == null ? 0 : table.size(),
                    CollectionUtils.isEmpty(table) ? null : JSON.toJSONStringWithDateFormat(table.get(table.size() - 1), DateUtil.datePattern)
                );

                if (CollectionUtils.isEmpty(table) || table.size() < batchReadCount) {
                    run = false;
                }

                //判断读取数据是否存在
                if (!CollectionUtils.isEmpty(table)) {
                    breakpoint = table.get(table.size() - 1).get("TIMEPOINT").toString();

                    //读取帖子算分系数
                    List<String> excludeCodes = CommonUtils.toList(appConstant.findExcludeCodeConfig, ",");

                    Integer recommendDel = null;
                    Integer del = 1;
                    Integer ttjjDel = 1;
                    Integer isEnabled = 0;

                    String _id = null;
                    String code = null;
                    Integer type = 0;
                    String content = null;
                    Integer likeCount = 0;
                    Integer pinglunNum = 0;
                    Integer clickNum = 0;


                    //是否包含敏感词
                    boolean containsSensitiveword = false;
                    String nowdate = DateUtil.dateToStr(new Date(), "yyyyMMdd");

                    double score = 0;

                    for (Map<String, Object> item : table) {

                        try {
                            _id = String.valueOf(item.get("ID"));
                            item.put("_id", _id);

                            recommendDel = item.get("RECOMMENDDEL") == null ? 0 : Integer.parseInt(item.get("RECOMMENDDEL").toString());     //推荐系统删除
                            code = item.get("CODE") == null ? null : (String)item.get("CODE");
                            type = item.get("TYPE") == null ? 0 : Integer.parseInt(item.get("TYPE").toString());
                            content = item.get("CONTENT") == null ? null : (String)item.get("CONTENT");
                            content = content == null ? "" : removeHtmlTag(content);
                            del = item.get("DEL") == null ? 1 : Integer.parseInt(item.get("DEL").toString());  //东财删除
                            ttjjDel = item.get("TTJJDEL") == null ? 1 : Integer.parseInt(item.get("TTJJDEL").toString());  //天天基金删除
                            isEnabled = item.get("ISENABLED") == null ? 0 : Integer.parseInt(item.get("ISENABLED").toString());  //是否可用

                            //是否包含敏感词
                            containsSensitiveword = isContainsSensitiveword(content);

                            //如果是推荐系统配置删除帖子 则进行赋分 分数为最小值
                            if (recommendDel == 1
                                || excludeCodes.contains(code)
                                || code.indexOf("43-") > -1 || type == EnumPostType.Notice.getValue()
                                || del != 0 || ttjjDel != 0 || isEnabled != 1
                                || containsSensitiveword) {

                                score = Integer.MIN_VALUE;

                            } else {
                                item.put("RANKDATE", nowdate);
                                item.put("FINDTYPE", 7);
                                likeCount = item.get("LIKECOUNT") == null ? 0 : Integer.parseInt(item.get("LIKECOUNT").toString());//点赞数
                                pinglunNum = item.get("PINGLUNNUM") == null ? 0 : Integer.parseInt(item.get("PINGLUNNUM").toString());//评论数
                                clickNum = item.get("CLICKNUM") == null ? 0 : Integer.parseInt(item.get("CLICKNUM").toString());//阅读数

                                if ((pinglunNum + likeCount) <= 3 || content == null || content.length() <= 20) {
                                    score = Integer.MIN_VALUE;//这里设置最小值,最后要删除
                                } else {
                                    /*int userScore = 0;//用户分
                                    PassportUserInfoModelNew userInfo = passportUserInfoDao.getPassportUserInfoById(String.valueOf(item.get("UID")));
                                    if (userInfo != null && "0".equals(userInfo.VtypeStatus)) {//大V
                                        //加V用户
                                        userScore = 3;
                                        if ("401".equals(userInfo.Vtype)) {
                                            userScore = 5;//基金经理
                                        }

                                    } else {
                                        int userFans = userRelationDao.getUserFansCountByUserId(String.valueOf(item.get("UID")));
                                        PassportUserBindInfo pUser = passportUserBindInfoDao.getInfo(String.valueOf(item.get("UID")));
                                        // 粉丝数<100 且 未开通交易账号
                                        if (userFans < 100 && (pUser == null || "0".equals(pUser.ISENABLED))) {
                                            userScore -= 20;
                                        } else {
                                            userScore = 0;
                                        }
                                    }*/

                                    score = (Math.log10(1 + clickNum) + 3 * Math.log10(1 + likeCount) + 4 * Math.log10(1 + pinglunNum)) * Math.exp(-0.4 * (getHours(new Date(), (Date)item.get("TIME")) - 24) / 24);
                                }
                            }

                            item.put("RANKSCORE", score);

                        } catch (Exception ex) {
                            logger.error(ex.getMessage(), ex);
                        }
                    }

                    logger.info("fundBarHotPost-3.计算-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        table == null ? 0 : table.size(),
                        CollectionUtils.isEmpty(table) ? null : JSON.toJSONStringWithDateFormat(table.get(table.size() - 1), DateUtil.datePattern)
                    );

                    // 最小分数帖子删除
                    List<Map<String, Object>> deleteList = table.stream().filter(a -> (double)a.get("RANKSCORE") == Integer.MIN_VALUE).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(deleteList)) {
                        List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(deleteList, 100);
                        for (List<Map<String, Object>> batch : batchList) {
                            findPostNewDao.removeByIds(batch.stream().map(o -> (String)o.get("_id")).collect(Collectors.toList()), BarMongodbConfig.TABLE_FINDPOSTNEW);
                        }
                    }

                    logger.info("fundBarHotPost-4.删除数据-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        deleteList == null ? 0 : deleteList.size(),
                        CollectionUtils.isEmpty(deleteList) ? null : JSON.toJSONStringWithDateFormat(deleteList.get(deleteList.size() - 1), DateUtil.datePattern)
                    );

                    table = table.stream().filter(item -> (double)item.get("RANKSCORE") != Integer.MIN_VALUE).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(table)) {
                        for (Map<String, Object> item : table) {
                            item.remove("PINGLUNNUM");
                            item.remove("LIKECOUNT");
                            item.remove("CLICKNUM");
                            item.remove("UPDATETIME");

                            item.put("RANKDATE", DateUtil.dateToStr((Date)item.get("TIME"), "yyyyMMddHH"));
                        }

                        table.sort((o1, o2) -> Double.compare((double)o2.get("RANKSCORE"), (double)o1.get("RANKSCORE")));

                        List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(table, 200);
                        for (List<Map<String, Object>> batch : batchList) {
                            findPostNewDao.upsertMany(batch);
                        }
                    }

                    logger.info("fundBarHotPost-5.写入数据-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        table == null ? 0 : table.size(),
                        CollectionUtils.isEmpty(table) ? null : JSON.toJSONStringWithDateFormat(table.get(table.size() - 1), DateUtil.datePattern)
                    );

                    //断点操作成功记录本次断点值
                    userRedisDao.set(breakpointName, breakpoint, 3600 * 24 * 90L);
                    logger.info("fundBarHotPost-6.更新断点-第{}轮。断点：{}", round, breakpoint);
                }

                //断点操作成功记录本次断点值
                userRedisDao.set(breakpointName, breakpoint, 3600 * 24 * 90L);
                logger.info("fundBarHotPost-6.更新断点-第{}轮。断点：{}", round, breakpoint);
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return true;
    }

    /**
     * 子账户评论推送(子账户评论推送 鲁容淼)
     */
    private boolean subAccountBarPushMsg(int batchReadCount) {
        try {
            String breakpointName = UserRedisConfig.SUBACCOUNTTASKJOB_SUBACCOUNTBARPUSHMSG_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = String.valueOf(DateUtil.getTimePoint(DateUtil.calendarDateByHour(-1)));

                logger.error("subAccountBarPushMsg-0.读取断点为空，使用默认断点。断点:{}", breakpoint);
            }
            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("subAccountBarPushMsg-1.读取断点。断点:{}", breakpoint);

            String subAccRegex = "我在(.*?)关注(.*?)子账户";

            int round = 0;
            while (true) {
                round++;

                List<PostInfoModel> postList = postInfoNewDao.getPostListForSubPush(breakpointDate, EnumSubPushHisType.SubAccPost.getValue(), batchReadCount);

                logger.info("subAccountBarPushMsg-2.读取数据-第{}轮。数量:{}，头部id列表：{}",
                    round,
                    postList == null ? 0 : postList.size(),
                    postList == null ? null : postList.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

                if (!CollectionUtils.isEmpty(postList)) {
                    for (PostInfoModel item : postList) {
                        try {
                            //如果帖子为非审核状态则不推送
                            if (item.DEL != 0) {
                                continue;
                            }

                            String codeOut = item.CODE.replace("43-", "");
                            FundBarModel accInfo = fundBarDao.getById(codeOut);
                            String curContent = item.CONTENT == null ? "" : item.CONTENT;
                            //自己发的帖子不推送消息  关注动态帖子不推送消息
                            if (accInfo != null && !item.UID.equals(accInfo.AdminPassportId) && !subAccRegex.matches(curContent)) {
                                //推送kafka 消息
                                PassportUserInfoModelNew managerUser = passportUserInfoDao.getPassportUserInfoById(accInfo.AdminPassportId);

                                PassportUserInfoModelNew replyUser = passportUserInfoDao.getPassportUserInfoById(item.UID);

                                if (replyUser != null && StringUtils.hasLength(replyUser.PassportID) && managerUser != null && StringUtils.hasLength(managerUser.PassportID)) {
                                    SubAccReplyOrFavorSubAccDto pushModel = new SubAccReplyOrFavorSubAccDto();
                                    pushModel.EID = "SubAccReply" + UUID.randomUUID().toString();
                                    pushModel.PassportID = managerUser.PassportID == null ? "" : managerUser.PassportID;
                                    pushModel.PassportName = managerUser.NickName == null ? "" : managerUser.NickName;
                                    pushModel.ReplyPassportID = replyUser.PassportID == null ? "" : replyUser.PassportID;
                                    pushModel.ReplyPassportName = replyUser.NickName == null ? "" : replyUser.NickName;
                                    pushModel.Content = item.CONTENT == null ? "" : item.CONTENT;
                                    pushModel.PostID = String.valueOf(item.ID);
                                    pushModel.BarCode = item.CODE == null ? "" : item.CODE.replace("43-", "");
                                    pushModel.BarName = accInfo.BarName == null ? "" : accInfo.BarName;
                                    pushModel.BarAdminUserName = managerUser.NickName == null ? "" : managerUser.NickName;
                                    pushModel.Source = "subaccount";
                                    pushModel.SourceType = 1;
                                    pushModel.SourceID = String.valueOf(item.ID);

                                    //推送历史记录
                                    SubPushHis subPushHis = new SubPushHis();
                                    subPushHis.Id = pushModel.PostID + pushModel.PassportID;
                                    subPushHis.PassportID = pushModel.PassportID;
                                    subPushHis.PostID = pushModel.PostID;
                                    subPushHis.Type = EnumSubPushHisType.SubAccPost.getValue();

                                    kafkaTemplate.send(KafkaTopicName.JIJINBA_SUBACC_PUSH_REPLY,
                                        pushModel.PostID + "_" + pushModel.PassportID, JSON.toJSONStringWithDateFormat(pushModel, "yyyy-MM-dd'T'HH:mm:ss"));
                                    //子帐户推送历史
                                    subpushHisDao.insert(subPushHis);
                                }
                            }
                        } catch (Exception ex) {
                            logger.error(ex.getMessage(), ex);
                        }
                    }

                    logger.info("subAccountBarPushMsg-3.推送数据-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        postList == null ? 0 : postList.size(),
                        postList == null ? null : postList.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

                    breakpointDate = postList.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;
                    breakpoint = DateUtil.dateToStr(breakpointDate);
                    userRedisDao.set(breakpointName, breakpoint, 3600 * 24 * 30L);

                    logger.info("subAccountBarPushMsg-4.更新断点-第{}轮。断点：{}", round, breakpoint);

                }

                if (postList == null || postList.size() < batchReadCount) {
                    break;
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return true;
    }

    /**
     * 子组合动态推送 推送基金吧管理人动态到粉丝(推送给 鲁容淼 kafka)
     */
    private boolean subAccountFavorDtoPushMsg(int batchReadCount) {
        try {

            String breakpointName = UserRedisConfig.SUBACCOUNTTASKJOB_SUBACCOUNTFAVORDTOPUSHMSG_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = String.valueOf(DateUtil.getTimePoint(DateUtil.calendarDateByHour(-1)));

                logger.error("subAccountFavorDtoPushMsg-0.读取断点为空，使用默认断点。断点:{}", breakpoint);
            }
            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("subAccountFavorDtoPushMsg-1.读取断点。断点:{}", breakpoint);

            int round = 0;
            while (true) {
                round++;

                List<PostInfoModel> postList = postInfoNewDao.getPostListForSubPush(breakpointDate, EnumSubPushHisType.ZHDT.getValue(), batchReadCount);

                logger.info("subAccountFavorDtoPushMsg-2.读取数据-第{}轮。数量:{}，头部id列表：{}",
                    round,
                    postList == null ? 0 : postList.size(),
                    postList == null ? null : postList.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

                if (!CollectionUtils.isEmpty(postList)) {

                    for (PostInfoModel item : postList) {
                        try {
                            /*
                             * 1.如果是待审核或者是删除的帖子不推送
                             * 2.如果帖子的状态和缓存的状态不一致则更新缓存
                             * 3.这里有坑，扫描数据会早于缓存数据
                             */
                            if (item.DEL != 0) {
                                continue;
                            }

                            String codeOut = item.CODE.replace("43-", "");
                            FundBarModel accInfo = fundBarDao.getById(codeOut);

                            //管理人发的帖子 推送给关注用户
                            if (accInfo != null && item.UID.equals(accInfo.AdminPassportId)) {

                                String redisValue = app.barredis.get(String.format(BarRedisKey.FUNDFAVORSUBACCCOUNTLIST, accInfo.BarCodeOut));

                                PassportUserInfoModelNew managerUser = passportUserInfoDao.getPassportUserInfoById(item.UID);

                                if (StringUtils.hasLength(redisValue) && managerUser != null && StringUtils.hasLength(managerUser.PassportID)) {
                                    List<FundFavorSubAccCountListResponse> userList = JacksonUtil.string2Obj(redisValue
                                        , List.class, FundFavorSubAccCountListResponse.class);
                                    if (!CollectionUtils.isEmpty(userList)) {
                                        for (FundFavorSubAccCountListResponse user : userList) {
                                            //关注用户
                                            PassportUserInfoModelNew favorUser = passportUserInfoDao.getPassportUserInfoById(user.Uid);
                                            /*
                                             * 如果关注的是自己则不给自己发
                                             */
                                            if (favorUser != null && StringUtils.hasLength(favorUser.PassportID)
                                                && !favorUser.PassportID.equals(managerUser.PassportID)) {
                                                SubAccReplyOrFavorSubAccDto pushModel = new SubAccReplyOrFavorSubAccDto();
                                                pushModel.EID = "FavorSubAccDto" + UUID.randomUUID().toString();
                                                pushModel.PassportID = favorUser.PassportID == null ? "" : favorUser.PassportID;
                                                pushModel.PassportName = favorUser.NickName == null ? "" : favorUser.NickName;
                                                pushModel.ReplyPassportID = managerUser.PassportID == null ? "" : managerUser.PassportID;
                                                pushModel.ReplyPassportName = managerUser.NickName == null ? "" : managerUser.NickName;
                                                pushModel.Content = item.CONTENT == null ? "" : item.CONTENT;
                                                pushModel.PostID = String.valueOf(item.ID);
                                                pushModel.BarCode = item.CODE == null ? "" : item.CODE.replace("43-", "");
                                                pushModel.BarName = accInfo.Name == null ? "" : accInfo.Name;
                                                pushModel.BarAdminUserName = managerUser.NickName == null ? "" : managerUser.NickName;
                                                pushModel.Source = "subaccount";
                                                pushModel.SourceType = 1;
                                                pushModel.SourceID = String.valueOf(item.ID);

                                                //推送历史记录
                                                SubPushHis subPushHis = new SubPushHis();
                                                subPushHis.Id = pushModel.PostID + pushModel.PassportID;
                                                subPushHis.PassportID = pushModel.PassportID;
                                                subPushHis.PostID = pushModel.PostID;
                                                subPushHis.Type = EnumSubPushHisType.ZHDT.getValue();

                                                //推送消息
                                                kafkaTemplate.send(KafkaTopicName.JIJINBA_SUBACC_PUSH_FAVORDTO,
                                                    pushModel.PostID + "_" + pushModel.PassportID, JSON.toJSONStringWithDateFormat(pushModel, "yyyy-MM-dd'T'HH:mm:ss"));
                                                //子帐户推送历史
                                                subpushHisDao.insert(subPushHis);
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (Exception ex) {
                            logger.error(ex.getMessage(), ex);
                        }
                    }

                    logger.info("subAccountFavorDtoPushMsg-3.推送数据-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        postList == null ? 0 : postList.size(),
                        postList == null ? null : postList.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

                    breakpointDate = postList.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;
                    breakpoint = DateUtil.dateToStr(breakpointDate);
                    userRedisDao.set(breakpointName, breakpoint, 3600 * 24 * 30L);

                    logger.info("subAccountFavorDtoPushMsg-4.更新断点-第{}轮。断点：{}", round, breakpoint);

                }

                if (postList == null || postList.size() < batchReadCount) {
                    break;
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return true;
    }

    /**
     * 话题 置顶帖
     */
    public boolean subAccountTopPost() {
        String key = BarRedisKey.FUND_SERVICE_JIJINBA_SUBACC_TOPICTOPPOST;
        try {

            try {

                String data = HttpHelper.requestPostJson(appConstant.topPostUrl, null, true);
                TopPostApiResponse dynamicData = JacksonUtil.string2Obj(data, TopPostApiResponse.class);

                logger.info("subAccountTopPost-1.读取数据。数量:{}，头部id列表：{}",
                    dynamicData == null || dynamicData.getRe() == null ? 0 : dynamicData.getRe().size(),
                    dynamicData == null || CollectionUtils.isEmpty(dynamicData.getRe()) ? null : JSON.toJSONStringWithDateFormat(dynamicData.getRe().get(0), DateUtil.datePattern)
                );

                List<TopicTopPostInfo> list = new ArrayList<>();
                if (dynamicData != null && dynamicData.getError_code() == 0) {
                    String[] configs = appConstant.hotTopicConfig.split(",");
                    if (!CollectionUtils.isEmpty(dynamicData.getRe())) {
                        for (TopPostModel item : dynamicData.getRe()) {
                            String topicid = item.getTopicid();//话题id
                            String postid = item.getPostid();//帖子id
                            TopicTopPostInfo model = postInfoNewDao.getTopicAndPostInfo(postid, topicid);
                            if (model != null && StringUtils.hasLength(model.HTID)) {
                                model.SCORE = model.PARTICIPANTCOUNT / Integer.parseInt(configs[0])
                                    - ((new Date().getTime() - model.SHOWTIME.getTime()) / (1000 * 3600 * 24.0)) * Integer.parseInt(configs[1]);
                                list.add(model);
                            }
                        }
                        if (list.size() > 0) {
                            app.barredis.set(key, JSON.toJSONStringWithDateFormat(list, "yyyy-MM-dd'T'HH:mm:ss"));
                        }

                        logger.info("subAccountTopPost-2.写缓存。数量:{}，头部id列表：{}",
                            list == null ? 0 : list.size(),
                            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
                        );
                    }
                }

            } catch (Exception ex) {
                logger.error(ex.getMessage(), ex);
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return true;
    }

    /**
     * 清除七天之前的历史数据
     */
    private boolean delHistoryShieldBrowseAndUser() {
        try {
            Date date = DateUtil.calendarDateByDays(-7);
            shieldBrowseDao.delByEutime(date);
            shieldUserDao.delByEutime(date);

            logger.info("delHistoryShieldBrowseAndUser-1.删除数据。时间：{}", DateUtil.dateToStr(date));
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return true;
    }

    /**
     * 计算两个日期相差的小时数
     */
    private double getHours(Date date1, Date date2) {
        return (date1.getTime() - date2.getTime()) / (3600.0 * 1000.0);
    }

    /**
     * 移除html标签
     */
    private String removeHtmlTag(String strhtml) {
        String result = strhtml;
        if (StringUtils.hasLength(strhtml)) {
            result = StringEscapeUtils.unescapeHtml4(strhtml);
            result = result.replaceAll("<[^>]+>|</[^>]+>", "");
        }
        return result;
    }

    /**
     * 判断是否包含敏感词
     */
    private boolean isContainsSensitiveword(String content) {
        boolean result = false;
        String cacheKey = "cache_config_DBSensitivewordConfig";
        Set<String> sensitiveWords = CacheHelper.get(cacheKey);
        if (sensitiveWords == null) {
            List<String> list = sensitiveWordConfigDao.getAll();
            if (list == null) {
                list = new ArrayList<>();
            }

            sensitiveWords = list.stream().collect(Collectors.toSet());
            CacheHelper.put(cacheKey, sensitiveWords, 30 * 60 * 1000);

        }

        for (String a : sensitiveWords) {
            if (content.contains(a)) {
                result = true;
                break;
            }
        }

        return result;
    }

}
