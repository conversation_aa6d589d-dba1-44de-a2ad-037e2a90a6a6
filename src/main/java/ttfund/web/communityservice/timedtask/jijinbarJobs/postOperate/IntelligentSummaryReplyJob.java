package ttfund.web.communityservice.timedtask.jijinbarJobs.postOperate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.IntelligentSummaryReplyByAtDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.dao.vertica.PostDigestBasicDrctAllDao;
import ttfund.web.communityservice.service.ReplyServiceImpl;
import ttfund.web.communityservice.service.entity.AddReplyRequest;
import ttfund.web.communityservice.service.entity.AddReplyResponse;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能摘要发评论job
 */
@JobHandler("IntelligentSummaryReplyJob")
@Component
public class IntelligentSummaryReplyJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(IntelligentSummaryReplyJob.class);

    private static String TEXT_FORMAT_AI_SUMMARY = "%s\n本内容由小助理生成，点击头像查看更多精彩内容";

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostDigestBasicDrctAllDao postDigestBasicDrctAllDao;

    @Autowired
    private ReplyServiceImpl replyService;

    @Autowired
    private IntelligentSummaryReplyByAtDao intelligentSummaryReplyByAtDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            String pic = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                pic = jsonObject.getString("pic");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }
            if (pic == null) {
                pic = "https://gbres.dfcfw.com/Files/picture/20240912/79C6F00877B08176DB1E7EBFCD4AC475_w588h192.jpg";
            }

            logger.info("0.打印参数。initBreakpoint：{}，batchReadCount：{}，pic：{}",
                    initBreakpoint,
                    batchReadCount,
                    pic
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.INTELLIGENTSUMMARYREPLYJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("0.初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            deal(batchReadCount, pic);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount, String pic) throws Exception {
        String breakpointName = UserRedisConfig.INTELLIGENTSUMMARYREPLYJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("0.读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("1.读取断点。断点:{}", breakpoint);


        List<Map<String, Object>> dataList = postDigestBasicDrctAllDao.getListByUpdatetime(breakpointDate, batchReadCount);

        logger.info("2.读取数据。数量:{}，头部id列表：{}",
                dataList == null ? 0 : dataList.size(),
                dataList == null ? null : dataList.stream().map(a -> a.get("POSTID")).limit(20).collect(Collectors.toList()));

        if (!CollectionUtils.isEmpty(dataList)) {

            breakpointDate = (Date) dataList.stream().max((Comparator.comparing(o -> (Date) o.get("EUTIME")))).get().get("EUTIME");

            int i = 0;
            for (Map<String, Object> a : dataList) {
                i++;

                a.put("pic", pic);

                addReply(a);

                postDigestBasicDrctAllDao.updateOne(a);

                logger.info("3.发评论详情-第{}/{}个。帖子id：{}，生成评论id：{}，错误信息：{}",
                        i,
                        dataList.size(),
                        a.get("POSTID"),
                        a.get("REPLYID"),
                        a.get("MESSAGE")
                );
            }

            logger.info("3.发评论完成。数量:{}，头部id列表：{}",
                    dataList == null ? 0 : dataList.size(),
                    dataList == null ? null : dataList.stream().map(a -> a.get("POSTID")).limit(20).collect(Collectors.toList()));

        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        logger.info("4.更新断点。断点：{}", breakpoint);

    }

    private void addReply(Map<String, Object> map) {
        //1：待发送
        //2：发送成功
        //3：发送失败
        //4：已处理过
        int status = 3;
        Long replyid = null;
        String message = null;

        Document document = intelligentSummaryReplyByAtDao.getOneByKeyIsValue(
                "_id",
                map.get("POSTID").toString(),
                Arrays.asList("_id", "aiReplyId"),
                Document.class
        );

        if (document != null) {
            status = 4;
        } else {

            AddReplyRequest request = new AddReplyRequest();
            request.setId(map.get("POSTID").toString());
            request.setText(String.format(TEXT_FORMAT_AI_SUMMARY, map.get("DIGEST").toString()));
            request.setPic(map.get("pic") == null ? "" : map.get("pic").toString());
            request.setT_type(0);
            request.setUid(appConstant.intelligentSummaryReplyUid);
            request.setBusiness_type(3);
            AddReplyResponse addReplyResponse = replyService.addReply(request);
            if (addReplyResponse != null && addReplyResponse.getReplyId() > 0) {
                replyid = addReplyResponse.getReplyId();
                status = 2;
            } else if (addReplyResponse != null && StringUtils.hasLength(addReplyResponse.getMe())) {
                message = addReplyResponse.getMe();
            } else {
                message = "默认信息：调股吧发评论接口失败";
            }

        }

        map.put("STATUS", status);
        map.put("REPLYID", replyid);
        map.put("MESSAGE", (message != null && message.length() > 12) ? message.substring(0, 12) : message);
        map.put("UPDATETIME", new Date());
    }

}
