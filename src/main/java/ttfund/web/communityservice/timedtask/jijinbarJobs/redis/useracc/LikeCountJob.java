package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.useracc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.core.register.AppCore;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.UserAccRedisKey;
import ttfund.web.communityservice.dao.msyql.LikeCountDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;

/**
 * 用户回答点赞数
 */
@JobHandler(value = "likeCountJob")
@Component
public class LikeCountJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(LikeCountJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private LikeCountDao likeCountDao;

    @Autowired
    private AppCore appCore;

    @Override
    public ReturnT<String> execute(String s) {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.LIKECOUNTJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            logicDeal(batchReadCount);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        return ReturnT.SUCCESS;
    }

    private void logicDeal(int batchReadCount) {

        try {
            // 用户回答点赞数统计 fund_guba_service_answerlikecount_{uid}

            String breakpointName = UserRedisConfig.LIKECOUNTJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("第一步，读取断点。断点:{}", breakpoint);

            int round = 0;

            while (true) {
                round++;

                List<Map<String, Object>> list = new ArrayList<>();

                List<String> uids = likeCountDao.getLikeCountUids(breakpointDate, batchReadCount);

                logger.info("第二步，读取点赞用户-第{}轮。数量:{}，头部数据：{}",
                        round,
                        CollectionUtils.isEmpty(uids) ? 0 : uids.size(),
                        CollectionUtils.isEmpty(uids) ? null : uids.get(0)
                );

                if (!CollectionUtils.isEmpty(uids)) {
                    List<List<String>> batchList = CommonUtils.toSmallList2(uids, 200);
                    for (List<String> batch : batchList) {
                        List<Map<String, Object>> tempList = likeCountDao.getAnswerLikeCounts(batch);
                        if (!CollectionUtils.isEmpty(tempList)) {
                            list.addAll(tempList);
                        }
                    }
                }

                logger.info("第三步，读取用户回答点赞数-第{}轮。数量:{}，头部数据：{}",
                        round,
                        CollectionUtils.isEmpty(list) ? 0 : list.size(),
                        CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
                );


                if (!CollectionUtils.isEmpty(list)) {

                    breakpointDate = list.stream()
                            .map(l -> (Date) l.get("UPDATETIME"))
                            .max(Comparator.naturalOrder())
                            .get();

                    String cacheKey = null;
                    int i = 0;
                    for (Map<String, Object> item : list) {
                        i++;

                        cacheKey = UserAccRedisKey.ANSWER_LIKE_COUNT_PASSPORT + item.get("UID").toString();
                        appCore.redisuserwrite.set(cacheKey, item.get("LIKECOUNT").toString());

                        logger.info("第四步，写redis详情-第{}轮。第{}/{}个。uid:{}，值：{}",
                                round,
                                i,
                                list.size(),
                                item.get("UID").toString(),
                                item.get("LIKECOUNT").toString()
                        );
                    }

                }

                logger.info("第四步，写redis完成-第{}轮。数量:{}，头部数据：{}",
                        round,
                        CollectionUtils.isEmpty(list) ? 0 : list.size(),
                        CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
                );

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("第五步，更新断点-第{}轮。断点：{}", round, breakpoint);

                if (list == null || list.size() < batchReadCount) {
                    break;
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

    }
}
