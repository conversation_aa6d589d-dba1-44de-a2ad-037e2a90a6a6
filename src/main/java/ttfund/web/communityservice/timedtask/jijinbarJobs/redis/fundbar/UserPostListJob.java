package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.SimplePostModel;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.SimplePostModelNew;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.JjbconfigDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 个人主页帖子列表
 */
@JobHandler("UserPostListJob")
@Component
public class UserPostListJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(UserPostListJob.class);

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private JjbconfigDao jjbconfigDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        logicDeal(s);

        return ReturnT.SUCCESS;
    }

    public void logicDeal(String s) {
        try {

            //限制多少天
            Integer keepDays = -90;
            //每页大小
            Integer cachePageSize = 500;

            //基金吧类型配置
            String initConfigs = null;

            String initBreakpoint = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                keepDays = jsonObject.getInteger("keepDays");
                cachePageSize = jsonObject.getInteger("cachePageSize");
                initConfigs = jsonObject.getString("initConfigs");
                initBreakpoint = jsonObject.getString("initBreakpoint");
            }

            if (keepDays == null) {
                keepDays = -90;
            }
            if (cachePageSize == null) {
                cachePageSize = 500;
            }

            logger.info("第零步，打印参数。keepDays：{}，cachePageSize：{}，initConfigs：{}，initBreakpoint：{}", keepDays, cachePageSize, initConfigs, initBreakpoint);

            String breakpointName = UserRedisConfig.USER_POST_LIST_JOB_BREAKPOINT;

            if (StringUtils.hasLength(initBreakpoint)) {
                DateUtil.strToDate(initBreakpoint, DATE_FORMAT);
                userRedisDao.set(breakpointName, initBreakpoint, 60 * 24 * 3600L);
                logger.info("第零-1步，设置初始化断点。initBreakpoint：{}", initBreakpoint);
                return;
            }

            //上次更新时间
            String breakpoint = userRedisDao.get(breakpointName);
            if (!StringUtils.hasLength(breakpoint)) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1), DATE_FORMAT);
                logger.error("第一-0步，读取断点为空，使用默认断点。breakpoint：{}", breakpoint);
            }
            logger.info("第一步，读取断点。breakpoint：{}", breakpoint);

            Date dateForBreakpoint = DateUtil.strToDate(breakpoint, DATE_FORMAT);
            int round = 0;
            while (true) {
                round++;

                List<SimplePostModelNew> list = postInfoNewDao.getUpdateSimpleListNew(dateForBreakpoint, keepDays, appConstant.batchCount, getCodeTypesSpecial(initConfigs));

                logger.info("第二步，读取帖子。第{}轮。数量：{},头部id列表：{}",
                        round,
                        list == null ? 0 : list.size(),
                        list == null ? null : list.stream().map(a -> a.Id).limit(50).collect(Collectors.toList()));

                if (!CollectionUtils.isEmpty(list)) {

                    //设置最后更新时间
                    dateForBreakpoint = list.stream().max(Comparator.comparing(o -> o.UpdateTime)).get().UpdateTime;
                    //根据用户ID分组
                    Map<String, List<SimplePostModelNew>> groupList = list.stream().collect(Collectors.groupingBy(a -> a.UId));
                    Set<Map.Entry<String, List<SimplePostModelNew>>> entries = groupList.entrySet();
                    for (Map.Entry<String, List<SimplePostModelNew>> entry : entries) {
                        //缓存名字
                        String cacheKey = String.format(BarRedisKey.FUND_GUBA_SERVICE_USER_POST_INFO_POST_LIST_NEW, entry.getKey());
                        //老的缓存数据
                        String value = app.barredis.get(cacheKey);

                        logger.info("第三步，读取帖子列表缓存。用户id：{}，是否有值：{}", entry.getKey(), StringUtils.hasLength(value));

                        List<SimplePostModel> cacheList = null;
                        if (StringUtils.hasLength(value)) {
                            cacheList = JacksonUtil.string2Obj(value, List.class, SimplePostModel.class);
                        }
                        if (cacheList == null) {
                            cacheList = new ArrayList<>();
                        }

                        //增量更新的帖子ID列表
                        List<Integer> updatePostIds = entry.getValue().stream().map(a -> a.Id).collect(Collectors.toList());
                        //从老的缓存中剔除已经存在的帖子
                        cacheList = cacheList.stream().filter(a -> !updatePostIds.contains(a.Id)).collect(Collectors.toList());

                        //添加新增的帖子列表
                        cacheList.addAll(entry.getValue());
                        //重新排序准备再次插入
                        cacheList = cacheList.stream()
                                .sorted((o1, o2) -> Long.compare(o2.TimePoint, o1.TimePoint))
                                .limit(cachePageSize)
                                .collect(Collectors.toList());

                        logger.info("第四步，帖子列表刷新处理。用户id：{}，头部帖子列表：{}", entry.getKey(), cacheList.stream().map(a -> a.Id).limit(5).collect(Collectors.toList()));

                        Boolean setResult = app.barredis.set(cacheKey, JacksonUtil.obj2String(cacheList), 90 * 24 * 3600L);
                        if (setResult != null && !setResult) {
                            app.barredis.set(cacheKey, JacksonUtil.obj2String(cacheList), 90 * 24 * 3600L);
                            if (setResult != null && !setResult) {
                                logger.error("写帖子列表缓存失败。用户id：{}，头部帖子列表：{}，结果：{}",
                                        entry.getKey(),
                                        cacheList.stream().map(a -> a.Id).limit(5).collect(Collectors.toList()),
                                        setResult);
                            }
                        }

                        logger.info("第五步，更新帖子列表缓存。用户id：{}，头部帖子列表：{}，更新结果：{}",
                                entry.getKey(),
                                cacheList.stream().map(a -> a.Id).limit(5).collect(Collectors.toList()),
                                setResult);


                    }

                }

                if (list == null || list.size() < appConstant.batchCount) {
                    //保存断点

                    breakpoint = DateUtil.dateToStr(dateForBreakpoint, DATE_FORMAT);
                    userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                    logger.info("第六步，更新断点。断点：{}", breakpoint);
                    break;
                }
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }


    private List<String> getCodeTypesSpecial(String initConfigs) {
        List<String> result = new ArrayList<>();
        if (StringUtils.hasLength(initConfigs)) {
            result.addAll(CommonUtils.toList(initConfigs, ","));
        }
        List<String> codeTypes = jjbconfigDao.getCodeTypes();
        if (!CollectionUtils.isEmpty(codeTypes)) {
            result.addAll(codeTypes);
        }

        result = result.stream().distinct().collect(Collectors.toList());
        return result;
    }


}
