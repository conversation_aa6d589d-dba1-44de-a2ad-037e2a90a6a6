package ttfund.web.communityservice.timedtask.jijinbarJobs.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostinfoExtraModel;
import ttfund.web.communityservice.bean.jijinBar.post.data.PostCountHisModel;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.msyql.PostCountHisDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 帖子点赞数、评论数、阅读数历史拍照数据
 * 只保留近4个小时数据
 */
@Slf4j
@JobHandler("PostCountHisJob")
@Component
public class PostCountHisJob extends IJobHandler {

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoExtraDao postInfoExtraDao;

    @Autowired
    private PostCountHisDao postCountHisDao;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            String codes = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                codes = jsonObject.getString("codes");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            if (codes == null) {
                codes = "jjft";
            }

            log.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，codes：{}",
                    initBreakpoint,
                    batchReadCount,
                    codes
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.POSTCOUNTHISJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            logicDeal(batchReadCount, CommonUtils.toList(codes, ","));

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void logicDeal(int batchReadCount, List<String> codes) {

        String breakpointName = UserRedisConfig.POSTCOUNTHISJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            log.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("第一步，读取断点。断点:{}", breakpoint);


        List<PostinfoExtraModel> list = postInfoExtraDao.getByUpdateTime(breakpointDate, DateUtil.calendarDateByMonth(-1), batchReadCount);

        log.info("第二步，读取数据。数量:{}，头部列表：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = list.get(list.size() - 1).UPDATETIME;

            list.forEach(a -> {
                a.CLICKNUM = a.CLICKNUM == null ? 0 : a.CLICKNUM;
                a.LIKECOUNT = a.LIKECOUNT == null ? 0 : a.LIKECOUNT;
                a.PINGLUNNUM = a.PINGLUNNUM == null ? 0 : a.PINGLUNNUM;
                a.SUBPINGLUNNUM = a.SUBPINGLUNNUM == null ? 0 : a.SUBPINGLUNNUM;
            });

            //排除直播贴（code='jjft'，记录直播信息，以及直播评论）
            if (!CollectionUtils.isEmpty(codes)) {
                list = list.stream().filter(a -> !codes.contains(a.CODE)).collect(Collectors.toList());
            }

            log.info("第三步，过滤数据。数量:{}，头部id列表：{}",
                    CollectionUtils.isEmpty(list) ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

            List<PostCountHisModel> hisPicList = convertToPCoutHisList(list);
            if (!CollectionUtils.isEmpty(hisPicList)) {
                List<List<PostCountHisModel>> batchList = CommonUtils.toSmallList2(hisPicList, 50);
                for (List<PostCountHisModel> batch : batchList) {
                    postCountHisDao.insertOrUpdate(batch);
                }

            }

            log.info("第四步，写库。数量:{}，头部id列表：{}",
                    CollectionUtils.isEmpty(hisPicList) ? 0 : hisPicList.size(),
                    CollectionUtils.isEmpty(hisPicList) ? null : JSON.toJSONStringWithDateFormat(hisPicList.get(0), DateUtil.datePattern)
            );

        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        log.info("第五步，更新断点。断点：{}", breakpoint);
    }

    /**
     * 帖子统计信息转换为快照信息
     */
    private List<PostCountHisModel> convertToPCoutHisList(List<PostinfoExtraModel> list) {
        List<PostCountHisModel> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            //当前时间的小时
            int type = (LocalTime.now().getHour() % 4) + 1;
            PostCountHisModel model = null;
            for (PostinfoExtraModel item : list) {

                //帖子统计快照信息
                model = new PostCountHisModel();
                model.CLICKNUM = item.CLICKNUM;
                model.LIKECOUNT = item.LIKECOUNT;
                model.PINGLUNNUM = (int) (item.PINGLUNNUM + item.SUBPINGLUNNUM);//评论+子评论数
                model.PostId = item.ID;
                model.Type = type;
                model.UPDATETIME = new Date();
                model.ID = model.PostId + "_" + model.Type;

                result.add(model);
            }

        }

        return result;
    }

}
