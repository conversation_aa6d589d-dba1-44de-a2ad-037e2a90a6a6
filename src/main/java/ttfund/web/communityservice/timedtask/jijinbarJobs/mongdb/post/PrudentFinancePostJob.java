package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mongodb.client.result.DeleteResult;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoExt;
import ttfund.web.communityservice.bean.jijinBar.post.PostVideoInfo;
import ttfund.web.communityservice.bean.jijinBar.post.finance.WJLCPostInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.finance.WJLCVUserModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.FindRecommendPostDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.mongo.PrudentFinanceDao;
import ttfund.web.communityservice.dao.mongo.WJLCPostInfoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 稳健理财专区 - 理财同路人
 */
@JobHandler(value = "prudentFinancePostJob")
@Component
public class PrudentFinancePostJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PrudentFinancePostJob.class);

    @Autowired
    private PrudentFinanceDao prudentFinanceDao;

    @Autowired
    private WJLCPostInfoDao wjlcPostInfoDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostDao postDao;

    @Autowired
    private FindRecommendPostDao findRecommendPostDao;

    @Autowired
    private App app;

    @Value("${finance.find.group}")
    private String groupName;

    private static final Set<Integer> typeSet = new HashSet<>(Arrays.asList(0, 20, 43, 48, 48, 50, 58));

    @Override
    public ReturnT<String> execute(String param) {

        logger.info("稳健理财专区-理财同路人开始");

        ReturnT result = ReturnT.SUCCESS;

        try {

            Date now = DateUtil.getNowDate();
            String breakTimeKey = "prudentFinancePostBreakTime";
            Date lastUpdatetime = userRedisDao.getBreakTime(breakTimeKey);

            // 获取有效作者，已删除作者的帖子从帖子底池表移除
            List<WJLCVUserModel> authorInfoList = getValidUserList(lastUpdatetime);
            logger.info("需要处理的作者数量：" + authorInfoList.size());

            if (CollectionUtils.isEmpty(authorInfoList)) {
                logger.info("没有需要处理的帖子");
            } else {
                // 全量更新
                fullUpdate(
                        authorInfoList.stream()
                                .map(WJLCVUserModel::getUid)
                                .collect(Collectors.toList())
                );
            }

            // 一年前的帖子删除
            wjlcPostInfoDao.deleteOldPost();

            // 设置时间断点
            userRedisDao.setBreakTime(breakTimeKey, now);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }

        // 塞缓存
        try {
            // 最热
            saveDataToRedis("SCORE", "TIME", BarRedisKey.PRUDENT_FINANCE_HOT_POST, -14);
            // 最新
            saveDataToRedis("TIME", "SCORE", BarRedisKey.PRUDENT_FINANCE_LAST_POST, null);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }

        logger.info("稳健理财专区-理财同路人结束");

        return result;

    }

    /**
     * 往Redis保存最热数据
     *
     * @param sortField1
     * @param sortField2
     * @param key
     * @param daysOffset
     */
    private void saveDataToRedis(String sortField1, String sortField2, String key, Integer daysOffset) {

        logger.info("往Redis保存数据开始");
        int pageSize = 5000;
        int pageIndex = 1;

        while (true) {

            List<WJLCPostInfoModel> postList = wjlcPostInfoDao.findAllPost(
                pageIndex,
                pageSize,
                sortField1,
                sortField2,
                daysOffset
            );

            if (CollectionUtils.isEmpty(postList)) {
                break;
            }

            app.barredis.set(key + pageIndex, JacksonUtil.obj2StringNoException(postList), 7 * 24 * 60 * 60L);

            logger.info("往Redis保存【{}】数量：{}", key, postList.size());

            if (postList.size() < pageSize) {
                break;
            }

            pageIndex++;
        }

        logger.info("往Redis保存数据结束");
    }

    /**
     * 数据转换
     *
     * @param scoreMap
     * @param toSaveModelList
     * @param item
     */
    private void mergeData(Map<Long, Long> scoreMap, List<WJLCPostInfoModel> toSaveModelList, PostInfoExt item) {
        WJLCPostInfoModel model = new WJLCPostInfoModel();
        model.setId(String.valueOf(item.ID));
        model.setTid(item.ID);
        model.setUid(item.UID);
        model.setTime(item.TIME);
        model.setQid(item.QID);
        model.setCode(item.CODE);
        model.setCreateTime(DateUtil.getNowDate());
        model.setUpdateTime(DateUtil.getNowDate());
        model.setIsDel(0);
        model.setScore(scoreMap.getOrDefault(item.ID, 0L));
        toSaveModelList.add(model);
    }

    /**
     * 全量更新
     *
     * @param userList
     * @throws JsonProcessingException
     */
    private void fullUpdate(List<String> userList) throws JsonProcessingException {

        logger.info("全量处理开始");

        // 近一年
        Date beginDate = DateUtil.calendarDateByYears(-1);

        List<PostInfoExt> validPostList = new ArrayList<>();
        Map<Long, Long> postScoreMap = new HashMap<>();
        List<WJLCPostInfoModel> toSaveModelList = new ArrayList<>();

        int size = 2000;

        while (true) {

            // 从PostInfo表获取符合条件的帖子
            List<PostInfoExt> postList = postDao.getFinanceFindPost(beginDate, userList, size);

            logger.info("PostInfo中的帖子数量：" + postList.size());

            if (CollectionUtils.isEmpty(postList)) {
                break;
            }

            beginDate = postList.get(postList.size() - 1).TIME;

            // PostInfo表中标记删除或者没有图和视频的帖子，从帖子底池表删除
            List<Long> toDeleteList = postList.stream()
                    .filter(w -> !FinanceFindHotPostJob.isValid(w))
                    .map(w -> w.ID)
                    .collect(Collectors.toList());

            logger.info("PostInfo表中标记删除或者没有图和视频的数量：" + toDeleteList.size());

            removeDeletedPost(toDeleteList);

            // 获取得分
            List<PostInfoExt> queryScoreList = postList.stream()
                    .filter(FinanceFindHotPostJob::isValid)
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(queryScoreList)) {

                Set<Long> idSet = queryScoreList.stream()
                        .map(w -> w.ID)
                        .collect(Collectors.toSet());

                findRecommendPostDao.findByID(idSet, groupName, postScoreMap);

                validPostList.addAll(queryScoreList);
            }

            if (postList.size() < size) {
                break;
            }
        }

        Set<Long> toDeleteSet = new HashSet<>();

        // 判断重复内容
        FinanceFindHotPostJob.checkDuplicate(validPostList, toDeleteSet);

        // 内容重复的从帖子底池删除
        removeDeletedPost(new ArrayList<>(toDeleteSet));

        // 数据封装
        for (PostInfoExt item : validPostList) {
            if (toDeleteSet.contains(item.ID)) {
                continue;
            }
            mergeData(postScoreMap, toSaveModelList, item);
        }

        // 保存
        HashMap<Integer, List<WJLCPostInfoModel>> subMap = CommonUtils.toSmallList(toSaveModelList, size);

        for (int i = 0; i < subMap.size(); i++) {

            List<WJLCPostInfoModel> subList = subMap.get(i);

            boolean flag = wjlcPostInfoDao.upsertBulk(subList);

            if (!flag) {
                logger.error("帖子底池表保存失败，数据：" + JacksonUtil.obj2String(toSaveModelList));
            } else {
                logger.info("全量更新-保存的数量：{}", toSaveModelList.size());
            }
        }

        logger.info("全量处理结束");
    }

    /**
     * 判断重复内容
     *
     * @param validPostList
     * @param postScoreMap
     * @param toDeleteSet
     */
    private static void checkDuplicate(List<PostInfoExt> validPostList, Map<Long, Long> postScoreMap,
                                       Set<Long> toDeleteSet) {

        Map<String, Map<String, PostInfoExt>> summaryMap = new HashMap<>();
        for (PostInfoExt item : validPostList) {
            if (StringUtils.isEmpty(item.SUMMARY)) {
                continue;
            }
            String summary = item.SUMMARY.replaceAll("[^\u4e00-\u9fa5]", "");
            if (summary.length() >= 50) {
                summary = summary.substring(0, 50);
            } else {
                continue;
            }
            Map<String, PostInfoExt> userSummaryMap = summaryMap.getOrDefault(item.UID, new HashMap<>());
            summaryMap.put(item.UID, userSummaryMap);
            if (userSummaryMap.containsKey(summary)) {
                PostInfoExt duplicateItem = userSummaryMap.get(summary);
                if (duplicateItem.TIME != null && duplicateItem.TIME.after(item.TIME)) {
                    toDeleteSet.add(item.ID);
                } else {
                    toDeleteSet.add(duplicateItem.ID);
                    userSummaryMap.put(summary, item);
                }
            } else {
                userSummaryMap.put(summary, item);
            }
        }
    }

    /**
     * 判断是否符合条件的帖子
     * 未删除、有图片或者视频、直播贴
     *
     * @param post
     * @return
     */
    public static boolean isValid(PostInfoExt post) {

        if (post.DEL == 1 || post.TTJJDEL == 1) {
            return false;
        }

        // 直播贴过滤
        if (post.ZMTLKType != null && post.ZMTLKType == 2) {
            return false;
        }

        if (!typeSet.contains(post.TYPE)) {
            return false;
        }

        if (!StringUtils.isEmpty(post.ALLPIC)) {
            if (isValidPic(post)) {
                return true;
            }
        }

        if (!CollectionUtils.isEmpty(post.Videos)) {
            for (PostVideoInfo video : post.Videos) {
                if (!StringUtils.isEmpty(video.ZMTLKVideoID) && !StringUtils.isEmpty(video.ZMTLKCover)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 校验图片是否合法
     *
     * @param post
     * @return
     */
    private static boolean isValidPic(PostInfoExt post) {
        String[] pics = post.ALLPIC.split(",");
        if (pics.length >= 1) {
            String pic = pics[0];
            if (pic.contains("empic.dfcfw.com")) {
                // 财富号
                String[] arrs = pic.split("/");
                if (arrs.length >= 3 && arrs[arrs.length - 2].contains("w")
                        && arrs[arrs.length - 2].contains("h")) {
                    return true;
                }
            } else if (pic.contains("@!gbpic") || pic.contains("gbres.dfcfw.com") || pic.contains("gbrestest.dfcfw.com")) {
                // 股吧
                return true;
            } else if (pic.contains("webquotepic.eastmoney.com/GetPic.aspx") && pic.contains("&imageType=r&")) {
                // 分时图
                return true;
            } else if (pic.contains("webquoteklinepic.eastmoney.com/GetPic.aspx")
                    && (pic.contains("&imageType=knews&") || pic.contains("&imageType=k&"))) {
                // k线图
                return true;
            }
        }
        return false;
    }

    /**
     * PostInfo表中标记删除的帖子，从帖子底池表删除
     *
     * @param toDeleteList
     * @throws JsonProcessingException
     */
    private void removeDeletedPost(List<Long> toDeleteList) throws JsonProcessingException {

        if (CollectionUtils.isEmpty(toDeleteList)) {
            return;
        }

        DeleteResult deleteResult = wjlcPostInfoDao.deleteByPostIds(toDeleteList);

        if (deleteResult != null && deleteResult.getDeletedCount() >= 0) {
            logger.info("帖子底池表删除的数量：{}", deleteResult.getDeletedCount());
        } else {
            logger.error("帖子底池根据帖子ID删除失败，帖子ID：" + JacksonUtil.obj2String(toDeleteList));
        }
    }

    /**
     * 获取作者列表
     *
     * @param lastUpdatetime
     * @return
     */
    private List<WJLCVUserModel> getValidUserList(Date lastUpdatetime) {

        List<WJLCVUserModel> result = new ArrayList<>();

        try {
            // 查询所有用户信息
            List<WJLCVUserModel> authorList = prudentFinanceDao.findAll();

            logger.info("查询到的作者数量：" + authorList.size());

            // 已标记删除的作者帖子直接从帖子底池删除
            List<String> deletedUserList = authorList.stream()
                    .filter(w -> w.getIsDel() == 1
                            && (lastUpdatetime == null || w.getUpdateTime().after(lastUpdatetime)))
                    .map(WJLCVUserModel::getUid)
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(deletedUserList)) {

                DeleteResult deleteResult = wjlcPostInfoDao.deleteByUsers(deletedUserList);

                if (deleteResult != null && deleteResult.getDeletedCount() >= 0) {
                    logger.info("根据通行证ID删除帖子成功，删除数量：" + deleteResult.getDeletedCount());
                } else {
                    logger.error("根据通行证ID删除帖子失败，通行证ID：" + JacksonUtil.obj2String(deletedUserList));
                }
            }

            return authorList.stream()
                    .filter(w -> w.getIsDel() == 0)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        return result;
    }
}
