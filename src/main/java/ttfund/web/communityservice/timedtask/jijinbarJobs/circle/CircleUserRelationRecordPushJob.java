package ttfund.web.communityservice.timedtask.jijinbarJobs.circle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.CircleBaseInfoAndExtendInfo;
import ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelationRecord;
import ttfund.web.communityservice.bean.jijinBar.data.CommunityPushInfoKafkaModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.ApiRedisConstantConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.CircleBaseInfoDao;
import ttfund.web.communityservice.dao.msyql.CircleUserRelationRecordDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.enums.CommunityPushRemindTypeEnum;
import ttfund.web.communityservice.enums.LeaveCircleErrorEnum;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 圈子用户关系流水推送job
 * 备注：    需求 #678045 【基金吧6.15】新增圈子功能
 */
@Slf4j
@JobHandler("CircleUserRelationRecordPushJob")
@Component
public class CircleUserRelationRecordPushJob extends IJobHandler {

    public static final String PUBLISHER_PATTERN = "yyyy-MM-dd_HH:mm:ss.SSS";

    @Autowired
    private App app;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private CircleBaseInfoDao circleBaseInfoDao;

    @Autowired
    private CircleUserRelationRecordDao circleUserRelationRecordDao;

    @Autowired
    @Qualifier(KafkaConfig.fundbar_kafka_temp_beanname)
    private KafkaTemplate<String, String> kafkaTemplate;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            Long expire = null;
            Boolean force = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                expire = jsonObject.getLong("expire");
                force = jsonObject.getBoolean("force");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            if (expire == null) {
                expire = 2 * 3600L;
            }


            if (force == null) {
                force = false;
            }


            log.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，expire：{}，force：{}",
                initBreakpoint,
                batchReadCount,
                expire,
                force
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.CIRCLEUSERRELATIONRECORDPUSHJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            deal(batchReadCount, expire, force);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void deal(int batchReadCount, Long expire, boolean force) {

        String breakpointName = UserRedisConfig.CIRCLEUSERRELATIONRECORDPUSHJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            log.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("第一步，读取断点。断点:{}", breakpoint);


        List<CircleUserRelationRecord> list = circleUserRelationRecordDao.getListByGt(breakpointDate, null, 1, null, batchReadCount);

        log.info("第二步，读取流水。数量:{}，头部列表：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = list.get(list.size() - 1).getUpdateTime();

            Map<String, CircleBaseInfoAndExtendInfo> circleMap = new HashMap<>();
            {
                List<String> circleIds = list.stream().map(a -> a.getCircleId()).distinct().collect(Collectors.toList());
                List<List<String>> batchList = CommonUtils.toSmallList2(circleIds, 100);
                for (List<String> batch : batchList) {
                    List<CircleBaseInfoAndExtendInfo> tempList = circleBaseInfoDao.getByCircleIds(batch);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        tempList.forEach(a -> circleMap.put(a.getCircleId(), a));
                    }
                }
            }


            log.info("第三步，读取圈子。数量:{}，头部列表：{}",
                CollectionUtils.isEmpty(circleMap) ? 0 : circleMap.size(),
                CollectionUtils.isEmpty(circleMap) ? null : JSON.toJSONStringWithDateFormat(circleMap.values().iterator().next(), DateUtil.datePattern)
            );


            CommunityPushInfoKafkaModel pushModel = null;
            CircleBaseInfoAndExtendInfo circle = null;
            String key = null;
            String value = null;
            String errorMessage = null;
            int i = 0;
            for (CircleUserRelationRecord a : list) {
                i++;

                pushModel = null;
                circle = null;
                errorMessage = null;

                key = String.format(ApiRedisConstantConfig.CIRCLE_USER_RELATION_RECORD_PUSH, a.getId());
                if (!force) {
                    value = app.redisapiread.get(key);
                    if (StringUtils.hasLength(value)) {

                        log.info("第四步，处理流水详情-第{}/{}个。结果：{}，数据：{}",
                            i,
                            list.size(),
                            "已推送过",
                            JSON.toJSONStringWithDateFormat(a, DateUtil.datePattern)
                        );
                        continue;
                    }
                }

                circle = circleMap.get(a.getCircleId());
                if (circle == null || circle.getIsDel() == 1) {
                    errorMessage = LeaveCircleErrorEnum.CIRCLE_IS_DEL.getMessage();

                    log.info("第四步，处理流水详情-第{}/{}个。结果：{}，数据：{}",
                        i,
                        list.size(),
                        errorMessage,
                        JSON.toJSONStringWithDateFormat(a, DateUtil.datePattern)
                    );
                    continue;
                }

                if (a.getType() == 0 && a.getDealResult() != null && a.getDealResult() == 1) {
                    pushModel = new CommunityPushInfoKafkaModel();
                    pushModel.setRemindType(CommunityPushRemindTypeEnum.JOINGROUP.getValue());
                    pushModel.setSubList(Arrays.asList(a.getUid()));
                    pushModel.setSubType(0);
                    pushModel.setPublisher(String.format("%s_%s", a.getCircleId(), DateUtil.dateToStr(new Date(), PUBLISHER_PATTERN)));
                    pushModel.setPubType(7);
                    pushModel.setAlertParam(Arrays.asList(circle.getName()));
                    pushModel.setDataParam(Arrays.asList(circle.getName()));
                    pushModel.setLinkParam(Arrays.asList(a.getCircleId().toString()));
                    pushModel.setPushStrategy(Arrays.asList(2));

                } else if (a.getType() == 1) {
                    pushModel = new CommunityPushInfoKafkaModel();
                    pushModel.setRemindType(CommunityPushRemindTypeEnum.INVITJOINGROUP.getValue());
                    pushModel.setSubList(Arrays.asList(a.getUid()));
                    pushModel.setSubType(0);
                    pushModel.setPublisher(String.format("%s_%s", a.getCircleId(), DateUtil.dateToStr(new Date(), PUBLISHER_PATTERN)));
                    pushModel.setPubType(7);
                    pushModel.setAlertParam(Arrays.asList(circle.getName()));
                    pushModel.setDataParam(Arrays.asList(circle.getName()));
                    pushModel.setLinkParam(Arrays.asList(a.getCircleId().toString()));
                    pushModel.setPushStrategy(Arrays.asList(2));

                } else if (a.getType() == 2 && a.getDealResult() != null && a.getDealResult() == 1) {
                    pushModel = new CommunityPushInfoKafkaModel();
                    pushModel.setRemindType(CommunityPushRemindTypeEnum.REMOVEFROMGROUP.getValue());
                    pushModel.setSubList(Arrays.asList(a.getUid()));
                    pushModel.setSubType(0);
                    pushModel.setPublisher(String.format("%s_%s", a.getCircleId(), DateUtil.dateToStr(new Date(), PUBLISHER_PATTERN)));
                    pushModel.setPubType(7);
                    pushModel.setAlertParam(Arrays.asList(circle.getName()));
                    pushModel.setDataParam(Arrays.asList(circle.getName()));
                    pushModel.setLinkParam(Arrays.asList(a.getCircleId().toString()));
                    pushModel.setPushStrategy(Arrays.asList(2));
                }

                if (pushModel != null) {
                    kafkaTemplate.send(KafkaTopicName.TOPIC_COMMON_PUSH,
                        pushModel.getSubList().get(0),
                        JSON.toJSONStringWithDateFormat(pushModel, DateUtil.datePattern)
                    );

                    app.redisapiwrite.set(key, "1", expire);

                    log.info("第四步，处理流水详情-第{}/{}个。结果：{}，kafka消息：{}，数据：{}",
                        i,
                        list.size(),
                        "推送成功",
                        JSON.toJSONStringWithDateFormat(pushModel, DateUtil.datePattern),
                        JSON.toJSONStringWithDateFormat(a, DateUtil.datePattern)
                    );
                    continue;
                }


                log.info("第四步，处理流水详情-第{}/{}个。结果：{}，数据：{}",
                    i,
                    list.size(),
                    "非推送类型",
                    JSON.toJSONStringWithDateFormat(a, DateUtil.datePattern)
                );
            }

            log.info("第四步，处理流水完成。数量:{}，头部列表：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        log.info("第五步，更新断点。断点：{}", breakpoint);
    }


}
