package ttfund.web.communityservice.timedtask.jijinbarJobs.circle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.CirclePostRelation;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.CirclePostRelationMysqlDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 圈子帖子精华贴job
 * 备注：    需求 #678045 【基金吧6.15】新增圈子功能
 */
@JobHandler("CirclePostTagJob")
@Component
public class CirclePostTagJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(CirclePostTagJob.class);

    @Autowired
    private App app;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private CirclePostRelationMysqlDao circlePostRelationMysqlDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            Integer limit = null;
            Long expire = null;
            String circleIds = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                limit = jsonObject.getInteger("limit");
                expire = jsonObject.getLong("expire");
                circleIds = jsonObject.getString("circleIds");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            if (limit == null) {
                limit = 500;
            }

            if (expire == null) {
                expire = 365 * 24 * 3600L;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，limit：{}，expire：{}，circleIds：{}",
                initBreakpoint,
                batchReadCount,
                limit,
                expire,
                circleIds
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.CIRCLEPOSTTAGJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            if (StringUtils.hasLength(circleIds)) {
                dealByCircleIds(CommonUtils.toList(circleIds, ","), limit, expire);
            } else {
                deal(batchReadCount, limit, expire);
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount, Integer limit, Long expire) {

        String breakpointName = UserRedisConfig.CIRCLEPOSTTAGJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("deal-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("deal-第一步，读取断点。断点:{}", breakpoint);


        List<CirclePostRelation> list = circlePostRelationMysqlDao.getList(breakpointDate, batchReadCount);

        logger.info("deal-第二步，读取圈子帖子。数量:{}，头部列表：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = list.get(list.size() - 1).getUpdateTime();

            //跟后台约定过   取消精华：1变0
            List<CirclePostRelation> tagList = list.stream()
                .filter(a -> a.getTag() != null && (a.getTag() == 0 || a.getTag() == 1))
                .collect(Collectors.toList());

            LinkedMultiValueMap<String, CirclePostRelation> circlePostMap = new LinkedMultiValueMap<>();

            tagList.forEach(a -> circlePostMap.add(a.getCircleId(), a));

            logger.info("deal-第三步，筛选精华帖子并按圈子分组。精华帖数量:{}，圈子数量：{}",
                CollectionUtils.isEmpty(tagList) ? 0 : tagList.size(),
                CollectionUtils.isEmpty(circlePostMap) ? 0 : circlePostMap.size()
            );

            int num = 0;
            for (Map.Entry<String, List<CirclePostRelation>> entry : circlePostMap.entrySet()) {
                num++;

                List<Map<String, Object>> newList = doDealForSingleCircle(false, entry.getKey(), entry.getValue(), limit, expire);

                logger.info("deal-第四步，按圈子处理精华帖子详情-第{}/{}个。圈子id：{}，头部数据：{}",
                    num,
                    circlePostMap.size(),
                    entry.getKey(),
                    CollectionUtils.isEmpty(newList) ? null : JSON.toJSONStringWithDateFormat(newList.get(0), DateUtil.datePattern)
                );
            }

            logger.info("deal-第四步，按圈子处理精华帖子完成。精华帖数量:{}，圈子数量：{}",
                CollectionUtils.isEmpty(tagList) ? 0 : tagList.size(),
                CollectionUtils.isEmpty(circlePostMap) ? 0 : circlePostMap.size()
            );

        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        logger.info("deal-第五步，更新断点。断点：{}", breakpoint);

    }

    private void dealByCircleIds(List<String> circleIds, Integer limit, Long expire) {

        logger.info("dealByCircleIds-第一步，打印参数。expire：{}，limit：{}，circleIds：{}",
            expire,
            limit,
            circleIds
        );

        if (CollectionUtils.isEmpty(circleIds)) {
            return;
        }

        List<CirclePostRelation> list = circlePostRelationMysqlDao.getTagList(circleIds);

        logger.info("dealByCircleIds-第二步，读取圈子帖子。数量:{}，头部列表：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            LinkedMultiValueMap<String, CirclePostRelation> circlePostMap = new LinkedMultiValueMap<>();
            list.forEach(a -> circlePostMap.add(a.getCircleId(), a));

            logger.info("dealByCircleIds-第三步，按圈子分组。精华帖数量:{}，圈子数量：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(circlePostMap) ? 0 : circlePostMap.size()
            );

            int num = 0;
            for (Map.Entry<String, List<CirclePostRelation>> entry : circlePostMap.entrySet()) {
                num++;

                List<Map<String, Object>> newList = doDealForSingleCircle(true, entry.getKey(), entry.getValue(), limit, expire);

                logger.info("dealByCircleIds-第四步，按圈子处理帖子详情-第{}/{}个。圈子id：{}，头部数据：{}",
                    num,
                    circlePostMap.size(),
                    entry.getKey(),
                    CollectionUtils.isEmpty(newList) ? null : JSON.toJSONStringWithDateFormat(newList.get(0), DateUtil.datePattern)
                );
            }

            logger.info("dealByCircleIds-第四步，按圈子处理帖子完成。精华帖数量:{}，圈子数量：{}",
                CollectionUtils.isEmpty(list) ? 0 : list.size(),
                CollectionUtils.isEmpty(circlePostMap) ? 0 : circlePostMap.size()
            );

        }

    }

    private List<Map<String, Object>> doDealForSingleCircle(boolean runAll, String circleId, List<CirclePostRelation> list, int limit, Long expire) {
        List<Map<String, Object>> newList = null;

        Map<String, Map<String, Object>> distinctMap = new HashMap<>();
        String key = null;
        String value = null;
        key = String.format(BarRedisKey.CIRCLE_POST_LIST_TAG, circleId);

        if (!runAll) {
            value = app.barredis.get(key);
        }

        if (StringUtils.hasLength(value)) {
            List<JSONObject> jsonArray = JSON.parseArray(value, JSONObject.class);
            jsonArray.forEach(a -> distinctMap.put(a.getString("_id"), a));
        }

        for (CirclePostRelation a : list) {

            if (!(a.getTag() == 1
                && a.getPostTimepoint() != null
                && a.getPostTimepoint() > CommonUtils.getTimePointOneYearAgo()
                && a.getState() == 0
                && a.getIsDel() == 0
                && a.getPostDel() == 0
                && a.getPostTtjjdel() == 0)
            ) {
                distinctMap.remove(a.getPostId());
                continue;
            }

            Map<String, Object> map = new HashMap<>();
            map.put("_id", a.getPostId());
            map.put("TIMEPOINT", a.getPostTimepoint());
            map.put("DEL", a.getPostDel());
            map.put("TTJJDEL", a.getPostDel());
            distinctMap.put((String)map.get("_id"), map);
        }

        newList = distinctMap.values().stream()
            .sorted(((o1, o2) -> Long.compare(Long.parseLong(o2.get("TIMEPOINT").toString()), Long.parseLong(o1.get("TIMEPOINT").toString()))))
            .limit(limit)
            .collect(Collectors.toList());

        app.barredis.set(key, JSON.toJSONStringWithDateFormat(newList, DateUtil.datePattern), expire);

        return newList;
    }

}
