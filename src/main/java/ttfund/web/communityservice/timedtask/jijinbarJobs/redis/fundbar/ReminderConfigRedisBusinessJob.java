package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.ReminderConfigDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 单品吧问题配置
 */
@Slf4j
@JobHandler("ReminderConfigRedisBusinessJob")
@Component
public class ReminderConfigRedisBusinessJob extends IJobHandler {

    @Autowired
    private ReminderConfigDao reminderConfigDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            Long expire = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                expire = jsonObject.getLong("expire");
            }

            if (expire == null) {
                expire = 10 * 60L;
            }

            log.info("0，打印参数。expire：{}", expire);

            asyncReminderConfig(expire);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void asyncReminderConfig(Long expire) {

        List<Map<String, Object>> list = reminderConfigDao.getKReminderConfigs();

        log.info("1.读取数据。数量：{}，头部数据：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        List<String> cacheList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            cacheList = list.stream().map(a -> (String)a.get("Title")).collect(Collectors.toList());
        }

        app.barredis.set(BarRedisKey.FUND_GUBA_SERVICE_QUESTIONCONFIGS, JSON.toJSONString(cacheList), expire);

        log.info("2.写缓存。数量：{}，头部数据：{}",
            CollectionUtils.isEmpty(cacheList) ? 0 : cacheList.size(),
            CollectionUtils.isEmpty(cacheList) ? null : cacheList.get(0)
        );

    }

}
