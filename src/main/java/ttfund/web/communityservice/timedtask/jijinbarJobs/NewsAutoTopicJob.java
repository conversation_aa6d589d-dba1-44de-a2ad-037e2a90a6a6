package ttfund.web.communityservice.timedtask.jijinbarJobs;

import com.ttfund.web.base.Constant;
import com.ttfund.web.base.base.HttpRequestMethod;
import com.ttfund.web.base.helper.DateHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.ttfund.web.core.register.AppCore;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.crawl.topic.CreateTopicBySystemRequest;
import ttfund.web.communityservice.bean.crawl.topic.CreateTopicBySystemResponse;
import ttfund.web.communityservice.bean.crawl.topic.TopicCrawlBean;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.enums.EnumTopicCrawl;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.*;

/**
 * 资讯自动创建话题
 */
@JobHandler(value = "newsAutoTopicJob")
@Component
public class NewsAutoTopicJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(NewsAutoTopicJob.class);

    @Autowired
    private App app;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private AppCore appCore;

    @Value("${api.crawl.addtopic.url}")
    private String createTopicUrl;

    @Value("${api.crawl.addtopic.token}")
    private String createTopicToken;

    @Override
    public ReturnT<String> execute(String param) {

        logger.info("资讯自动创建话题任务开始");

        ReturnT<String> result = ReturnT.SUCCESS;
        List<TopicCrawlBean> topicList = new ArrayList<>();

        try {

            String breakPointName = "NewsAutoTopicBreakPoint";
            String lastTime = appCore.redisuserread.get(breakPointName);

            logger.info("时间断点 读取缓存key:" + breakPointName + ",value:" + lastTime);

            if (StringUtils.isEmpty(lastTime)) {
                lastTime = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1), "yyyy-MM-dd HH:mm:ss.SSSSSS");
            }

            // 查询新话题
            lastTime = queryTopics(topicList, lastTime);

            if (!CollectionUtils.isEmpty(topicList)) {
                // 创建话题
                createTopics(topicList);
                appCore.redisuserwrite.set(breakPointName, lastTime, Constant.CACHETIMEDAY7);
                logger.info("时间断点 设置缓存key:" + breakPointName + ",value:" + lastTime);
            } else {
                logger.info("没有新增话题");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }

        logger.info("资讯自动创建话题任务结束");

        return result;
    }

    /**
     * 增量获取话题
     *
     * @param topicList
     * @param lastTime
     * @return
     */
    private String queryTopics(List<TopicCrawlBean> topicList, String lastTime) {

        try {
            String sql = " SELECT TOPIC_INTRODUCTION, to_char(EUTIME, 'YYYY-MM-DD HH24:MI:SS.US') EUTIME " +
                    " FROM CONTENT.JJB_TOPIC_AUTO_PARA_DRCT_ALL " +
                    " WHERE EUTIME > ? " +
                    " ORDER BY EUTIME ";
            List<Object> params = new ArrayList<>();
            params.add(lastTime);

            List<Map> result = app.bigdataVertica.executeQuery(sql, params);

            if (CollectionUtils.isEmpty(result)) {
                return null;
            }

            for (Map map : result) {
                if (map.containsKey("TOPIC_INTRODUCTION")) {

                    String content = (String) map.get("TOPIC_INTRODUCTION");

                    if (content == null) {
                        continue;
                    }

                    if (content.length() > 2 && content.startsWith("\"") && content.endsWith("\"")) {
                        content = content.substring(1, content.length() - 1);
                    }

                    String[] contentArr = content.split("\\[导语]：");

                    if (contentArr.length >= 2) {
                        TopicCrawlBean bean = new TopicCrawlBean();
                        bean.setTitle(contentArr[0].replace("[话题]：", "").trim());
                        bean.setDesc(contentArr[1].trim());
                        bean.setSource(EnumTopicCrawl.NEWS_AUTO);

                        topicList.add(bean);
                    }
                }
            }

            return (String) result.get(result.size() - 1).get("EUTIME");

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 创建话题
     *
     * @param topicList
     */
    private void createTopics(List<TopicCrawlBean> topicList) {

        int count = 0;

        for (TopicCrawlBean topic : topicList) {

            try {
                Thread.sleep(100L);

                // 用研发中心接口创建话题
                HashMap<String, String> headers = new HashMap<>();
                headers.put("token", createTopicToken);

                CreateTopicBySystemRequest request = buildRequestParam(topic);

                String content = HttpHelper.request(
                        createTopicUrl,
                        JacksonUtil.obj2StringNoException(request),
                        HttpRequestMethod.JSON,
                        3000,
                        "UTF-8",
                        headers,
                        true
                );

                if (StringUtils.isEmpty(content)) {
                    logger.error("创建话题失败：" + topic);
                    continue;
                }

                CreateTopicBySystemResponse response = JacksonUtil.string2Obj(
                        content,
                        CreateTopicBySystemResponse.class
                );

                if (response != null && response.getRc() == 1) {
                    logger.info("创建话题成功：" + topic);
                    count++;
                } else {
                    logger.error("创建话题失败：" + topic);
                }
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }

        logger.info("新增话题数量：" + count);
    }

    /**
     * 构建创建话题请求体
     *
     * @param topic
     * @return
     */
    private static CreateTopicBySystemRequest buildRequestParam(TopicCrawlBean topic) {

        CreateTopicBySystemRequest request = new CreateTopicBySystemRequest();

        if (topic.getTitle().length() > 27) {
            request.setName(topic.getTitle().substring(0, 25) + "……");
        } else {
            request.setName(topic.getTitle());
        }

        if (StringUtils.isEmpty(topic.getDesc())) {
            request.setIntroduction(topic.getTitle());
        } else if (topic.getDesc().length() > 200) {
            request.setIntroduction(topic.getDesc().substring(0, 200) + "……");
        } else {
            request.setIntroduction(topic.getDesc());
        }

        request.setFrom(topic.getSourceName());

        return request;
    }
}
