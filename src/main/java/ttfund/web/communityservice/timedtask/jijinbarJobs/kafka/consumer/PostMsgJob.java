package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.CacheHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumHTType;
import ttfund.web.communityservice.bean.jijinBar.post.PostExtendInfo;
import ttfund.web.communityservice.bean.jijinBar.post.PostFundRelation;
import ttfund.web.communityservice.bean.jijinBar.post.PostHtRelationItem;
import ttfund.web.communityservice.bean.jijinBar.post.config.ArticleTG;
import ttfund.web.communityservice.bean.jijinBar.post.config.JJBTypeConfig;
import ttfund.web.communityservice.bean.jijinBar.post.guba.PostInfoExtendKafka;
import ttfund.web.communityservice.bean.jijinBar.post.guba.PostInfoKafka;
import ttfund.web.communityservice.bean.jijinBar.post.guba.ZQHtRelation;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.JjbTypeConfigDao;
import ttfund.web.communityservice.dao.msyql.NotjijinPostinfoDao;
import ttfund.web.communityservice.dao.msyql.PostFundRelationDao;
import ttfund.web.communityservice.dao.msyql.PostInfoDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtendMysqlDao;
import ttfund.web.communityservice.dao.msyql.PostTopicRetDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 社区源数据同步服务-帖子
 */
@Slf4j
@Component
public class PostMsgJob {

    public static final String KAFKA_LISTENER_ID = "PostMsgJob";

    private static List<String> jjbConfig = Arrays.asList("xstl", "jjmnds", "jjdss", "zf", "jjdp", "jjxx", "jjxt",
        "jjxcx", "yllc", "ztmb", "tgcp", "jjjlyjxz", "jjzs", "jjry", "jjft", "sxdt", "jtzh", "jjcl", "jjdt",
        "jjspzh", "zsjj", "cfhpl", "zqjj", "hqb", "cfhpl", "jjzh", "jmyhs");

    @Autowired
    private JjbTypeConfigDao jjbTypeConfigDao;

    @Autowired
    private PostTopicRetDao postTopicRetDao;

    @Autowired
    private PostFundRelationDao postFundRelationDao;

    @Autowired
    private PostInfoDao postInfoDao;

    @Autowired
    private NotjijinPostinfoDao notjijinPostinfoDao;

    @Autowired
    private PostInfoExtendMysqlDao postInfoExtendMysqlDao;


    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.GubaPostInfo4FundQueue}
        , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_POSTMSGJOB + "}"
        , containerFactory = KafkaConfig.kafkaListenerContainerFactory_gubapost_new_zp)
    private void onListen(ConsumerRecord<String, String> record) {
        postMsg(record, log);
    }

    /**
     * 帖子
     */
    public void postMsg(ConsumerRecord<String, String> record, Logger logger) {
        try {

            logger.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            String value = record.value();
            if (!StringUtils.hasLength(value)) {
                return;
            }

            PostInfoKafka post = JacksonUtil.deserialize(value, PostInfoKafka.class);

            //处理gd开头的基金和下划线后面带数字的基金code
            post.Code = filterCode(post.Code);

            //基金吧问答帖单独处理
            if (post.Type == 49 || post.Type == 50) {

                if ("del".equals(post.action_type)) {
                    setPostExtendInfo(post);
                }

                return;
            }

            //转发贴逻辑处理
            if (post.zf_sourcepost == 1 && post.Project != 1) {
                post.Project = 1;
                if (!jjbConfig.contains(post.Code)) {
                    post.Code = "zf";
                }
            }

            JSONObject extinfo = null;
            //处理code前缀
            if (post.Type != null && !"del".equals(post.action_type)) {
                JJBTypeConfig prefs = getPrefix(String.valueOf(post.Type), true);
                //需要特殊处理的code
                if (prefs != null) {
                    if (!StringUtils.hasLength(post.Extend)) {
                        return;
                    } else {
                        extinfo = JSON.parseObject(post.Extend);
                        String code = extinfo.getString("code");
                        //传来的code为空，过滤
                        if (!StringUtils.hasLength(code)) {
                            return;
                        }
                        if ("58".equals(prefs.Value)) {
                            post.Code = code;
                        } else {
                            post.Code = prefs.Value + "-" + code;
                        }
                    }
                }
            }

            //IP超长数据处理
            if (StringUtils.hasLength(post.HuiFuIP) && post.HuiFuIP.length() > 40) {
                post.HuiFuIP = post.HuiFuIP.substring(0, 40);
            }
            if (StringUtils.hasLength(post.IP) && post.IP.length() > 200) {
                post.IP = post.IP.substring(0, 200);
            }
            if (post.UpdateTime == null) {
                post.UpdateTime = new Date();
            }

            try {

                /**
                 * 基金话题、证券话题
                 */
                //处理帖子和话题对应关系
                List<PostHtRelationItem> listRelationItems = getRelationItems(post.Extend, String.valueOf(post.ID));
                //处理话题和帖子关联关系
                if (!CollectionUtils.isEmpty(listRelationItems)) {
                    postTopicRetDao.insertOrUpdate(listRelationItems);
                }


            } catch (Exception ex) {
                logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

                logger.error(ex.getMessage(), ex);
            }


            /**
             * 处理一贴多发的情况（投顾产品） --对接人 周星敏
             */
            try {
                //数据我们是透传给你的,股吧不落的,所有我们这边推给你的流水，只有add，article_tg才会有值
                //翁一鸣: 我看了下 发过的财富号文章，不能修改的
                if (post != null && "add".equals(post.action_type) && StringUtils.hasLength(post.Article_TG)) {
                    List<PostFundRelation> listPostFundRel = new ArrayList<>();
                    List<ArticleTG> tgList = JSON.parseArray(post.Article_TG, ArticleTG.class);
                    if (!CollectionUtils.isEmpty(tgList)) {
                        for (ArticleTG item : tgList) {
                            PostFundRelation model = new PostFundRelation();
                            model.FCode = item.TGCode;
                            model.FName = item.TGName;
                            model.FType = item.Type;
                            model.PostId = String.valueOf(post.ID);
                            model.UpDateTime = new Date();
                            model.ID = model.PostId + "_" + model.FCode + "_" + model.FType;
                            listPostFundRel.add(model);
                        }
                    }

                    postFundRelationDao.insertOrUpdateBulk(listPostFundRel);

                }

            } catch (Exception ex) {
                logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

                logger.error(ex.getMessage(), ex);
            }

            /**
             * 处理一贴多发的情况（组合产品） 对接人 -周星敏
             */
            try {
                //数据我们是透传给你的,股吧不落的,所有我们这边推给你的流水，只有add，ZMTJJZhCode才会有值
                //翁一鸣: 我看了下 发过的财富号文章，不能修改的 ZMTJJZhCode放在拓展字段
                if (post != null && StringUtils.hasLength(post.Extend)) {
                    String ZMTJJZhCode = null;
                    extinfo = JSON.parseObject(post.Extend);
                    if (extinfo != null) {
                        ZMTJJZhCode = extinfo.getString("ZMTJJZhCode");
                    }

                    List<PostFundRelation> listPostFundRel = new ArrayList<>();
                    if (StringUtils.hasLength(ZMTJJZhCode)) {
                        List<String> jjzhList = CommonUtils.toList(ZMTJJZhCode, ",");
                        if (!CollectionUtils.isEmpty(jjzhList)) {
                            post.Code = "43-" + jjzhList.get(0);
                            post.Project = 1;
                            if ("add".equals(post.action_type)) {

                                for (String fcode : jjzhList) {
                                    PostFundRelation model = new PostFundRelation();
                                    model.FCode = fcode;
                                    model.FName = "";
                                    model.FType = 43;
                                    model.PostId = String.valueOf(post.ID);
                                    model.UpDateTime = new Date();
                                    model.ID = model.PostId + "_" + model.FCode + "_" + model.FType;
                                    listPostFundRel.add(model);
                                }
                            }
                            postFundRelationDao.insertOrUpdateBulk(listPostFundRel);
                        }
                    }
                }

            } catch (Exception ex) {
                logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

                logger.error(ex.getMessage(), ex);
            }

            switch (post.action_type) {
                case "del"://删除
                    if (post.Project == 1) {
                        postInfoDao.disabled(post.ID, post.Del);
                    } else {
                        notjijinPostinfoDao.disabledNotJijin(post.ID, post.Del);
                    }
                    setPostExtendInfo(post);
                    break;
                case "rec"://恢复
                    setPostExtendInfo(post);
                    if (post.Project == 1) {
                        postInfoDao.resumeKafka(post);
                    } else {
                        notjijinPostinfoDao.resumeNotJijin(post);
                    }
                    break;
                case "addrt"://增加
                case "add":
                case "daoru_zhuti":
                    setPostExtendInfo(post);

                    if (post.Project == 1) {
                        postInfoDao.insertOrUpdate(post);
                    } else {
                        notjijinPostinfoDao.insertOrUpdateNotJijin(post);
                    }
                    break;
                case "update"://修改
                case "modify":
                case "zmtdistribute":
                case "updatecodelist":
                case "set_pinglun_quanxian":
                case "modify_delpic":
                case "code_change": {
                    setPostExtendInfo(post);
                    try {

                        if (post.Project == 1) {
                            postInfoDao.insertOrUpdate(post);
                        } else {
                            notjijinPostinfoDao.insertOrUpdateNotJijin(post);
                        }
                    } catch (Exception ex) {
                        logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                            record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

                        logger.error(ex.getMessage(), ex);
                    }
                }
                break;
                case "updatetextmodel"://修改拓展字段
                    boolean updateResult = false;
                    if (post.Project == 1) {
                        updateResult = postInfoDao.updateJijinExtend(post) > 0;

                    } else {
                        updateResult = notjijinPostinfoDao.updateNotJijinExtend(post) > 0;
                    }
                    if (!updateResult) {
                        logger.error("类型：updatetextmodel，涉及帖子：{}不存在", post.ID);
                    }

                    setPostExtendInfo(post);
                    break;
            }

        } catch (Exception ex) {
            logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            logger.error(ex.getMessage(), ex);
        }

    }

    /**
     * 处理特殊的Code
     */
    public String filterCode(String code) {
        String result = code;
        //处理gd开头的基金和下划线后面带数字的基金code
        if (StringUtils.hasLength(result)) {
            if (result.length() > 6) {
                if (result.startsWith("gd")) {
                    result = result.substring(2);
                }
                if (result.contains("_")) {
                    result = result.split("_")[0];
                }
            } else if (result.endsWith("_1")) {
                result = result.substring(0, result.length() - 2);
            }
        }
        return result;
    }


    public List<PostHtRelationItem> getRelationItems(String extend, String postId) {
        if (!StringUtils.hasLength(extend)) {
            return null;
        }

        /**
         * 基金话题
         */
        List<PostHtRelationItem> list = new ArrayList<>();
        PostExtendInfo info = JSON.parseObject(extend, PostExtendInfo.class);
        if (info != null && info.FundTopicPost != null) {
            info.FundTopicPost.forEach(item -> {
                item.id = postId + "_" + item.htid + "_" + item.voteid + "_" + EnumHTType.JJ.getValue();
                item.postid = postId;
                item.HTType = EnumHTType.JJ.getValue();
            });
            info.FundTopicPost = info.FundTopicPost.stream().filter(a -> StringUtils.hasLength(a.htid) && !"0".equals(a.htid)).collect(Collectors.toList());
            if (info.FundTopicPost.size() > 0) {
                list.addAll(info.FundTopicPost);
            }
        }


        /**
         * 证券话题
         */
        ZQHtRelation zqHT = JSON.parseObject(extend, ZQHtRelation.class);
        if (zqHT != null && zqHT.GubaTalkId != null && zqHT.GubaTalkId > 0) {
            PostHtRelationItem model = new PostHtRelationItem();
            model.id = postId + "_" + zqHT.GubaTalkId + "_0_" + EnumHTType.ZQ.getValue();
            model.htid = zqHT.GubaTalkId.toString();
            model.HTType = EnumHTType.ZQ.getValue();
            model.voteid = "";
            model.postid = postId;
            list.add(model);
        }


        return list;
    }

    /**
     * 设置帖子拓展表信息
     */
    private void setPostExtendInfo(PostInfoKafka post) {

        //设置评论权限的操作
        if ("set_pinglun_quanxian".equals(post.action_type) || "zmtdistribute".equals(post.action_type) || "updatetextmodel".equals(post.action_type)) {
            PostInfoExtendKafka extendInfo = new PostInfoExtendKafka();
            extendInfo.Id = post.ID;
            extendInfo.AllowLikesState = post.AllowLikesState;
            extendInfo.SystemCommentAuthority = post.SystemCommentAuthority;
            extendInfo.F1 = 2;

            JSONObject extend = JSON.parseObject(post.Extend);
            if (extend.getBoolean("noinlist") != null && extend.getBoolean("noinlist")) {
                extendInfo.F1 = 1;
            }

            postInfoExtendMysqlDao.insertOrUpdate(extendInfo);
        } else if ("del".equals(post.action_type)) {
            //记录谁删除了 该贴

            PostInfoExtendKafka extendInfo = new PostInfoExtendKafka();
            extendInfo.Id = post.ID;
            extendInfo.F3 = "2";

            if (StringUtils.hasLength(post.whoDel) && post.whoDel.toLowerCase().contains("delself")) {
                extendInfo.F3 = "1";
            }

            postInfoExtendMysqlDao.insertOrUpdateF3(extendInfo);
        } else if ("add".equals(post.action_type) && post.Del == 1) {
            //添加帖子时 触发了敏感词 被删除

            PostInfoExtendKafka extendInfo = new PostInfoExtendKafka();
            extendInfo.Id = post.ID;
            extendInfo.F3 = "2";

            postInfoExtendMysqlDao.insertOrUpdateF3(extendInfo);
        }

    }


    public JJBTypeConfig getPrefix(String type, boolean useCache) {
        JJBTypeConfig result = null;
        String key = null;
        if (useCache) {
            key = String.format("JjbTypeConfigDao_getPrefix_%s", type);
            result = CacheHelper.get(key);
        }
        if (result == null) {
            if (StringUtils.hasLength(type)) {
                result = jjbTypeConfigDao.getPrefix(type);
            }
        }
        if (useCache) {
            CacheHelper.put(key, result, 5 * 60 * 1000);
        }

        return result;
    }

}
