package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.FaxianWeightModel;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.PostRecommendModel;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.mongo.FindRecommendPostDao;
import ttfund.web.communityservice.dao.msyql.FaxianWeightDao;
import ttfund.web.communityservice.utils.CommonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 推荐帖子列表服务
 */
@JobHandler("FindRecommendPostCacheJob")
@Component
public class FindRecommendPostCacheJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(FindRecommendPostCacheJob.class);

    @Autowired
    private FindRecommendPostDao findRecommendPostDao;

    @Autowired
    private FaxianWeightDao faxianWeightDao;

    @Autowired
    private App app;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        logicDeal();
        logicDealNew();

        return ReturnT.SUCCESS;
    }

    public void logicDeal() {
        try {
            List<PostRecommendModel> list = findRecommendPostDao.getListSortByFindscore(5000);

            logger.info("logicDeal-第一步，读取数据。数量:{}，头部id列表：{}",
                    list == null ? 0 : list.size(),
                    list == null ? null : list.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(list)) {
                list = list.stream().sorted(((o1, o2) -> {
                    int i = o2.FINDSCORE.compareTo(o1.FINDSCORE);
                    if (i == 0) {
                        return o2.TIMEPOINT.compareTo(o1.TIMEPOINT);
                    } else {
                        return i;
                    }
                })).collect(Collectors.toList());

                List<Map<String, Object>> mapList = new ArrayList<>(list.size());
                for (PostRecommendModel a : list) {
                    mapList.add(CommonUtils.beanToMap(a));
                }

                app.barredis.set(BarRedisKey.FUND_GUBA_SERVICE_FINDRECOMMENDPOST, JSON.toJSONStringWithDateFormat(mapList, "yyyy-MM-dd'T'HH:mm:ss.SSS+08:00"));
            }

            logger.info("logicDeal-第二步，数据写redis。数量:{}，头部id列表：{}",
                    list == null ? 0 : list.size(),
                    list == null ? null : list.stream().map(a -> a.ID).limit(20).collect(Collectors.toList()));

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    public void logicDealNew() {

        try {
            int pageSize = 10000;
            List<Document> list = findRecommendPostDao.getListSortByTimepoint(pageSize);

            logger.info("logicDealNew-第一步，读取帖子。数量:{}，头部id列表：{}",
                    list == null ? 0 : list.size(),
                    list == null ? null : list.stream().map(a -> a.get("ID")).limit(20).collect(Collectors.toList()));

            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<FaxianWeightModel> configList = faxianWeightDao.getAll();

            logger.info("logicDealNew-第二步，读取配置。数量:{}，头部列表：{}",
                    configList == null ? 0 : configList.size(),
                    configList == null ? null : JSON.toJSONString(configList.stream().limit(20).collect(Collectors.toList())));

            if (CollectionUtils.isEmpty(configList)) {
                return;
            }

            int num = 0;
            for (FaxianWeightModel item : configList) {
                num++;

                Boolean result = null;
                List<Document> tempList = null;

                if (StringUtils.hasLength(item.GroupName)) {
                    String cacheKey = String.format(BarRedisKey.FUND_GUBA_SERVICE_FINDRECOMMENDPOST_GROUP, item.GroupName);
                    //字段名
                    String filedName = getFindScoreFiledName(item.GroupName);
                    String sortTimePointt = "TIMEPOINT";

                    tempList = list.stream().filter(a -> a.containsKey(filedName) && a.containsKey(sortTimePointt)).sorted(((o1, o2) -> {
                        Double i1 = (o1.get(filedName) == null || "".equals(o1.get(filedName))) ? null : Double.parseDouble(o1.get(filedName).toString());
                        Double i2 = (o2.get(filedName) == null || "".equals(o2.get(filedName))) ? null : Double.parseDouble(o2.get(filedName).toString());
                        if (i1 == null && i2 == null) {
                            return 0;
                        } else if (i1 == null && i2 != null) {
                            return 1;
                        } else if (i1 != null && i2 == null) {
                            return -1;
                        } else {
                            int i = i2.compareTo(i1);
                            if (i == 0) {
                                Long s1 = (o1.get(sortTimePointt) == null || "".equals(o1.get(sortTimePointt))) ? null : Long.parseLong(o1.get(sortTimePointt).toString());
                                Long s2 = (o2.get(sortTimePointt) == null || "".equals(o2.get(sortTimePointt))) ? null : Long.parseLong(o2.get(sortTimePointt).toString());
                                if (s1 == null && s2 == null) {
                                    return 0;
                                } else if (s1 == null && s2 != null) {
                                    return 1;
                                } else if (s1 != null && s2 == null) {
                                    return -1;
                                } else {
                                    return s2.compareTo(s1);
                                }
                            } else {
                                return i;
                            }
                        }
                    })).limit(5000).collect(Collectors.toList());

                    if (!CollectionUtils.isEmpty(tempList)) {
                        result = app.barredis.set(cacheKey, JSON.toJSONStringWithDateFormat(tempList, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"), 365 * 24 * 3600L);
                        if (result != null && !result) {
                            result = app.barredis.set(cacheKey, JSON.toJSONStringWithDateFormat(tempList, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"), 365 * 24 * 3600L);
                        }
                    }
                }

                logger.info("logicDealNew-第三步，数据写redis-详情-第{}/{}个。GroupName：{}，result：{}，头部列表：{}",
                        num,
                        configList.size(),
                        item.GroupName,
                        result,
                        tempList == null ? null : JSON.toJSONString(tempList.stream().limit(1).collect(Collectors.toList()))
                );
            }

            logger.info("logicDealNew-第三步，数据写redis");

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

    }

    /**
     * 根据分组名获取 分数字段ID
     */
    private String getFindScoreFiledName(String groupName) {
        if (StringUtils.hasLength(groupName)) {
            groupName = groupName.toUpperCase();
        } else {
            return "";
        }
        return "FINDSCORE_" + groupName;
    }

}
