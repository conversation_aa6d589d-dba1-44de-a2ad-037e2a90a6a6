package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.fasterxml.jackson.core.type.TypeReference;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.bigshotshow.BigShotShowApiResponse;
import ttfund.web.communityservice.bean.bigshotshow.BigShotShowSimpleResponse;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 理财页大咖秀列表同步至Redis
 */
@JobHandler(value = "financeBigShotShowJob")
@Component
public class FinanceBigShotShowJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(FinanceBigShotShowJob.class);

    @Autowired
    private App app;

    @Value("${api.bigshotshow.url}")
    private String bigShotShowUrl;

    @Override
    public ReturnT<String> execute(String param) {

        logger.info("大咖秀列表同步任务开始");

        ReturnT<String> result = ReturnT.SUCCESS;
        List<BigShotShowSimpleResponse> list = new ArrayList<>();

        try {

            for (int i = 1; i <= 50; i++) {

                // 获取大咖秀列表
                StringBuilder url = new StringBuilder()
                        .append(bigShotShowUrl)
                        .append("/bigshotshow/getRecommendShowList?")
                        .append("pageIndex=")
                        .append(i)
                        .append("&pageNum=100");

                String content = HttpHelper.requestGet(url.toString(), 3000);

                if (!StringUtils.hasLength(content)) {
                    break;
                }

                BigShotShowApiResponse<List<BigShotShowSimpleResponse>, Object> response = JacksonUtil.string2Obj(
                        content,
                        new TypeReference<BigShotShowApiResponse<List<BigShotShowSimpleResponse>, Object>>() {}
                );

                if (response == null || CollectionUtils.isEmpty(response.getData())) {
                    break;
                }

                // 只需要往前一个月 + 往后七天的
                List<BigShotShowSimpleResponse> subList = response.getData()
                        .stream()
                        .filter(w -> DateUtil.calendarDateByMonth(-1).compareTo(w.getBegin()) <= 0
                                && DateUtil.calendarDateByDays(7).compareTo(w.getBegin()) >= 0)
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(subList)) {
                    // 保存详细信息
                    saveDetailData(subList);

                    list.addAll(subList);
                }

                if (response.getData().size() < 100) {
                    break;
                }
            }

            // 保存列表
            List<String> idList = list.stream()
                    .map(w -> w.getShowId())
                    .collect(Collectors.toList());

            app.barredis.set(
                    BarRedisKey.FUND_BAR_SERVICE_BIG_SHOT_SHOW_LIST,
                    JacksonUtil.obj2StringNoException(idList),
                    3600 * 24 * 7L
            );

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = ReturnT.FAIL;
        }

        logger.info("大咖秀列表同步任务结束，同步数量：" + list.size());

        return result;
    }

    /**
     * 保存详细信息
     *
     * @param list
     */
    private void saveDetailData(List<BigShotShowSimpleResponse> list) {
        for (BigShotShowSimpleResponse item : list) {
            String key = String.format(BarRedisKey.FUND_BAR_SERVICE_BIG_SHOT_SHOW_DETAIL, item.getShowId());
            app.barredis.set(key, JacksonUtil.obj2StringNoException(item), 3600 * 24 * 7L);
        }
    }
}
