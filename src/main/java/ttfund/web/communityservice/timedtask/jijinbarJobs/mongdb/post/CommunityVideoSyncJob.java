package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoExtendField;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoForMysqlModel;
import ttfund.web.communityservice.bean.jijinBar.post.VideoAuthorConfigDo;
import ttfund.web.communityservice.bean.jijinBar.post.data.CommunityVideo;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.GetChannelInfoApiResult;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.CommunityVideoDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.msyql.PostInfoDao;
import ttfund.web.communityservice.dao.msyql.VideoAuthorConfigDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.service.LangKeLvbApiServiceImpl;
import ttfund.web.communityservice.service.LangKeLvbCooperationApiServiceImpl;
import ttfund.web.communityservice.service.entity.AvBatchInfoDataInfoSet;
import ttfund.web.communityservice.service.entity.AvBatchInfoDataInfoSetValue;
import ttfund.web.communityservice.service.entity.AvBatchInfoResponse;
import ttfund.web.communityservice.service.entity.QuoteModel;
import ttfund.web.communityservice.service.entity.SubTagInfo;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频贴同步job
 */
@JobHandler("CommunityVideoSyncJob")
@Component
public class CommunityVideoSyncJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(CommunityVideoSyncJob.class);

    private static List<String> FIELDS = Arrays.asList("_id", "Videos", "TITLE", "TYPE", "UID", "TIME", "TIMEPOINT", "CODE", "DEL", "TTJJDEL", "UPDATETIME", "ZMTLKType");

    private static List<String> SET_ON_INSERT_FIELDS = Arrays.asList("createTime");

    private static final List<String> LABEL_SET = Arrays.asList("行业热点", "产品推荐", "市场点评", "其他");

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostDao postDao;

    @Autowired
    private PostInfoDao postInfoDao;

    @Autowired
    private LangKeLvbCooperationApiServiceImpl langKeLvbCooperationApiService;

    @Autowired
    private LangKeLvbApiServiceImpl langKeLvbApiService;

    @Autowired
    private CommunityVideoDao communityVideoDao;

    @Autowired
    private VideoAuthorConfigDao videoAuthorConfigDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            Integer backTime = null;
            String zmtLkType = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
                backTime = jsonObject.getInteger("backTime");
                zmtLkType = jsonObject.getString("zmtLkType");
            }

            if (batchReadCount == null) {
                batchReadCount = 10000;
            }

            if (backTime == null) {
                backTime = -2 * 60;
            }

            if (zmtLkType == null) {
                zmtLkType = "2,102";
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}，backTime：{}，zmtLkType：{}", initBreakpoint, batchReadCount, backTime, zmtLkType);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.COMMUNITYVIDEOSYNCJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("0.初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }


            List<Integer> zmtLkTypeList = CommonUtils.toList(zmtLkType, ",").stream().map(a -> Integer.parseInt(a)).collect(Collectors.toList());

            deal(batchReadCount, backTime, zmtLkTypeList);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchReadCount, int backTime, List<Integer> zmtLkTypeList) throws Exception {

        String breakpointName = UserRedisConfig.COMMUNITYVIDEOSYNCJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("0.读取断点为空，使用默认断点。断点：{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("1.读取断点。断点：{}", breakpoint);

        Date end = DateUtil.calendarDateBySecond(backTime);
        List<Document> dataList = postDao.getListByUpdateTime(FIELDS, Document.class, batchReadCount, breakpointDate, end);

        logger.info("2.读取数据。start：{}，end：{}，数量：{}，头部列表：{}",
                DateUtil.dateToStr(breakpointDate),
                DateUtil.dateToStr(end),
                CollectionUtils.isEmpty(dataList) ? 0 : dataList.size(),
                CollectionUtils.isEmpty(dataList) ? null : JSON.toJSONStringWithDateFormat(dataList.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(dataList)) {

            breakpointDate = dataList.get(dataList.size() - 1).getDate("UPDATETIME");

            List<Document> videoDataList = dataList.stream()
                    .filter(a -> !CollectionUtils.isEmpty(a.getList("Videos", Document.class))
                            && (CollectionUtils.isEmpty(zmtLkTypeList) || zmtLkTypeList.contains(a.getInteger("ZMTLKType")))
                    )
                    .collect(Collectors.toList());

            List<CommunityVideo> totalList = generate(videoDataList);


            logger.info("3.筛选。数量：{}，头部列表：{}",
                    CollectionUtils.isEmpty(totalList) ? 0 : totalList.size(),
                    CollectionUtils.isEmpty(totalList) ? null : JSON.toJSONStringWithDateFormat(totalList.get(0), DateUtil.datePattern)
            );

            List<CommunityVideo> upsertList = new ArrayList<>();
            List<String> deleteList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(totalList)) {
                upsertList.addAll(totalList);
            }

            logger.info("4.分类完成。数量：{}，头部列表：{}",
                    CollectionUtils.isEmpty(totalList) ? 0 : totalList.size(),
                    CollectionUtils.isEmpty(totalList) ? null : JSON.toJSONStringWithDateFormat(totalList.get(0), DateUtil.datePattern)
            );
            //补充短视频标签和相关基金信息
            getLangKeInfo(upsertList);

            //补充拉黑作者信息
            updateInvisible(upsertList);

            if (!CollectionUtils.isEmpty(upsertList)) {
                List<Map<String, Object>> mapList = null;
                mapList = new ArrayList<>(upsertList.size());
                Map<String, Object> map = null;
                for (CommunityVideo a : upsertList) {
                    map = CommonUtils.beanToMap(a);
                    mapList.add(map);
                }

                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 100);
                for (List<Map<String, Object>> batch : batchList) {
                    communityVideoDao.upsertManyBySetWithSetOnInsertFields(batch, SET_ON_INSERT_FIELDS, "_id");
                }
            }

            if (!CollectionUtils.isEmpty(deleteList)) {

                List<List<String>> batchList = CommonUtils.toSmallList2(deleteList, 100);
                for (List<String> batch : batchList) {
                    communityVideoDao.removeByIds(batch, BarMongodbConfig.TABLE_COMMUNITYVIDEO);
                }
            }

            logger.info("5.写库。数量：{}，头部列表：{}",
                    CollectionUtils.isEmpty(totalList) ? 0 : totalList.size(),
                    CollectionUtils.isEmpty(totalList) ? null : JSON.toJSONStringWithDateFormat(totalList.get(0), DateUtil.datePattern)
            );

        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        logger.info("6.更新断点。断点：{}", breakpoint);

    }

    public List<CommunityVideo> generate(List<Document> list) {
        List<CommunityVideo> result = null;
        if (!CollectionUtils.isEmpty(list)) {
            result = new ArrayList<>(list.size());

            Map<String, PostInfoForMysqlModel> postMap = new HashMap<>();
            List<Integer> postIds = list.stream().map(a -> Integer.parseInt(a.getString("_id"))).collect(Collectors.toList());
            List<PostInfoForMysqlModel> posts = postInfoDao.getPostListByIds(postIds);
            if (!CollectionUtils.isEmpty(posts)) {
                posts.forEach(o -> postMap.put(String.valueOf(o.ID), o));
            }


            CommunityVideo model = null;
            List<Document> videos = null;
            for (Document a : list) {
                model = new CommunityVideo();

                videos = a.getList("Videos", Document.class);
                model.setVideoId(videos.get(0).getString("ZMTLKVideoID"));
                model.setVideoCover(generateVideoCover(postMap.get(a.getString("_id"))));
                model.setTitle(a.getString("TITLE"));
                model.setUid(a.getString("UID"));
                model.setTimePoint(a.getLong("TIMEPOINT"));
                model.setPostId(a.getString("_id"));
                model.setPostTime(a.getDate("TIME"));
                model.setPostType(a.getInteger("TYPE"));
                model.setPostCode(a.getString("CODE"));
                model.setState((Objects.equals(a.getInteger("DEL"), 0) && Objects.equals(a.getInteger("TTJJDEL"), 0)) ? 1 : 0);
                model.setZmtLkType(a.getInteger("ZMTLKType"));
                model.setCreateTime(new Date());
                model.setUpdateTime(new Date());
                model.set_id(model.getVideoId() + "_" + model.getPostId());

                result.add(model);
            }
        }

        return result;
    }


    private String generateVideoCover(PostInfoForMysqlModel post) {
        String result = null;
        if (post == null) {
            return result;
        }

        PostInfoExtendField extendField = null;
        if (!StringUtils.hasLength(post.EXTEND)) {
            return result;
        }

        extendField = JSON.parseObject(post.EXTEND, PostInfoExtendField.class);
        if (StringUtils.hasLength(extendField.ZMTLKVideoID)) {
            //视频封面顺序优先级
            if (StringUtils.hasLength(extendField.ZMTLKVCover)) {
                result = extendField.ZMTLKVCover;
            } else if (StringUtils.hasLength(extendField.ZMTLKHCover)) {
                result = extendField.ZMTLKHCover;
            } else if (StringUtils.hasLength(extendField.ZMTLKCover)) {
                result = extendField.ZMTLKCover;
            } else {
                GetChannelInfoApiResult response = langKeLvbCooperationApiService.getChannelDetailReturnResponse(extendField.ZMTLKVideoID);
                if (response != null && response.getData() != null && response.getData().getRoom_cover_resource() != null) {
                    result = response.getData().getRoom_cover_resource().getV_cover();
                    if (!StringUtils.hasLength(result)) {
                        result = response.getData().getRoom_cover_resource().getH_cover();
                    }
                    if (!StringUtils.hasLength(result)) {
                        result = response.getData().getRoom_cover_resource().getCover();
                    }
                    if (!StringUtils.hasLength(result)) {
                        result = response.getData().getRoom_cover_resource().getFrame_cover();
                    }
                }
            }
        }

        return result;
    }

    /**
     * 将短视频标签和相关基金置入列表元素
     * @param list
     */
    private void getLangKeInfo(List<CommunityVideo> list) {
        AvBatchInfoResponse avBatchInfoResponse = langKeLvbApiService.avBatchInfoReturnResponse(list.stream().
            map(CommunityVideo::getVideoId)
            .collect(Collectors.toList()));

        if (avBatchInfoResponse == null || avBatchInfoResponse.getData() == null || avBatchInfoResponse.getData().getInfo_set() == null) {
            return;
        }
        Map<String, AvBatchInfoDataInfoSet> batchInfoMap = avBatchInfoResponse.getData().getInfo_set().stream().
            collect(Collectors.groupingBy(AvBatchInfoDataInfoSet::getKey,
                Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
        for (CommunityVideo video : list) {
            AvBatchInfoDataInfoSet avBatchInfoDataInfoSet = batchInfoMap.get(video.getVideoId());
            AvBatchInfoDataInfoSetValue value = avBatchInfoDataInfoSet.getValue();
            if (value == null) {
                continue;
            }
            logger.info("videoId: {}, tagSet:{}, refQuote:{}", video.getVideoId(), value.getTag_set(), value.getRef_quote());
            if (value.getTag_set() != null) {

                List<String> tagSet = value.getTag_set().stream().
                    filter(info -> LABEL_SET.contains(info.getName())).
                    map(info -> info.getSubtag_set().stream().
                        map(SubTagInfo::getName).
                        collect(Collectors.joining(","))).
                    collect(Collectors.toList());
                video.setTagIdSet(tagSet);
            }
            if (value.getRef_quote() != null) {
                video.setAboutFund(value.getRef_quote().stream().
                    map(QuoteModel::getQ_code).
                    collect(Collectors.toList()));
            }
        }
    }

    private void updateInvisible(List<CommunityVideo> list) {
        List<VideoAuthorConfigDo> videoAuthorConfig = videoAuthorConfigDao.getVideoAuthorConfig();
        Map<String, String> authorInvisibleMap = videoAuthorConfig.stream().
            collect(Collectors.toMap(VideoAuthorConfigDo::getAuthorId, VideoAuthorConfigDo::getInvisibleLocation));

        for (CommunityVideo communityVideo : list) {
            String uid = communityVideo.getUid();
            String invisible = authorInvisibleMap.get(uid);
            if (StringUtils.isEmpty(invisible)) {
                continue;
            }
            communityVideo.setAuthorInvisible(Arrays.asList(invisible.split(",")));
            logger.info("videoId: {}, uid: {}, authorInvisible:{}", communityVideo.getVideoId(), uid, invisible);
        }

    }

}
