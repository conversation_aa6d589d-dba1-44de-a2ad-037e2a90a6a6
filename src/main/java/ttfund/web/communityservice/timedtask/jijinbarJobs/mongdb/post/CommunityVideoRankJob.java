package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostinfoExtraModel;
import ttfund.web.communityservice.bean.jijinBar.post.data.CommunityVideo;
import ttfund.web.communityservice.bean.jijinBar.post.data.CommunityVideoRank;
import ttfund.web.communityservice.dao.mongo.CommunityVideoDao;
import ttfund.web.communityservice.dao.mongo.CommunityVideoRankDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 社区视频热榜job
 */
@Slf4j
@JobHandler("CommunityVideoRankJob")
@Component
public class CommunityVideoRankJob extends IJobHandler {

    private static List<String> FIELDS = Arrays.asList("_id", "videoId", "videoCover", "uid", "timePoint", "title", "state", "postId", "postTime", "authorInvisible", "invisibleLocation");

    private static List<String> SET_ON_INSERT_FIELDS = Arrays.asList("createTime", "updateUser", "score", "sourceType");

    private static List<String> INCLUDE_FIELDS = Arrays.asList("updateTime", "score", "sourceType");

    @Autowired
    private CommunityVideoDao communityVideoDao;

    @Autowired
    private PostInfoExtraDao postInfoExtraDao;

    @Autowired
    private CommunityVideoRankDao communityVideoRankDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            Integer batchCount = null;
            Integer interval = null;
            String zmtLkType = null;
            String configs = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                batchCount = jsonObject.getInteger("batchCount");
                interval = jsonObject.getInteger("interval");
                zmtLkType = jsonObject.getString("zmtLkType");
                configs = jsonObject.getString("configs");

            }

            if (batchCount == null) {
                batchCount = 100;
            }

            if (interval == null) {
                interval = -96 * 3600;
            }

            if (zmtLkType == null) {
                zmtLkType = "102";
            }

            if (configs == null) {
                configs = "10,100,200,0.2";
            }

            log.info("第零步，打印参数。batchCount：{}，interval：{}，zmtLkType：{}，configs：{}", batchCount, interval, zmtLkType, configs);

            List<Integer> zmtLkTypeList = CommonUtils.toList(zmtLkType, ",").stream().map(a -> Integer.parseInt(a)).collect(Collectors.toList());

            List<Double> calculateConfig = CommonUtils.toList(configs, ",").stream().map(a -> Double.parseDouble(a)).collect(Collectors.toList());
            deal(batchCount, interval, zmtLkTypeList, calculateConfig);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void deal(int batchCount, int interval, List<Integer> zmtLkTypeList, List<Double> calculateConfig) throws Exception {

        Date now = new Date();
        Date start = DateUtil.calendarDateBySecond(now, interval);

        log.info("1.计算区间。start：{}，end：{}", DateUtil.getTimePoint(start), DateUtil.getTimePoint(now));

        List<CommunityVideo> dataList = communityVideoDao.getListByTimePointInterval(
            FIELDS,
            CommunityVideo.class,
            DateUtil.getTimePoint(start),
            DateUtil.getTimePoint(now),
            zmtLkTypeList
        );

        log.info("2.读取视频贴。数量：{}，头部列表：{}",
            CollectionUtils.isEmpty(dataList) ? 0 : dataList.size(),
            CollectionUtils.isEmpty(dataList) ? null : JSON.toJSONStringWithDateFormat(dataList.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(dataList)) {

            Map<String, PostinfoExtraModel> postExtraMap = new HashMap<>();
            List<String> postIds = dataList.stream().map(a -> a.getPostId()).collect(Collectors.toList());
            {
                List<List<String>> batchList = CommonUtils.toSmallList2(postIds, batchCount);
                for (List<String> batch : batchList) {
                    List<PostinfoExtraModel> postExtraList = postInfoExtraDao.getByIds(batch.stream().map(o -> Integer.parseInt(o)).collect(Collectors.toList()), "*");
                    if (!CollectionUtils.isEmpty(postExtraList)) {
                        for (PostinfoExtraModel o : postExtraList) {
                            postExtraMap.put(String.valueOf(o.ID), o);
                        }
                    }
                }
            }

            log.info("3.读取帖子额外数据。数量：{}，头部列表：{}",
                CollectionUtils.isEmpty(postExtraMap) ? 0 : postExtraMap.size(),
                CollectionUtils.isEmpty(postExtraMap) ? null : JSON.toJSONStringWithDateFormat(postExtraMap.entrySet().iterator().next(), DateUtil.datePattern)
            );

            List<CommunityVideoRank> modelList = generate(calculateConfig, dataList, postExtraMap);
            if (!CollectionUtils.isEmpty(modelList)) {
                List<Map<String, Object>> mapList = new ArrayList<>();
                Map<String, Object> map = null;
                for (CommunityVideoRank a : modelList) {
                    map = CommonUtils.beanToMap(a);
                    mapList.add(map);
                }

                List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 100);
                for (List<Map<String, Object>> batch : batchList) {
                    communityVideoRankDao.upsertMany(batch, SET_ON_INSERT_FIELDS);
                    communityVideoRankDao.updateManyWhenSetScore(batch, INCLUDE_FIELDS);
                }
            }

            log.info("4.写库。数量：{}，头部列表：{}",
                CollectionUtils.isEmpty(modelList) ? 0 : modelList.size(),
                CollectionUtils.isEmpty(modelList) ? null : JSON.toJSONStringWithDateFormat(modelList.get(0), DateUtil.datePattern)
            );


        }

        communityVideoRankDao.deleteManyByTimePoint(DateUtil.getTimePoint(start));
        log.info("5.删除。timePoint：{}", DateUtil.getTimePoint(start));

    }

    private List<CommunityVideoRank> generate(List<Double> calculateConfig,
                                              List<CommunityVideo> list,
                                              Map<String, PostinfoExtraModel> postExtraMap) {
        List<CommunityVideoRank> result = null;
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        result = JSON.parseArray(JSON.toJSONString(list), CommunityVideoRank.class);
        for (CommunityVideoRank a : result) {
            a.setClickCount(0);
            a.setLikeCount(0);
            a.setReplyCount(0);
            a.setSourceType(0);
            a.setCreateTime(new Date());
            a.setUpdateTime(new Date());

            PostinfoExtraModel postExtra = postExtraMap.get(a.getPostId());
            if (postExtra != null) {
                a.setClickCount(postExtra.CLICKNUM == null ? 0 : postExtra.CLICKNUM.intValue());
                a.setLikeCount(postExtra.LIKECOUNT == null ? 0 : postExtra.LIKECOUNT.intValue());
                a.setReplyCount(postExtra.PINGLUNNUM == null ? 0 : postExtra.PINGLUNNUM.intValue());
            }

            a.setScore(compute(calculateConfig, a.getClickCount(), a.getLikeCount(), a.getReplyCount(), a.getPostTime().getTime()));
        }

        return result;
    }

    public Double compute(List<Double> calculateConfig, int clickCount, int likeCount, int replyCount, long publishTime) {
        Long currentTime = System.currentTimeMillis();
        // 计算分子部分
        double numerator = clickCount * calculateConfig.get(0) + likeCount * calculateConfig.get(1) + replyCount * calculateConfig.get(2);
        // 计算分母部分
        // 当前时间和发布时间的差值（以小时为单位向下取整）
        long timeDifferenceHours = (currentTime - publishTime) / (60 * 60 * 1000);
        double denominator = Math.exp(calculateConfig.get(3) * timeDifferenceHours);

        // 计算热度指数
        double score = numerator / denominator;

        return score;
    }

}
