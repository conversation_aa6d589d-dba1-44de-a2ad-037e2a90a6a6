package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.data.SetActivityEntity;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.redis.BarRedisKey;
import ttfund.web.communityservice.dao.msyql.SetActivityDao;

import java.util.ArrayList;
import java.util.List;

/**
 * 活动推广配置缓存
 */
@JobHandler(value = "setActivityConfigRedisBusinessJob")
@Component
public class SetActivityConfigRedisBusinessJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(SetActivityConfigRedisBusinessJob.class);

    @Autowired
    private App app;

    @Autowired
    private SetActivityDao setActivityDao;

    @Override
    public ReturnT<String> execute(String s) throws JsonProcessingException {

        try {

            Long expireForAd = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                expireForAd = jsonObject.getLong("expireForAd");
            }

            if (expireForAd == null) {
                expireForAd = 5 * 60L;
            }

            logger.info("0.打印参数。expireForAd：{}", expireForAd);

            syncActivityConfigForPost(expireForAd);

            syncActivityConfigForNews(expireForAd);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 活动配置  帖子广告
     */
    private void syncActivityConfigForPost(Long expireForAd) {
        try {

            SetActivityEntity activityConfig = setActivityDao.getActivityForPost();

            logger.info("1.读取数据-帖子广告。数据：{}", JSON.toJSONStringWithDateFormat(activityConfig, "yyyy-MM-dd'T'HH:mm:ss"));

            String value = activityConfig == null ? "" : JSON.toJSONStringWithDateFormat(activityConfig, "yyyy-MM-dd'T'HH:mm:ss");
            app.barredis.set(BarRedisKey.FUND_GUBA_SERVICE_ACTIVITYCONFIG_UNIQUE,
                    value,
                    expireForAd);

            logger.info("2.写缓存-帖子广告。数据：{}", value);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 活动配置  资讯广告
     */
    private void syncActivityConfigForNews(Long expireForAd) {
        try {

            List<SetActivityEntity> list = new ArrayList<>();

            SetActivityEntity activityConfig = setActivityDao.getActivityForNews();
            if (activityConfig != null) {
                list.add(activityConfig);
            }

            logger.info("1.读取数据-资讯广告。数据：{}", JSON.toJSONStringWithDateFormat(activityConfig, "yyyy-MM-dd'T'HH:mm:ss"));

            String value = list == null ? "" : JSON.toJSONStringWithDateFormat(list, "yyyy-MM-dd'T'HH:mm:ss");

            app.barredis.set(BarRedisKey.FUND_GUBA_SERVICE_ACTIVITYCONFIG_UNIQUE_NEWS,
                    value,
                    expireForAd);

            logger.info("2.写缓存-资讯广告。数据：{}", value);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

}
