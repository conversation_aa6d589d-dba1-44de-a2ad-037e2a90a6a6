package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.user.CFHVPassportUserInfo;
import ttfund.web.communityservice.bean.jijinBar.user.CFHVUserInfoModel;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.mongo.PassportUserCFHVInfoDao;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.msyql.PassportUserInfoMysqlDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 财富号认证V用户同步
 */
@Component
public class PassportUserInfoCFHVJob {

    private static final Logger logger = LoggerFactory.getLogger(PassportUserInfoCFHVJob.class);

    public static final String KAFKA_LISTENER_ID = "PassportUserInfoCFHVJob";

    @Autowired
    private PassportUserCFHVInfoDao passportUserCFHVInfoDao;

    @Autowired
    private PassportUserInfoDao passportUserInfoDao;

    @Autowired
    private PassportUserInfoMysqlDao passportUserInfoMysqlDao;

    @Autowired
    private UserRedisDao userRedisDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.Fund_SyncAccreditation}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_PASSPORTUSERINFOCFHVJOB + "}",
            containerFactory = KafkaConfig.kafkaListenerContainerFactory_fundBarOld)
    public void kafkaListener(String message) {

        try {
            logger.info("收到的消息：" + JacksonUtil.obj2String(message));

            if (StringUtils.isEmpty(message)) {
                return;
            }

            // 财富号认证用户信息
            CFHVPassportUserInfo cfhVUserInfo = JacksonUtil.string2Obj(message, CFHVPassportUserInfo.class);

            if (cfhVUserInfo != null && !StringUtils.isEmpty(cfhVUserInfo.uid)) {
                initCFHVUserInfo(cfhVUserInfo, message);
            } else {
                logger.error("用户信息不正确：{}", message);
            }

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 初始化用户信息
     *
     * @param cfhVUserInfo
     * @param kafkamsg
     */
    private void initCFHVUserInfo(CFHVPassportUserInfo cfhVUserInfo, String kafkamsg) throws JsonProcessingException {

        //审核中的状态不处理
        if ("2".equals(cfhVUserInfo.status) || "3".equals(cfhVUserInfo.status)) {
            return;
        }

        /**
         * 我的业务场景只需要展示用户加V标签，不关心用户认证申请进度的。
         * 答：过滤推送数据，只保留status=0  and accreditationtype!=001001 的数据。
         * 如果遇到abandon=1，则需要remove该用户认证数据
         */
        logger.info("1-开始。uid：{}，data:{}", cfhVUserInfo.uid, kafkamsg);


        // mongdb 保存一份独立数据
        cfhVUserInfo.updatetime = new Date();
        cfhVUserInfo.id = cfhVUserInfo.uid + "_" + cfhVUserInfo.accreditationType;
        passportUserCFHVInfoDao.save(cfhVUserInfo);

        logger.info("2-写财富号用户认证信息-mongo。uid：{}，实体:{}", cfhVUserInfo.uid, JacksonUtil.obj2String(cfhVUserInfo));

        PassportUserInfoModelNew passPortUserInfo = passportUserInfoDao.getFromMongdb(cfhVUserInfo.uid);

        if (passPortUserInfo == null) {
            passPortUserInfo = new PassportUserInfoModelNew();
            passPortUserInfo.CFHVList = new ArrayList<>();
            passPortUserInfo.PassportID = cfhVUserInfo.uid;
        }

        //财富认证用户状态
        passPortUserInfo.abandon = cfhVUserInfo.abandon;
        if (!StringUtils.isEmpty(cfhVUserInfo.vid)) {
            passPortUserInfo.CaifuhaoID = cfhVUserInfo.vid;
        }

        //财富号认证状态
        passPortUserInfo.CFHVSstatus = cfhVUserInfo.status;
        passPortUserInfo.CFHUpdateTime = new Date();

        //财富号认证列表
        passPortUserInfo.CFHVList = initPassportCFHVList(passPortUserInfo.CFHVList, cfhVUserInfo);
        //根据财富V认证列表重新计算V用户
        passPortUserInfo.accreditationType = getVType(passPortUserInfo.CFHVList);

        int res = passportUserInfoMysqlDao.updatePassportUserCFHID(passPortUserInfo.PassportID, passPortUserInfo.CaifuhaoID);
        if (res != 1) {
            res = passportUserInfoMysqlDao.updatePassportUserCFHID(passPortUserInfo.PassportID, passPortUserInfo.CaifuhaoID);
            if (res != 1) {
                logger.error("更新财富号用户ID失败。uid：{}，data:{}", cfhVUserInfo.uid, kafkamsg);
            }
        }

        logger.info("3-写通行证用户信息-mysql。uid：{}，结果：{}，实体:{}", cfhVUserInfo.uid, res, JacksonUtil.obj2String(passPortUserInfo));

        boolean mongdbRes = passportUserInfoDao.updateCFHVUserToMongdb(passPortUserInfo);
        if (!mongdbRes) {
            {
                mongdbRes = passportUserInfoDao.updateCFHVUserToMongdb(passPortUserInfo);
                if (!mongdbRes) {
                    logger.error("财富号认证用户同步Mongdb失败。uid：{}，data:{}", cfhVUserInfo.uid, kafkamsg);
                }
            }
        }

        logger.info("4-写通行证用户信息-mongo。uid：{}，结果：{}", cfhVUserInfo.uid, mongdbRes);

        if (mongdbRes) {

            String key = String.format(UserRedisConfig.Asp_Net_Fund_Service_Passport_Info_pid, passPortUserInfo.PassportID);
            userRedisDao.set(key, JacksonUtil.obj2String(passPortUserInfo), 10 * 24 * 3600L);

            logger.info("5-写通行证用户信息-redis。uid：{}，实体：{}", cfhVUserInfo.uid, JacksonUtil.obj2String(passPortUserInfo));
        }

    }

    /**
     * 根据财富号用户认证类型列表转换为老的V类型
     *
     * @param cfhvList
     * @return
     */
    private String getVType(List<CFHVUserInfoModel> cfhvList) {

        if (!CollectionUtils.isEmpty(cfhvList)) {
            // 审核通过列表
            List<CFHVUserInfoModel> aduitList = cfhvList.stream()
                    .filter(a -> "0".equals(a.Status))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(aduitList)) {

                List<String> accreditationTypeList = aduitList.stream()
                        .map(a -> a.AccreditationType)
                        .collect(Collectors.toList());

                if (accreditationTypeList.contains("002")) {
                    // 企业官方认证
                    return "303";
                }

                if (accreditationTypeList.contains("001005")) {
                    // 基金经理
                    return "402";
                }

                if (accreditationTypeList.contains("003002")) {
                    // 基金公司
                    return "401";
                }

                if (accreditationTypeList.contains("001")) {
                    // 普通理财师
                    return "301";
                }

                if (accreditationTypeList.contains("001007")) {
                    // 普通个人
                    return "302";
                }
            }
        }
        return "";
    }

    /**
     * 获取新的V类型 源财富号类型
     *
     * @param cfhvList
     * @param cfhVUserInfo
     * @return
     */
    private List<CFHVUserInfoModel> initPassportCFHVList(List<CFHVUserInfoModel> cfhvList, CFHVPassportUserInfo cfhVUserInfo) {

        /**
         * 我的业务场景只需要展示用户加V标签，不关心用户认证申请进度的。
         * 答：过滤推送数据，只保留status=0  and accreditationtype!=001001 的数据。
         * 如果遇到abandon=1，则需要remove该用户认证数据
         */
        if (cfhVUserInfo != null) {
            if (cfhvList == null) {
                cfhvList = new ArrayList<>();
            }
            if (!StringUtils.isEmpty(cfhVUserInfo.accreditationType)) {

                //财富号认证信息
                CFHVUserInfoModel userInfo = new CFHVUserInfoModel();
                userInfo.Abandon = cfhVUserInfo.abandon;
                userInfo.AccreditationType = cfhVUserInfo.accreditationType;
                userInfo.Status = cfhVUserInfo.status;
                userInfo.Vid = cfhVUserInfo.vid;
                userInfo.CFHIntroduction = cfhVUserInfo.introduction;

                //禁用认证类型
                if ("1".equals(cfhVUserInfo.abandon)) {

                    // 先判断列表中是否存在该记录，如果存在则删除
                    cfhvList = cfhvList.stream()
                            .filter(a -> !Objects.equals(a.AccreditationType, cfhVUserInfo.accreditationType))
                            .collect(Collectors.toList());

                } else if ("0".equals(cfhVUserInfo.status) && !"001001".equals(cfhVUserInfo.accreditationType)) {

                    // 先判断列表中是否存在该记录，如果存在则删除
                    cfhvList = cfhvList.stream()
                            .filter(a -> !Objects.equals(a.AccreditationType, cfhVUserInfo.accreditationType))
                            .collect(Collectors.toList());
                    cfhvList.add(userInfo);
                }
            }
        }

        return cfhvList;
    }
}

