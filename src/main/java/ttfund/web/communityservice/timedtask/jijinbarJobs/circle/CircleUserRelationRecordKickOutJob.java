package ttfund.web.communityservice.timedtask.jijinbarJobs.circle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelationRecord;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.msyql.CircleUserRelationRecordDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.service.CircleServiceImpl;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;
import java.util.List;

/**
 * 圈子用户关系流水管理员踢出job
 * 备注：    需求 #678045 【基金吧6.15】新增圈子功能
 */
@Slf4j
@JobHandler("CircleUserRelationRecordKickOutJob")
@Component
public class CircleUserRelationRecordKickOutJob extends IJobHandler {

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private CircleUserRelationRecordDao circleUserRelationRecordDao;

    @Autowired
    private CircleServiceImpl circleService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            log.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                initBreakpoint,
                batchReadCount);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.CIRCLEUSERRELATIONRECORDKICKOUTJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            deal(batchReadCount);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void deal(int batchReadCount) {

        String breakpointName = UserRedisConfig.CIRCLEUSERRELATIONRECORDKICKOUTJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            log.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("第一步，读取断点。断点:{}", breakpoint);


        List<CircleUserRelationRecord> list = circleUserRelationRecordDao.getListByGte(breakpointDate, 2, 0, null, batchReadCount);

        log.info("第二步，读取数据。数量:{}，头部列表：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = list.get(list.size() - 1).getUpdateTime();

            int i = 0;
            for (CircleUserRelationRecord relationRecord : list) {
                i++;

                circleService.leave(relationRecord);

                log.info("第三步，处理数据详情-第{}/{}个。数据：{}",
                    i,
                    list.size(),
                    JSON.toJSONStringWithDateFormat(relationRecord, DateUtil.datePattern)
                );
            }

        }

        log.info("第三步，处理数据完成。数量:{}，头部列表：{}",
            CollectionUtils.isEmpty(list) ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );


        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        log.info("第四步，更新断点。断点：{}", breakpoint);
    }


}
