package ttfund.web.communityservice.timedtask.jijinbarJobs.vertica;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.JsonUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.data.TopicInsightDo;
import ttfund.web.communityservice.bean.jijinBar.post.TopicInsightModel;
import ttfund.web.communityservice.dao.mongo.TopicInsightMongoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.dao.vertica.FundTopicViewVerticaDao;
import ttfund.web.communityservice.dao.vertica.VerticaTaskRecordDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Zhuyuang
 * @version : 1.0
 * @date : 2024-08-19 14:54
 * @description :
 */
@JobHandler("TopicInsightJob")
@Component
public class TopicInsightJob extends IJobHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(TopicInsightJob.class);

    private static final String LOG_PREFIX = "TopicInsightJob[话题洞见同步服务]";

    private static final String BREAK_TIME_KEY = "TopicInsightJobBreakTime";

    private static final String TABLE_NAME = "CONTENT.JJB_FUNDTOPIC_VIEW_BASIC_APP_ALL";

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private VerticaTaskRecordDao verticaTaskRecordDao;

    @Autowired
    private FundTopicViewVerticaDao fundTopicViewVerticaDao;

    @Autowired
    private TopicInsightMongoDao topicInsightMongoDao;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date breakTime = StringUtils.isEmpty(s) ?
            userRedisDao.getBreakTime(BREAK_TIME_KEY, new Date()) : DateUtil.strToDate(s);
        Date completeTime = verticaTaskRecordDao.selectLatestRecordExist(TABLE_NAME, breakTime);
        if (completeTime == null) {
            LOGGER.info("{}: Vertica write task has not completed", LOG_PREFIX);
            return null;
        }

        List<TopicInsightDo> topicInsightList = fundTopicViewVerticaDao.select(breakTime);
        LOGGER.info("{}: 从Vertica拉取话题洞见数量为: {}", LOG_PREFIX, topicInsightList.size());
        if (CollectionUtils.isEmpty(topicInsightList)) {
            return null;
        }
        topicInsightList = topicInsightList.stream().
            filter(insightDo -> isValidJson(insightDo.getView())).
            collect(Collectors.toList());
        Optional<TopicInsightDo> latestTime = topicInsightList.stream().
            max(Comparator.comparing(TopicInsightDo::getUpdateTime));
        breakTime = latestTime.get().getUpdateTime();

        Date updateTime = new Date();
        List<TopicInsightModel> topicInsightModelList = new ArrayList<>();
        for (TopicInsightDo insightDo : topicInsightList) {
            TopicInsightModel model = new TopicInsightModel();
            model.setTopicId(insightDo.getTopicId());
            try {
                model.setInsights(convert2Insight(insightDo.getView()));
                model.setUpdateTime(updateTime);
                topicInsightModelList.add(model);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
        boolean result = topicInsightMongoDao.upsertMany(topicInsightModelList, "_id");
        LOGGER.info("{}: 同步结果为: {}", LOG_PREFIX, result);
        if (result) {
            userRedisDao.setBreakTime(BREAK_TIME_KEY, breakTime);
        }
        return ReturnT.SUCCESS;
    }

    private boolean isValidJson(String jsonString) {
        try {
            OBJECT_MAPPER.readTree(jsonString);
            return true; // 如果能够成功解析，则是有效的JSON
        } catch (JsonProcessingException e) {
            return false; // 如果抛出异常，则不是有效的JSON
        }
    }

    private List<TopicInsightModel.Insight> convert2Insight(String str) throws Exception {
        JsonNode jsonNode = JsonUtils.toJsonNode(str);
        List<TopicInsightModel.Insight> insights = new ArrayList<>();

        // 遍历 JSON
        Iterator<String> keys = jsonNode.fieldNames();
        while (keys.hasNext()) {
            String key = keys.next();
            JsonNode opinionNode = jsonNode.get(key);

            TopicInsightModel.Insight insight = new TopicInsightModel.Insight();
            insight.setOpinionId(key);
            insight.setContent(opinionNode.get("内容").asText());

            // 将细节内容转为列表
            List<String> details = OBJECT_MAPPER.readValue(
                opinionNode.get("细节").toString(),
                OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, String.class)
            );
            insight.setDetail(details);

            insights.add(insight);
        }
        return insights;
    }
}
