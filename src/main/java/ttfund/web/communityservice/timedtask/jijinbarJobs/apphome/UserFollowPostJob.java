package ttfund.web.communityservice.timedtask.jijinbarJobs.apphome;

import com.eastmoney.particle.common.utils.CollectionUtils;
import com.eastmoney.particle.common.utils.JsonUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.bean.jijinBar.enums.EnumPostType;
import ttfund.web.communityservice.bean.jijinBar.post.QA.QuestionModel;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.CFHArticleVideoInfo;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.ReplyInfoView;
import ttfund.web.communityservice.bean.jijinBar.post.recommend.UserFollowPostInfo;
import ttfund.web.communityservice.bean.jijinBar.post.videoArticle.CFHArticleModel;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.constant.DateConstant;
import ttfund.web.communityservice.dao.mongo.AppHomePostInfoDao;
import ttfund.web.communityservice.dao.mongo.CFHArticleDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.mongo.ReplyInfoMongoDao;
import ttfund.web.communityservice.dao.msyql.JjbconfigDao;
import ttfund.web.communityservice.dao.msyql.QuestionDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.enums.EnumCFHArtType;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户关注帖子列表（保留近一周数据）
 */
@JobHandler("UserFollowPostJob")
@Component
public class UserFollowPostJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserFollowPostJob.class);

    private boolean updateMongdb = false;

    private static final int MAX_NUM = 500;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private PostDao postDao;

    @Autowired
    private JjbconfigDao jjbconfigDao;

    @Autowired
    private ReplyInfoMongoDao replyInfoDao;

    @Autowired
    private CFHArticleDao cfhArticleDao;

    @Autowired
    private QuestionDao questionDao;

    @Autowired
    private AppHomePostInfoDao appHomePostInfoDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {
            logicDeal();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        //只保留近一周的帖子
        clearUserFollowPostHisData();
        return ReturnT.SUCCESS;
    }

    public void logicDeal() {
        String breakpoint = userRedisDao.get(UserRedisConfig.USER_FOLLOW_POST_JOB_LOGIC_DEAL_BREAKPOINT);
        Date time = DateUtil.calendarDateByDays(-1 * appConstant.appHomePostKeepDays);
        Date dateForBreakPoint = StringUtils.isEmpty(breakpoint) ? time : DateUtil.strToDate(breakpoint, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT);
        List<UserFollowPostInfo> postList = postDao.getPostList(time, dateForBreakPoint, 20000, jjbconfigDao.getCodeTypes()
            , CommonUtils.toList(appConstant.appHomePostType, ",").stream().map(Integer::parseInt).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(postList)) {
            LOGGER.error("UserFollowPostJob[用户关注帖子列表]:无用户关注帖子列表");
            return;
        } else {
            LOGGER.info("UserFollowPostJob[用户关注帖子列表]:拉取到帖子信息:{}", JsonUtils.toJsonString(postList));
        }

        //为所有帖子最多保留一条附加评论
        for (UserFollowPostInfo postInfo : postList) {
            //剔除无评论或仅有一条评论的帖子
            if (CollectionUtils.isEmpty(postInfo.REPLYS) || postInfo.REPLYS.size() == 1) {
                continue;
            }
            //从数据库中获取一条最新的评论
            List<ReplyInfoView> replys = replyInfoDao.getList(postInfo.ID, 1);
            if (CollectionUtils.isNotEmpty(replys)) {
                postInfo.REPLYS = replys;
            }
        }

        //获取财富号视频文章相关信息
        //财富号文章的帖子
        List<UserFollowPostInfo> cfhArticleList = postList.stream().filter(a -> a.TYPE == EnumPostType.WeMedia.getValue()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cfhArticleList)) {
            List<String> artCodeList = cfhArticleList.stream().map(a -> a.NEWSID).collect(Collectors.toList());
            List<CFHArticleModel> videoList = cfhArticleDao.getVideoList(artCodeList);
            if (!CollectionUtils.isEmpty(videoList)) {
                //剔除无效的视频文章
                videoList = videoList.stream().
                    filter(a -> a.IsDeleted != null && a.IsDeleted == 0 && a.ApprovalState != null && a.ApprovalState == 1).
                    collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(videoList)) {
                    for (CFHArticleModel video : videoList) {
                        UserFollowPostInfo obj = postList.stream().
                            filter(a -> a.NEWSID.equals(video.ArtCode)).
                            findFirst().orElse(null);
                        if (obj != null) {
                            //视频封面
                            obj.VIDEOPIC = video.VideoPic;
                            //视频列表
                            obj.VIDEOS = JsonUtils.toList(video.Videos, CFHArticleVideoInfo.class);
                            //视频文章类型
                            if (video.IsSimpleVideo != null && video.IsSimpleVideo == 1) {
                                obj.CFHARTTYPE = EnumCFHArtType.VIDEO.getValue();
                            } else if (video.IsSimpleVideo != null && video.IsSimpleVideo == 0) {
                                obj.CFHARTTYPE = EnumCFHArtType.VIDEO_EMBED.getValue();
                            }
                        }
                    }
                }
            }
        }

        //基金吧问答信息
        //悬赏金额
        List<UserFollowPostInfo> qaList = postList.stream().filter(a -> a.TYPE == EnumPostType.QAQuestion.getValue()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(qaList)) {
            List<Long> articleIds = qaList.stream().map(a -> a.ID).collect(Collectors.toList());
            List<QuestionModel> qTempList = questionDao.getList(articleIds);
            if (!CollectionUtils.isEmpty(qTempList)) {
                qTempList.forEach(
                    item -> {
                        UserFollowPostInfo obj = postList.stream().filter(a -> a.ID == item.ArticleId).findFirst().orElse(null);
                        if (obj != null) {
                            obj.AMOUNT = item.Amount;
                            obj.QID = item.QID;
                        }
                    }
                );
            }
        }

        //获取基金吧问答帖子回复信息
        if (!CollectionUtils.isEmpty(qaList)) {
            //基金吧问答回复 需要单独更新字段
            qaList.forEach(
                item -> {
                    UserFollowPostInfo obj = postList.stream().filter(a -> a.ID == item.ID).findFirst().orElse(null);
                    //获取基金吧问答信息
                    List<ReplyInfoView> answerList = questionDao.getListByPostId(item.ID, 1);
                    if (CollectionUtils.isNotEmpty(answerList)) {
                        for (ReplyInfoView answer : answerList) {
                            answer.ID = item.ID;
                        }
                    }
                    if (obj != null && CollectionUtils.isNotEmpty(answerList)) {
                        obj.REPLYS = answerList;
                    }
                }
            );
        }

        LOGGER.info("UserFollowPostJob[用户关注帖子列表]:待插入帖子信息:{}", JsonUtils.toJsonString(postList));
        //数据更新到mongdb
        boolean mongdbUpdateResult = appHomePostInfoDao.insertOrUpdate(postList);
        if (!mongdbUpdateResult) {
            appHomePostInfoDao.insertOrUpdate(postList);
        }
        if (mongdbUpdateResult) {
            dateForBreakPoint = postList.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;
            breakpoint = DateUtil.dateToStr(dateForBreakPoint, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT);
            userRedisDao.set(UserRedisConfig.USER_FOLLOW_POST_JOB_LOGIC_DEAL_BREAKPOINT, breakpoint, 90 * DateConstant.ONE_DAY);
        }
        updateMongdb = mongdbUpdateResult;
        LOGGER.info("UserFollowPostJob[用户关注帖子列表]:更新mongoDB结果:{}", updateMongdb);
    }

    /**
     * 清除历史帖子，保留近一周的帖子
     */
    public void clearUserFollowPostHisData() {
        try {
            //这里判断数据是否更新
            if (updateMongdb) {
                String breakpoint = userRedisDao.get(UserRedisConfig.USER_FOLLOW_POST_JOB_UPDATE_QA_POST_ANSWER_BREAKPOINT);
                if (StringUtils.isEmpty(breakpoint)) {
                    breakpoint = "1900-01-01 00:00:00.000";
                }
                //每天7点10分以后执行一次
                //获取当前时间
                LocalDateTime now = LocalDateTime.now();
                LocalTime nowTime = now.toLocalTime();
                //定义目标时间 7:10
                LocalTime targetTime = LocalTime.of(7, 10);
                //定义时区
                ZoneId zoneId = ZoneId.systemDefault();
                //转换为 Date
                Date nowDate = Date.from(now.atZone(zoneId).toInstant());
                if (DateUtil.strToDate(breakpoint, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT).compareTo(nowDate) < 0
                    && nowTime.isAfter(targetTime)) {
                    appHomePostInfoDao.remove(DateUtil.calendarDateByDays(-8));
                    userRedisDao.set(UserRedisConfig.USER_FOLLOW_POST_JOB_UPDATE_QA_POST_ANSWER_BREAKPOINT,
                        DateUtil.dateToStr(nowDate, DateConstant.YYYY_MM_DD_HH_MM_SS_SSS_FORMAT), 90 * DateConstant.ONE_DAY);
                }
            }
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
        }
    }

}
