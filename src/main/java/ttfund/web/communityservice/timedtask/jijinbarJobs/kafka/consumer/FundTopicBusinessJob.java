package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import com.alibaba.fastjson.JSON;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.FundTopic;
import ttfund.web.communityservice.bean.jijinBar.post.TopicDetailsForKafka;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.mongo.FundTopicMongoDao;
import ttfund.web.communityservice.dao.msyql.FundTopicDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;

/**
 * 基金话题同步
 * 2022-02-15 新增话题类型字段
 */
@Component
public class FundTopicBusinessJob {

    private static Logger logger = LoggerFactory.getLogger(FundTopicBusinessJob.class);

    public static final String KAFKA_LISTENER_ID = "FundTopicBusinessJob";

    @Autowired
    private FundTopicDao fundTopicDao;

    @Autowired
    private FundTopicMongoDao fundTopicMongoDao;

    @Autowired
    private AppConstant appConstant;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.NEWCORE_GUBA_SYNC_FUNDFOCUSTOPIC}
            , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_FUNDTOPICBUSINESSJOB + "}"
            , containerFactory = KafkaConfig.kafkaListenerContainerFactory_qa)
    private void onListen(ConsumerRecord<String, String> record) {
        hanlderFundTopic((record));
    }


    /**
     * 基金话题同步到mysql
     */
    public void hanlderFundTopic(ConsumerRecord<String, String> record) {

        try {

            TopicDetailsForKafka model = JSON.parseObject(record.value(), TopicDetailsForKafka.class);

            if (!"updateTopic".equalsIgnoreCase(model.ACTIONTYPE)) {
                logger.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                        record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));
            }


            if (model != null) {
                model._id = String.valueOf(model.HTID);

                //同步到mysql
                List<FundTopic> existList = fundTopicDao.getByIdList(Arrays.asList(model.HTID));
                Map<String, Object> map = CommonUtils.beanToMap(model);
                map.put("T_VIEWPOINTOPTIONS", JSON.toJSONString(model.T_VIEWPOINTOPTIONS));
                map.put("T_ABOUTCODE", JSON.toJSONString(model.T_ABOUTCODE));
                map.put("T_CODE", JSON.toJSONString(model.T_CODE));
                map.put("T_HOTUID", JSON.toJSONString(model.T_HOTUID));
                map.put("DEL", "deletetopic".equalsIgnoreCase(model.ACTIONTYPE) ? 1 : 0);

                if (CollectionUtils.isEmpty(existList)) {
                    fundTopicDao.insert(map);
                } else {
                    fundTopicDao.update(map);
                }

                //同步到mongodb
                if ("deletetopic".equalsIgnoreCase(model.ACTIONTYPE)) {
                    fundTopicMongoDao.deleteByIds(Arrays.asList(model._id));
                } else {
                    if (model.T_VIEWPOINTOPTIONS == null) {
                        model.T_VIEWPOINTOPTIONS = new ArrayList<>();
                    }

                    model.SCORE = 0;
                    try {

                        model.SCORE = getFundTopicScore(model.T_VIEWPOINTSSHOWTIME, model.PARTICIPANTCOUNT);

                    } catch (Exception ex) {
                        logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                                record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));
                        logger.error(ex.getMessage(), ex);
                    }
                    List<Map<String, Object>> mapList = Arrays.asList(CommonUtils.beanToMap(model));

                    fundTopicMongoDao.upsertMany(mapList);
                }

            }


        } catch (Exception ex) {
            logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                    record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            logger.error(ex.getMessage(), ex);
        }

    }

    /**
     * 计算话题得分
     *
     * @param t_viewpointsshowtime 观点显示时间
     * @param participantcount     参与人数
     * @return
     */
    private double getFundTopicScore(String t_viewpointsshowtime, int participantcount) {

        //200,20
        String[] configs = appConstant.fundTopicConfig.split(",");
        Date showTime = StringUtils.hasLength(t_viewpointsshowtime) ? DateUtil.strToDate(t_viewpointsshowtime, DateUtil.dateTimeDefaultPattern) : DateUtil.calendarDateByYears(-3);
        if (showTime == null) {
            return 0;
        }
        double score = participantcount / Integer.parseInt(configs[0])
                - getTotalDays(new Date(), showTime) * Integer.parseInt(configs[1]);
        return score;

    }

    private double getTotalDays(Date d1, Date d2) {
        return (d1.getTime() - d2.getTime()) / (24 * 3600 * 1000.0);
    }

}

