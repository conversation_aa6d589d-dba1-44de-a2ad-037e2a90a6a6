package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.useracc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.redis.UserAccRedisKey;
import ttfund.web.communityservice.dao.mongo.PassportUserBindInfoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 交易用户绑定信息
 */
@JobHandler(value = "UserTradeBindJob")
@Component
public class UserTradeBindJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(UserTradeBindJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PassportUserBindInfoDao passportUserBindInfoDao;

    @Override
    public ReturnT<String> execute(String s) {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 300;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.USERTRADEBINDJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            logicDeal(batchReadCount);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }

    private void logicDeal(int batchReadCount) {

        try {
            String breakpointName = UserRedisConfig.USERTRADEBINDJOB_BREAKPOINT;
            String breakpoint = userRedisDao.get(breakpointName);

            if (breakpoint == null) {
                breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

                logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
            }

            Date breakpointDate = DateUtil.strToDate(breakpoint);

            logger.info("第一步，读取断点。断点:{}", breakpoint);

            int round = 0;
            while (true) {
                round++;

                List<Document> list = passportUserBindInfoDao.getListByUpdateTime(Document.class, null, breakpointDate, batchReadCount);

                logger.info("第二步，读取数据-第{}轮。数量:{}，头部数据：{}",
                        round,
                        CollectionUtils.isEmpty(list) ? 0 : list.size(),
                        CollectionUtils.isEmpty(list) ? null : list.get(0)
                );


                if (!CollectionUtils.isEmpty(list)) {

                    breakpointDate = list.stream().max(Comparator.comparing(o -> o.getDate("UPDATETIME"))).get().getDate("UPDATETIME");

                    String cacheKey = null;
                    boolean result = false;
                    int i = 0;
                    for (Document item : list) {
                        i++;

                        cacheKey = String.format(UserAccRedisKey.FUND_USER_INFO, item.getString("UID"));
                        result = userRedisDao.set(cacheKey, JSON.toJSONStringWithDateFormat(item, "yyyy-MM-dd'T'HH:mm:ss"), 720 * 24 * 3600L);
                        if (!result) {
                            // 如果插入失败重试一次
                            result = userRedisDao.set(cacheKey, JSON.toJSONStringWithDateFormat(item, "yyyy-MM-dd'T'HH:mm:ss"), 720 * 24 * 3600L);

                            if (!result) {
                                // 如果再次失败，跳出
                                logger.error("缓存插入失败-第{}轮。第{}/{}个。uid：{}，数据：{}",
                                        round,
                                        i,
                                        list.size(),
                                        item.getString("UID"),
                                        JSON.toJSONStringWithDateFormat(item, "yyyy-MM-dd'T'HH:mm:ss")
                                );
                            }

                        }

                        logger.info("第三步，写redis详情-第{}轮。第{}/{}个。uid:{}",
                                round,
                                i,
                                list.size(),
                                item.getString("UID")
                        );
                    }

                    logger.info("第三步，写redis完成-第{}轮。数量:{}，头部数据：{}",
                            round,
                            CollectionUtils.isEmpty(list) ? 0 : list.size(),
                            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
                    );
                }

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("第四步，更新断点-第{}轮。断点：{}", round, breakpoint);

                if (list == null || list.size() < batchReadCount) {
                    break;
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

}
