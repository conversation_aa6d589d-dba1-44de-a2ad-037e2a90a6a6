package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.eastmoney.particle.common.concurrent.ThreadFactoryImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.HtmlUtils;
import ttfund.web.communityservice.bean.api.TtAgentPostSummaryHighlightResponse;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.ArticleIntelligentInfoDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.dao.vertica.UserContentPreferenceTagDao;
import ttfund.web.communityservice.service.TtAgentApiServiceImpl;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * AI文章智能信息job  帖子
 */
@Slf4j
@JobHandler("AiArticleIntelligentInfoByPostJob")
@Component
public class AiArticleIntelligentInfoByPostJob extends IJobHandler {

    private static final String SELECT_FIELDS = "a.ID,a.TITLE,a.TYPE,a.CONTENT,a.TIME,b.CONTENTCOUNT ";

    private static final List<Integer> FILTER_TYPE = Arrays.asList(49, 50);

    private static List<String> SET_ON_INSERT_FIELDS = Arrays.asList("createTime");

    //html标签
    private static final String TAB_REGEX = "<[^>]*?>";
    private static final Pattern TAB_REGEX_PATTERN = Pattern.compile(TAB_REGEX);

    //艾特用户
    private static final String USER_REGEX = "\\[at=(.*?)\\]([\\s\\S]*?)\\[/at\\]";
    private static final Pattern USER_REGEX_PATTERN = Pattern.compile(USER_REGEX);

    private ThreadPoolExecutor executor = new ThreadPoolExecutor(5, 5, 5L, TimeUnit.MINUTES,
        new LinkedBlockingQueue<>(20000), new ThreadFactoryImpl("AiArticleThreadPool"));

    private static final Long expireTime = 60 * 60 * 24 * 7L;

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private TtAgentApiServiceImpl ttAgentApiService;

    @Autowired
    private ArticleIntelligentInfoDao articleIntelligentInfoDao;

    @Autowired
    private UserContentPreferenceTagDao userContentPreferenceTagDao;

    public ThreadPoolExecutor getExecutor() {
        return executor;
    }

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer backTime = null;//回溯时间 负值

            //字数限制
            Integer charCountLimit = null;

            Integer batchReadCount = null;

            Boolean initThreadPool = null;
            Integer corePoolSize = null;
            Integer maximumPoolSize = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                backTime = jsonObject.getInteger("backTime");
                charCountLimit = jsonObject.getInteger("charCountLimit");
                batchReadCount = jsonObject.getInteger("batchReadCount");

                initThreadPool = jsonObject.getBoolean("initThreadPool");
                corePoolSize = jsonObject.getInteger("corePoolSize");
                maximumPoolSize = jsonObject.getInteger("maximumPoolSize");

            }

            if (backTime == null) {
                backTime = -90;
            }
            if (charCountLimit == null) {
                charCountLimit = 200;
            }
            if (batchReadCount == null) {
                batchReadCount = 10000;
            }
            if (corePoolSize == null) {
                corePoolSize = 5;
            }
            if (maximumPoolSize == null) {
                maximumPoolSize = 5;
            }

            log.info("第零步，打印参数。initBreakpoint：{}，backTime：{}，charCountLimit：{}，batchReadCount：{}，initThreadPool：{}，corePoolSize：{}，maximumPoolSize：{}",
                initBreakpoint,
                backTime,
                charCountLimit,
                batchReadCount,
                initThreadPool,
                corePoolSize,
                maximumPoolSize
            );

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.AIARTICLEINTELLIGENTINFOBYPOSTJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                log.info("0.初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            if (Objects.equals(initThreadPool, true)) {
                if (executor != null) {
                    executor.shutdownNow();
                }
                executor = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 5L, TimeUnit.MINUTES,
                    new LinkedBlockingQueue<>(20000), new ThreadFactoryImpl("AiArticleThreadPool"));

                log.info("0.初始化线程池。");
            }

            deal(charCountLimit, backTime, batchReadCount);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void deal(int charCountLimit, int backTime, int batchReadCount) {

        String breakpointName = UserRedisConfig.AIARTICLEINTELLIGENTINFOBYPOSTJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-10));

            log.error("0.读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        log.info("1.读取断点。断点:{}", breakpoint);

        Date end = DateUtil.calendarDateBySecond(backTime);
        List<Map<String, Object>> list = postInfoNewDao.getPostWithExtraInfoByTime(SELECT_FIELDS, breakpointDate, end, batchReadCount);

        log.info("2.读取帖子数据。start：{}，end：{}，数量:{}，数据：{}",
            DateUtil.dateToStr(breakpointDate),
            DateUtil.dateToStr(end),
            list == null ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        //在内容里添加10个用户最关注主题
        String hotTags = userRedisDao.get(UserRedisConfig.USERCONTENTPREFERENCETAG_HOT10TAGS);
        if (!StringUtils.hasLength(hotTags)) {
            List<String> hot10Tags = userContentPreferenceTagDao.getHot10Tags();
            if (!CollectionUtils.isEmpty(hot10Tags)) {
                hotTags = String.join(",", hot10Tags);
                userRedisDao.set(UserRedisConfig.USERCONTENTPREFERENCETAG_HOT10TAGS, hotTags, expireTime);
            }
        }

        log.info("3.读取热门标签数据。数据：{}", hotTags);

        List<Map<String, Object>> filterList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            breakpointDate = (Date)list.get(list.size() - 1).get("TIME");

            String content = null;
            for (Map<String, Object> a : list) {
                content = (String)a.get("CONTENT");
                if (StringUtils.hasLength(content) && filter(a, charCountLimit)) {

                    //html解码
                    content = HtmlUtils.htmlUnescape(content);

                    Matcher tabMatcher = TAB_REGEX_PATTERN.matcher(content);
                    while (tabMatcher.find()) {
                        content = content.replace(tabMatcher.group(0), "");
                    }

                    //处理艾特用户
                    Matcher atUserMatcher = USER_REGEX_PATTERN.matcher(content);
                    while (atUserMatcher.find()) {
                        content = content.replace(atUserMatcher.group(0), atUserMatcher.group(2));
                    }
                    log.info("4.1.处理内容。帖子ID：{}，标题：{}，内容：{}",
                        a.get("ID"),
                        a.get("TITLE"),
                        content
                    );

                    a.put("PURECONTENT", content);
                    filterList.add(a);
                }
            }
        }

        if (!CollectionUtils.isEmpty(filterList)) {
            Set<String> existPostIds = new HashSet<>();
            List<String> postIds = filterList.stream().map(a -> a.get("ID").toString()).collect(Collectors.toList());
            List<List<String>> batchList = CommonUtils.toSmallList2(postIds, 200);
            for (List<String> batch : batchList) {
                List<Document> tempList = articleIntelligentInfoDao.getManyByKeyInValue("_id", batch, Arrays.asList("_id"), Document.class);
                if (!CollectionUtils.isEmpty(tempList)) {
                    existPostIds.addAll(tempList.stream().map(a -> a.getString("_id")).collect(Collectors.toList()));
                }
            }

            filterList = filterList.stream().filter(a -> !existPostIds.contains(a.get("ID").toString())).collect(Collectors.toList());
        }

        log.info("5.过滤。数量：{}，头部数据：{}",
            CollectionUtils.isEmpty(filterList) ? 0 : filterList.size(),
            CollectionUtils.isEmpty(filterList) ? null : JSON.toJSONStringWithDateFormat(filterList.get(0), DateUtil.datePattern)
        );

        Map<String, Future<TtAgentPostSummaryHighlightResponse>> futureMap = new LinkedHashMap<>();
        Map<String, Map<String, Object>> postMap = new HashMap<>();
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(filterList)) {

            for (Map<String, Object> a : filterList) {

                Map<String, Object> paramMap = new HashMap<>();
                Map<String, Object> userInput = new HashMap<>();
                userInput.put("topic", hotTags);
                userInput.put("context", a.get("PURECONTENT"));

                paramMap.put("customerNo", "postSummary");
                paramMap.put("userInput", JSON.toJSONString(userInput));

                Future<TtAgentPostSummaryHighlightResponse> future = executor.submit(() -> ttAgentApiService.postSummaryHighlight(paramMap));
                futureMap.put(a.get("ID").toString(), future);
                postMap.put(a.get("ID").toString(), a);
            }

            int i = 0;
            for (Map.Entry<String, Future<TtAgentPostSummaryHighlightResponse>> entry : futureMap.entrySet()) {
                i++;
                TtAgentPostSummaryHighlightResponse response = null;
                Map<String, Object> post = null;
                try {
                    response = entry.getValue().get(45, TimeUnit.SECONDS);
                    post = postMap.get(entry.getKey());

                    if (response != null) {
                        Map<String, Object> intoDbMap = new HashMap<>();
                        intoDbMap.put("_id", post.get("ID").toString());
                        intoDbMap.put("articleId", post.get("ID").toString());
                        intoDbMap.put("title", post.get("TITLE").toString());
                        //0：帖子 1：资讯
                        intoDbMap.put("articleType", 0);
                        intoDbMap.put("aiHighLights", response.getSentences());
                        intoDbMap.put("aiSummary", response.getSummary());
                        intoDbMap.put("aiAsking", response.getQuestion());
                        intoDbMap.put("createTime", new Date());
                        intoDbMap.put("updateTime", new Date());

                        mapList.add(intoDbMap);
                    }

                    log.info("6.生成详情。第{}/{}个，帖子数据：{}，ai信息：{}",
                        i,
                        futureMap.size(),
                        JSON.toJSONStringWithDateFormat(post, DateUtil.datePattern),
                        JSON.toJSONStringWithDateFormat(response, DateUtil.datePattern)
                    );

                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                    log.error("6.生成详情。第{}/{}个，帖子数据：{}，ai信息：{}",
                        i,
                        futureMap.size(),
                        JSON.toJSONStringWithDateFormat(post, DateUtil.datePattern),
                        JSON.toJSONStringWithDateFormat(response, DateUtil.datePattern)
                    );
                }
            }
        }

        log.info("6.生成完成。数量：{}，头部数据：{}",
            CollectionUtils.isEmpty(filterList) ? 0 : filterList.size(),
            CollectionUtils.isEmpty(filterList) ? null : JSON.toJSONStringWithDateFormat(filterList.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(mapList)) {
            List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(mapList, 100);
            for (List<Map<String, Object>> batch : batchList) {
                articleIntelligentInfoDao.upsertManyBySetWithSetOnInsertFields(batch, SET_ON_INSERT_FIELDS, "_id");
            }
        }

        log.info("7.写库完成。数量：{}，头部数据：{}",
            CollectionUtils.isEmpty(mapList) ? 0 : mapList.size(),
            CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONStringWithDateFormat(mapList.get(0), DateUtil.datePattern)
        );

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        log.info("8.更新断点。断点：{}", breakpoint);

    }

    private boolean filter(Map<String, Object> data, Integer charCountLimit) {
        boolean result = false;
        Integer type = data.get("TYPE") == null ? null : Integer.parseInt(data.get("TYPE").toString());
        Integer contentCount = data.get("CONTENTCOUNT") == null ? null : Integer.parseInt(data.get("CONTENTCOUNT").toString());
        if (!FILTER_TYPE.contains(type) && contentCount != null && contentCount > charCountLimit) {
            result = true;
        }

        return result;
    }

}
