package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.FindPostDao;
import ttfund.web.communityservice.dao.mongo.PostFindRankDao;
import ttfund.web.communityservice.dao.mongo.PostRankDao;
import ttfund.web.communityservice.dao.msyql.JjbconfigDao;
import ttfund.web.communityservice.dao.msyql.PostInfoExtraDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 计算帖子排名
 */
@JobHandler("PostRankCalcJob")
@Component
public class PostRankCalcJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PostRankCalcJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private PostInfoExtraDao postInfoExtraDao;

    @Autowired
    private JjbconfigDao jjbconfigDao;

    @Autowired
    private PostRankDao postRankDao;

    @Autowired
    private PostFindRankDao postFindRankDao;

    @Autowired
    private FindPostDao findPostDao;

    @Autowired
    private AppConstant appConstant;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint1 = null;
            String initBreakpoint2 = null;
            Integer batchReadCount1 = null;
            Integer batchReadCount2 = null;
            String codeType = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint1 = jsonObject.getString("initBreakpoint1");
                initBreakpoint2 = jsonObject.getString("initBreakpoint2");
                codeType = jsonObject.getString("codeType");
                batchReadCount1 = jsonObject.getInteger("batchReadCount1");
                batchReadCount2 = jsonObject.getInteger("batchReadCount2");
            }

            if (batchReadCount1 == null) {
                batchReadCount1 = 5000;
            }

            if (batchReadCount2 == null) {
                batchReadCount2 = 1000;
            }

            logger.info("第零步，打印参数。initBreakpoint1：{}，initBreakpoint2：{}，codeType：{}，batchReadCount1：{}，batchReadCount2：{}",
                    initBreakpoint1,
                    initBreakpoint2,
                    codeType,
                    batchReadCount1,
                    batchReadCount2
            );

            if (StringUtils.hasLength(initBreakpoint1) || StringUtils.hasLength(initBreakpoint2)) {

                if (StringUtils.hasLength(initBreakpoint1)) {
                    userRedisDao.set(UserRedisConfig.POSTRANKCALCJOB_FUNDBARPOSTRANKCALC_BREAKPOINT, initBreakpoint1, 30 * 24 * 3600L);
                    logger.info("第零步，初始化断点。initBreakpoint1：{}", initBreakpoint1);
                }

                if (StringUtils.hasLength(initBreakpoint2)) {
                    userRedisDao.set(UserRedisConfig.POSTRANKCALCJOB_FUNDBARPOSTFINDRANKCALC_BREAKPOINT, initBreakpoint2, 30 * 24 * 3600L);
                    logger.info("第零步，初始化断点。initBreakpoint2：{}", initBreakpoint2);
                }

                return ReturnT.SUCCESS;
            }

            fundBarPostRankCalc(codeType, batchReadCount1);
            fundBarPostFindRankCalc(codeType, batchReadCount2);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 基金吧帖子排序分数计算
     */
    private void fundBarPostRankCalc(String codeType, int batchReadCount) {
        String breakpointName = UserRedisConfig.POSTRANKCALCJOB_FUNDBARPOSTRANKCALC_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("fundBarPostRankCalc-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("fundBarPostRankCalc-第一步，读取断点。断点:{}", breakpoint);

        List<String> codeTypes = getCodeTypesSpecial(codeType);
        String[] configs = appConstant.rankPostConfig.split(",");

        logger.info("fundBarPostRankCalc-第二步，打印配置。codeTypes:{}，rankPostConfig：{}",
                codeTypes,
                appConstant.rankPostConfig
        );

        int round = 0;
        while (true) {
            round++;

            List<Map<String, Object>> table = postInfoExtraDao.getPostExtra(breakpointDate, codeTypes, batchReadCount);

            logger.info("fundBarPostRankCalc-第三步，读取数据-第{}轮。数量:{}，头部id列表：{}",
                    round,
                    table == null ? 0 : table.size(),
                    table == null ? null : table.stream().map(a -> a.get("ID")).limit(20).collect(Collectors.toList()));


            double score = 0;
            if (!CollectionUtils.isEmpty(table)) {

                breakpointDate = (Date) table.stream().max((Comparator.comparing(o -> ((Date) o.get("UPDATETIME"))))).get().get("UPDATETIME");

                for (Map<String, Object> a : table) {
                    a.put("_id", a.get("ID").toString());

                    if (a.get("TIME") == null || a.get("POSTUPDATETIME") == null) {
                        continue;
                    }

                    //评论数 + 点赞数 * 1.5 + 阅读数 / 10000 - 发表距现在小时数 * 2 - 最后更新距现在小时数
                    score = Integer.parseInt(a.get("PINGLUNNUM").toString()) * Integer.parseInt(configs[0])
                            + Long.parseLong(a.get("LIKECOUNT").toString()) * Double.parseDouble(configs[1])
                            + Long.parseLong(a.get("CLICKNUM").toString()) / Integer.parseInt(configs[2])
                            - getHours(new Date(), (Date) a.get("TIME")) * Double.parseDouble(configs[3])
                            - getHours(new Date(), (Date) a.get("POSTUPDATETIME")) * Double.parseDouble(configs[4]);


                    a.put("RANKSCORE", score);
                    a.remove("CREATETIME");
                    a.remove("UPDATETIME");
                    a.remove("CONTENTCOUNT");
                    a.remove("PINGLUNNUM");
                    a.remove("POSTUPDATETIME");
                    a.remove("LIKECOUNT");
                    a.remove("CLICKNUM");
                    a.remove("DEL");
                    a.remove("TTJJDEL");
                    //a.remove("TYPE"); 加上类型
                    a.remove("QID");
                }

                Set<String> deleteSet = table.stream()
                        .filter(a -> (a.get("DEL") != null && (Integer) a.get("DEL") != 0) ||
                                (a.get("TTJJDEL") != null && (Integer) a.get("TTJJDEL") == 1))
                        .map(a -> String.valueOf(a.get("ID")))
                        .collect(Collectors.toSet());

                if (!CollectionUtils.isEmpty(deleteSet)) {
                    List<String> deleteList = deleteSet.stream().collect(Collectors.toList());
                    List<List<String>> batchList = CommonUtils.toSmallList2(deleteList, 200);
                    for (List<String> batch : batchList) {
                        postRankDao.removeByIds(batch, BarMongodbConfig.TABLE_POSTRANK);
                    }
                }

                logger.info("fundBarPostRankCalc-第四步，删除数据-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        deleteSet == null ? 0 : deleteSet.size(),
                        deleteSet == null ? null : deleteSet.stream().limit(20).collect(Collectors.toList()));

                List<Map<String, Object>> insertList = table.stream()
                        .filter(a -> !deleteSet.contains(String.valueOf(a.get("ID"))) && a.get("RANKSCORE") != null)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(insertList)) {
                    List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(insertList, 200);
                    for (List<Map<String, Object>> batch : batchList) {
                        postRankDao.upsertMany(batch);
                    }
                }

                logger.info("fundBarPostRankCalc-第五步，数据写库-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        insertList == null ? 0 : insertList.size(),
                        insertList == null ? null : insertList.stream().map(a -> a.get("ID")).limit(20).collect(Collectors.toList()));

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("fundBarPostRankCalc-第六步，更新断点-第{}轮。断点：{}", round, breakpoint);

            }

            if (table == null || table.size() < batchReadCount) {
                break;
            }
        }

    }

    /**
     * 发现页帖子算分逻辑
     * TYPE =0 (默认)
     */
    private void fundBarPostFindRankCalc(String codeType, int batchReadCount) {
        String breakpointName = UserRedisConfig.POSTRANKCALCJOB_FUNDBARPOSTFINDRANKCALC_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("fundBarPostFindRankCalc-第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("fundBarPostFindRankCalc-第一步，读取断点。断点:{}", breakpoint);

        List<String> codeTypes = getCodeTypesSpecial(codeType);
        String[] configs = appConstant.findRankPostConfig.split(",");

        logger.info("fundBarPostFindRankCalc-第二步，打印配置。codeTypes:{}，rankPostConfig：{}，findExcludeCodeConfig：{}",
                codeTypes,
                appConstant.findRankPostConfig,
                appConstant.findExcludeCodeConfig
        );

        int round = 0;
        while (true) {
            round++;

            List<Map<String, Object>> table = postInfoExtraDao.getPostExtraForFind(breakpointDate, codeTypes, batchReadCount);

            logger.info("fundBarPostFindRankCalc-第三步，读取数据-第{}轮。数量:{}，头部id列表：{}",
                    round,
                    table == null ? 0 : table.size(),
                    table == null ? null : table.stream().map(a -> a.get("ID")).limit(20).collect(Collectors.toList()));

            List<String> excludeCodes = CommonUtils.toList(appConstant.findExcludeCodeConfig, ",");
            String rankDate = DateUtil.dateToStr(new Date(), "yyyyMMdd");
            double score = 0;
            if (!CollectionUtils.isEmpty(table)) {

                breakpointDate = (Date) table.stream().max((Comparator.comparing(o -> ((Date) o.get("UPDATETIME"))))).get().get("UPDATETIME");

                for (Map<String, Object> a : table) {

                    a.put("_id", a.get("ID").toString());

                    if (a.get("TIME") == null || a.get("POSTUPDATETIME") == null) {
                        continue;
                    }

                    //推荐系统删除
                    if (a.get("RECOMMENDDEL") != null && Integer.parseInt(a.get("RECOMMENDDEL").toString()) == 1) {
                        //如果推荐系统删除给最小值
                        score = Integer.MIN_VALUE;
                    } else {
                        //点赞数 + 评论数 * 1.5 + 阅读数 / 10000 - 发表距现在小时数 * 2 - 最后更新距现在小时数
                        /*
                            分数=点赞+评论*1.5+阅读数/10000-发表时间*0.3-0.1*最后更新时间+用户分数 - 惩罚分数

                            点赞+评论=0 或 字数≤20时，惩罚分数=50

                            LIKECOUNT:点赞
                            PINGLUNNUM：评论
                            CONTENTLENGTH：字数
                         */

                        score = Integer.parseInt(a.get("LIKECOUNT").toString()) * Integer.parseInt(configs[0])
                                + Long.parseLong(a.get("PINGLUNNUM").toString()) * Double.parseDouble(configs[1])
                                + Long.parseLong(a.get("CLICKNUM").toString()) / Integer.parseInt(configs[2])
                                - getHours(new Date(), (Date) a.get("TIME")) * Double.parseDouble(configs[3])
                                - getHours(new Date(), (Date) a.get("POSTUPDATETIME")) * Double.parseDouble(configs[4]);

                        if (Integer.parseInt(a.get("LIKECOUNT").toString()) + Long.parseLong(a.get("PINGLUNNUM").toString()) < 1
                                || Long.parseLong(a.get("CONTENTLENGTH").toString()) <= 20) {

                            score = score - Double.parseDouble(configs[5]);
                        }
                    }

                    a.put("RANKSCORE", score);
                    a.put("RANKDATE", rankDate);

                    a.remove("CREATETIME");
                    a.remove("UPDATETIME");
                    a.remove("CONTENTCOUNT");
                    a.remove("PINGLUNNUM");
                    a.remove("POSTUPDATETIME");
                    a.remove("LIKECOUNT");
                    a.remove("CLICKNUM");
                    a.remove("DEL");
                    a.remove("TTJJDEL");
                    a.remove("RECOMMENDDEL");//推荐删除  正式先不上
                    //a.remove()("TYPE"); 加上类型
                    a.remove("QID");
                }

                //推荐删除，不算分，20190611
                Set<String> deleteSet = table.stream()
                        .filter(a -> (a.get("DEL") != null && (Integer) a.get("DEL") != 0)
                                || (a.get("TTJJDEL") != null && (Integer) a.get("TTJJDEL") == 1)
                                || excludeCodes.contains(a.get("CODE"))
                                || (a.get("CODE") != null && a.get("CODE").toString().indexOf("43-") > -1)
                                || (a.get("RECOMMENDDEL") != null && (Integer) a.get("RECOMMENDDEL") == 1)
                        )
                        .map(a -> String.valueOf(a.get("ID")))
                        .collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(deleteSet)) {
                    List<String> deleteList = deleteSet.stream().collect(Collectors.toList());
                    List<List<String>> batchList = CommonUtils.toSmallList2(deleteList, 200);
                    for (List<String> batch : batchList) {
                        //从PostFindRank表中移除
                        postFindRankDao.removeByIds(batch, BarMongodbConfig.TABLE_POSTFINDRANK);
                        //从findpost表删除
                        findPostDao.removeByIds(batch, BarMongodbConfig.TABLE_FINDPOST);
                    }
                }

                logger.info("fundBarPostFindRankCalc-第四步，删除数据-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        deleteSet == null ? 0 : deleteSet.size(),
                        deleteSet == null ? null : deleteSet.stream().limit(20).collect(Collectors.toList()));

                List<Map<String, Object>> insertList = table.stream()
                        .filter(a -> !deleteSet.contains(String.valueOf(a.get("ID"))) && a.get("RANKSCORE") != null)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(insertList)) {
                    List<List<Map<String, Object>>> batchList = CommonUtils.toSmallList2(insertList, 200);
                    for (List<Map<String, Object>> batch : batchList) {
                        postFindRankDao.upsertMany(batch);
                    }
                }

                logger.info("fundBarPostFindRankCalc-第五步，数据写库-第{}轮。数量:{}，头部id列表：{}",
                        round,
                        insertList == null ? 0 : insertList.size(),
                        insertList == null ? null : insertList.stream().map(a -> a.get("ID")).limit(20).collect(Collectors.toList()));

                breakpoint = DateUtil.dateToStr(breakpointDate);
                userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

                logger.info("fundBarPostFindRankCalc-第六步，更新断点-第{}轮。断点：{}", round, breakpoint);

            }

            if (table == null || table.size() < batchReadCount) {
                break;
            }
        }

    }

    private List<String> getCodeTypesSpecial(String initConfigs) {
        List<String> result = new ArrayList<>();
        if (StringUtils.hasLength(initConfigs)) {
            result.addAll(CommonUtils.toList(initConfigs, ","));
        }
        List<String> codeTypes = jjbconfigDao.getCodeTypes();
        if (!CollectionUtils.isEmpty(codeTypes)) {
            result.addAll(codeTypes);
        }

        result = result.stream().distinct().collect(Collectors.toList());
        return result;
    }

    /**
     * 计算两个日期相差的小时数
     */
    private double getHours(Date date1, Date date2) {
        return (date1.getTime() - date2.getTime()) / (3600.0 * 1000.0);
    }

}
