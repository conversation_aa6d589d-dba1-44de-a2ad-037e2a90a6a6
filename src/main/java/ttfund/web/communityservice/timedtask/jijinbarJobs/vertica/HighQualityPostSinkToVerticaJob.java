package ttfund.web.communityservice.timedtask.jijinbarJobs.vertica;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.config.HighQualityPostModel;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.HighQualityPostDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.dao.vertica.JjbTbPostQuilityBasicAppMedDao;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.*;

/**
 * 优质贴状态回写vertica
 */
@JobHandler("HighQualityPostSinkToVerticaJob")
@Component
public class HighQualityPostSinkToVerticaJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(HighQualityPostSinkToVerticaJob.class);

    private static List<String> FIELDS = Arrays.asList("_id", "title", "uid", "time", "state", "updateTime");

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private HighQualityPostDao highQualityPostDao;

    @Autowired
    private JjbTbPostQuilityBasicAppMedDao jjbTbPostQuilityBasicAppMedDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {
            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }


            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount
            );

            if (StringUtils.hasLength(initBreakpoint)) {
                DateUtil.strToDate(initBreakpoint);
                userRedisDao.set(UserRedisConfig.HIGHQUALITYPOSTSINKTOVERTICAJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);

                logger.info("第零步，初始化断点。breakpoint：{}", initBreakpoint);
                return ReturnT.SUCCESS;
            }

            deal(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }


    private void deal(int batchReadCount) throws Exception {

        String breakpointName = UserRedisConfig.HIGHQUALITYPOSTSINKTOVERTICAJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);
        if (!StringUtils.hasLength(breakpoint)) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByMinute(-60));

            logger.error("第一步，获取断点为空，使用默认断点。断点：{}", breakpoint);
        }

        logger.info("第一步，打印断点。断点：{}", breakpoint);

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        List<HighQualityPostModel> list = highQualityPostDao.getList(breakpointDate, batchReadCount, FIELDS);

        logger.info("第三步，处理数据。数量：{}，头部数据：{}",
                list == null ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );


        if (!CollectionUtils.isEmpty(list)) {

            breakpointDate = (list.get(list.size() - 1).getUpdateTime());

            List<Map<String, Object>> mapList = new ArrayList<>();
            Map<String, Object> map = null;

            for (HighQualityPostModel item : list) {
                Integer state = item.getState();
                if (state != null && (state == 0 || state == 1)) {
                    map = new HashMap<>();
                    map.put("POSTID", item.get_id());
                    map.put("FINALYZT", "");
                    map.put("TITLE", item.getTitle() == null ? "" : item.getTitle());

                    mapList.add(map);
                } else if (state != null && state == 2) {

                    map = new HashMap<>();
                    map.put("POSTID", item.get_id());
                    map.put("FINALYZT", "1");
                    map.put("TITLE", item.getTitle() == null ? "" : item.getTitle());

                    mapList.add(map);

                }
            }

            logger.info("第三步，处理数据。数量：{}，头部数据：{}",
                    mapList == null ? 0 : mapList.size(),
                    CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONStringWithDateFormat(mapList.get(0), DateUtil.datePattern)
            );

            if (!CollectionUtils.isEmpty(mapList)) {
                for (Map<String, Object> a : mapList) {
                    jjbTbPostQuilityBasicAppMedDao.updateOne(a);
                }
            }

            logger.info("第四步，写库。数量：{}，头部数据：{}",
                    mapList == null ? 0 : mapList.size(),
                    CollectionUtils.isEmpty(mapList) ? null : JSON.toJSONStringWithDateFormat(mapList.get(0), DateUtil.datePattern)
            );

        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);
        logger.info("第五步，更新断点。断点：{}", breakpoint);

    }

}

