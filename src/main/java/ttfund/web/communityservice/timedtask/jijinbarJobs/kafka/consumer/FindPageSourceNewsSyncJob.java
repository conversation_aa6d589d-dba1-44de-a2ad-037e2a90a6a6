package ttfund.web.communityservice.timedtask.jijinbarJobs.kafka.consumer;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.news.CmsArticle;
import ttfund.web.communityservice.bean.news.CmsArticleKafkaRecord;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.mongo.FindPageSourceNewsDao;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 社区发现页来源帖子 -同步job
 * 备注：需求 #711535 【基金吧6.17】资讯改社区
 */
@Component
public class FindPageSourceNewsSyncJob {
    private static Logger logger = LoggerFactory.getLogger(FindPageSourceNewsSyncJob.class);

    public static final String KAFKA_LISTENER_ID = "FindPageSourceNewsSyncJob";

    public static final List<String> SET_ON_INSERT_FIELDS = Arrays.asList("createTime");

    @Autowired
    private AppConstant appConstant;

    @Autowired
    private FindPageSourceNewsDao findPageSourceNewsDao;

    @KafkaListener(id = KAFKA_LISTENER_ID, topics = {KafkaTopicName.NP_PUSH_FUND_CMS}
        , groupId = "${" + AppConstantConfig.KAFKA_CONSUMER_GROUP_ID_FINDPAGESOURCENEWSSYNCJOB + "}"
        , containerFactory = KafkaConfig.KAFKA_LISTENER_CONTAINER_FACTORY_CMS)
    private void onListen(ConsumerRecord<String, String> record) {
        deal(record);
    }

    private void deal(ConsumerRecord<String, String> record) {
        try {

            logger.info(String.format("打印。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));

            CmsArticleKafkaRecord kafkaRecord = JacksonUtil.string2Obj(record.value(), CmsArticleKafkaRecord.class);

            CmsArticle model = JacksonUtil.string2Obj(JacksonUtil.obj2String(kafkaRecord.getCmsInfo()), CmsArticle.class);

            model.set_id(model.getArtCode());
            model.generateTimePoint();

            model.setCreateTime(new Date());
            model.setUpdateTime(new Date());

            Map<String, Object> map = CommonUtils.beanToMap(model);
            List<String> columns = CommonUtils.toList(model.getArtColumnList(), ",");
            map.remove("artColumnList");
            map.put("artColumnListArray", columns);

            boolean need = true;
            if (!CollectionUtils.isEmpty(appConstant.findPageSourceNewsColumn)) {
                need = false;
                if (!CollectionUtils.isEmpty(columns)) {
                    for (String column : columns) {
                        if (appConstant.findPageSourceNewsColumn.contains(column)) {
                            need = true;
                            break;
                        }
                    }
                }
            }

            if (need) {
                findPageSourceNewsDao.upsertManyBySetWithSetOnInsertFields(Arrays.asList(map), SET_ON_INSERT_FIELDS, "_id");
            }

        } catch (Exception ex) {
            logger.error(String.format("报错。partition：%s，offset：%s，timestamp：%s，key：%s，数据：%s",
                record.partition(), record.offset(), record.timestamp(), record.key(), record.value()));
            logger.error(ex.getMessage(), ex);
        }
    }
}
