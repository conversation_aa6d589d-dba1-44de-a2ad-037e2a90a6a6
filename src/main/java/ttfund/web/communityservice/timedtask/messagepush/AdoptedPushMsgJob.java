package ttfund.web.communityservice.timedtask.messagepush;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.RemindTypeEnum;
import ttfund.web.communityservice.bean.jijinBar.enums.SourceTypeEnum;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.post.QA.FundAnswerInfoModel;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.bean.messagepush.AnswerOrAdoptedMsgRequest;
import ttfund.web.communityservice.dao.kafka.PushToolsToKafkaDao;
import ttfund.web.communityservice.dao.mongo.FundAnswerInfoDao;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.msyql.PostMySqlDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;
import ttfund.web.communityservice.utils.NullUtil;

import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 采纳我的回答 消息推送
 */
@JobHandler(value = "AdoptedPushMsgJob")
@Component
public class AdoptedPushMsgJob extends IJobHandler {
    private final Logger logger = LoggerFactory.getLogger(AdoptedPushMsgJob.class);
    private String logpre = "MessagePush=>[AdoptedPushMsgJob]=>";

    @Autowired
    FundAnswerInfoDao fundAnswerInfoDao;

    @Autowired
    PassportUserInfoDao passportUserInfoDao;

    @Autowired
    PostDao postDao;

    @Autowired
    PushToolsToKafkaDao pushToolsToKafkaDao;

    @Autowired
    UserRedisDao userRedisDao;

    @Autowired
    PostMySqlDao postMySqlDao;

    @Override
    public ReturnT<String> execute(String param) {
        try {
            String breakTimeName = "adoptedpushmsgjob";
            Date lastUpdate = userRedisDao.getBreakTime(breakTimeName);
            if (lastUpdate != null) {
                logger.info(logpre + "开始执行,上次断点时间为:" + DateUtil.dateToStr(lastUpdate));
            } else {
                logger.info(logpre + "开始执行");
            }
            //限制更新时间
            Date updateTime=DateUtil.calendarDateByDays(-3);
            List<FundAnswerInfoModel> list = fundAnswerInfoDao.getList(false, 1, 1,updateTime, 5000);
            if (!CollectionUtils.isEmpty(list)) {

                logger.info(logpre + "本轮获取数量:" + list.size());
                for (FundAnswerInfoModel item : list) {
                    //回答者
                    PassportUserInfoModelNew answerUser = passportUserInfoDao.getPassportUserInfoById(item.CreatorID);
                    //提问者
                    PassportUserInfoModelNew questionUser = passportUserInfoDao.getPassportUserInfoById(item.UserId);


                    if (answerUser != null && !NullUtil.isNull(answerUser.PassportID) && questionUser != null && !NullUtil.isNull(questionUser.PassportID)) {

                        AnswerOrAdoptedMsgRequest request = new AnswerOrAdoptedMsgRequest();
                        int remindType = getAdoptedType(item);//获取采纳类型
                        request.EID = item._id;
                        request.PassportID = item.CreatorID; //回答者passportid
                        request.PassportName = answerUser.NickName; //回答者昵称
                        request.TriggerPassportID = item.UserId;//提问者passportid
                        request.TriggerPassportName = questionUser.NickName;//提问者昵称
                        request.PostID = String.valueOf(item.ArticleId);
                        request.SourceType = SourceTypeEnum.AID.getValue();
                        request.SourceID = item.AID;
                        request.Aid = (NullUtil.isNull(item.AID) ? "" : item.AID);
                        request.Qid = item.QID;
                        request.RemindTypeChild = remindType;

                        //PostInfoModel postinfo = postDao.getFromMong(String.valueOf(request.PostID), null, PostInfoModel.class);
                        PostInfoNewModel postinfo = postDao.getFromMong(String.valueOf(request.PostID), null, PostInfoNewModel.class);
                        logger.info(logpre + "AdoptedPushMsgJobPostInfo:" + JacksonUtil.serialize(postinfo) + ",postid:" + request.PostID);
                        //帖子信息没有推过来，则不推送消息
                        if (postinfo == null || NullUtil.isNull(postinfo.SUMMARY)) {
                            continue;
                        }

                        request.Content = postinfo.SUMMARY.substring(0, Math.min(100, postinfo.SUMMARY.length()));

                        String id = item.QID + "_" + item.AID;
                        boolean updateResult = fundAnswerInfoDao.updatePushAdoped(id, true);
                        logger.info(logpre + " 涉及更新数据为:_id:" + id);
                        if (updateResult) {
                            pushToolsToKafkaDao.adoptedPushMsg(request);
                            logger.info(logpre + " 涉及更新数据为:_id:" + id + "，推送数据为：" + JacksonUtil.serialize(request));
                        }
                        Thread.sleep(100);
                    } else {
                        logger.info(logpre + "  通行证用户不存在 :" + JacksonUtil.serialize(item));
                    }
                }
                logger.info(logpre + " 本轮涉及数量为:" + list.size());
                Date lastTime = list.stream().max(Comparator.comparing(FundAnswerInfoModel::getUpdateTime)).get().getUpdateTime();
                userRedisDao.setBreakTime(breakTimeName, lastTime);
            }
            logger.info(logpre + "执行完成");
        } catch (Exception ex) {
            logger.error(logpre + ex.getMessage(), ex);
        }
        return ReturnT.SUCCESS;
    }

    public int getAdoptedType(FundAnswerInfoModel item) {
        int type = RemindTypeEnum.QuestionerAdopted.getValue();
        try {
            if (item == null) return type;
            switch (item.AdopedType) {
                case 1:
                    type = RemindTypeEnum.QuestionerAdopted.getValue();
                    break; //主动采纳
                case 2:
                    type = RemindTypeEnum.SystemAdopted.getValue();
                    break;//系统自动采纳
                default:
                    type = RemindTypeEnum.QuestionerAdopted.getValue();
                    break;
            }
        } catch (Exception ex) {
            logger.error(logpre + "getAdoptedType=>" + ex.getMessage(), ex);
        }
        return type;
    }

}
