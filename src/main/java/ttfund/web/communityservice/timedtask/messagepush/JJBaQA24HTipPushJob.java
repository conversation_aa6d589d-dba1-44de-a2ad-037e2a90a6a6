package ttfund.web.communityservice.timedtask.messagepush;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.barrage.PassportUserInfoModel;
import ttfund.web.communityservice.bean.jijinBar.enums.MessagePushInfoSource;
import ttfund.web.communityservice.bean.jijinBar.post.QA.QuestionAnswerAcceptCount;
import ttfund.web.communityservice.bean.jijinBar.post.QA.QuestionEntityWithPostInfo;
import ttfund.web.communityservice.bean.messagepush.EnumRemindType;
import ttfund.web.communityservice.bean.messagepush.JijinbaServicePushModel;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.AnswerDao;
import ttfund.web.communityservice.dao.msyql.QuestionDao;
import ttfund.web.communityservice.service.redis.UserRedisService;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基金吧问答悬赏剩余24小时提醒
 */
@JobHandler("JJBaQA24HTipPushJob")
@Component
public class JJBaQA24HTipPushJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(JJBaQA24HTipPushJob.class);

    @Autowired
    private QuestionDao questionDao;

    @Autowired
    private AnswerDao answerDao;

    @Autowired
    private UserRedisService userRedisService;

    //基金吧kafka
    @Autowired
    @Qualifier(KafkaConfig.fundbar_kafka_temp_beanname)
    private KafkaTemplate<String, String> kafkaTemplate;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {
            Integer batchReadCount = null;
            String qaAccept24HPushPre = null;
            String passportImg = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                batchReadCount = jsonObject.getInteger("batchReadCount");
                qaAccept24HPushPre = jsonObject.getString("qaAccept24HPushPre");
                passportImg = jsonObject.getString("passportImg");
            }

            if (batchReadCount == null) {
                batchReadCount = 1000;
            }
            if (qaAccept24HPushPre == null) {
                qaAccept24HPushPre = "我的提问: ";
            }
            if (passportImg == null) {
                passportImg = "https://avator.eastmoney.com/qface/%s/30";
            }

            logger.info("第零步，打印参数。batchReadCount：{}，qaAccept24HPushPre：{}，passportImg：{}",
                    batchReadCount,
                    qaAccept24HPushPre,
                    passportImg
            );

            pushData(batchReadCount, qaAccept24HPushPre, passportImg);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    private void pushData(int batchReadCount, String qaAccept24HPushPre, String passportImg) throws Exception {


        List<QuestionEntityWithPostInfo> needPushList = new ArrayList<>();

        //kafka 推送列表
        List<QuestionEntityWithPostInfo> kafkaPushList = new ArrayList<>();

        List<JijinbaServicePushModel> pushModelList = new ArrayList<>();

        /*
         * 1.先获取需要提醒的问题
         * 2.计算达到条件的问题
         * 3.更新问题的push状态
         */
        List<QuestionEntityWithPostInfo> list = questionDao.get24HUnAdoptedQuestions(batchReadCount);

        logger.info("1.读取提问。数量：{}，头部数据：{}",
                list == null ? 0 : list.size(),
                CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
        );

        if (!CollectionUtils.isEmpty(list)) {
            List<String> listQid = list.stream().map(a -> a.QID).collect(Collectors.toList());

            //获取问题的回答统计列表
            List<QuestionAnswerAcceptCount> answerList = answerDao.getAnswerCount(listQid);
            if (!CollectionUtils.isEmpty(answerList)) {
                //有效回答数大于0 且 采纳个数为0的问答
                Set<String> pushQidSet = answerList.stream().filter(a -> a.TotalCount > 0 && a.AcceptCount == 0).map(a -> a.QID).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(pushQidSet)) {
                    needPushList = list.stream().filter(a -> pushQidSet.contains(a.QID)).collect(Collectors.toList());
                }
            }

            logger.info("2.读取回答。总数量：{}，满足条件数量：{}，满足条件头部id列表：{}",
                    answerList == null ? 0 : answerList.size(),
                    needPushList == null ? 0 : needPushList.size(),
                    needPushList == null ? null : needPushList.stream().map(a -> a.QID).limit(20).collect(Collectors.toList())
            );
        }

        if (!CollectionUtils.isEmpty(needPushList)) {
            List<String> uidList = needPushList.stream().map(a -> a.UserId).distinct().collect(Collectors.toList());
            Map<String, PassportUserInfoModel> userInfoMap = new HashMap<>();
            List<PassportUserInfoModel> userInfoList = userRedisService.passportUserInfoCache(uidList);
            if (!CollectionUtils.isEmpty(userInfoList)) {
                for (PassportUserInfoModel a : userInfoList) {
                    userInfoMap.put(a.PassportID, a);
                }
            }

            logger.info("3.读取用户信息。数量：{}，头部id列表：{}",
                    userInfoList == null ? 0 : userInfoList.size(),
                    CollectionUtils.isEmpty(userInfoList) ? null : JSON.toJSONStringWithDateFormat(userInfoList.get(0), DateUtil.datePattern)
            );

            int i = 0;
            PassportUserInfoModel passportUserInfo = null;
            for (QuestionEntityWithPostInfo item : needPushList) {
                i++;

                passportUserInfo = userInfoMap.get(item.UserId);

                if (passportUserInfo == null) {
                    logger.error("4.生成推送信息-第{}/{}个-通行证用户信息不存在。提问id：{}，通行证id：{}",
                            i,
                            needPushList.size(),
                            item.QID, item.UserId
                    );
                    continue;
                }
                if (!StringUtils.hasLength(item.title)) {
                    logger.error("4.生成推送信息-第{}/{}个-帖子标题不存在。提问id：{}，帖子id:{}",
                            i,
                            needPushList.size(),
                            item.QID, item.ArticleId
                    );
                    continue;
                }

                String title = item.title;
                JijinbaServicePushModel pushModel = new JijinbaServicePushModel();

                pushModel.PassportId = item.UserId;
                pushModel.Pid = item.UserId + "_" + item.QID + "_" + item.StockBarCode;
                pushModel.RemindType = EnumRemindType.QANOADOPT;
                pushModel.AlertParam = Arrays.asList("");
                pushModel.CustomerNo = "";
                pushModel.DataParam = Arrays.asList(
                        passportUserInfo.NickName,
                        subString(qaAccept24HPushPre + title, 50, "..."),
                        getPassportImg(passportImg, item.UserId)
                );
                pushModel.DeviceId = "";
                pushModel.Fcode = item.StockBarCode;
                pushModel.LinkParam = Arrays.asList(String.valueOf(item.ArticleId), item.QID);
                pushModel.Source = "基金吧服务";

                kafkaPushList.add(item);
                pushModelList.add(pushModel);

                logger.info("4.生成推送信息-第{}/{}个。提问id：{}，数据：{}",
                        i,
                        needPushList.size(),
                        item.QID,
                        JSON.toJSONStringWithDateFormat(pushModel, DateUtil.datePattern)
                );

            }

            logger.info("4.生成推送信息-完成。数量：{}",
                    kafkaPushList == null ? 0 : kafkaPushList.size()
            );
        }

        //更新为已经推送
        if (!CollectionUtils.isEmpty(kafkaPushList)) {
            List<String> ids = kafkaPushList.stream().map(a -> a.QID).collect(Collectors.toList());

            List<List<String>> batchList = CommonUtils.toSmallList2(ids, 100);
            for (List<String> batch : batchList) {
                questionDao.setPushed(batch);
            }

            logger.info("5.更新提问状态。数量：{}，头部id列表：{}",
                    ids == null ? 0 : ids.size(),
                    ids == null ? 0 : ids.stream().limit(20).collect(Collectors.toList())
            );
        }

        if (!CollectionUtils.isEmpty(pushModelList)) {
            int i = 0;
            //数据更新成功之后才进行推送，为了避免重复推送（这个很重要）
            for (JijinbaServicePushModel a : pushModelList) {
                i++;

                kafkaTemplate.send(KafkaTopicName.JIJINBA_SERVICE_PUSH, JacksonUtil.obj2String(a));

                logger.info("6.推送kafka-第{}/{}个。id：{}",
                        i,
                        pushModelList.size(),
                        a.Pid
                );
            }

            logger.info("6.推送kafka-完成。数量：{}，头部id列表：{}",
                    pushModelList == null ? 0 : pushModelList.size(),
                    pushModelList == null ? 0 : pushModelList.stream().map(a -> a.Pid).limit(20).collect(Collectors.toList())
            );

        }

    }

    private static String subString(String str, int length, String suffix) {
        if (!StringUtils.hasLength(str) || str.length() <= length) return str;
        return str.substring(0, length) + suffix;
    }

    /**
     * 获取通行证头像地址
     */
    private static String getPassportImg(String passportImg, String PassportID) {
        return String.format(passportImg, PassportID);
    }
}
