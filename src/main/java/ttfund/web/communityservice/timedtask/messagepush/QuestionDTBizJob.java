package ttfund.web.communityservice.timedtask.messagepush;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.QA.PinZhongDTEntity;
import ttfund.web.communityservice.bean.jijinBar.post.QA.PingZhongQDTEntity;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.QuestionDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.enums.EnumBusintype;
import ttfund.web.communityservice.enums.EnumPZDTType;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.*;

/**
 * 品种页动态(问题)
 * 备注：废弃。因为已跟王哥确定.net老服务没有使用
 */
@JobHandler("QuestionDTBizJob")
@Component
public class QuestionDTBizJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(QuestionDTBizJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private QuestionDao questionDao;

    @Autowired
    @Qualifier(KafkaConfig.fundbar_kafka_temp_beanname)
    private KafkaTemplate<String, String> kafkaTemplate;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount);


            if (StringUtils.hasLength(initBreakpoint)) {
                DateUtil.strToDate(initBreakpoint);
                userRedisDao.set(UserRedisConfig.QUESTIONDTBIZJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);

                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            pushData(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 推送数据逻辑
     */
    private void pushData(int batchReadCount) throws Exception {

        String breakpointName = UserRedisConfig.QUESTIONDTBIZJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("第一步，读取断点。断点:{}", breakpoint);

        int round = 0;
        while (true) {

            round++;

            List<PingZhongQDTEntity> qList = questionDao.getListByUpdateTime(breakpointDate, batchReadCount);

            logger.info("第二步-读取数据-轮次{}。数量：{}，头部数据：{}",
                    round,
                    qList == null ? 0 : qList.size(),
                    CollectionUtils.isEmpty(qList) ? null : JSON.toJSONStringWithDateFormat(qList.get(0), DateUtil.datePattern)
            );

            List<PinZhongDTEntity> list = new ArrayList<>();
            if (!CollectionUtils.isEmpty(qList)) {
                //获取最大更新时间
                breakpointDate = qList.stream().max((Comparator.comparing(o -> o.UpdateTime))).get().UpdateTime;

                for (PingZhongQDTEntity a : qList) {
                    PinZhongDTEntity model = new PinZhongDTEntity();
                    model.EID = String.valueOf(a.ArticleId);
                    model.CATEGORY = String.valueOf(EnumPZDTType.HU_DONG.getValue());
                    model.BUSINTYPE = String.valueOf(EnumBusintype.TI_WEN.getValue());
                    model.PASSPORTID = a.UserId;
                    model.CODE = a.CODE;
                    model.PARENTBUSINTYPE = a.QID;
                    model.SUMMARY = a.SUMMARY;
                    model.TIME = DateUtil.dateToStr(a.TIME, "yyyy-MM-dd HH:mm:ss");
                    model.STATUS = getDel(a);

                    list.add(model);
                }
            }

            if (!CollectionUtils.isEmpty(list)) {
                int i = 0;
                for (PinZhongDTEntity item : list) {
                    i++;

                    kafkaTemplate.send(KafkaTopicName.EM_FUND_USER_DYNAMIC, JacksonUtil.obj2String(Collections.singletonList(item)));


                    logger.info("第{}轮-第{}/{}个-推送详情。数据：{}",
                            round,
                            i,
                            list.size(),
                            item.EID
                    );

                }
            }


            logger.info("第三步-推送完成-轮次{}。数量：{}，头部数据：{}",
                    round,
                    list == null ? 0 : list.size(),
                    CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
            );

            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

            logger.info("第四步-保存断点-轮次{}。断点：{}", round, breakpoint);

            if (qList == null || qList.size() < batchReadCount) {
                break;
            }
        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 30 * 24 * 3600L);

        logger.info("第四步-保存断点-轮次{}。断点：{}", round, breakpoint);
    }


    private int getDel(PingZhongQDTEntity item) {
        if (item.DEL == 0 && item.TTJJDEL == 0 && item.ISENABLED == 1 && item.IsEnable == 1 && item.AuditStatusType == 1) {
            return 0;
        } else if (item.DEL == 2 && item.TTJJDEL == 0 && item.ISENABLED == 1 && item.IsEnable == 1 && item.AuditStatusType == 1) {
            return 2;
        }
        return 1;
    }


}
