package ttfund.web.communityservice.timedtask.messagepush;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.RemindTypeEnum;
import ttfund.web.communityservice.bean.jijinBar.enums.SendPiontType;
import ttfund.web.communityservice.bean.jijinBar.enums.SourceTypeEnum;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserBindInfo;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.bean.messagepush.SendPointMsgRequest;
import ttfund.web.communityservice.bean.messagepush.SendPointRecord;
import ttfund.web.communityservice.dao.kafka.PushToolsToKafkaDao;
import ttfund.web.communityservice.dao.mongo.PassportUserBindInfoDao;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.mongo.SendPointRecordDao;
import ttfund.web.communityservice.utils.BeanUtil;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;
import ttfund.web.communityservice.utils.NullUtil;
import ttfund.web.communityservice.utils.TwoTuple;

import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 财富币发放消息推送 悬赏回退
 */
@JobHandler(value = "SendPointPushMsgJob")
@Component
public class SendPointPushMsgJob extends IJobHandler {

    private final Logger logger = LoggerFactory.getLogger(SendPointPushMsgJob.class);
    private String logpre = "MessagePush=>[SendPointPushMsgJob]=>";


    @Autowired
    SendPointRecordDao sendPointRecordDao;

    @Autowired
    PassportUserBindInfoDao passportUserBindInfoDao;
    @Autowired
    PassportUserInfoDao passportUserInfoDao;

    @Autowired
    PostDao postDao;


    @Autowired
    PushToolsToKafkaDao pushToolsToKafkaDao;

    @Override
    public ReturnT<String> execute(String param) {


        try {

            Integer backDays = null; //负值

            if (StringUtils.hasLength(param)) {
                JSONObject jsonObject = JSON.parseObject(param);
                backDays = jsonObject.getInteger("backDays");
            }

            if (backDays == null) {
                backDays = -3;
            }

            logger.info("0，打印参数。backDays：{}", backDays);

            logger.info(logpre + "开始执行");
            List<SendPointRecord> list = sendPointRecordDao.getPushList(1000, backDays);
            if (!CollectionUtils.isEmpty(list)) {

                for (SendPointRecord item : list) {
                    PassportUserBindInfo user = passportUserBindInfoDao.getInfo(item.UserId);
                    PassportUserInfoModelNew passportUserInfo = passportUserInfoDao.getPassportUserInfoById(item.UserId);

                    if (user != null && !NullUtil.isNull(user.CUSTOMERNO)) {
                        logger.info(logpre + "SendPointRecordItem:" + JacksonUtil.serialize(item));

                        SendPointMsgRequest request = new SendPointMsgRequest();
                        request.EID = item._id;
                        int remindType = getRemindType(item.Type);
                        TwoTuple<Integer, String> source = getSourceType(item);
                        request.PassportID = item.UserId;
                        request.PassportName = passportUserInfo.NickName;
                        request.PointNum = String.format("%.2f", item.Amount);
                        request.RemindTypeChild = remindType;
                        request.PostID = String.valueOf(item.PostID);
                        request.SourceType = source.first; // item.Type == (int)SendPiontType.Adopted ? (int)SourceTypeEnum.AID : (int)SourceTypeEnum.QID;
                        request.SourceID = source.second; // item.Type == (int)SendPiontType.Adopted ? item.AID : item.QID;
                        request.Qid = item.QID;
                        request.Aid = NullUtil.isNull(item.AID) ? "" : item.AID;


//                        PostInfoModel postinfo = postDao.getFromMong(String.valueOf(request.PostID), null, PostInfoModel.class);
                        PostInfoNewModel postinfo = postDao.getFromMong(String.valueOf(request.PostID), null, PostInfoNewModel.class);

                        if (postinfo == null) {
                            postinfo = new PostInfoNewModel();
                        } else {
                            postinfo.CONTENT = postinfo.CONTENTEND;
                            postinfo.CONTENT = BeanUtil.getPostInfoContent(postinfo.CONTENT, postinfo.KEYWORDLIST);
                        }

                        if (item.Type == SendPiontType.Adopted.getValue()) {
                            if (NullUtil.isNull(postinfo.CONTENT)) {
                                logger.info(logpre + "推送奖励消息失败,帖子content为空:" + item.getPostID());
                                continue;
                            } else {
                                request.Content = postinfo.CONTENT.substring(0, Math.min(100, postinfo.CONTENT.length()));
                            }
                        } else {
                            if (NullUtil.isNull(postinfo.TITLE)) {
                                logger.info(logpre + "推送奖励消息失败,帖子title为空：" + item.PostID);
                                continue;
                            } else {
                                request.Content = postinfo.TITLE;
                            }
                        }
                        //request.Content =item.Type==1? postinfo.CONTENT.Substring(0, Math.Min(100, postinfo.CONTENT.Length)):  postinfo.TITLE.Substring(0, Math.Min(100, postinfo.TITLE.Length));
//                        LogUtility.LogHelper.LogInfo($"Result：{JsonConvert.SerializeObject(request)}", "发送奖励推送消息.txt");
//                        LogUtility.LogHelper.LogInfo($"Adopted：{item.Type == (int)SendPiontType.Adopted}", "发送奖励推送消息.txt");

                        Boolean result = sendPointRecordDao.updateisPush(item._id, true);
                        if (result) {

                            boolean pushResult = false;
                            if (item.Type == SendPiontType.Adopted.getValue()) {
                                //采纳奖励发放
                                pushResult = pushToolsToKafkaDao.sendRewardPushMsg(request);
                            } else {
                                //奖励退还
                                pushResult = pushToolsToKafkaDao.sendRefundPushMsg(request);
                            }
                            logger.info(logpre + "SendPointPushMsgJobPushdata=>请求参数EID：" + request.EID + ",涉及数据:" + JacksonUtil.serialize(request));
                        } else {
                            logger.info(logpre + " 设置已推送失败,_id:：" + item._id + " QID_UserId" + item.QID + "_" + item.UserId);
                        }
                    }
                }
                Date lastTime = list.stream().max(Comparator.comparing(SendPointRecord::getEndTime)).get().getEndTime();
                logger.info(logpre + " 本次数据最新更新时间为：" + DateUtil.dateToStr(lastTime));
            }
            logger.info(logpre + "结束执行，涉及数量:" + (list == null ? 0 : list.size()));
        } catch (Exception ex) {
            logger.error(logpre + ex.getMessage(), ex);
        }


        return ReturnT.SUCCESS;

    }

    public int getRemindType(int sendType) {
        int type = RemindTypeEnum.PushSuccess.getValue();
        if (sendType == SendPiontType.Adopted.getValue()) {
            type = RemindTypeEnum.PushSuccess.getValue();
        } else if (sendType == SendPiontType.RefundForDel.getValue()) {
            type = RemindTypeEnum.RefundForDel.getValue();
        } else if (sendType == SendPiontType.RefundNoAnswer.getValue()) {
            type = RemindTypeEnum.RefundNoAnswer.getValue();
        } else if (sendType == SendPiontType.RefundForNoauth.getValue()) {
            type = RemindTypeEnum.RefundNoAuth.getValue();
        } else if (sendType == SendPiontType.RefundForAnswerDel.getValue()) {
            type = RemindTypeEnum.RefundAnswerDel.getValue();
        }
        return type;
    }

    public TwoTuple<Integer, String> getSourceType(SendPointRecord item) {
        int type = SourceTypeEnum.AID.getValue();
        String id = item.AID;
        if (item.Type == SendPiontType.Adopted.getValue()) {
            //采纳
            type = SourceTypeEnum.AID.getValue();
            id = item.AID;
        } else if (item.Type == SendPiontType.RefundForDel.getValue()) {
            //删除或未审核通过退回
            type = (int)SourceTypeEnum.QID.getValue();
            id = item.QID;
        } else if (item.Type == SendPiontType.RefundNoAnswer.getValue()) {
            //无最佳答案退回
            type = (int)SourceTypeEnum.QID.getValue();
            id = item.QID;
        } else if (item.Type == SendPiontType.RefundForNoauth.getValue()) {
            //采纳用户未绑定交易账号退回
            type = (int)SourceTypeEnum.QID.getValue();
            id = item.QID;
        } else if (item.Type == SendPiontType.RefundForAnswerDel.getValue()) {
            //采纳答案被删除退还
            type = (int)SourceTypeEnum.AID.getValue();
            id = item.AID;
        }
        TwoTuple<Integer, String> tuple = new TwoTuple<Integer, String>(type, id);
        return tuple;
    }
}
