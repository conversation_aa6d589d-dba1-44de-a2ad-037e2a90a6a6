package ttfund.web.communityservice.timedtask.messagepush;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.CacheHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.barrage.PassportUserInfoModel;
import ttfund.web.communityservice.bean.jijinBar.data.CommunityNewsPushInfoModel;
import ttfund.web.communityservice.bean.jijinBar.data.CommunityPushInfoKafkaModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.mongo.CommunityNewsPushAuthorDao;
import ttfund.web.communityservice.dao.mongo.CommunityNewsPushInfoDao;
import ttfund.web.communityservice.dao.mongo.PushTestUserGroupDao;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.msyql.UserRelationDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.service.redis.UserRedisService;
import ttfund.web.communityservice.utils.CommonUtils;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 社区官方消息推送job
 * 需求：#650421 【基金吧6.11.6】新增社区官方消息推送
 */
@JobHandler("CommunityOfficialNewsPushJob")
@Component
public class CommunityOfficialNewsPushJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(CommunityOfficialNewsPushJob.class);

    @Autowired
    private UserRedisDao userRedisDao;

    @Autowired
    private CommunityNewsPushInfoDao communityNewsPushInfoDao;

    @Autowired
    private PostInfoNewDao postInfoNewDao;

    @Autowired
    private UserRedisService userRedisService;

    @Autowired
    private CommunityNewsPushAuthorDao communityNewsPushAuthorDao;

    @Autowired
    private PushTestUserGroupDao pushTestUserGroupDao;

    @Autowired
    private UserRelationDao userRelationDao;

    @Autowired
    @Qualifier(KafkaConfig.fundbar_kafka_temp_beanname)
    private KafkaTemplate<String, String> kafkaTemplate;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                initBreakpoint,
                batchReadCount);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.COMMUNITYOFFICIALNEWSPUSHJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            deal(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }


        return ReturnT.SUCCESS;
    }


    private void deal(int batchReadCount) throws Exception {

        String breakpointName = UserRedisConfig.COMMUNITYOFFICIALNEWSPUSHJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("第一步，读取断点。断点:{}", breakpoint);

        List<CommunityNewsPushInfoModel> list = communityNewsPushInfoDao.getListByUpdateTime(breakpointDate, batchReadCount);

        logger.info("第二步，读取推送数据主表。数量:{}，头部数据：{}",
            list == null ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), "yyyy-MM-dd HH:mm:ss.SSS"));

        if (!CollectionUtils.isEmpty(list)) {

            List<PostInfoNewModel> postList = new ArrayList<>();
            List<Long> postIds = list.stream().map(a -> Long.parseLong(a.getTid())).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(postIds)) {
                List<List<Long>> batchList = CommonUtils.toSmallList2(postIds, 100);
                for (List<Long> batch : batchList) {
                    List<PostInfoNewModel> tempList = postInfoNewDao.getByIds(batch, "ID,TITLE,UID,DEL,TTJJDEL");
                    if (!CollectionUtils.isEmpty(tempList)) {
                        postList.addAll(tempList);
                    }
                }
            }

            logger.info("第三步，读取帖子。数量:{}，头部数据：{}",
                postList == null ? 0 : postList.size(),
                CollectionUtils.isEmpty(postList) ? null : JSON.toJSONStringWithDateFormat(postList.get(0), "yyyy-MM-dd HH:mm:ss.SSS"));


            List<PassportUserInfoModel> userList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(postList)) {
                List<String> uids = postList.stream().map(a -> a.UID).distinct().collect(Collectors.toList());
                List<List<String>> batchList = CommonUtils.toSmallList2(uids, 100);
                for (List<String> batch : batchList) {
                    List<PassportUserInfoModel> tempList = userRedisService.passportUserInfoCache(batch);
                    if (!CollectionUtils.isEmpty(tempList)) {
                        userList.addAll(tempList);
                    }
                }
            }

            logger.info("第四步，读取通行证用户信息。数量:{}，头部数据：{}",
                userList == null ? 0 : userList.size(),
                CollectionUtils.isEmpty(userList) ? null : JSON.toJSONStringWithDateFormat(userList.get(0), "yyyy-MM-dd HH:mm:ss.SSS"));

            List<Document> remindTypeList = new ArrayList<>();
            remindTypeList = communityNewsPushAuthorDao.getAll(Arrays.asList("UID", "PushType", "RemindType"));

            logger.info("第五步，读取作者推送模板表。数量:{}，头部数据：{}",
                remindTypeList == null ? 0 : remindTypeList.size(),
                CollectionUtils.isEmpty(remindTypeList) ? null : JSON.toJSONStringWithDateFormat(remindTypeList.get(0), "yyyy-MM-dd HH:mm:ss.SSS"));

            Map<String, PostInfoNewModel> postMap = new HashMap<>();
            Map<String, PassportUserInfoModel> userMap = new HashMap<>();
            Map<String, String> remindTypeMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(postList)) {
                postList.forEach(a -> postMap.put(String.valueOf(a.ID), a));
            }
            if (!CollectionUtils.isEmpty(userList)) {
                userList.forEach(a -> userMap.put(String.valueOf(a.PassportID), a));
            }
            if (!CollectionUtils.isEmpty(remindTypeList)) {
                remindTypeList.forEach(a -> remindTypeMap.put(a.getString("UID"), a.getString("RemindType")));
            }


            PostInfoNewModel tempPost = null;
            PassportUserInfoModel tempUser = null;
            String remindType = null;
            boolean isValid = false;
            List<String> allSubscriberUids = null;

            int i = 0;
            for (CommunityNewsPushInfoModel a : list) {
                i++;

                breakpointDate = list.get(list.size() - 1).getUpdateTime();


                isValid = false;
                tempPost = null;
                tempUser = null;
                remindType = null;

                tempPost = postMap.get(a.getTid());
                if (tempPost != null) {
                    tempUser = userMap.get(tempPost.UID);
                }
                if (tempPost != null) {
                    remindType = remindTypeMap.get(tempPost.UID);
                }

                /**
                 * 处理推送给测试组用户
                 */
                if (a.getTestPushState() != null && a.getTestPushState() == 1) {

                    isValid = checkValidByPostAndUser(a, tempPost, tempUser, true);
                    if (isValid) {
                        if (remindType == null) {
                            a.setTestPushMessage("作者对应推送模板未找到");
                            isValid = false;
                        }
                    }

                    if (isValid) {
                        allSubscriberUids = getAllTestGroupUids();
                        if (CollectionUtils.isEmpty(allSubscriberUids)) {
                            a.setTestPushMessage("订阅者数量为0");
                            isValid = false;
                        }
                    }

                    //判断推送
                    if (isValid) {
                        List<CommunityPushInfoKafkaModel> kafkaModels = createKafkaModels(a, tempPost, tempUser, allSubscriberUids, remindType);

                        for (CommunityPushInfoKafkaModel kafkaModel : kafkaModels) {
                            kafkaTemplate.send(KafkaTopicName.TOPIC_COMMON_PUSH, JSON.toJSONStringWithDateFormat(kafkaModel, "yyyy-MM-dd HH:mm:ss"));
                        }

                        a.setTestPushState(2);
                    } else {

                        a.setTestPushState(3);
                    }

                    a.setTestPushTime(new Date());

                    Map<String, Object> upsertMap = createMap(a, true);
                    communityNewsPushInfoDao.upsertOne(upsertMap);

                    logger.info("第六步，推送详情。第{}/{}个，类型：{}，id：{}，数据：{}",
                        i,
                        list.size(),
                        "测试组推送",
                        a.getTid(),
                        JSON.toJSONStringWithDateFormat(a, "yyyy-MM-dd HH:mm:ss.SSS")
                    );
                }


                /**
                 * 处理推送给正常用户
                 */
                if (a.getOnlinePushState() != null && a.getOnlinePushState() == 1) {
                    isValid = checkValidByPostAndUser(a, tempPost, tempUser, false);
                    if (isValid) {
                        if (remindType == null) {
                            a.setOnlinePushMessage("作者对应推送模板未找到");
                            isValid = false;
                        }
                    }

                    if (isValid) {
                        allSubscriberUids = userRelationDao.getUserFans(tempPost.UID);
                        if (CollectionUtils.isEmpty(allSubscriberUids)) {
                            a.setOnlinePushMessage("订阅者数量为0");
                            isValid = false;
                        }
                    }

                    //判断推送
                    if (isValid) {
                        List<CommunityPushInfoKafkaModel> kafkaModels = createKafkaModels(a, tempPost, tempUser, allSubscriberUids, remindType);

                        for (CommunityPushInfoKafkaModel kafkaModel : kafkaModels) {
                            kafkaTemplate.send(KafkaTopicName.TOPIC_COMMON_PUSH, JSON.toJSONStringWithDateFormat(kafkaModel, "yyyy-MM-dd HH:mm:ss"));
                        }

                        a.setOnlinePushState(2);
                    } else {

                        a.setOnlinePushState(3);
                    }

                    a.setOnlinePushTime(new Date());

                    Map<String, Object> upsertMap = createMap(a, false);
                    communityNewsPushInfoDao.upsertOne(upsertMap);

                    logger.info("第六步，推送详情。第{}/{}个，类型：{}，id：{}，数据：{}",
                        i,
                        list.size(),
                        "正式推送",
                        a.getTid(),
                        JSON.toJSONStringWithDateFormat(a, "yyyy-MM-dd HH:mm:ss.SSS")
                    );
                }
            }

        }

        logger.info("第六步，推送完成。数量：{}，头部数据：{}",
            list == null ? 0 : list.size(),
            CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), "yyyy-MM-dd HH:mm:ss.SSS"));

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        logger.info("第七步，更新断点。断点：{}", breakpoint);

    }

    private List<String> getAllTestGroupUids() {
        List<String> result = null;
        String key = "CommunityOfficialNewsPushJob_getAllTestGroupUids";
        result = CacheHelper.get(key);
        if (result == null) {
            List<Document> docs = pushTestUserGroupDao.getAll(Arrays.asList("UID"));
            if (!CollectionUtils.isEmpty(docs)) {
                result = docs.stream().map(a -> a.getString("UID")).collect(Collectors.toList());
            }

            if (result == null) {
                result = new ArrayList<>();
            }

            CacheHelper.put(key, result, 3 * 60 * 1000);
        }

        return result;
    }

    private boolean checkValidByPostAndUser(CommunityNewsPushInfoModel a,
                                            PostInfoNewModel tempPost,
                                            PassportUserInfoModel tempUser,
                                            boolean isPushTestGroup) {
        boolean result = false;
        String message = null;

        if (tempPost == null) {
            message = "帖子未找到";
        } else if (tempPost.DEL == null || tempPost.DEL != 0 || tempPost.TTJJDEL == null || tempPost.TTJJDEL != 0) {
            message = "帖子已删除";
        } else if (tempUser == null) {
            message = "发布者用户信息未找到";
        } else {
            result = true;
        }

        if (message != null) {
            if (isPushTestGroup) {
                a.setTestPushMessage(message);
            } else {
                a.setOnlinePushMessage(message);
            }
        }

        return result;
    }

    /**
     * 生成kafka消息实体
     */
    private List<CommunityPushInfoKafkaModel> createKafkaModels(CommunityNewsPushInfoModel a,
                                                                PostInfoNewModel post,
                                                                PassportUserInfoModel passportUser,
                                                                List<String> allSubscriberUids,
                                                                String remindType) {

        List<CommunityPushInfoKafkaModel> result = new ArrayList<>();

        List<List<String>> batchList = CommonUtils.toSmallList2(allSubscriberUids, 1000);
        for (List<String> batch : batchList) {
            CommunityPushInfoKafkaModel model = new CommunityPushInfoKafkaModel();
            model.setRemindType(remindType);
            model.setSubList(batch);
            model.setSubType(0);
            model.setPublisher(a.getTid());
            model.setPubType(1);
            model.setAlertParam(Arrays.asList(post.TITLE));
            model.setDataParam(Arrays.asList(post.TITLE));
            model.setLinkParam(Arrays.asList(a.getTid()));
            model.setPushStrategy(Arrays.asList(2));

            result.add(model);
        }

        return result;
    }

    private Map<String, Object> createMap(CommunityNewsPushInfoModel a, boolean isPushTestGroup) {
        Map<String, Object> map = new HashMap<>();
        map.put("_id", a.get_id());
        if (isPushTestGroup) {
            map.put("TestPushState", a.getTestPushState());
            map.put("TestPushTime", a.getTestPushTime());
            map.put("TestPushMessage", a.getTestPushMessage());
        } else {
            map.put("OnlinePushState", a.getOnlinePushState());
            map.put("OnlinePushTime", a.getOnlinePushTime());
            map.put("OnlinePushMessage", a.getOnlinePushMessage());
        }

        return map;
    }

}
