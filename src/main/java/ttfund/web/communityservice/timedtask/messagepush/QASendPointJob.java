package ttfund.web.communityservice.timedtask.messagepush;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.core.register.AppCore;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.enums.RemindTypeEnum;
import ttfund.web.communityservice.bean.jijinBar.enums.SendPiontType;
import ttfund.web.communityservice.bean.jijinBar.enums.SourceTypeEnum;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserBindInfo;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserInfoModelNew;
import ttfund.web.communityservice.bean.messagepush.FundQuestionInfo;
import ttfund.web.communityservice.bean.messagepush.QAData;
import ttfund.web.communityservice.bean.messagepush.RewardPointResponse;
import ttfund.web.communityservice.bean.messagepush.SendPointMsgRequest;
import ttfund.web.communityservice.bean.messagepush.SendPointRecord;
import ttfund.web.communityservice.bean.messagepush.SendResultKafka;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.kafka.PushToolsToKafkaDao;
import ttfund.web.communityservice.dao.mongo.FundAnswerInfoDao;
import ttfund.web.communityservice.dao.mongo.FundQuestionInfoDao;
import ttfund.web.communityservice.dao.mongo.PassportUserBindInfoDao;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.mongo.PostDao;
import ttfund.web.communityservice.dao.mongo.SendPointRecordDao;
import ttfund.web.communityservice.dao.msyql.PostMySqlDao;
import ttfund.web.communityservice.utils.BeanUtil;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;
import ttfund.web.communityservice.utils.NullUtil;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 基金吧问答财富币 发放，消息推送，财富币退回
 */

@JobHandler(value = "QASendPointJob")
@Component
public class QASendPointJob extends IJobHandler {

    private final Logger logger = LoggerFactory.getLogger(QASendPointJob.class);
    private String logpre = "MessagePush=>[QASendPointJob]=>";
    @Autowired
    SendPointRecordDao sendPointRecordDao;
    //kafka
    @Autowired
    @Qualifier(KafkaConfig.gubarqareward_kafka_temp_beanname)
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    PassportUserInfoDao passportUserInfoDao;

    @Autowired
    PassportUserBindInfoDao passportUserBindInfoDao;

    @Autowired
    AppConstant appConstant;

    @Autowired
    FundAnswerInfoDao fundAnswerInfoDao;


    @Autowired
    PostDao postDao;

    @Autowired
    PushToolsToKafkaDao pushToolsToKafkaDao;

    @Autowired
    FundQuestionInfoDao fundQuestionInfoDao;

    @Resource
    AppCore appCore;

    @Autowired
    PostMySqlDao postMySqlDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {


            Integer backDays = null; //负值

            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                backDays = jsonObject.getInteger("backDays");
            }

            if (backDays == null) {
                backDays = -3;
            }

            logger.info("0，打印参数。backDays：{}", backDays);

            logger.info(logpre + "开始执行");
            long cacheSeconds = 60 * 60 * 24 * 4L;
            /*3天之前已结束，且财富币未发放的用户*/
            List<SendPointRecord> list = sendPointRecordDao.getSendList(1000, DateUtil.calendarDateByDays(backDays));
            if (!CollectionUtils.isEmpty(list)) {

                //根据问题ID 分组
                Map<String, List<SendPointRecord>> recGroup = list.stream().collect(Collectors.groupingBy(SendPointRecord::getQID));

                for (Map.Entry<String, List<SendPointRecord>> recItem : recGroup.entrySet()) {


                    List<SendResultKafka> sendResultList = new ArrayList<>();

                    QAData kafkaPushMsg = new QAData();
                    kafkaPushMsg.AppType = 1;
                    kafkaPushMsg.QID = recItem.getKey();
                    kafkaPushMsg.DataType = 600;
                    kafkaPushMsg.CreatedTime = DateUtil.getNowDate();

                    for (SendPointRecord item : recItem.getValue()) {
                        PassportUserBindInfo user = passportUserBindInfoDao.getInfo(item.UserId);
                        PassportUserInfoModelNew passportUserInfo = passportUserInfoDao.getPassportUserInfoById(item.UserId);
                        logger.info(logpre + "User：{" + JacksonUtil.serialize(user));

                        /**
                         * 如果通行证用户  和 交易账户存在则进行奖励发放
                         */
                        if (user != null && passportUserInfo != null && !NullUtil.isNull(passportUserInfo.NickName)) {

                            /**
                             * 发放用户奖励信息
                             */

                            int point = item.Amount.intValue();
                            String reference = item.Type == 1 ? "最佳回答奖励" : "悬赏提问退回";
                            String taskId = item.Type == 1 ? appConstant.REWARDSTASKID : appConstant.REFUNDTASKID;
                            RewardPointResponse result =
                                sendPointRecordDao.acceptActiveChangeUserPointNew(user.CUSTOMERNO, taskId, reference,
                                    point, item.UserId, "Web", "");

                            logger.info(logpre + "Result：" + JacksonUtil.serialize(result));

                            SendResultKafka resultContent = new SendResultKafka();
                            {
                                resultContent.UID = item.UserId;
                                resultContent.Amount = point;
                            }
                            /**
                             *如果奖励发放成功，更新回答数据  更新奖励发放记录
                             */
                            if (result != null && result.sendstatus == 0) {

                                //更新回答表的 奖励状态
                                String id = item.QID + "_" + item.AID;
                                /**
                                 * 更新回答信息
                                 */
                                fundAnswerInfoDao.update(id, 1, item.getAmount().intValue());
                                logger.info(logpre + "发放成功：" + JacksonUtil.serialize(item));
                                if (item.Type == SendPiontType.Adopted.getValue()) {
                                    resultContent.PayResult = 1;
                                    resultContent.Message = "采纳奖励发放成功";
                                    resultContent.Time = DateUtil.getNowDate();
                                    sendResultList.add(resultContent);
                                    logger.info(logpre + "需要推送的kafka数据：" + JacksonUtil.serialize(resultContent));
                                }
                                /**
                                 * 更新奖励发放记录
                                 */
                                Boolean isSuccess = sendPointRecordDao.update(item._id, true, result.orderid, user.CUSTOMERNO, JacksonUtil.serialize(result));
                                Thread.sleep(3500);

                            } else if (result != null && result.sendstatus == 2) {

                                //采纳用户未绑定交易账号，退回提问者，将当前记录删除，插入新记录
                                try {
                                    //推送未绑定交易账号导致发送失败的消息给回答用户
                                    SendPointMsgRequest request = new SendPointMsgRequest();
                                    request.EID = item._id;
                                    request.PassportID = item.UserId;
                                    request.PassportName = passportUserInfo.NickName;
                                    request.PointNum = String.format("%.2f", item.Amount);
                                    request.RemindTypeChild = RemindTypeEnum.PushFailure.getValue();
                                    request.PostID = String.valueOf(item.PostID);
                                    request.SourceType = SourceTypeEnum.AID.getValue();
                                    request.SourceID = item.AID;
                                    request.Qid = item.QID;
                                    request.Aid = item.AID;
                                    // PostInfoModel postinfo = postDao.getFromMong(String.valueOf(request.PostID), null, PostInfoModel.class);
                                    PostInfoNewModel postinfo = postDao.getFromMong(String.valueOf(request.PostID), null, PostInfoNewModel.class);

                                    if (postinfo != null) {
                                        postinfo.CONTENT = postinfo.CONTENTEND;
                                        postinfo.CONTENT = BeanUtil.getPostInfoContent(postinfo.CONTENT, postinfo.KEYWORDLIST);
                                    }
                                    if (postinfo == null || NullUtil.isNull(postinfo.CONTENT)) continue;


                                    request.Content = postinfo.CONTENT;
                                    logger.info(logpre + "财富币发放失败提醒用户 request：" + JacksonUtil.serialize(request));
                                    boolean pushSucess = false;

                                    //这里是判断是否推送过
                                    String cacheKey = "SendPointMsgRequest_" + request.EID;
                                    String isPush = appCore.redisuserread.get(cacheKey);
                                    if (NullUtil.isNull(isPush)) {
                                        if (item.getType() == 1) {
                                            pushSucess = pushToolsToKafkaDao.sendRewardPushMsg(request);
                                        } else {
                                            pushSucess = pushToolsToKafkaDao.sendRefundPushMsg(request);
                                        }
                                        //设置推送标识
                                        appCore.redisuserwrite.set(cacheKey, "1", cacheSeconds);
                                    }
                                    logger.info(logpre + "[QASendPointJob02]财富币发放失败提醒用户 response：" + JacksonUtil.serialize(pushSucess));

                                    String id = item.QID + "_" + item.AID;
                                    fundAnswerInfoDao.update(id, 0, item.getAmount().intValue());
                                    //更新回答表的 奖励状态
                                    logger.info(logpre + "未绑定交易账号 发放失败：" + JacksonUtil.serialize(item));

                                    logger.info(logpre + "财富币发放失败 记录Fail Record：" + JacksonUtil.serialize(item));
                                    if (item.Type == SendPiontType.Adopted.getValue()) {
                                        resultContent.PayResult = 2;
                                        resultContent.Message = "采纳用户未绑定交易账号，积分发放失败";
                                        resultContent.Time = DateUtil.getNowDate();
                                        sendResultList.add(resultContent);
                                        logger.info(logpre + "问答财富币发放=>需要推送的kafka数据：" + JacksonUtil.serialize(resultContent));
                                    }
                                    logger.info(logpre + "问答财富币发放，resultContent.PayResult:" + resultContent.PayResult + ";用户未绑定交易账号CustomerNo不为空{" + user.CUSTOMERNO + "},record {" + item._id + "}，删除该条记录，插入一条返还记录");

                                    sendPointRecordDao.remove(item._id);

                                    FundQuestionInfo rec = fundQuestionInfoDao.get(item.QID);

                                    SendPointRecord record = new SendPointRecord();
                                    {
                                        record._id = rec.QID + "_" + rec.UserId + "_" + item.AID;
                                        record.AID = "";
                                        record.Amount = item.Amount;
                                        record.CustomerNo = "";
                                        record.EndTime = rec.EndTime;
                                        record.IsSend = false;
                                        record.PayId = "";
                                        record.QID = rec.QID;
                                        record.ResultMessage = "";
                                        record.Type = SendPiontType.RefundForNoauth.getValue(); //未绑定交易账号退还
                                        record.UserId = rec.UserId;
                                        record.PostID = rec.ArticleId;
                                    }
                                    sendPointRecordDao.saveData(record);
                                } catch (Exception ex) {
                                    logger.error(logpre + "QASendPoint问答财富币退回提问者记录插入异常：" + ex.getMessage(), ex);
                                }
                            }
                        } else if ((user == null || NullUtil.isNull(user.CUSTOMERNO)) && passportUserInfo != null)//未绑定交易账号
                        {
                            //推送未绑定交易账号导致发送失败的消息给回答用户
                            SendPointMsgRequest request = new SendPointMsgRequest();
                            request.EID = item._id;
                            request.PassportID = item.UserId;
                            request.PassportName = passportUserInfo.NickName;
                            request.PointNum = String.format("%.2f", item.Amount);
                            request.RemindTypeChild = RemindTypeEnum.PushFailure.getValue();
                            request.PostID = String.valueOf(item.PostID);
                            request.SourceType = SourceTypeEnum.AID.getValue();
                            request.SourceID = item.AID;
                            request.Qid = item.QID;
                            request.Aid = item.AID;

//                            PostInfoModel postinfo = postDao.getFromMong(String.valueOf(request.PostID), null, PostInfoModel.class);
                            PostInfoNewModel postinfo = postDao.getFromMong(String.valueOf(request.PostID), null, PostInfoNewModel.class);
                            if (postinfo == null) {
                                postinfo = new PostInfoNewModel();
                            } else {
                                postinfo.CONTENT = postinfo.CONTENTEND;
                                postinfo.CONTENT = BeanUtil.getPostInfoContent(postinfo.CONTENT, postinfo.KEYWORDLIST);
                            }
                            if (NullUtil.isNotNull(postinfo.CONTENT)) {
                                request.Content = postinfo.CONTENT;
                            }

                            logger.info(logpre + "未绑定交易账号退回财富币 request：{" + JacksonUtil.serialize(request) + "}");
                            boolean pushresult = false;

                            //这里是判断是否推送过
                            String cacheKey = "SendPointMsgRequest_" + request.EID;
                            String isPush = appCore.redisuserread.get(cacheKey);
                            if (NullUtil.isNull(isPush)) {
                                if (item.getType() == 1) {
                                    pushresult = pushToolsToKafkaDao.sendRewardPushMsg(request);
                                } else {
                                    pushresult = pushToolsToKafkaDao.sendRefundPushMsg(request);
                                }
                                appCore.redisuserwrite.set(cacheKey, "1", cacheSeconds);
                            }


                            logger.info(logpre + "未绑定交易账号退回财富币 response：" + JacksonUtil.serialize(pushresult));
                            String id = item.QID + "_" + item.AID;
                            //更新回答表的 奖励状态
                            fundAnswerInfoDao.update(id, 0, item.getAmount().intValue());

                            logger.info(logpre + "未绑定交易账号 不发放：" + JacksonUtil.serialize(item));
                            SendResultKafka resultContent = new SendResultKafka();
                            {
                                resultContent.UID = item.UserId;
                                resultContent.Amount = item.Amount.intValue();
                            }
                            ;
                            logger.info(logpre + "未绑定交易账号退回财富币 推送kafka Record  out：" + JacksonUtil.serialize(item));
                            if (item.Type == SendPiontType.Adopted.getValue()) {
                                resultContent.PayResult = 2;
                                resultContent.Message = "采纳用户未绑定交易账号，退回作者";
                                resultContent.Time = DateUtil.getNowDate();
                                sendResultList.add(resultContent);
                                logger.info(logpre + "采纳用户未绑定交易账号 需要推送的kafka数据：" + JacksonUtil.serialize(resultContent));
                            }
                            logger.info(logpre + "问答财富币发放，用户未绑定交易账号CustomerNo为空,record {" + item._id + "}，删除该条记录，插入一条返还记录");
                            try {
                                sendPointRecordDao.remove(item._id);

                                FundQuestionInfo rec = fundQuestionInfoDao.get(item.QID);

                                SendPointRecord record = new SendPointRecord();
                                {
                                    record._id = rec.QID + "_" + rec.UserId + "_" + item.AID;
                                    record.AID = "";
                                    record.Amount = item.Amount;
                                    record.CustomerNo = "";
                                    record.EndTime = rec.EndTime;
                                    record.IsSend = false;
                                    record.PayId = "";
                                    record.QID = rec.QID;
                                    record.ResultMessage = "";
                                    record.Type = SendPiontType.RefundForNoauth.getValue(); //未绑定交易账号退还
                                    record.UserId = rec.UserId;
                                    record.PostID = rec.ArticleId;
                                }
                                sendPointRecordDao.saveData(record);
                            } catch (Exception ex) {
                                logger.error(logpre + "QASendPoint问答财富币退回提问者记录插入异常：" + ex.getMessage(), ex);
                            }
                        }


                    }
                    if (!CollectionUtils.isEmpty(sendResultList)) {
                        try {
                            kafkaPushMsg.Content = JacksonUtil.serialize(sendResultList);
                            logger.info(logpre + "发放财富币推送kafka数据：{" + JacksonUtil.serialize(kafkaPushMsg) + "}");
                            List<QAData> kafkaList = new ArrayList<>();
                            kafkaList.add(kafkaPushMsg);
                            kafkaTemplate.send(KafkaTopicName.NEWCORE_GUBA_QAPLATFORM_DATA_REWARD, kafkaPushMsg.getQID(), JacksonUtil.serialize(kafkaList));
                        } catch (Exception ex) {
                            logger.error(logpre + "推送kafka数据ERROR：{JsonConvert.SerializeObject(kafkaPushMsg)}===>>>" + ex.getMessage(), ex);
                        }
                    }
                }
                Date lastTime = list.stream().max(Comparator.comparing(SendPointRecord::getEndTime)).get().getEndTime();
                logger.info(logpre + " 本次数据最新更新时间为：" + DateUtil.dateToStr(lastTime));
            }
            logger.info(logpre + "结束执行，涉及数量:" + (list == null ? 0 : list.size()));
        } catch (Exception ex) {
            logger.error(logpre + ex.getMessage(), ex);
        }
        return ReturnT.SUCCESS;
    }

    /*@Override
    public ReturnT<String> execute(String param) {

        try {

            logger.info(logpre + "开始执行");
            long cacheSeconds=60*60*24*4L;
            *//*3天之前已结束，且财富币未发放的用户*//*
            List<SendPointRecord> list = sendPointRecordDao.getSendList(1000, DateUtil.calendarDateByDays(-3));
            if (!CollectionUtils.isEmpty(list)) {

                //根据问题ID 分组
                Map<String, List<SendPointRecord>> recGroup = list.stream().collect(Collectors.groupingBy(SendPointRecord::getQID));

                for (Map.Entry<String, List<SendPointRecord>> recItem : recGroup.entrySet()) {


                    List<SendResultKafka> sendResultList = new ArrayList<>();

                    QAData kafkaPushMsg = new QAData();
                    kafkaPushMsg.AppType = 1;
                    kafkaPushMsg.QID = recItem.getKey();
                    kafkaPushMsg.DataType = 600;
                    kafkaPushMsg.CreatedTime = DateUtil.getNowDate();

                    for (SendPointRecord item : recItem.getValue()) {
                        PassportUserBindInfo user = passportUserBindInfoDao.getInfo(item.UserId);
                        PassportUserInfoModelNew passportUserInfo = passportUserInfoDao.getPassportUserInfoById(item.UserId);
                        logger.info(logpre + "User：{" + JacksonUtil.serialize(user));

                        *//**
     * 如果通行证用户  和 交易账户存在则进行奖励发放
     *//*
                        if (user != null && passportUserInfo != null && !NullUtil.isNull(passportUserInfo.NickName)) {

                            *//**
     * 发放用户奖励信息
     *//*

                            int point = item.Amount.intValue();
                            String reference = item.Type == 1 ? "最佳回答奖励" : "悬赏提问退回";
                            String taskId = item.Type == 1 ? appConstant.REWARDSTASKID : appConstant.REFUNDTASKID;
                            ServiceResult<SendPointResult> result =
                                    sendPointRecordDao.acceptActiveChangeUserPoint(user.CUSTOMERNO, taskId, reference,
                                            point, item.UserId, "Web", "");
                            logger.info(logpre + "Result：" + JacksonUtil.serialize(result));

                            SendResultKafka resultContent = new SendResultKafka();
                            {
                                resultContent.UID = item.UserId;
                                resultContent.Amount = point;
                            }
                            *//**
     *如果奖励发放成功，更新回答数据  更新奖励发放记录
     *//*
                            if (result != null &&
                                    Objects.equals(result.Success, true) &&
                                    result.Data != null &&
                                    Objects.equals(result.getData().SendSuccess, true)
                                ) {

                                //更新回答表的 奖励状态
                                String id = item.QID + "_" + item.AID;
                                *//**
     * 更新回答信息
     *//*
                                fundAnswerInfoDao.update(id, 1, item.getAmount().intValue());
                                logger.info(logpre + "发放成功：" + JacksonUtil.serialize(item));
                                if (item.Type == SendPiontType.Adopted.getValue()) {
                                    resultContent.PayResult = 1;
                                    resultContent.Message = "采纳奖励发放成功";
                                    resultContent.Time = DateUtil.getNowDate();
                                    sendResultList.add(resultContent);
                                    logger.info(logpre + "需要推送的kafka数据：" + JacksonUtil.serialize(resultContent));
                                }
                                *//**
     * 更新奖励发放记录
     *//*
                                Boolean isSuccess = sendPointRecordDao.update(item._id, true, result.Data.OrderID, user.CUSTOMERNO, JacksonUtil.serialize(result.Data));
                                Thread.sleep(3500);

                            } else if (result != null && result.ErrorCode != null && result.ErrorCode.equals("100")) {

                                //采纳用户未绑定交易账号，退回提问者，将当前记录删除，插入新记录
                                try {
                                    //推送未绑定交易账号导致发送失败的消息给回答用户
                                    SendPointMsgRequest request = new SendPointMsgRequest();
                                    request.EID = item._id;
                                    request.PassportID = item.UserId;
                                    request.PassportName = passportUserInfo.NickName;
                                    request.PointNum = String.format("%.2f", item.Amount);
                                    request.RemindTypeChild = RemindTypeEnum.PushFailure.getValue();
                                    request.PostID = String.valueOf(item.PostID);
                                    request.SourceType = SourceTypeEnum.AID.getValue();
                                    request.SourceID = item.AID;
                                    request.Qid = item.QID;
                                    request.Aid = item.AID;
                                   // PostInfoModel postinfo = postDao.getFromMong(String.valueOf(request.PostID), null, PostInfoModel.class);
                                    PostInfoNewModel postinfo = postDao.getFromMong(String.valueOf(request.PostID), null, PostInfoNewModel.class);

                                    if(postinfo!=null){
                                        postinfo.CONTENT=postinfo.CONTENTEND;
                                        postinfo.CONTENT= BeanUtil.getPostInfoContent(postinfo.CONTENT,postinfo.getKEYWORDLIST());
                                    }
                                    if (postinfo == null || NullUtil.isNull(postinfo.getCONTENT())) continue;



                                    request.Content = postinfo.CONTENT.substring(0, Math.min(100, postinfo.CONTENT.length()));
                                    logger.info(logpre + "财富币发放失败提醒用户 request：" + JacksonUtil.serialize(request));
                                    boolean pushSucess = false;

                                    //这里是判断是否推送过
                                    String cacheKey = "SendPointMsgRequest_" + request.EID;
                                    String isPush = appCore.redisuserread.get(cacheKey);
                                    if (NullUtil.isNull(isPush)) {
                                        if (item.getType() == 1) {
                                            pushSucess = pushToolsToKafkaDao.sendRewardPushMsg(request);
                                        } else {
                                            pushSucess = pushToolsToKafkaDao.sendRefundPushMsg(request);
                                        }
                                        //设置推送标识
                                        appCore.redisuserwrite.set(cacheKey, "1", cacheSeconds);
                                    }
                                    logger.info(logpre + "[QASendPointJob02]财富币发放失败提醒用户 response：" + JacksonUtil.serialize(pushSucess));

                                    String id = item.QID + "_" + item.AID;
                                    fundAnswerInfoDao.update(id, 0, item.getAmount().intValue());
                                    //更新回答表的 奖励状态
                                    logger.info(logpre + "未绑定交易账号 发放失败：" + JacksonUtil.serialize(item));

                                    logger.info(logpre + "财富币发放失败 记录Fail Record：" + JacksonUtil.serialize(item));
                                    if (item.Type == SendPiontType.Adopted.getValue()) {
                                        resultContent.PayResult = 2;
                                        resultContent.Message = "采纳用户未绑定交易账号，积分发放失败";
                                        resultContent.Time = DateUtil.getNowDate();
                                        sendResultList.add(resultContent);
                                        logger.info(logpre + "问答财富币发放=>需要推送的kafka数据：" + JacksonUtil.serialize(resultContent));
                                    }
                                    logger.info(logpre + "问答财富币发放，resultContent.PayResult:" + resultContent.PayResult + ";用户未绑定交易账号CustomerNo不为空{" + user.CUSTOMERNO + "},record {" + item._id + "}，删除该条记录，插入一条返还记录");

                                    sendPointRecordDao.remove(item._id);

                                    FundQuestionInfo rec = fundQuestionInfoDao.get(item.QID);

                                    SendPointRecord record = new SendPointRecord();
                                    {
                                        record._id = rec.QID + "_" + rec.UserId + "_" + item.AID;
                                        record.AID = "";
                                        record.Amount = item.Amount;
                                        record.CustomerNo = "";
                                        record.EndTime = rec.EndTime;
                                        record.IsSend = false;
                                        record.PayId = "";
                                        record.QID = rec.QID;
                                        record.ResultMessage = "";
                                        record.Type = SendPiontType.RefundForNoauth.getValue(); //未绑定交易账号退还
                                        record.UserId = rec.UserId;
                                        record.PostID = rec.ArticleId;
                                    }
                                    sendPointRecordDao.saveData(record);
                                } catch (Exception ex) {
                                    logger.error(logpre + "QASendPoint问答财富币退回提问者记录插入异常：" + ex.getMessage(), ex);
                                }
                            }
                        } else if ((user == null || NullUtil.isNull(user.CUSTOMERNO)) && passportUserInfo != null)//未绑定交易账号
                        {
                            //推送未绑定交易账号导致发送失败的消息给回答用户
                            SendPointMsgRequest request = new SendPointMsgRequest();
                            request.EID = item._id;
                            request.PassportID = item.UserId;
                            request.PassportName = passportUserInfo.NickName;
                            request.PointNum = String.format("%.2f", item.Amount);
                            request.RemindTypeChild = RemindTypeEnum.PushFailure.getValue();
                            request.PostID = String.valueOf(item.PostID);
                            request.SourceType = SourceTypeEnum.AID.getValue();
                            request.SourceID = item.AID;
                            request.Qid = item.QID;
                            request.Aid = item.AID;

//                            PostInfoModel postinfo = postDao.getFromMong(String.valueOf(request.PostID), null, PostInfoModel.class);
                            PostInfoNewModel postinfo = postDao.getFromMong(String.valueOf(request.PostID), null, PostInfoNewModel.class);
                            if (postinfo == null) {
                                postinfo = new PostInfoNewModel();
                            }
                            else {
                                postinfo.CONTENT=postinfo.getCONTENTEND();
                                postinfo.CONTENT=BeanUtil.getPostInfoContent(postinfo.CONTENT,postinfo.getKEYWORDLIST());
                            }
                            if (NullUtil.isNotNull(postinfo.CONTENT)) {
                                request.Content = postinfo.CONTENT.substring(0, Math.min(100, postinfo.CONTENT.length()));
                            }

                            logger.info(logpre + "未绑定交易账号退回财富币 request：{" + JacksonUtil.serialize(request) + "}");
                            boolean pushresult = false;

                            //这里是判断是否推送过
                            String cacheKey = "SendPointMsgRequest_" + request.EID;
                            String isPush = appCore.redisuserread.get(cacheKey);
                            if(NullUtil.isNull(isPush)){
                                if (item.getType() == 1) {
                                    pushresult = pushToolsToKafkaDao.sendRewardPushMsg(request);
                                } else {
                                    pushresult = pushToolsToKafkaDao.sendRefundPushMsg(request);
                                }
                                appCore.redisuserwrite.set(cacheKey,"1",cacheSeconds);
                            }


                            logger.info(logpre + "未绑定交易账号退回财富币 response：" + JacksonUtil.serialize(pushresult));
                            String id = item.QID + "_" + item.AID;
                            //更新回答表的 奖励状态
                            fundAnswerInfoDao.update(id, 0, item.getAmount().intValue());

                            logger.info(logpre + "未绑定交易账号 不发放：" + JacksonUtil.serialize(item));
                            SendResultKafka resultContent = new SendResultKafka();
                            {
                                resultContent.UID = item.UserId;
                                resultContent.Amount = item.Amount.intValue();
                            }
                            ;
                            logger.info(logpre + "未绑定交易账号退回财富币 推送kafka Record  out：" + JacksonUtil.serialize(item));
                            if (item.Type == SendPiontType.Adopted.getValue()) {
                                resultContent.PayResult = 2;
                                resultContent.Message = "采纳用户未绑定交易账号，退回作者";
                                resultContent.Time = DateUtil.getNowDate();
                                sendResultList.add(resultContent);
                                logger.info(logpre + "采纳用户未绑定交易账号 需要推送的kafka数据：" + JacksonUtil.serialize(resultContent));
                            }
                            logger.info(logpre + "问答财富币发放，用户未绑定交易账号CustomerNo为空,record {" + item._id + "}，删除该条记录，插入一条返还记录");
                            try {
                                sendPointRecordDao.remove(item._id);

                                FundQuestionInfo rec = fundQuestionInfoDao.get(item.QID);

                                SendPointRecord record = new SendPointRecord();
                                {
                                    record._id = rec.QID + "_" + rec.UserId + "_" + item.AID;
                                    record.AID = "";
                                    record.Amount = item.Amount;
                                    record.CustomerNo = "";
                                    record.EndTime = rec.EndTime;
                                    record.IsSend = false;
                                    record.PayId = "";
                                    record.QID = rec.QID;
                                    record.ResultMessage = "";
                                    record.Type = SendPiontType.RefundForNoauth.getValue(); //未绑定交易账号退还
                                    record.UserId = rec.UserId;
                                    record.PostID = rec.ArticleId;
                                }
                                sendPointRecordDao.saveData(record);
                            } catch (Exception ex) {
                                logger.error(logpre + "QASendPoint问答财富币退回提问者记录插入异常：" + ex.getMessage(), ex);
                            }
                        }


                    }
                    if (!CollectionUtils.isEmpty(sendResultList)) {
                        try {
                            kafkaPushMsg.Content = JacksonUtil.serialize(sendResultList);
                            logger.info(logpre + "发放财富币推送kafka数据：{" + JacksonUtil.serialize(kafkaPushMsg) + "}");
                            List<QAData> kafkaList = new ArrayList<>();
                            kafkaList.add(kafkaPushMsg);
                            kafkaTemplate.send(KafkaTopicName.NEWCORE_GUBA_QAPLATFORM_DATA_REWARD, kafkaPushMsg.getQID(), JacksonUtil.serialize(kafkaList));
                        } catch (Exception ex) {
                            logger.error(logpre + "推送kafka数据ERROR：{JsonConvert.SerializeObject(kafkaPushMsg)}===>>>" + ex.getMessage(), ex);
                        }
                    }
                }
                Date lastTime = list.stream().max(Comparator.comparing(SendPointRecord::getEndTime)).get().getEndTime();
                logger.info(logpre + " 本次数据最新更新时间为：" + DateUtil.dateToStr(lastTime));
            }
            logger.info(logpre + "结束执行，涉及数量:" + (list == null ? 0 : list.size()));
        } catch (Exception ex) {
            logger.error(logpre + ex.getMessage(), ex);
        }
        return ReturnT.SUCCESS;
    }*/

}
