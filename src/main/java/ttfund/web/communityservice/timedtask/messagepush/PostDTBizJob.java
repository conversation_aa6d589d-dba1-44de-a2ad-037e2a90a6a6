package ttfund.web.communityservice.timedtask.messagepush;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.messagepush.PinZhongDTEntity;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.dao.msyql.PostInfoNewDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.enums.EnumBusintype;
import ttfund.web.communityservice.enums.EnumPZDTType;
import ttfund.web.communityservice.utils.DateUtil;
import ttfund.web.communityservice.utils.JacksonUtil;

import javax.annotation.Resource;
import java.util.*;

/**
 * 品种页动态(帖子)
 * 备注：废弃。因为已跟王哥确定.net老服务没有使用
 */
@JobHandler("PostDTBizJob")
@Component
public class PostDTBizJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PostDTBizJob.class);

    @Resource
    UserRedisDao userRedisDao;

    @Resource
    PostInfoNewDao postInfoNewDao;


    @Autowired
    @Qualifier(KafkaConfig.fundbar_kafka_temp_beanname)
    private KafkaTemplate<String, String> kafkaTemplate;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {
            String initBreakpoint = null;
            Integer batchReadCount = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                initBreakpoint = jsonObject.getString("initBreakpoint");
                batchReadCount = jsonObject.getInteger("batchReadCount");
            }

            if (batchReadCount == null) {
                batchReadCount = 5000;
            }

            logger.info("第零步，打印参数。initBreakpoint：{}，batchReadCount：{}",
                    initBreakpoint,
                    batchReadCount);

            if (StringUtils.hasLength(initBreakpoint)) {

                userRedisDao.set(UserRedisConfig.POSTDTBIZJOB_BREAKPOINT, initBreakpoint, 30 * 24 * 3600L);
                logger.info("第零步，初始化断点。initBreakpoint：{}", initBreakpoint);

                return ReturnT.SUCCESS;
            }

            pushData(batchReadCount);

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return ReturnT.SUCCESS;

    }


    /**
     * 推送数据逻辑
     */
    private void pushData(int batchReadCount) {

        String breakpointName = UserRedisConfig.POSTDTBIZJOB_BREAKPOINT;
        String breakpoint = userRedisDao.get(breakpointName);

        if (breakpoint == null) {
            breakpoint = DateUtil.dateToStr(DateUtil.calendarDateByHour(-1));

            logger.error("第零步，读取断点为空，使用默认断点。断点:{}", breakpoint);
        }

        Date breakpointDate = DateUtil.strToDate(breakpoint);

        logger.info("第一步，读取断点。断点:{}", breakpoint);

        int round = 0;
        while (true) {
            round++;

            List<PostInfoNewModel> postList = postInfoNewDao.getNotQAListByUpdatetime(breakpointDate, batchReadCount);

            logger.info("第二步，读取数据-第{}轮。数量:{}，头部数据：{}",
                    round,
                    postList == null ? 0 : postList.size(),
                    CollectionUtils.isEmpty(postList) ? null : JSON.toJSONStringWithDateFormat(postList.get(0), DateUtil.datePattern)
            );

            if (!CollectionUtils.isEmpty(postList)) {

                breakpointDate = postList.stream().max(Comparator.comparing(o -> o.UPDATETIME)).get().UPDATETIME;

                List<PinZhongDTEntity> list = new ArrayList<>();
                for (PostInfoNewModel item : postList) {
                    PinZhongDTEntity model = new PinZhongDTEntity();
                    model.EID = item.ID.toString();
                    model.CATEGORY = String.valueOf(EnumPZDTType.HU_DONG.getValue());
                    model.BUSINTYPE = String.valueOf(EnumBusintype.TIE_ZI.getValue());
                    model.PASSPORTID = item.UID;
                    model.CODE = item.CODE;
                    model.PARENTBUSINTYPE = "";
                    model.SUMMARY = item.SUMMARY;
                    model.TIME = DateUtil.dateToStr(item.TIME, DateUtil.dateTimeDefaultPattern);
                    model.STATUS = getDel(item);
                    list.add(model);
                }

                int i = 0;
                for (PinZhongDTEntity item : list) {
                    i++;

                    try {

                        kafkaTemplate.send(KafkaTopicName.EM_FUND_USER_DYNAMIC, JacksonUtil.obj2String(Collections.singletonList(item)));


                        logger.info("第{}轮-第{}/{}个-推送详情。数据：{}",
                                round,
                                i,
                                list.size(),
                                item.EID
                        );

                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                    }
                }

                logger.info("第三步，推送完成-第{}轮。数量:{}，头部数据：{}",
                        round,
                        list == null ? 0 : list.size(),
                        CollectionUtils.isEmpty(list) ? null : JSON.toJSONStringWithDateFormat(list.get(0), DateUtil.datePattern)
                );

            }

            breakpoint = DateUtil.dateToStr(breakpointDate);
            userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

            logger.info("第四步，更新断点-第{}轮。断点：{}", round, breakpoint);

            if (postList == null || postList.size() < batchReadCount) {
                break;
            }
        }

        breakpoint = DateUtil.dateToStr(breakpointDate);
        userRedisDao.set(breakpointName, breakpoint, 60 * 24 * 3600L);

        logger.info("第四步，更新断点-第{}轮。断点：{}", round, breakpoint);

    }

    /**
     * 获取删除状态 0 未删除，1 删除
     */
    private int getDel(PostInfoNewModel pNew) {
        if (pNew.DEL == 0 && pNew.TTJJDEL == 0 && pNew.ISENABLED == 1) {
            return 0;
        } else if (pNew.DEL == 2 && pNew.TTJJDEL == 0 && pNew.ISENABLED == 1) {
            return 2;
        }
        return 1;
    }
}
