package ttfund.web.communityservice.timedtask.inner;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.FundCommunityServiceApplication;

import java.lang.management.ManagementFactory;

/**
 * 手动触发GC
 */
@JobHandler(value = "GCJob")
@Component
public class GCJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(GCJob.class);

    @Override
    public ReturnT<String> execute(String param) {
        Runtime.getRuntime().gc();
        logger.info("Heap Memrogy Info:" + ManagementFactory.getMemoryMXBean().getHeapMemoryUsage());
        logger.info("NonHeap Memrogy Info:" + ManagementFactory.getMemoryMXBean().getNonHeapMemoryUsage());
        logger.info("CPU Num:" + Runtime.getRuntime().availableProcessors());
        logger.info("Max Memory:" + Runtime.getRuntime().maxMemory());
        logger.info("Free Memory:" + Runtime.getRuntime().freeMemory());
        logger.info("Total Memory:" + Runtime.getRuntime().totalMemory());
        return ReturnT.SUCCESS;
    }
}

