package ttfund.web.communityservice.timedtask.sync;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.communityservice.service.sync.TopicSyncService;

/**
 * @Author: ShaMo
 * @Date: 2023/4/6
 * @ApiNote:
 * @Status:
 */
@Component
@JobHandler(value = "topicSyncJob")
public class TopicSyncJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(TopicSyncJob.class);

    @Autowired
    private TopicSyncService topicSyncService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            topicSyncService.syncTopicAndTopicDetail();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
