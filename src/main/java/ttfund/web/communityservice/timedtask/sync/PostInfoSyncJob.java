package ttfund.web.communityservice.timedtask.sync;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.service.sync.PostInfoSyncService;

/**
 * @Author: ShaMo
 * @Date: 2023/4/6
 * @ApiNote:
 * @Status:
 */
@Component
@JobHandler(value = "postInfoSyncJob")
public class PostInfoSyncJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(PostInfoSyncJob.class);
    @Autowired
    private PostInfoSyncService postInfoSyncService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try {

            Integer limit = null;
            if (StringUtils.hasLength(s)) {
                JSONObject jsonObject = JSON.parseObject(s);
                limit = jsonObject.getInteger("limit");
            }

            if (limit == null) {
                limit = 5000;
            }

            logger.info("第零步，打印参数。limit：{}", limit);

            deal(limit);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }

    private void deal(int limit) {
        postInfoSyncService.syncPostInfo(limit);
    }
}
