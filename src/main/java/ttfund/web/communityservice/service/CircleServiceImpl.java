package ttfund.web.communityservice.service;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.data.CircleBaseInfoAndExtendInfo;
import ttfund.web.communityservice.bean.jijinBar.data.CircleBaseInfoCondition;
import ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelation;
import ttfund.web.communityservice.bean.jijinBar.data.CircleUserRelationRecord;
import ttfund.web.communityservice.bean.jijinBar.user.PassportUserBindInfo;
import ttfund.web.communityservice.dao.mongo.PassportUserBindInfoDao;
import ttfund.web.communityservice.dao.msyql.CircleBaseInfoDao;
import ttfund.web.communityservice.dao.msyql.CircleUserRelationDao;
import ttfund.web.communityservice.dao.msyql.CircleUserRelationRecordDao;
import ttfund.web.communityservice.enums.JoinCircleErrorEnum;
import ttfund.web.communityservice.enums.LeaveCircleErrorEnum;
import ttfund.web.communityservice.service.common.UserProfileWebApiService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 圈子service
 */
@Slf4j
@Service
public class CircleServiceImpl {

    @Autowired
    private UserProfileWebApiService userProfileWebApiService;

    @Autowired
    private CircleBaseInfoDao circleBaseInfoDao;

    @Autowired
    private CircleUserRelationRecordDao circleUserRelationRecordDao;

    @Autowired
    private CircleUserRelationDao circleUserRelationDao;

    @Autowired
    private PassportUserBindInfoDao passportUserBindInfoDao;

    /**
     * 加入圈子
     */
    public String join(CircleUserRelationRecord relationRecord) {

        Map<String, Object> dealInfo = new HashMap<>();
        String errorMessage = null;
        boolean isMatch = false;

        CircleBaseInfoAndExtendInfo circleBaseAndExtendInfo = circleBaseInfoDao.getByCircleId(relationRecord.getCircleId());
        if (circleBaseAndExtendInfo == null || (circleBaseAndExtendInfo.getIsDel() != null && circleBaseAndExtendInfo.getIsDel() == 1)) {
            errorMessage = JoinCircleErrorEnum.CIRCLE_IS_DEL.getMessage();
        }

        if (errorMessage == null) {
            CircleUserRelation circleUserRelation = circleUserRelationDao.get(relationRecord.getCircleId(), relationRecord.getUid());
            if (circleUserRelation != null && circleUserRelation.getIsDel() == 0) {
                if (circleUserRelation.getState() == 0) {
                    errorMessage = JoinCircleErrorEnum.ALREADY_JOIN.getMessage();
                } else if (circleUserRelation.getState() == 1) {
                    errorMessage = JoinCircleErrorEnum.ALREADY_LEAVE.getMessage();
                }
            }
        }

        if (errorMessage == null) {
            if (circleBaseAndExtendInfo.getMaxMembers() != null
                && circleBaseAndExtendInfo.getCurMembers() != null
                && circleBaseAndExtendInfo.getMaxMembers() <= circleBaseAndExtendInfo.getCurMembers()) {
                errorMessage = JoinCircleErrorEnum.ALREADY_FULL.getMessage();
            }
        }

        List<CircleBaseInfoCondition> notMatchConditions = new ArrayList<>();
        if (errorMessage == null) {
            isMatch = true;
            List<CircleBaseInfoCondition> conditionList = null;
            if (StringUtils.hasLength(circleBaseAndExtendInfo.getConditions())) {
                conditionList = JSON.parseArray(circleBaseAndExtendInfo.getConditions(), CircleBaseInfoCondition.class);
            }

            if (!CollectionUtils.isEmpty(conditionList)) {
                for (CircleBaseInfoCondition condition : conditionList) {
                    isMatch = false;
                    if (condition.getType() == null || condition.getType() == 0) {
                        isMatch = userProfileWebApiService.isUserInUserGroup(condition.getGroupId(), relationRecord.getUid());
                    } else {
                        List<PassportUserBindInfo> binds = passportUserBindInfoDao.getByUids(Arrays.asList(relationRecord.getUid()));
                        if (!CollectionUtils.isEmpty(binds)) {
                            isMatch = userProfileWebApiService.isUserInUserGroup(condition.getGroupId(), binds.get(0).CUSTOMERNO);
                        }
                    }

                    if (!isMatch) {
                        notMatchConditions.add(condition);
                        break;
                    }
                }
            }
        }

        if (isMatch) {
            int joinType = 0;
            Integer curMembersAfter = null;
            try {
                curMembersAfter = circleUserRelationDao.joinCircle(relationRecord.getCircleId(), relationRecord.getUid(), joinType, circleBaseAndExtendInfo.getMaxMembers());
            } catch (Exception ex) {
                errorMessage = ex.getMessage();
                log.error(ex.getMessage(), ex);
            }

            if (StringUtils.hasLength(errorMessage) && errorMessage.startsWith(JoinCircleErrorEnum.REPEAT_OPT.getMessage())) {
                errorMessage = JoinCircleErrorEnum.REPEAT_OPT.getMessage();
            } else if (curMembersAfter == null) {
                errorMessage = JoinCircleErrorEnum.ALREADY_FULL.getMessage();
            }
        }

        if (errorMessage != null) {
            dealInfo.put("errorMessage", errorMessage);
        }
        if (!CollectionUtils.isEmpty(notMatchConditions)) {
            dealInfo.put("notMatchConditions", notMatchConditions);
        }

        String dealInfoString = JSON.toJSONString(dealInfo);

        relationRecord.setState(1);
        relationRecord.setDealTime(new Date());
        relationRecord.setUpdateTime(new Date());
        if (!CollectionUtils.isEmpty(notMatchConditions)) {
            relationRecord.setDealResult(0);
        } else if (errorMessage != null) {
            relationRecord.setDealResult(0);
        } else {
            relationRecord.setDealResult(1);
        }

        if (!CollectionUtils.isEmpty(dealInfo)) {
            relationRecord.setDealInfo(dealInfoString);
        }

        circleUserRelationRecordDao.updateOneWhenDeal(relationRecord);

        return dealInfoString;
    }

    /**
     * 离开圈子
     */
    public String leave(CircleUserRelationRecord relationRecord) {
        Map<String, Object> dealInfo = new HashMap<>();
        String errorMessage = null;

        CircleBaseInfoAndExtendInfo circleBaseAndExtendInfo = circleBaseInfoDao.getByCircleId(relationRecord.getCircleId());
        if (circleBaseAndExtendInfo == null || (circleBaseAndExtendInfo.getIsDel() != null && circleBaseAndExtendInfo.getIsDel() == 1)) {
            errorMessage = LeaveCircleErrorEnum.CIRCLE_IS_DEL.getMessage();
        }

        if (errorMessage == null) {
            CircleUserRelation circleUserRelation = circleUserRelationDao.get(relationRecord.getCircleId(), relationRecord.getUid());
            if (circleUserRelation == null || circleUserRelation.getIsDel() == 1) {
                errorMessage = LeaveCircleErrorEnum.JOIN_FIRST.getMessage();
            } else if (circleUserRelation.getState() == 1) {
                errorMessage = LeaveCircleErrorEnum.ALREADY_LEAVE.getMessage();
            }
        }

        if (errorMessage == null) {
            boolean leaveResult = false;
            try {
                leaveResult = circleUserRelationDao.leaveCircle(relationRecord.getCircleId(), relationRecord.getUid(), 1);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
            if (!leaveResult) {
                errorMessage = LeaveCircleErrorEnum.ALREADY_LEAVE.getMessage();
            }
        }

        dealInfo.put("errorMessage", errorMessage);

        String dealInfoString = JSON.toJSONString(dealInfo);

        relationRecord.setState(1);
        relationRecord.setDealTime(new Date());
        relationRecord.setUpdateTime(new Date());
        if (errorMessage != null) {
            relationRecord.setDealResult(0);
        } else {
            relationRecord.setDealResult(1);
        }

        if (errorMessage != null) {
            relationRecord.setDealInfo(dealInfoString);
        }

        circleUserRelationRecordDao.updateOneWhenDeal(relationRecord);

        return dealInfoString;
    }

    /**
     * 强制加入圈子
     */
    public String forceJoin(boolean recordWhenFail, String circleId, String uid, String proposer) {

        Map<String, Object> dealInfo = new HashMap<>();
        String errorMessage = null;

        CircleBaseInfoAndExtendInfo circleBaseAndExtendInfo = circleBaseInfoDao.getByCircleId(circleId);
        if (circleBaseAndExtendInfo == null || (circleBaseAndExtendInfo.getIsDel() != null && circleBaseAndExtendInfo.getIsDel() == 1)) {
            errorMessage = JoinCircleErrorEnum.CIRCLE_IS_DEL.getMessage();
        }

        if (errorMessage == null) {
            CircleUserRelation circleUserRelation = circleUserRelationDao.get(circleId, uid);
            if (circleUserRelation != null && circleUserRelation.getIsDel() == 0) {
                if (circleUserRelation.getState() == 0) {
                    errorMessage = JoinCircleErrorEnum.ALREADY_JOIN.getMessage();
                } else if (circleUserRelation.getState() == 1) {
                    errorMessage = JoinCircleErrorEnum.ALREADY_LEAVE.getMessage();
                }
            }
        }

        if (errorMessage == null) {
            if (circleBaseAndExtendInfo.getMaxMembers() != null
                && circleBaseAndExtendInfo.getCurMembers() != null
                && circleBaseAndExtendInfo.getMaxMembers() <= circleBaseAndExtendInfo.getCurMembers()) {
                errorMessage = JoinCircleErrorEnum.ALREADY_FULL.getMessage();
            }
        }

        if (errorMessage == null) {
            int joinType = 1;
            Integer curMembersAfter = null;
            try {
                curMembersAfter = circleUserRelationDao.joinCircle(circleId, uid, joinType, circleBaseAndExtendInfo.getMaxMembers());
            } catch (Exception ex) {
                errorMessage = ex.getMessage();
                log.error(ex.getMessage(), ex);
            }

            if (StringUtils.hasLength(errorMessage) && errorMessage.startsWith(JoinCircleErrorEnum.REPEAT_OPT.getMessage())) {
                errorMessage = JoinCircleErrorEnum.REPEAT_OPT.getMessage();
            } else if (curMembersAfter == null) {
                errorMessage = JoinCircleErrorEnum.ALREADY_FULL.getMessage();
            }
        }

        if (errorMessage != null) {
            dealInfo.put("errorMessage", errorMessage);
        }

        String dealInfoString = JSON.toJSONString(dealInfo);

        if (!recordWhenFail && errorMessage != null) {
            return dealInfoString;
        }

        CircleUserRelationRecord relationRecord = new CircleUserRelationRecord();
        relationRecord.setCircleId(circleId);
        relationRecord.setUid(uid);
        relationRecord.setState(1);
        relationRecord.setType(1);
        relationRecord.setProposer(proposer);
        relationRecord.setProposeTime(new Date());
        relationRecord.setDealTime(new Date());
        relationRecord.setIsDel(0);
        relationRecord.setCreateTime(new Date());
        relationRecord.setUpdateTime(new Date());
        if (errorMessage != null) {
            relationRecord.setDealResult(0);
        } else {
            relationRecord.setDealResult(1);
        }

        if (!CollectionUtils.isEmpty(dealInfo)) {
            relationRecord.setDealInfo(dealInfoString);
        }

        circleUserRelationRecordDao.insertOne(relationRecord);

        return dealInfoString;
    }

}
