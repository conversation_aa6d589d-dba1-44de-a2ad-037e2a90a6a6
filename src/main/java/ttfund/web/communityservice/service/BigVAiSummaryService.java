package ttfund.web.communityservice.service;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.base.helper.HttpHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.ai.BigVAiTaskResponse;
import ttfund.web.communityservice.utils.HttpClientUtil;

import java.net.HttpURLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.List;

/**
 * 大V AI总结服务
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@Service
public class BigVAiSummaryService {

    private static final Logger logger = LoggerFactory.getLogger(BigVAiSummaryService.class);

    @Value("${polaris.api.base.url:http://localhost:8081/polaris}")
    private String polarisApiBaseUrl;

    /**
     * 提交单篇总结任务
     */
    public BigVAiTaskResponse submitSingleSummaryTask(String message) {
        try {
            String url = polarisApiBaseUrl + "/bigv/sing/async";

            // 构建表单参数字符串
            String formParams = "message=" + URLEncoder.encode(message, StandardCharsets.UTF_8.name());

            logger.info("提交AI总结任务 - URL: {}", url);
            logger.info("提交AI总结任务 - 表单参数: {}", formParams);

            // 使用HttpClientUtil发送POST请求
            String response = HttpClientUtil.post(url, formParams);

            logger.info("AI总结任务响应: {}", response);

            if (StringUtils.hasLength(response)) {
                // 解析AsyncTaskResponse格式的响应
                return parseAsyncTaskResponse(response);
            }

            logger.error("提交AI总结任务失败，响应为空。URL: {}, FormParams: {}", url, formParams);
            return null;

        } catch (Exception e) {
            logger.error("提交AI总结任务异常，URL: {}", polarisApiBaseUrl + "/bigv/sing/async", e);
            return null;
        }
    }

    /**
     * 解析AsyncTaskResponse格式的响应，转换为BigVAiTaskResponse
     */
    private BigVAiTaskResponse parseAsyncTaskResponse(String response) {
        try {
            // 首先解析为通用的JSON对象
            com.alibaba.fastjson.JSONObject jsonResponse = JSON.parseObject(response);

            logger.info("解析响应JSON: {}", jsonResponse);

            BigVAiTaskResponse result = new BigVAiTaskResponse();

            // 从AsyncTaskResponse格式中提取字段
            result.setTaskId(jsonResponse.getString("taskId"));
            result.setStatus(jsonResponse.getString("status"));
            result.setMessage(jsonResponse.getString("message"));
            result.setPollUrl(jsonResponse.getString("pollUrl"));
            result.setEstimatedTime(jsonResponse.getString("estimatedTime"));
            result.setModel(jsonResponse.getString("model"));
            result.setInstitution(jsonResponse.getString("institution"));
            result.setTaskMonth(jsonResponse.getString("taskMonth"));
            result.setCreatedAt(jsonResponse.getString("createdAt"));

            // 处理data字段 - 现在是JSON格式
            if (jsonResponse.containsKey("data")) {
                Object dataObj = jsonResponse.get("data");
                if (dataObj != null) {
                    // 将data对象转换为JSON字符串保存
                    result.setData(JSON.toJSONString(dataObj));
                }
            }

            // 如果有processingTimeMs字段，也设置进去
            if (jsonResponse.containsKey("processingTimeMs")) {
                result.setProcessingTimeMs(jsonResponse.getLong("processingTimeMs"));
            }

            logger.info("转换后的BigVAiTaskResponse: taskId={}, status={}, message={}",
                    result.getTaskId(), result.getStatus(), result.getMessage());

            return result;

        } catch (Exception e) {
            logger.error("解析AsyncTaskResponse失败，原始响应: {}", response, e);
            return null;
        }
    }

    /**
     * 轮询任务结果
     */
    public BigVAiTaskResponse pollTaskResult(String taskId) {
        try {
            String url = polarisApiBaseUrl + "/bigv/sing/async";

            // 构建表单参数字符串，传递taskId参数
            String formParams = "taskId=" + URLEncoder.encode(taskId, StandardCharsets.UTF_8.name());

            logger.info("轮询任务结果 - URL: {}, TaskId: {}", url, taskId);
            logger.info("轮询任务结果 - 表单参数: {}", formParams);

            // 使用HttpClientUtil发送POST请求
            String response = HttpClientUtil.post(url, formParams);

            logger.info("轮询任务结果原始响应: {}", response);

            if (StringUtils.hasLength(response)) {
                // 同样使用parseAsyncTaskResponse方法解析
                return parseAsyncTaskResponse(response);
            }

            logger.warn("轮询任务结果失败，响应为空。TaskId: {}", taskId);
            return null;

        } catch (Exception e) {
            logger.error("轮询任务结果异常，TaskId: {}", taskId, e);
            return null;
        }
    }

    /**
     * 提交多机构总结任务
     */
    public BigVAiTaskResponse submitMultiSummaryTask(String message) {
        try {
            String url = polarisApiBaseUrl + "/bigv/merge/async";

            // 构建表单参数字符串
            String formParams = "message=" + URLEncoder.encode(message, StandardCharsets.UTF_8.name());

            logger.info("提交AI多机构总结任务 - URL: {}", url);
            logger.info("提交AI多机构总结任务 - 表单参数: {}", formParams);

            // 使用HttpClientUtil发送POST请求
            String response = HttpClientUtil.post(url, formParams);

            logger.info("AI多机构总结任务响应: {}", response);

            if (StringUtils.hasLength(response)) {
                // 解析AsyncTaskResponse格式的响应
                return parseAsyncTaskResponse(response);
            }

            logger.error("提交AI多机构总结任务失败，响应为空。URL: {}, FormParams: {}", url, formParams);
            return null;

        } catch (Exception e) {
            logger.error("提交AI多机构总结任务异常，URL: {}", polarisApiBaseUrl + "/bigv/merge/async", e);
            return null;
        }
    }

    /**
     * 轮询多机构任务结果
     */
    public BigVAiTaskResponse pollMultiTaskResult(String taskId) {
        try {
            String url = polarisApiBaseUrl + "/bigv/merge/async";

            // 构建表单参数字符串，传递taskId参数
            String formParams = "taskId=" + URLEncoder.encode(taskId, StandardCharsets.UTF_8.name());

            logger.info("轮询多机构任务结果 - URL: {}, TaskId: {}", url, taskId);
            logger.info("轮询多机构任务结果 - 表单参数: {}", formParams);

            // 使用HttpClientUtil发送POST请求
            String response = HttpClientUtil.post(url, formParams);

            logger.info("轮询多机构任务结果原始响应: {}", response);

            if (StringUtils.hasLength(response)) {
                // 同样使用parseAsyncTaskResponse方法解析
                return parseAsyncTaskResponse(response);
            }

            logger.warn("轮询多机构任务结果失败，响应为空。TaskId: {}", taskId);
            return null;

        } catch (Exception e) {
            logger.error("轮询多机构任务结果异常，TaskId: {}", taskId, e);
            return null;
        }
    }

    /**
     * 格式化消息内容
     */
    public String formatMessage(String nickName, String title, String content) {
        StringBuilder sb = new StringBuilder();
        sb.append("大V：").append(nickName).append("\n");
        if (StringUtils.hasLength(title)) {
            sb.append(title).append("\n");
        }
        if (StringUtils.hasLength(content)) {
            sb.append(content);
        }
        return sb.toString();
    }

    /**
     * 格式化多机构消息内容
     */
    public String formatMultiMessage(List<PostData> postDataList) {
        StringBuilder sb = new StringBuilder();
        for (PostData postData : postDataList) {
            sb.append("大V：").append(postData.getNickName()).append("\n");
            if (StringUtils.hasLength(postData.getTitle())) {
                sb.append(postData.getTitle()).append("\n");
            }
            if (StringUtils.hasLength(postData.getContent())) {
                sb.append(postData.getContent()).append("\n");
            }
        }
        return sb.toString();
    }

    /**
     * 帖子数据内部类
     */
    public static class PostData {
        private String nickName;
        private String title;
        private String content;
        private String postId;
        private String uid;

        public PostData(String nickName, String title, String content, String postId, String uid) {
            this.nickName = nickName;
            this.title = title;
            this.content = content;
            this.postId = postId;
            this.uid = uid;
        }

        // Getters
        public String getNickName() { return nickName; }
        public String getTitle() { return title; }
        public String getContent() { return content; }
        public String getPostId() { return postId; }
        public String getUid() { return uid; }
    }

    /**
     * 拆分AI总结为精简版和详细版（仅支持JSON格式）
     */
    public SummaryParts splitSummary(String fullSummary) {
        if (!StringUtils.hasLength(fullSummary)) {
            return new SummaryParts("", "");
        }

        try {
            return splitJsonSummary(fullSummary);
        } catch (Exception e) {
            logger.error("拆分AI总结失败", e);
            // 如果拆分失败，将完整内容作为详细版
            return new SummaryParts("", fullSummary);
        }
    }

    /**
     * 拆分JSON格式的总结（新格式）
     */
    private SummaryParts splitJsonSummary(String jsonSummary) {
        try {
            com.alibaba.fastjson.JSONObject dataObj = JSON.parseObject(jsonSummary);

            String briefContent = "";
            String detailedContent = "";

            // 提取精简版内容 - 格式化为文本
            if (dataObj.containsKey("simplifiedVersion")) {
                briefContent = formatSimplifiedVersion(dataObj.getJSONObject("simplifiedVersion"));
            }

            // 提取详细版内容 - 保存为JSON字符串
            if (dataObj.containsKey("detailedVersion")) {
                detailedContent = dataObj.getJSONObject("detailedVersion").toJSONString();
            }

            logger.info("JSON格式拆分完成 - 精简版长度: {}, 详细版长度: {}",
                    briefContent.length(), detailedContent.length());

            return new SummaryParts(briefContent, detailedContent);

        } catch (Exception e) {
            logger.error("解析JSON格式总结失败", e);
            return new SummaryParts("", jsonSummary);
        }
    }





    /**
     * 总结拆分结果类
     */
    public static class SummaryParts {
        private final String briefSummary;
        private final String detailedSummary;

        public SummaryParts(String briefSummary, String detailedSummary) {
            this.briefSummary = briefSummary;
            this.detailedSummary = detailedSummary;
        }

        public String getBriefSummary() {
            return briefSummary;
        }

        public String getDetailedSummary() {
            return detailedSummary;
        }
    }

    /**
     * 格式化精简版内容
     */
    private String formatSimplifiedVersion(com.alibaba.fastjson.JSONObject simplifiedVersion) {
        StringBuilder sb = new StringBuilder();

        try {
            // 处理行情分析
            if (simplifiedVersion.containsKey("marketAnalysis")) {
                com.alibaba.fastjson.JSONObject marketAnalysis = simplifiedVersion.getJSONObject("marketAnalysis");
                sb.append("**").append(marketAnalysis.getString("title")).append("**\n");

                if (marketAnalysis.containsKey("overallJudgment")) {
                    com.alibaba.fastjson.JSONObject overallJudgment = marketAnalysis.getJSONObject("overallJudgment");
                    sb.append(overallJudgment.getString("title")).append("：")
                      .append(overallJudgment.getString("content")).append("\n\n");
                }
            }

            // 处理今日操作
            if (simplifiedVersion.containsKey("todayOperations")) {
                com.alibaba.fastjson.JSONObject todayOperations = simplifiedVersion.getJSONObject("todayOperations");
                sb.append("**").append(todayOperations.getString("title")).append("**\n");

                if (todayOperations.containsKey("operations")) {
                    com.alibaba.fastjson.JSONArray operations = todayOperations.getJSONArray("operations");
                    for (int i = 0; i < operations.size(); i++) {
                        com.alibaba.fastjson.JSONObject operation = operations.getJSONObject(i);
                        sb.append(operation.getString("type")).append("：")
                          .append(operation.getString("strategy")).append("\n");
                    }
                }
            }

        } catch (Exception e) {
            logger.error("格式化精简版内容失败", e);
            return simplifiedVersion.toJSONString();
        }

        return sb.toString().trim();
    }

    /**
     * 格式化详细版内容
     */
    private String formatDetailedVersion(com.alibaba.fastjson.JSONObject detailedVersion) {
        StringBuilder sb = new StringBuilder();

        try {
            // 处理行情分析
            if (detailedVersion.containsKey("marketAnalysis")) {
                com.alibaba.fastjson.JSONObject marketAnalysis = detailedVersion.getJSONObject("marketAnalysis");
                sb.append("**").append(marketAnalysis.getString("title")).append("**\n");

                if (marketAnalysis.containsKey("details")) {
                    com.alibaba.fastjson.JSONArray details = marketAnalysis.getJSONArray("details");
                    for (int i = 0; i < details.size(); i++) {
                        com.alibaba.fastjson.JSONObject detail = details.getJSONObject(i);
                        sb.append((i + 1)).append(". ")
                          .append(detail.getString("category")).append("：")
                          .append(detail.getString("content")).append("\n");
                    }
                    sb.append("\n");
                }
            }

            // 处理实盘操作
            if (detailedVersion.containsKey("practicalOperations")) {
                com.alibaba.fastjson.JSONObject practicalOperations = detailedVersion.getJSONObject("practicalOperations");
                sb.append("**").append(practicalOperations.getString("title")).append("**\n");

                if (practicalOperations.containsKey("coreLogic")) {
                    sb.append("核心逻辑：").append(practicalOperations.getString("coreLogic")).append("\n\n");
                }

                // 处理大V操作
                if (practicalOperations.containsKey("bigVOperations")) {
                    com.alibaba.fastjson.JSONArray bigVOperations = practicalOperations.getJSONArray("bigVOperations");
                    for (int i = 0; i < bigVOperations.size(); i++) {
                        com.alibaba.fastjson.JSONObject bigVOp = bigVOperations.getJSONObject(i);
                        sb.append(bigVOp.getString("bigVName")).append("\n");

                        if (bigVOp.containsKey("operations")) {
                            com.alibaba.fastjson.JSONArray operations = bigVOp.getJSONArray("operations");
                            for (int j = 0; j < operations.size(); j++) {
                                com.alibaba.fastjson.JSONObject operation = operations.getJSONObject(j);
                                sb.append((j + 1)).append(". ").append(operation.getString("type")).append("：\n");

                                if (StringUtils.hasLength(operation.getString("product"))) {
                                    sb.append("   产品：").append(operation.getString("product"));
                                    if (StringUtils.hasLength(operation.getString("productCode"))) {
                                        sb.append("（").append(operation.getString("productCode")).append("）");
                                    }
                                    sb.append("\n");
                                }

                                if (StringUtils.hasLength(operation.getString("reason"))) {
                                    sb.append("   理由：").append(operation.getString("reason")).append("\n");
                                }
                            }
                        }
                        sb.append("\n");
                    }
                }
            }

        } catch (Exception e) {
            logger.error("格式化详细版内容失败", e);
            return detailedVersion.toJSONString();
        }

        return sb.toString().trim();
    }


}
