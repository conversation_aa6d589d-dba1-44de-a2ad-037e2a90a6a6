package ttfund.web.communityservice.service;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.base.helper.HttpHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.bean.jijinBar.post.ai.BigVAiTaskResponse;
import ttfund.web.communityservice.utils.HttpClientUtil;

import java.net.HttpURLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.List;

/**
 * 大V AI总结服务
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@Service
public class BigVAiSummaryService {

    private static final Logger logger = LoggerFactory.getLogger(BigVAiSummaryService.class);

    @Value("${polaris.api.base.url:http://localhost:8081/polaris}")
    private String polarisApiBaseUrl;

    /**
     * 提交单篇总结任务
     */
    public BigVAiTaskResponse submitSingleSummaryTask(String message) {
        try {
            String url = polarisApiBaseUrl + "/bigv/sing/async";

            // 构建表单参数字符串
            String formParams = "message=" + URLEncoder.encode(message, StandardCharsets.UTF_8.name());

            logger.info("提交AI总结任务 - URL: {}", url);
            logger.info("提交AI总结任务 - 表单参数: {}", formParams);

            // 使用HttpClientUtil发送POST请求
            String response = HttpClientUtil.post(url, formParams);

            logger.info("AI总结任务响应: {}", response);

            if (StringUtils.hasLength(response)) {
                // 解析AsyncTaskResponse格式的响应
                return parseAsyncTaskResponse(response);
            }

            logger.error("提交AI总结任务失败，响应为空。URL: {}, FormParams: {}", url, formParams);
            return null;

        } catch (Exception e) {
            logger.error("提交AI总结任务异常，URL: {}", polarisApiBaseUrl + "/bigv/sing/async", e);
            return null;
        }
    }

    /**
     * 解析AsyncTaskResponse格式的响应，转换为BigVAiTaskResponse
     */
    private BigVAiTaskResponse parseAsyncTaskResponse(String response) {
        try {
            // 首先解析为通用的JSON对象
            com.alibaba.fastjson.JSONObject jsonResponse = JSON.parseObject(response);

            logger.info("解析响应JSON: {}", jsonResponse);

            BigVAiTaskResponse result = new BigVAiTaskResponse();

            // 从AsyncTaskResponse格式中提取字段
            result.setTaskId(jsonResponse.getString("taskId"));
            result.setStatus(jsonResponse.getString("status"));
            result.setMessage(jsonResponse.getString("message"));
            result.setPollUrl(jsonResponse.getString("pollUrl"));
            result.setEstimatedTime(jsonResponse.getString("estimatedTime"));
            result.setModel(jsonResponse.getString("model"));
            result.setInstitution(jsonResponse.getString("institution"));
            result.setTaskMonth(jsonResponse.getString("taskMonth"));
            result.setCreatedAt(jsonResponse.getString("createdAt"));

            // 如果有data字段，也设置进去
            if (jsonResponse.containsKey("data")) {
                result.setData(jsonResponse.getString("data"));
            }

            // 如果有processingTimeMs字段，也设置进去
            if (jsonResponse.containsKey("processingTimeMs")) {
                result.setProcessingTimeMs(jsonResponse.getLong("processingTimeMs"));
            }

            logger.info("转换后的BigVAiTaskResponse: taskId={}, status={}, message={}",
                    result.getTaskId(), result.getStatus(), result.getMessage());

            return result;

        } catch (Exception e) {
            logger.error("解析AsyncTaskResponse失败，原始响应: {}", response, e);
            return null;
        }
    }

    /**
     * 轮询任务结果
     */
    public BigVAiTaskResponse pollTaskResult(String taskId) {
        try {
            String url = polarisApiBaseUrl + "/bigv/sing/async";

            // 构建表单参数字符串，传递taskId参数
            String formParams = "taskId=" + URLEncoder.encode(taskId, StandardCharsets.UTF_8.name());

            logger.info("轮询任务结果 - URL: {}, TaskId: {}", url, taskId);
            logger.info("轮询任务结果 - 表单参数: {}", formParams);

            // 使用HttpClientUtil发送POST请求
            String response = HttpClientUtil.post(url, formParams);

            logger.info("轮询任务结果原始响应: {}", response);

            if (StringUtils.hasLength(response)) {
                // 同样使用parseAsyncTaskResponse方法解析
                return parseAsyncTaskResponse(response);
            }

            logger.warn("轮询任务结果失败，响应为空。TaskId: {}", taskId);
            return null;

        } catch (Exception e) {
            logger.error("轮询任务结果异常，TaskId: {}", taskId, e);
            return null;
        }
    }

    /**
     * 提交多机构总结任务
     */
    public BigVAiTaskResponse submitMultiSummaryTask(String message) {
        try {
            String url = polarisApiBaseUrl + "/bigv/merge/async";

            // 构建表单参数字符串
            String formParams = "message=" + URLEncoder.encode(message, StandardCharsets.UTF_8.name());

            logger.info("提交AI多机构总结任务 - URL: {}", url);
            logger.info("提交AI多机构总结任务 - 表单参数: {}", formParams);

            // 使用HttpClientUtil发送POST请求
            String response = HttpClientUtil.post(url, formParams);

            logger.info("AI多机构总结任务响应: {}", response);

            if (StringUtils.hasLength(response)) {
                // 解析AsyncTaskResponse格式的响应
                return parseAsyncTaskResponse(response);
            }

            logger.error("提交AI多机构总结任务失败，响应为空。URL: {}, FormParams: {}", url, formParams);
            return null;

        } catch (Exception e) {
            logger.error("提交AI多机构总结任务异常，URL: {}", polarisApiBaseUrl + "/bigv/merge/async", e);
            return null;
        }
    }

    /**
     * 轮询多机构任务结果
     */
    public BigVAiTaskResponse pollMultiTaskResult(String taskId) {
        try {
            String url = polarisApiBaseUrl + "/bigv/merge/async";

            // 构建表单参数字符串，传递taskId参数
            String formParams = "taskId=" + URLEncoder.encode(taskId, StandardCharsets.UTF_8.name());

            logger.info("轮询多机构任务结果 - URL: {}, TaskId: {}", url, taskId);
            logger.info("轮询多机构任务结果 - 表单参数: {}", formParams);

            // 使用HttpClientUtil发送POST请求
            String response = HttpClientUtil.post(url, formParams);

            logger.info("轮询多机构任务结果原始响应: {}", response);

            if (StringUtils.hasLength(response)) {
                // 同样使用parseAsyncTaskResponse方法解析
                return parseAsyncTaskResponse(response);
            }

            logger.warn("轮询多机构任务结果失败，响应为空。TaskId: {}", taskId);
            return null;

        } catch (Exception e) {
            logger.error("轮询多机构任务结果异常，TaskId: {}", taskId, e);
            return null;
        }
    }

    /**
     * 格式化消息内容
     */
    public String formatMessage(String nickName, String title, String content) {
        StringBuilder sb = new StringBuilder();
        sb.append("大V：").append(nickName).append("\n");
        if (StringUtils.hasLength(title)) {
            sb.append(title).append("\n");
        }
        if (StringUtils.hasLength(content)) {
            sb.append(content);
        }
        return sb.toString();
    }

    /**
     * 格式化多机构消息内容
     */
    public String formatMultiMessage(List<PostData> postDataList) {
        StringBuilder sb = new StringBuilder();
        for (PostData postData : postDataList) {
            sb.append("大V：").append(postData.getNickName()).append("\n");
            if (StringUtils.hasLength(postData.getTitle())) {
                sb.append(postData.getTitle()).append("\n");
            }
            if (StringUtils.hasLength(postData.getContent())) {
                sb.append(postData.getContent()).append("\n");
            }
        }
        return sb.toString();
    }

    /**
     * 帖子数据内部类
     */
    public static class PostData {
        private String nickName;
        private String title;
        private String content;
        private String postId;
        private String uid;

        public PostData(String nickName, String title, String content, String postId, String uid) {
            this.nickName = nickName;
            this.title = title;
            this.content = content;
            this.postId = postId;
            this.uid = uid;
        }

        // Getters
        public String getNickName() { return nickName; }
        public String getTitle() { return title; }
        public String getContent() { return content; }
        public String getPostId() { return postId; }
        public String getUid() { return uid; }
    }

    /**
     * 拆分AI总结为精简版和详细版
     */
    public SummaryParts splitSummary(String fullSummary) {
        if (!StringUtils.hasLength(fullSummary)) {
            return new SummaryParts("", "");
        }

        try {
            // 先过滤备注信息
            String cleanedSummary = filterRemarks(fullSummary);

            // 查找精简版和详细版的分隔标识
            String briefMarker = "**精简版：**";
            String detailedMarker = "**详细版：**";

            int briefStart = cleanedSummary.indexOf(briefMarker);
            int detailedStart = cleanedSummary.indexOf(detailedMarker);

            String briefContent = "";
            String detailedContent = "";

            if (briefStart != -1 && detailedStart != -1) {
                // 两个版本都存在
                briefContent = cleanedSummary.substring(briefStart + briefMarker.length(), detailedStart).trim();
                detailedContent = cleanedSummary.substring(detailedStart + detailedMarker.length()).trim();
            } else if (briefStart != -1) {
                // 只有精简版
                briefContent = cleanedSummary.substring(briefStart + briefMarker.length()).trim();
            } else if (detailedStart != -1) {
                // 只有详细版
                detailedContent = cleanedSummary.substring(detailedStart + detailedMarker.length()).trim();
            } else {
                // 没有明确标识，尝试其他分割方式
                return splitSummaryByAlternativeMethod(cleanedSummary);
            }

            return new SummaryParts(briefContent, detailedContent);

        } catch (Exception e) {
            logger.error("拆分AI总结失败", e);
            // 如果拆分失败，将完整内容作为详细版
            return new SummaryParts("", fullSummary);
        }
    }

    /**
     * 备用的拆分方法（当没有明确标识时）
     */
    private SummaryParts splitSummaryByAlternativeMethod(String fullSummary) {
        try {
            // 尝试通过其他关键词分割
            String[] lines = fullSummary.split("\n");
            StringBuilder briefPart = new StringBuilder();
            StringBuilder detailedPart = new StringBuilder();

            boolean inDetailedSection = false;

            for (String line : lines) {
                String trimmedLine = line.trim();

                // 检查是否进入详细版部分
                if (trimmedLine.contains("详细") || trimmedLine.contains("实盘操作") ||
                    trimmedLine.contains("核心逻辑") || trimmedLine.contains("机构行为")) {
                    inDetailedSection = true;
                }

                if (inDetailedSection) {
                    if (detailedPart.length() > 0) {
                        detailedPart.append("\n");
                    }
                    detailedPart.append(line);
                } else {
                    if (briefPart.length() > 0) {
                        briefPart.append("\n");
                    }
                    briefPart.append(line);
                }
            }

            return new SummaryParts(briefPart.toString().trim(), detailedPart.toString().trim());

        } catch (Exception e) {
            logger.error("备用拆分方法失败", e);
            return new SummaryParts("", fullSummary);
        }
    }

    /**
     * 总结拆分结果类
     */
    public static class SummaryParts {
        private final String briefSummary;
        private final String detailedSummary;

        public SummaryParts(String briefSummary, String detailedSummary) {
            this.briefSummary = briefSummary;
            this.detailedSummary = detailedSummary;
        }

        public String getBriefSummary() {
            return briefSummary;
        }

        public String getDetailedSummary() {
            return detailedSummary;
        }
    }

    /**
     * 过滤备注信息
     */
    private String filterRemarks(String content) {
        if (!StringUtils.hasLength(content)) {
            return content;
        }

        try {
            String result = content;

            // 过滤以"（注："开头的备注信息（包括多行）
            result = result.replaceAll("（注：[^）]*）", "");

            // 过滤以"(注:"开头的备注信息
            result = result.replaceAll("\\(注：[^)]*\\)", "");

            // 过滤其他常见的备注格式
            result = result.replaceAll("（备注：[^）]*）", "");
            result = result.replaceAll("\\(备注：[^)]*\\)", "");

            // 过滤说明性文字
            result = result.replaceAll("（[^）]*原文未提及[^）]*）", "");
            result = result.replaceAll("\\([^)]*原文未提及[^)]*\\)", "");

            // 过滤"已过滤"相关的说明
            result = result.replaceAll("（[^）]*已过滤[^）]*）", "");
            result = result.replaceAll("\\([^)]*已过滤[^)]*\\)", "");

            // 清理多余的空行
            result = result.replaceAll("\n{3,}", "\n\n");

            // 清理首尾空白
            result = result.trim();

            logger.debug("过滤备注信息 - 原长度: {}, 过滤后长度: {}", content.length(), result.length());

            return result;

        } catch (Exception e) {
            logger.error("过滤备注信息失败", e);
            return content;
        }
    }
}
