package ttfund.web.communityservice.service.hanlp;

import com.alibaba.fastjson.JSON;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.corpus.tag.Nature;
import com.hankcs.hanlp.model.perceptron.PerceptronLexicalAnalyzer;
import com.hankcs.hanlp.seg.common.Term;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.jijinBar.post.KeywordsEntity;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.dao.msyql.KeywordDao;
import ttfund.web.communityservice.utils.CommonUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * hanlp  service
 */
@Service
public class KeywordHanlpService {

    private static Logger logger = LoggerFactory.getLogger(KeywordHanlpService.class);

    private static Set<Character> NOT_STANDARD_CHAR_SET = new HashSet<>();

    public PerceptronLexicalAnalyzer perceptronLexicalAnalyzer = null;

    /**
     * 关键字类型 到 词性类型 的关系
     */
    public static Map<Integer, String> TYPE_BIND_MAP = new HashMap<>();

    /**
     * 关键字类型 到 词性类型大类 的关系
     */
    public static Map<Integer, List<String>> TYPE_GROUP_MAP = new HashMap<>();

    private boolean isLearn = false;

    @Autowired
    private KeywordDao keywordDao;

    @Autowired
    private AppConstant appConstant;

    static {
        List<Character> temp = Arrays.asList(' ', '(', ')', '（', '）', ',', '，', '·', '.');
        temp.forEach(a -> NOT_STANDARD_CHAR_SET.add(a));
    }

    static {
        TYPE_BIND_MAP.put(17, Nature.nr.toString());
    }

    static {
        TYPE_GROUP_MAP.put(17, Arrays.asList(Nature.n.toString()));
    }


    @PostConstruct
    private void init() {
        try {
            perceptronLexicalAnalyzer = new PerceptronLexicalAnalyzer(HanLP.Config.PerceptronCWSModelPath,
                    HanLP.Config.PerceptronPOSModelPath,
                    HanLP.Config.PerceptronNERModelPath);
        } catch (Throwable throwable) {
            logger.error(throwable.getMessage(), throwable);
        }
    }

    public void learnKeyword() {
        Map<String, KeywordsEntity> result = keywordDao.getKeywordsDic();
        if (result != null) {
            Set<String> set = CommonUtils.toSet(appConstant.hanlpLearnWords, ",");
            List<KeywordsEntity> collect = result.values().stream().filter(a -> set.contains(a.Name)).collect(Collectors.toList());
            learn(collect);
        }
    }

    /**
     * 在线学习
     */
    private void learn(KeywordsEntity keyword) {

        if (keyword.Type == 17) {
            for (Character a : keyword.Name.toCharArray()) {
                if (NOT_STANDARD_CHAR_SET.contains(a)) {
                    return;
                }
            }
            perceptronLexicalAnalyzer.learn(keyword.Name + "/nr");
        }
    }

    /**
     * 在线学习
     */
    private void learn(Collection<KeywordsEntity> keywordList) {
        if (!CollectionUtils.isEmpty(keywordList)) {
            for (KeywordsEntity item : keywordList) {
                learn(item);
            }
        }
    }

    /**
     * 词性标注
     *
     * @param content   文本
     * @param fromIndex 词在文本中的原始索引位置
     * @param keyword   社区关键字
     * @return 0:分词里没有该词   1:词性严格匹配   2:词性同属一个词性大类
     */
    public int analyze(String content, int fromIndex, KeywordsEntity keyword) {
        int result = 0;
        try {
            if (!isLearn) {
                learnKeyword();
                isLearn = true;
            }

            perceptronLexicalAnalyzer.enableIndexMode(true);
            List<Term> termList = perceptronLexicalAnalyzer.seg(content);
            for (Term term : termList) {
                if (term.offset == fromIndex && term.length() == keyword.Name.length()) {
                    if (TYPE_BIND_MAP.get(keyword.Type).equals(term.nature.toString())) {
                        result = 1;
                    } else if (TYPE_GROUP_MAP.get(keyword.Type).contains(term.nature.toString().substring(0, 1))) {
                        result = 2;
                    }
                }
                if (term.offset > fromIndex) {
                    break;
                }
            }

            logger.info("nlp词性标注，结果：{}。content：{}，fromIndex：{}，termList：{}，keyword：{}", result, content, fromIndex, termList, JSON.toJSONString(keyword));
        } catch (Throwable throwable) {
            logger.error(throwable.getMessage(), throwable);
        }
        return result;
    }

}
