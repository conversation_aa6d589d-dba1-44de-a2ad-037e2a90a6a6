package ttfund.web.communityservice.service.entity;

import com.ttfund.web.core.utils.MD5Utils;
import lombok.Data;

@Data
public class PostArticleRequest {

    private static final String DEFAULT_IP = "************";

    private static final String DEFAULT_DEVICE_ID = MD5Utils.stringToMD5(DEFAULT_IP);

    private static final String DEFAULT_VERSION = "200";

    private static final String DEFAULT_PRODUCT = "Fund";

    private static final String DEFAULT_PLAT = "Web";

    /**
     * 股吧ID(转发时可不传)，特定业务可不传，如私募路演，私募学堂导入等
     */
    private String code;

    /**
     * 帖子内容
     */
    private String text;

    /**
     * 帖子标题，为空时截取text40个字符
     */
    private String title;

    /**
     * 帖子类型
     */
    private Integer type;

    /**
     * 三方帖子id
     */
    private String sourceid;

    /**
     * 作者uid
     */
    private String uid;

    /**
     * 作者昵称
     */
    private String nicheng;

    /**
     * 发帖显示时间
     */
    private String time;

    /**
     * 用户的发帖ip
     */
    private String ip = DEFAULT_IP;

    /**
     * 用户的发帖端口
     */
    private String port;

    /**
     * 是否需要审核，1：进入审核，0 其他 不进入审核
     */
    private Integer approvalfirst;

    /**
     * 一贴多发关联吧。多个用逗号隔开
     */
    private String codelist;

    /**
     * 图片链接，多个用逗号分隔，最多5张
     */
    private String pic;

    /**
     * 扩展数据，json 对象，需要带有业务标记的前缀，防止与其他业务字段名称冲突,为更好的做扩展，数据不能直接传json 数组字符窜，如果为数组数据，需将数组设置到一个对象的字段上，如 {"aa":[]}
     */
    private String textmodel;

    /**
     * 内部用户发帖业务来源:ai_answer_50 智能回答 ai_fundpost_0 AI基金发帖_普通帖
     */
    private String bizfrom;

    private String deviceid = DEFAULT_DEVICE_ID;

    private String version = DEFAULT_VERSION;

    private String product = DEFAULT_PRODUCT;

    private String plat = DEFAULT_PLAT;

}