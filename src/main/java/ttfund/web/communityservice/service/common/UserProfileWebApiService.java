package ttfund.web.communityservice.service.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.HttpHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ttfund.web.communityservice.config.appconfig.AppConstant;

@Service
public class UserProfileWebApiService {

    @Autowired
    private AppConstant appConstant;

    /**
     * 判断用户是否在用户组
     *
     * @param userGroupId 用户组id
     * @param userid      用户id。可以是用户
     * @return
     */
    public boolean isUserInUserGroup(String userGroupId, String userid) {
        boolean result = false;
        if (StringUtils.hasLength(userGroupId) && StringUtils.hasLength(userid)) {
            String url = appConstant.userProfileWebApi + "/api/UserGroupApi/GetAutoSqlQueryNo";

            StringBuilder builder = new StringBuilder();
            builder.append(url);
            builder.append("?customerNo=" + userid);
            builder.append("&tags=" + userGroupId);
            String fullUrl = builder.toString();
            String html = HttpHelper.requestGet(fullUrl);
            if (StringUtils.hasLength(html)) {
                JSONObject jsonObject = JSON.parseObject(html);
                if (jsonObject != null && !CollectionUtils.isEmpty(jsonObject.getJSONArray("Data"))) {
                    result = true;
                }
            }
        }
        return result;
    }

}
