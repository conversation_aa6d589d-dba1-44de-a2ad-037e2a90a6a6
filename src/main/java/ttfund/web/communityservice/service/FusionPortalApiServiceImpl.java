package ttfund.web.communityservice.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.eastmoney.particle.common.model.Result;
import com.ttfund.web.base.helper.HttpHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ttfund.web.communityservice.config.appconfig.AppConstant;
import ttfund.web.communityservice.service.entity.FusionData;

import java.util.Map;

/**
 *
 */
@Service
public class FusionPortalApiServiceImpl {

    @Autowired
    private AppConstant appConstant;


    public String getVideoPostScoreReturnString(Map<String, Object> request) {
        String result = null;
        if (request == null) {
            return result;
        }

        String url = appConstant.fusionPortalApiAddress + "/fusion-portal/portal/query";

        result = HttpHelper.requestPostJson(url, JSON.toJSONString(request), true);
        return result;
    }


    public Result<FusionData> getVideoPostScoreReturnResponse(Map<String, Object> request) {
        Result<FusionData> result = null;
        if (request == null) {
            return result;
        }

        String html = getVideoPostScoreReturnString(request);
        if (StringUtils.isNotEmpty(html)) {
            result = JSON.parseObject(html, new TypeReference<Result<FusionData>>() {
            });
        }

        return result;
    }

}
