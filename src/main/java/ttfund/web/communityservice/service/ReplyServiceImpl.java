package ttfund.web.communityservice.service;

import com.eastmoney.particle.common.utils.JsonUtils;
import com.eastmoney.particle.common.utils.StringUtils;
import com.ttfund.web.base.helper.HttpHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import ttfund.web.communityservice.config.appconfig.AppConstantConfig;
import ttfund.web.communityservice.service.entity.AddReplyRequest;
import ttfund.web.communityservice.service.entity.AddReplyResponse;
import ttfund.web.communityservice.utils.CommonUtils;

/**
 * <AUTHOR> Zhuyuang
 * @date : 2024-08-21 14:26
 * @description :
 */
@Service
public class ReplyServiceImpl {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReplyServiceImpl.class);

    @Value("${" + AppConstantConfig.config_gubahostnewserver + "}")
    public String gubaHost;

    public AddReplyResponse addReply(AddReplyRequest replyRequest) {
        String url = gubaHost + "/replyopt/api/Reply/ReplyArticle";
        String html = HttpHelper.requestPostFrom(url, CommonUtils.getPostFromStr(replyRequest), true);
        if (StringUtils.isEmpty(html)) {
            return null;
        }
        return JsonUtils.toObject(html, AddReplyResponse.class);
    }
}
