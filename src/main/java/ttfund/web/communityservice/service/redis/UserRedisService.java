package ttfund.web.communityservice.service.redis;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.barrage.PassportUserInfoModel;
import ttfund.web.communityservice.bean.barrage.UserBasicInfoResponse;
import ttfund.web.communityservice.bean.barrage.UserProfitModel;
import ttfund.web.communityservice.config.dataconfig.UserRedisConfig;
import ttfund.web.communityservice.dao.mongo.PassportUserInfoDao;
import ttfund.web.communityservice.dao.redis.UserRedisDao;
import ttfund.web.communityservice.handler.CommanHandler;
import ttfund.web.communityservice.utils.JacksonUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: liu
 * @Date: 2021/10/19 16:51
 * @Description:
 */
@Service
public class UserRedisService {


    @Autowired
    UserRedisDao userRedisDao;

    @Autowired
    PassportUserInfoDao passportUserInfoDao;


    /**
     * 用户基本信息
     *
     * @param uid
     * @return
     */
    public UserBasicInfoResponse getUserBasicInfo(String uid) throws JsonProcessingException {
        UserBasicInfoResponse resultItem = new UserBasicInfoResponse();
        List<PassportUserInfoModel> pidInfos = this.passportUserInfoCache(Arrays.asList(uid));
        if (!CollectionUtils.isEmpty(pidInfos) && pidInfos.get(0) != null) {
            PassportUserInfoModel item = pidInfos.get(0);
            resultItem.setPassportId(item.PassportID);
            resultItem.setNickname(item.NickName);
            resultItem.setGender(item.Gender);
            resultItem.setIntroduce(item.Introduce);
            resultItem.setUserV(CommanHandler.NewUserVType(item));
            resultItem.setMgrId(item.MGRID);
        }
        return resultItem;
    }

    /**
     * 通行证用户信息
     *
     * @param uidlist
     * @return
     */
    public List<PassportUserInfoModel> passportUserInfoCache(List<String> uidlist) throws JsonProcessingException {
        List<PassportUserInfoModel> result = new ArrayList<>();
        if (uidlist != null && uidlist.size() > 0) {
            List<String> uidNoList;
            List<String> keyList;
            keyList = uidlist.parallelStream().distinct().map(s -> String.format(UserRedisConfig.Asp_Net_Fund_Service_Passport_Info_pid, s)).collect(Collectors.toList());
            Map<String, String> res = userRedisDao.getPassportUserInfoCache(keyList);
            if (res != null && res.size() > 0) {
                for (Map.Entry<String, String> entry : res.entrySet()) {
                    if (keyList.contains(entry.getKey())) {
                        result.add(JacksonUtil.string2Obj(entry.getValue(),PassportUserInfoModel.class));
                    }
                }
            }
            List<String> uidhavelist = result.parallelStream().map(m -> m.PassportID).collect(Collectors.toList());
            uidNoList = uidlist.parallelStream().filter(f -> !uidhavelist.contains(f)).collect(Collectors.toList());
            if (uidNoList != null && uidNoList.size() > 0) {
                uidNoList = uidNoList.stream().filter(f -> StringUtils.isNotEmpty(f)).collect(Collectors.toList());
            }
            if (uidNoList != null && uidNoList.size() > 0) {
                List<PassportUserInfoModel> docs = passportUserInfoDao.getPassportUserInfoListById(uidNoList);
                for (PassportUserInfoModel doc : docs) {
                    if (doc != null) {
                        result.add(doc);
                        String keyfull = String.format(UserRedisConfig.Asp_Net_Fund_Service_Passport_Info_pid, doc._id);
                        userRedisDao.setPassportUserInfoCache(keyfull,doc);
                    }
                }
            }
        }
        return result;
    }


    /** 用户收益状态
     * @param barCode
     * @param passportId
     * @return
     * @throws JsonProcessingException
     */
    public UserProfitModel getUserProfit(String barCode, String passportId) throws JsonProcessingException {
        return userRedisDao.getUserProfit(barCode, passportId);
    }
}
