package ttfund.web.communityservice.service;

import com.ttfund.web.base.helper.CacheHelper;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

/**
 * 缓存方法
 **/
@Component
public class CacheService {

    public <T> T get(Supplier<T> supplier, String key, long cacheSeconds) {

        T result = CacheHelper.get(key);

        if (result == null) {
            result = supplier.get();
            if (result != null) {
                CacheHelper.put(key, result, cacheSeconds * 1000L);
            }
        }

        return result;
    }
}