package ttfund.web.communityservice.kafka.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.bean.favor.FavorCodeKafkaModel;
import ttfund.web.communityservice.bean.favor.FavorKafkaData;
import ttfund.web.communityservice.config.appconfig.App;
import ttfund.web.communityservice.config.dataconfig.PersonalizedRedisConstantConfig;
import ttfund.web.communityservice.config.kafka.KafkaConfig;
import ttfund.web.communityservice.config.kafka.KafkaTopicName;
import ttfund.web.communityservice.utils.JacksonUtil;
import ttfund.web.communityservice.utils.redis.RedisUtils;

import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
/**
 * 自选Kafka
 *
 * <AUTHOR>
 **/
public class FavorKafkaConsumer {

    private static final Logger logger = LoggerFactory.getLogger(FavorKafkaConsumer.class);

    @Autowired
    private App app;

    /**
     * 加自选
     */
    public static final String ADD_FAVOR = "push.sync.favor.fund.add";

    /**
     * 删除自选
     */
    public static final String DELETE_FAVOR = "push.sync.favor.fund.del";

    @KafkaListener(topics = {KafkaTopicName.FAVOR_FCODE_ACTION}, groupId = "community_service_java_favor",
            containerFactory = KafkaConfig.KAFKA_LISTENER_CONTAINER_FACTORY_FAVOR)
    public void onListen(List<String> records) throws JsonProcessingException {

        TypeReference<FavorKafkaData<FavorCodeKafkaModel>> typeReference = new TypeReference<FavorKafkaData<FavorCodeKafkaModel>>() {};

        for (String record : records) {
            try {
                if (!record.contains(ADD_FAVOR) && !record.contains(DELETE_FAVOR)) {
                    continue;
                }
                FavorKafkaData<FavorCodeKafkaModel> result = JacksonUtil.string2Obj(record, typeReference);
                if (result != null && !CollectionUtils.isEmpty(result.getData())) {
                    if (ADD_FAVOR.equals(result.getActionType())) {
                        addCache(result.passportId, result.getData());
                    } else if (DELETE_FAVOR.equals(result.getActionType())) {
                        deleteCache(result.passportId, result.getData());
                    }
                }
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 加入缓存
     *
     * @param passportId
     * @param data
     */
    private void addCache(String passportId, List<FavorCodeKafkaModel> data) {
        List<String> codes = data.stream().map(w -> w.getFcode()).distinct().collect(Collectors.toList());
        String cacheKey = PersonalizedRedisConstantConfig.JAVA_SERVICE_FAVOR_PASSPORT + passportId;
        RedisUtils.sadd(app.personalizedRedisWrite, cacheKey, codes.toArray(new String[codes.size()]));
        app.personalizedRedisWrite.expire(cacheKey, 90 * 24 * 3600L);
    }

    /**
     * 删除缓存
     *
     * @param passportId
     * @param data
     */
    private void deleteCache(String passportId, List<FavorCodeKafkaModel> data) {
        List<String> codes = data.stream().map(w -> w.getFcode()).distinct().collect(Collectors.toList());
        String cacheKey = PersonalizedRedisConstantConfig.JAVA_SERVICE_FAVOR_PASSPORT + passportId;
        RedisUtils.srem(app.personalizedRedisWrite, cacheKey, codes.toArray(new String[codes.size()]));
        app.personalizedRedisWrite.expire(cacheKey, 90 * 24 * 3600L);
    }
}


