package ttfund.web.communityservice.mapper.vertica;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import ttfund.web.communityservice.bean.jijinBar.data.AiCommentDo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024-08-21 13:43
 * @description :
 */
@Mapper
public interface PostAiCmtMapper {
    @Select("select POSTID as postId, AI_COMMENT as comment, to_char(EUTIME, 'YYYY-MM-DD HH24:MI:SS.US')" +
        " as updateTime from CONTENT.POST_AI_CMT_BASIC_DRCT_ALL " +
        "where EUTIME > #{updateTime} AND AUDIT_TYPE = 1")
    List<AiCommentDo> select(@Param("updateTime") String updateTime);
}
