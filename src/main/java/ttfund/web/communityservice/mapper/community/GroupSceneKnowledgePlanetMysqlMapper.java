package ttfund.web.communityservice.mapper.community;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.data.GroupSceneKnowledgePlanet;

import java.util.List;

@Repository
public interface GroupSceneKnowledgePlanetMysqlMapper {

    @Select(" select * from tb_group_scene_knowledge_planet where groupType = 2 and isDel = 0 ")
    List<GroupSceneKnowledgePlanet> getAllGroupIds();

    @Select("select * from tb_group_scene_knowledge_planet where groupId =#{groupId}")
    GroupSceneKnowledgePlanet getByGroupId(@Param("groupId") Integer groupId);
}
