package ttfund.web.communityservice.mapper.community;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Repository
public interface GroupExtendInfoMapper {

    @Update("update tb_group_extend_info set curPosts = #{curPosts} where groupId = #{groupId}")
    int updateCurPosts(@Param("groupId") String groupId, @Param("curPosts") Integer curPosts);

}
