package ttfund.web.communityservice.mapper.barread;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.PostCountModel;
import ttfund.web.communityservice.bean.jijinBar.post.ReplyLikeCountModel;
import ttfund.web.communityservice.bean.jijinBar.post.guba.TbLikeCount;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * tb_likecount表 mapper接口
 */
@Repository
public interface LikeCountMapper {

    /**
     * 根据更新时间获取指定数量的的点赞信息
     * TYPE=1 帖子点赞
     * TYPE=2 评论点赞
     */
    @Select("SELECT ID,LIKECOUNT LikeNum,UPDATETIME UpdateTime FROM tb_likecount " +
            "where  UPDATETIME >= #{updateTime} and ISENABLED = 1 and  TYPE=1 order by UpdateTime ASC limit #{limit}")
    List<PostCountModel> getList(@Param("updateTime") Date updateTime, @Param("limit") int limit);

    @Select("SELECT ID,LIKECOUNT,UPDATETIME FROM tb_likecount where ISENABLED = 1 and TYPE=2 and UPDATETIME>= #{updateTime} order by UPDATETIME  limit #{batchReadCount}")
    List<ReplyLikeCountModel> getLikeCounTable(@Param("updateTime") Date updateTime, @Param("batchReadCount") int batchReadCount);

    @Select(" SELECT DISTINCT b.UID " +
            " FROM tb_likecount a " +
            " INNER JOIN tb_postinfo_new b " +
            " ON a.ID = b.ID " +
            " AND b.PROJECT = 1 " +
            " WHERE a.ISENABLED = 1 " +
            " and b.DEL=0 " +
            " AND a.TYPE = 1  and b.TYPE = 50  " +
            " and a.UPDATETIME > #{updateTime} " +
            " limit #{batchReadCount} ")
    List<String> getLikeCountUids(@Param("updateTime") Date updateTime, @Param("batchReadCount") int batchReadCount);

    List<Map<String, Object>> getAnswerLikeCounts(@Param("uidList") List<String> uidList);

    int insertOrUpdate(@Param("info") TbLikeCount info);

}
