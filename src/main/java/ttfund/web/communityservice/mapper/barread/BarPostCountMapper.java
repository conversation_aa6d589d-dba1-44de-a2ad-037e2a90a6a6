package ttfund.web.communityservice.mapper.barread;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoByDailyCount;

import java.util.Date;
import java.util.List;

/**
 * 表tb_barpost_count mapper接口
 */
@Repository
public interface BarPostCountMapper {

    @Select("SELECT * from tb_barpost_count where  UpdateTime>= #{updateTime}   order by UpdateTime limit #{limitCount}")
    List<PostInfoByDailyCount> getCountList(@Param("updateTime") Date updateTime, @Param("limitCount") int limitCount);

    @Select("SELECT * from tb_barpost_count where id = #{id}")
    PostInfoByDailyCount getById(@Param("id") String id);

    int upsertPostCount(@Param("list") List<PostInfoByDailyCount> list);

    int upsert(@Param("list") List<PostInfoByDailyCount> list);

    int mergeByUpdateTimeInterval(@Param("start") Date start, @Param("end") Date end, @Param("updateTime") Date updateTime, @Param("limit") int limit);

}
