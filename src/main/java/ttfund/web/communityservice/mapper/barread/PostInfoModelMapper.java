package ttfund.web.communityservice.mapper.barread;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoForMysqlModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.post.YuanDetailModel;
import ttfund.web.communityservice.bean.jijinBar.post.guba.PostInfoKafka;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
@Component
public interface PostInfoModelMapper extends BaseMapper<PostInfoModel> {

    @Select("SELECT * FROM tb_postinfo where ID = #{id}")
    PostInfoModel get(@Param("id") long id);

    List<PostInfoForMysqlModel> getPostList(@Param("updateTime") Date updateTime, @Param("batchReadCount") int batchReadCount, @Param("isDesc") boolean isDesc);

    List<PostInfoForMysqlModel> getPostListByIds(@Param("idList") List<Integer> idList);

    List<PostInfoNewModel> getPostInfoNewWithTimePoint(@Param("updateTime") Date updateTime, @Param("timePoint") String timePoint, @Param("limit") int limit);

    List<PostInfoNewModel> getPostInfoNew(@Param("updateTime") Date updateTime, @Param("limit") int limit);

    YuanDetailModel getYuanDetail(@Param("ID") int postID);

    @Select(" SELECT * FROM tb_postinfo where UPDATETIME>#{updateTime} and UPDATETIME<#{updateTime1} " +
            " and del=0 and ISENABLED=1 order by UPDATETIME limit #{limit}")
    List<PostInfoForMysqlModel> getPostListByUpdateTime(@Param("updateTime") Date updateTime, @Param("updateTime1") Date updateTime1, @Param("limit") int limit);

    @Select("select UPDATETIME from tb_postinfo order by UPDATETIME desc limit 1")
    Map<String, Object> getLatestUpdateTime();

    List<Integer> getIdsByIds(@Param("idList") List<Integer> idList);

    @Select("SELECT * FROM tb_postinfo where DEL=0 and ISENABLED=1 and  UPDATETIME>= #{updateTime} and uid regexp '^[0-9]+$'  order by UPDATETIME asc limit #{batchReadCount}")
    List<PostInfoForMysqlModel> getList(@Param("updateTime") Date updateTime, @Param("batchReadCount") int batchReadCount);

    List<PostInfoKafka> getByIds(@Param("ids") List<Integer> ids);

    int updateKafka(@Param("info") PostInfoKafka info);

    int insertOrUpdate(@Param("post") PostInfoKafka post);

    @Update("Update TB_POSTINFO SET PIC =#{post.Pic},CONTENT =#{post.Content},UPDATETIME =NOW() WHERE ID=#{post.ID}")
    int updatePostPic(@Param("post") PostInfoKafka post);

    @Update("Update TB_POSTINFO set PUSHTIME=#{info.PushTime},UPDATETIME =NOW(),EXTEND=#{info.Extend} WHERE ID=#{info.ID}")
    int updateJijinExtend(@Param("info") PostInfoKafka info);
}
