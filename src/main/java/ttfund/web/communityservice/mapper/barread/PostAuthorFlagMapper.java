package ttfund.web.communityservice.mapper.barread;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.context.annotation.Bean;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.PostAuthorFlag;

import java.util.List;

@Repository
public interface PostAuthorFlagMapper extends BaseMapper<PostAuthorFlag> {

   List<PostAuthorFlag> getListByPidNew(@Param("uid") String uid);
}