package ttfund.web.communityservice.mapper.barread;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.HuifuDetailModel;
import ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoNew;
import ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoNewModel;
import ttfund.web.communityservice.bean.jijinBar.post.config.PostActivityParticipateModel;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface ReplyInfoNewMapper extends BaseMapper<ReplyInfoModel> {

    /**
     * 获取在指定时间范围内对某个帖子进行一级评论的用户id集合
     *
     * <AUTHOR>
     */
    List<PostActivityParticipateModel> getUidsByPostidAndTime(@Param("postId") long postId, @Param("start") Date start, @Param("end") Date end, @Param("breakpoint") Date breakpoint);

    List<ReplyInfoNewModel> getReplyList(@Param("updateTime") Date updateTime, @Param("limit") int limit);

    @Select(" SELECT * FROM tb_replyinfo_new where TOPICID = #{ID}")
    List<ReplyInfoNewModel> getReplyInfosByIdInvalid(@Param("ID") int postId);

    @Select(" SELECT * FROM tb_replyinfo_new where TOPICID = #{ID} and DEL=0 and ISENABLED=1")
    List<ReplyInfoNewModel> getReplyInfosByIdValid(@Param("ID") int postId);

    @Select(" SELECT trn.`ID`,trn.`TOPICID`,trn.`LOUCENG`,trn.`DEL`,ifnull( trn.`CODE`, '') as CODE,ifnull( trn.`UID`, '') as UID,ifnull( trn.`NICHENG`, '') as NICHENG,trn.`TIME`," +
            "            ifnull( trn.`TEXT`, '') as TEXT,ifnull( trn.`TEXTEND`, '') as TEXTEND,ifnull( trn.`KEYWORDLIST`, '') as KEYWORDLIST,ifnull( trn.`IP`, '') as IP,ifnull( trn.`HUIFUIDLIST`, '') as HUIFUIDLIST," +
            "            trn.`PUSHTIME`, trn.`ISENABLED`, trn.`CREATETIME`,trn.`UPDATETIME`, trn.`TTJJDEL`,trn.`TIMEPOINT`" +
            "            FROM tb_replyinfo_new trn inner join tb_postinfo_new tpn on trn.TOPICID=tpn.ID " +
            "            WHERE tpn.UID=trn.UID and trn.TOPICID= #{TOPICID} and trn.DEL=0 and trn.TTJJDEL=0 AND trn.ISENABLED=1")
    List<ReplyInfoNewModel> getAuthorReplyTable(@Param("TOPICID") int postId);

    @Select("SELECT ID,UID,NICHENG FROM tb_replyinfo_new where DEL=0 and ISENABLED=1 and id in(#{huifuIdList})")
    List<HuifuDetailModel> getHuifuDetail(@Param("huifuIdList") String huifuIdList);


    int insertModel(@Param("model") ReplyInfoNew model);

    int updateModel(@Param("model") ReplyInfoNew model);

    @Select("SELECT count(*) FROM tb_replyinfo_new where ID = #{id}")
    int getCountById(@Param("id") long id);

    @Select("select UPDATETIME from tb_replyinfo_new order by UPDATETIME desc limit 1")
    Map<String, Object> getLatestUpdateTime();

    List<ReplyInfoNewModel> getReplyTable(@Param("updateTime") Date updateTime, @Param("batchReadCount") int batchReadCount);

    List<Map<String, Object>> getTotalReplyCountByUids(@Param("uids") List<String> uids);

    List<Map<String, Object>> getPeriodReplyCountByUids(@Param("uids") List<String> uids, @Param("start") Date start, @Param("end") Date end);

    List<Map<String, Object>> getExcellentComment(@Param("time") Date time,
                                                  @Param("interactCountLimit") int interactCountLimit,
                                                  @Param("batchReadCount") int batchReadCount
    );

    @Select("select * from tb_replyinfo_new where UPDATETIME >= #{start} and UPDATETIME < #{end}  order by UPDATETIME asc limit #{batchReadCount}")
    List<ReplyInfoNewModel> getListByUpdateTime(@Param("start") Date start, @Param("end") Date end, @Param("batchReadCount") int batchReadCount);

    List<Map<String, Object>> getByTime(@Param("fields") String fields, @Param("start") Date start, @Param("end") Date end, @Param("limit") int limit);

    @Select("select ${fields} from tb_replyinfo_new where UPDATETIME > #{updateTime} and del=0   order by UPDATETIME asc limit #{limit}")
    List<ReplyInfoNewModel> getList(@Param("fields") String fields, @Param("updateTime") Date updateTime, @Param("limit") int limit);

    @Select("select count(*) SubReplyNum from tb_replyinfo_new where  TOPICID=#{topicId} and  HUIFUIDLIST !='' and del=0")
    Integer getSubReplyCount(@Param("topicId") String topicId);

}
