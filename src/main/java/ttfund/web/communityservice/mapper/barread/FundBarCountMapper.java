package ttfund.web.communityservice.mapper.barread;


import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.FundbarCountModel;

/**
 * tb_fundbar_count表 持久层操作实体
 */
@Repository
public interface FundBarCountMapper {

    @Select("select * from tb_fundbar_count where code =#{fcode}")
    FundbarCountModel get(@Param("fcode") String fcode);

}
