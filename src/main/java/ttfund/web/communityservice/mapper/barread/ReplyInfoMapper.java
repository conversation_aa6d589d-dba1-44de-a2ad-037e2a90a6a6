package ttfund.web.communityservice.mapper.barread;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.ReplyInfo;
import ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoModel;
import ttfund.web.communityservice.bean.jijinBar.post.guba.ReplyInfoKafka;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * tb_replyinfo表 mapper接口
 */
@Repository
public interface ReplyInfoMapper {

    /**
     * 根据帖子ID 获取帖子信息
     *
     * @param id
     * @return
     */
    @Select("SELECT * FROM tb_replyinfo where ID =#{id}")
    ReplyInfoModel getReplyInfoById(@Param("id") String id);

    @Select("SELECT * FROM tb_replyinfo where UPDATETIME>= #{updateTime}  order by UPDATETIME limit #{batchReadCount}")
    List<ReplyInfo> getReplyList(@Param("updateTime") Date updateTime, @Param("batchReadCount") int batchReadCount);

    List<ReplyInfo> getReplyListByIds(@Param("ids") List<Long> ids);

    @Select("select UPDATETIME from tb_replyinfo order by UPDATETIME desc limit 1")
    Map<String, Object> getLatestUpdateTime();

    List<Map<String, Object>> getReplyCountOfFundBar(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<ReplyInfoModel> getLatestReply(@Param("postIds") List<Integer> postIdList);

    @Select("select * from TB_REPLYINFO where id=#{id}")
    ReplyInfoKafka get(@Param("id") long id);

    int updateKafka(@Param("reply") ReplyInfoKafka reply);

    int insertOrResume(@Param("reply") ReplyInfoKafka reply);

}
