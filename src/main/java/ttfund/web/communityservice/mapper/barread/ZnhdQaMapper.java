package ttfund.web.communityservice.mapper.barread;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.QA.ZnhdCountModel;
import ttfund.web.communityservice.bean.jijinBar.post.QA.ZnhdQaModel;

import java.util.Date;
import java.util.List;

@Repository
public interface ZnhdQaMapper {

    List<ZnhdQaModel> getList(@Param("states") List<Integer> states, @Param("replyPostState") int replyPostState, @Param("count") int count);

    @Update("update tb_znhd_qa set ReplyPostState=#{replyPostState},AID=#{aid},UpdateTime=now() where ID=#{id}")
    int updateState(@Param("replyPostState") int replyPostState, @Param("aid") String aid, @Param("id") String id);

    @Select("select count(*) as #{countName}, DistributionPassportId PassportId from tb_znhd_qa " +
        "WHERE CreatTime>=#{start} and CreatTime<#{end} and Del=0 and DataType=0 #{where} GROUP BY DistributionPassportId")
    List<ZnhdCountModel> getCount(@Param("start") Date start, @Param("end")Date end, @Param("countName")String countName, @Param("where")String where);

    @Insert("<script> INSERT INTO tb_znhd_qa_count " +
        "(ID,CFHID, CFHName,PassportId,NickName,UnAnswerCount,AnsweredCount,AnswerPercent,AnswerRank,InvalidQuCount,InvalidQuPercent,TotalCount,TransferTTCount,TransferTTPercent,CountType,State,CreatTime,ZnhdMatchCount,DataDate) VALUES " +
        "<foreach collection = 'list' item = 'record' separator = ',' >" +
        "(#{record.ID},#{record.CFHID},#{record.CFHName},#{record.PassportId},#{record.NickName},#{record.UnAnswerCount}," +
        "#{record.AnsweredCount},#{record.AnswerPercent},#{record.AnswerRank},#{record.InvalidQuCount},#{record.InvalidQuPercent}," +
        "#{record.TotalCount},#{record.TransferTTCount},#{record.TransferTTPercent},#{record.CountType},#{record.State}," +
        "#{record.CreatTime},#{record.ZnhdMatchCount},#{record.DataDate})</foreach> ON DUPLICATE KEY" +
        " Update CFHID=values(CFHID),CFHName=values(CFHName),PassportId=values(PassportId)," +
        "NickName=values(NickName),UnAnswerCount=values(UnAnswerCount),AnsweredCount=values(AnsweredCount),AnswerPercent=values(AnswerPercent)," +
        "AnswerRank=values(AnswerRank),InvalidQuCount=values(InvalidQuCount)," +
        "InvalidQuPercent=values(InvalidQuPercent),TotalCount=values(TotalCount),TransferTTCount=values(TransferTTCount)," +
        "TransferTTPercent=values(TransferTTPercent),CountType=values(CountType),CreatTime=values(CreatTime)," +
        "ZnhdMatchCount=values(ZnhdMatchCount),DataDate=values(DataDate) </script>")
    int insert(List<ZnhdCountModel> list);
    int updateDel(@Param("postids") List<Integer> postids, @Param("del") int del);

    Integer insertMany(@Param("list") List<ZnhdQaModel> list);
}
