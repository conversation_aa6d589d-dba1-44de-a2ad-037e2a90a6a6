package ttfund.web.communityservice.mapper.barwrite;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfoSyncTempModel;

import java.util.List;

/**
 * @Author: ShaMo
 * @Date: 2023/4/6
 * @ApiNote:
 * @Status:
 */
@Repository
public interface PostInfoSyncMapper {
    @Select("(select \n" +
            "a.code as code1\n" +
            ",1 as project1\n" +
            ",a.type as type1\n" +
            ",a.*\n" +
            ",now() updatetime1\n" +
            "from jijinba.tb_notjijinpostinfo a\n" +
            "left join jijinba.tb_postinfo b on a.id = b.id\n" +
            "where a.updatetime>DATE_SUB(now(),INTERVAL 1 DAY)\n" +
            " and (a.code in (select code \n" +
            "from tb_jjbconfig\n" +
            "where state = 1) or a.type in (select value from tb_jjbtypeconfig\n" +
            "where state = 1))\n" +
            " and (b.pushtime is null or a.pushtime > b.pushtime or a.isenabled <> b.isenabled) and a.code != 'cfhpl' limit #{limit})\n" +
            "union all\n" +
            "(select \n" +
            "case when a.code = 'cfhpl' and position('of' in a.codelist)>0 then substring(a.codelist,position('of' in a.codelist)+2,6)\n" +
            "else a.code end as code1,\n" +
            "case when a.code = 'cfhpl' and position('of' in a.codelist)>0 then 1\n" +
            "else a.project end as project1,\n" +
            "case when a.code = 'cfhpl' and position('of' in a.codelist)>0 then 0\n" +
            "else a.type end as type1,\n" +
            "a.*,\n" +
            "now() updatetime1\n" +
            "from jijinba.tb_notjijinpostinfo a\n" +
            "left join jijinba.tb_postinfo b on a.id = b.id\n" +
            "where a.type in (1,20) and a.updatetime>DATE_SUB(now(),INTERVAL 1 DAY) and (a.code not in (select code from tb_jjbconfig where state = 1) or a.code = 'cfhpl')\n" +
            "and (b.pushtime is null or a.pushtime > b.pushtime or a.isenabled <> b.isenabled) limit #{limit})\n" +
            "union all\n" +
            "(select \n" +
            "substring(a.codelist,position('of' in a.codelist)+2,6) as code1\n" +
            ",1 as project1\n" +
            ",0 as type1\n" +
            ",a.*\n" +
            ",now() updatetime1\n" +
            "from jijinba.tb_notjijinpostinfo a\n" +
            "left join jijinba.tb_postinfo b on a.id = b.id\n" +
            "where a.type in (1,20)  and a.code = 'cfhpl' and position('of' in a.codelist)>0\n" +
            "and a.updatetime>DATE_SUB(now(),INTERVAL 1 DAY)\n" +
            "and (b.pushtime is null or a.pushtime > b.pushtime or a.isenabled <> b.isenabled) limit #{limit})")
    List<PostInfoSyncTempModel> getSqlRet1(@Param("limit") int limit);


    @Select("select a.*,now() updatetime1\n" +
            "from jijinba.tb_notjijinpostinfo a\n" +
            "left join jijinba.tb_postinfo b on a.id = b.id\n" +
            "where a.code = 'zf' and a.yuanid in(select id from jijinba.tb_postinfo) and a.updatetime>DATE_SUB(now(),INTERVAL 1 DAY) and (b.pushtime is null or a.pushtime > b.pushtime or a.isenabled <> b.isenabled) limit #{limit}")
    List<PostInfoSyncTempModel> getSqlRet2(@Param("limit") int limit);

    @Insert("<script> INSERT INTO tb_postinfo "
            + "(ID, NEWSID, CODELIST, PIC, DISPLAYTIME, PDF, " +
            "HUIFUIP, HUIFUNICHENG, HUIFUUID, IP, TITLE, HUIFUTIME, " +
            "TIME, UID, NICHENG, CODE, Project, State, " +
            "PINGLUNNUM, ZHUANFANUM, YUANID, Del, PingLunqianxian, Type," +
            " Postfrom, ZhiDing, ZbZhiDing, Img, Server, " +
            "Color, NUMXISHU, HuiFuTable, PUSHTIME, CONTENT" +
            ", ISENABLED, CREATETIME, UPDATETIME, EXTEND, REPOSTSTATE) "
            + "VALUES "
            + "<foreach collection = 'postInfoList' item='record' separator=',' > "
            + "(#{record.ID, jdbcType=INTEGER}," +
            "#{record.NEWSID, jdbcType=VARCHAR}," +
            "#{record.CODELIST, jdbcType=VARCHAR}," +
            "#{record.PIC, jdbcType=VARCHAR}," +
            "#{record.DISPLAYTIME, jdbcType=TIMESTAMP}," +
            "#{record.PDF, jdbcType=VARCHAR}," +
            "#{record.HUIFUIP, jdbcType=VARCHAR}," +
            "#{record.HUIFUNICHENG, jdbcType=VARCHAR}," +
            "#{record.HUIFUUID, jdbcType=VARCHAR}," +
            "#{record.IP, jdbcType=VARCHAR}," +
            "#{record.TITLE, jdbcType=VARCHAR}," +
            "#{record.HUIFUTIME, jdbcType=TIMESTAMP}," +
            "#{record.TIME, jdbcType=TIMESTAMP}," +
            "#{record.UID, jdbcType=VARCHAR}," +
            "#{record.NICHENG, jdbcType=VARCHAR}," +
            "#{record.CODE, jdbcType=VARCHAR}," +
            "#{record.PROJECT, jdbcType=INTEGER}," +
            "#{record.STATE, jdbcType=INTEGER}," +
            "#{record.PINGLUNNUM, jdbcType=INTEGER}," +
            "#{record.ZHUANFANUM, jdbcType=INTEGER}," +
            "#{record.YUANID, jdbcType=INTEGER}," +
            "#{record.DEL, jdbcType=INTEGER}," +
            "#{record.PINGLUNQIANXIAN, jdbcType=INTEGER}," +
            "#{record.TYPE, jdbcType=INTEGER}," +
            "#{record.POSTFROM, jdbcType=INTEGER}," +
            "#{record.ZHIDING, jdbcType=INTEGER}," +
            "#{record.ZBZHIDING, jdbcType=INTEGER}," +
            "#{record.IMG, jdbcType=INTEGER}," +
            "#{record.SERVER, jdbcType=INTEGER}," +
            "#{record.COLOR, jdbcType=INTEGER}," +
            "#{record.NUMXISHU, jdbcType=INTEGER}," +
            "#{record.HUIFUTABLE, jdbcType=INTEGER}," +
            "#{record.PUSHTIME, jdbcType=TIMESTAMP}," +
            "#{record.CONTENT, jdbcType=VARCHAR}," +
            "#{record.ISENABLED, jdbcType=INTEGER}," +
            "#{record.CREATETIME, jdbcType=TIMESTAMP}," +
            "now()," +
            "#{record.EXTEND, jdbcType=VARCHAR}," +
            "#{record.REPOSTSTATE, jdbcType=INTEGER})"
            + " </foreach> "
            + "ON DUPLICATE KEY UPDATE NEWSID = VALUES(NEWSID),CODELIST = VALUES(CODELIST),PIC = VALUES(PIC),DISPLAYTIME = VALUES(DISPLAYTIME),PDF = VALUES(PDF),HUIFUIP = VALUES(HUIFUIP),HUIFUNICHENG = VALUES(HUIFUNICHENG),HUIFUUID = VALUES(HUIFUUID),IP = VALUES(IP),TITLE = VALUES(TITLE),HUIFUTIME = VALUES(HUIFUTIME),TIME = VALUES(TIME),UID = VALUES(UID),NICHENG = VALUES(NICHENG),CODE = VALUES(CODE),Project = VALUES(Project),State = VALUES(State),PINGLUNNUM = VALUES(PINGLUNNUM),ZHUANFANUM = VALUES(ZHUANFANUM),YUANID = VALUES(YUANID),Del = VALUES(Del),PingLunqianxian = VALUES(PingLunqianxian),Type = VALUES(Type),Postfrom = VALUES(Postfrom),ZhiDing = VALUES(ZhiDing),ZbZhiDing = VALUES(ZbZhiDing),Img = VALUES(Img),Server = VALUES(Server),Color = VALUES(Color),NUMXISHU = VALUES(NUMXISHU),HuiFuTable = VALUES(HuiFuTable),PUSHTIME = VALUES(PUSHTIME),CONTENT = VALUES(CONTENT),ISENABLED = VALUES(ISENABLED),CREATETIME = VALUES(CREATETIME),UPDATETIME = VALUES(UPDATETIME),EXTEND = VALUES(EXTEND),REPOSTSTATE = VALUES(REPOSTSTATE)"
            + "</script>")
    void updatePostInfo(@Param("postInfoList") List<PostInfoSyncTempModel> postInfoList);
}
