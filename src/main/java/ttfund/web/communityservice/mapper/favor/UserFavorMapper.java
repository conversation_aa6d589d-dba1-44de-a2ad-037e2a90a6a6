package ttfund.web.communityservice.mapper.favor;


import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;
import ttfund.web.communityservice.bean.favor.UserFavorBean;

import java.util.List;

@Repository
@Component
public interface UserFavorMapper {

    /**
     * 获取自选基金
     *
     * @param table
     * @param userId
     * @param fundCode
     * @param size
     * @param updateTime
     * @return
     */
    List<UserFavorBean> getUserFavorFund(@Param("table") String table, @Param("userId") String userId,
                                         @Param("fundCode") String fundCode, @Param("size") int size,
                                         @Param("updateTime") String updateTime);

    /**
     * 获取自选组合
     *
     * @param userId
     * @param code
     * @param size
     * @param updateTime
     * @return
     */
    List<UserFavorBean> getUserFavorCombine(@Param("userId") String userId, @Param("code") String code,
                                            @Param("size") int size, @Param("updateTime") String updateTime);

    /**
     * 获取自选投顾
     *
     * @param userId
     * @param code
     * @param size
     * @param updateTime
     * @return
     */
    List<UserFavorBean> getUserFavorInvest(@Param("userId") String userId, @Param("code") String code,
                                           @Param("size") int size, @Param("updateTime") String updateTime);
}
