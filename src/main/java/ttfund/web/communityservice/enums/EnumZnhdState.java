package ttfund.web.communityservice.enums;

/**
 * 智能互动状态枚举
 */
public enum EnumZnhdState {

    /**
     * 机构待处理
     */
    JI_GOU_DAI_CHU_LI(0),

    /**
     * 基金待处理
     */
    JI_JIN_DAI_CHU_LI(1),

    /**
     * 机构待审核
     */
    JI_GOU_DAI_SHEN_HE(2),

    /**
     * 基金待审核
     */
    JI_JIN_DAI_SHEN_HE(3),

    /**
     * 审状态通过
     */
    SHEN_ZHUANG_TAI_TONG_GUO(4),

    /**
     * 无效关闭
     */
    WU_XIAO_GUAN_BI(5),

    /**
     * 基金吧后台回复
     */
    JI_JIN_BA_HOU_TAI_HUI_FU(6),

    /**
     * 转天天客服
     * 备注：财富后台转过来的
     */
    ZHUAN_TIAN_TIAN_KE_FU(7),

    /**
     * 基金吧后台关闭
     * 备注：如果机构处理了，基金吧后台对应的问答关闭
     */
    JI_JIN_BA_HOU_TAI_GUAN_BI(8);

    private int value;

    EnumZnhdState(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

}
