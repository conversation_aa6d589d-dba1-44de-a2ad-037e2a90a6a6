package ttfund.web.communityservice.enums;

/**
 * 退出圈子错误枚举
 */
public enum LeaveCircleErrorEnum {

    /**
     * 圈子已关闭或无法找到
     */
    CIRCLE_IS_DEL("圈子无法找到"),

    /**
     * 已退出圈子
     */
    ALREADY_LEAVE("已退出圈子"),

    /**
     * 无法找到加入圈子记录
     */
    JOIN_FIRST("无法找到加入圈子记录");

    private String message;

    LeaveCircleErrorEnum(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

}
