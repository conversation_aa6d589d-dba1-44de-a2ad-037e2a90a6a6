package ttfund.web.communityservice.enums;

/**
 * 社区发现页来源帖子 -枚举
 *
 * <AUTHOR>
 */
public enum FindPageSourcePostEnum {

    /**
     * 人工优质贴
     */
    ARTIFICIAL_QUALITY_POST(1),

    /**
     * AI优质贴
     */
    AI_QUALITY_POST(2),

    /**
     * 单吧热帖
     */
    BAR_HOT_POST(3),

    /**
     * 自选基金单吧贴
     */
    FAVOR_POST(4),

    /**
     * 话题贴
     */
    TOPIC_POST(5),

    /**
     * 圈子贴
     */
    CIRCLE_POST(6),

    /**
     * 模板贴
     */
    TEMPLATE_POST(7),

    /**
     * 用户贴
     */
    USER_POST(8),

    /**
     * 资讯贴
     */
    NEWS_POST(9),

    /**
     * 运营贴
     */
    CONFIG_POST(10);

    private int value;

    FindPageSourcePostEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

}
