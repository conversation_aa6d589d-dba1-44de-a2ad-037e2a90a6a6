package ttfund.web.communityservice;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.lang.management.ManagementFactory;

/**
 * @description:
 * @author: liulijun
 * @date: 2021/10/20 14:51
 **/
@SpringBootApplication(exclude = {MongoAutoConfiguration.class, MongoDataAutoConfiguration.class})
@MapperScan("ttfund.web.communityservice.mapper.**")
@ComponentScan({"com.ttfund.web.core", "ttfund.web"})
@EnableTransactionManagement
public class FundCommunityServiceApplication {
    private static final Logger logger = LoggerFactory.getLogger(FundCommunityServiceApplication.class);

    public static void main(String[] args) {

        SpringApplication.run(FundCommunityServiceApplication.class, args);
        logger.info("Heap Memrogy Info:" + ManagementFactory.getMemoryMXBean().getHeapMemoryUsage());
        logger.info("NonHeap Memrogy Info:" + ManagementFactory.getMemoryMXBean().getNonHeapMemoryUsage());
        logger.info("CPU Num:" + Runtime.getRuntime().availableProcessors());
        logger.info("Max Memory:" + Runtime.getRuntime().maxMemory());
        logger.info("Free Memory:" + Runtime.getRuntime().freeMemory());
        logger.info("Total Memory:" + Runtime.getRuntime().totalMemory());
        logger.info("Java Version:" + System.getProperty("java.version"));

    }

}
