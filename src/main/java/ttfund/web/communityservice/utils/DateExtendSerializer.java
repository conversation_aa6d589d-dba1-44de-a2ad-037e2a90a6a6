package ttfund.web.communityservice.utils;


import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Date;


/**
 * jackson 自定义格式反序列化
 *
 * <AUTHOR>
 */
public class DateExtendSerializer extends JsonDeserializer<Date> {

    private static final Logger logger = LoggerFactory.getLogger(DateExtendSerializer.class);

    private static final String[] patterns = {
            "yyyy-MM-dd'T'HH:mm:ss.SSSXXX",
            "yyyy-MM-dd'T'HH:mm:ssXXX",
            "yyyy-MM-dd'T'HH:mm:ss"
    };

    @SneakyThrows
    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        try {
            if (jsonParser == null || StringUtils.isEmpty(jsonParser.getText())) {
                return null;
            }
            return DateUtils.parseDate(jsonParser.getText(), patterns);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }
}
