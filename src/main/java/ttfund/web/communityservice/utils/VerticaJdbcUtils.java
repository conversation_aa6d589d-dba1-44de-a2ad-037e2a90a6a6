package ttfund.web.communityservice.utils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Properties;

/**
 * jdbc操作vertica工具类
 *
 * <AUTHOR>
 */
public class VerticaJdbcUtils {
    /**
     * 获取一个连接,传url、user、password
     */
    public static Connection getConnection(String url, String user, String password) throws Exception {
        Class.forName("com.vertica.jdbc.Driver");
        return DriverManager.getConnection(url, user, password);
    }

    /**
     * 获取一个连接,传url
     */
    public static Connection getConnection(String url) throws Exception {
        Class.forName("com.vertica.jdbc.Driver");
        return DriverManager.getConnection(url);
    }

}
