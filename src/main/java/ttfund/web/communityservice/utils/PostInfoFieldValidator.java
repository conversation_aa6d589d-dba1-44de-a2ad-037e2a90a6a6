package ttfund.web.communityservice.utils;

import ttfund.web.communityservice.bean.jijinBar.post.PostInfo;

import java.util.Date;

/**
 * PostInfo字段验证工具类
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
public class PostInfoFieldValidator {

    /**
     * 验证PostInfo字段访问
     */
    public static void validatePostInfoFields() {
        PostInfo post = new PostInfo();
        
        // 设置测试数据
        post._id = "test_post_id";
        post.ID = 123456L;
        post.TITLE = "测试标题";
        post.CONTENTEND = "测试内容";
        post.UID = "test_uid";
        post.TIME = new Date();
        post.DEL = 0;
        post.TTJJDEL = 0;
        post.ISENABLED = 1;
        
        // 验证字段访问
        System.out.println("=== PostInfo字段验证 ===");
        System.out.println("_id: " + post._id);
        System.out.println("ID: " + post.ID);
        System.out.println("TITLE: " + post.TITLE);
        System.out.println("CONTENTEND: " + post.CONTENTEND);
        System.out.println("UID: " + post.UID);
        System.out.println("TIME: " + post.TIME);
        System.out.println("DEL: " + post.DEL);
        System.out.println("TTJJDEL: " + post.TTJJDEL);
        System.out.println("ISENABLED: " + post.ISENABLED);
        System.out.println("=== 验证完成 ===");
    }

    public static void main(String[] args) {
        validatePostInfoFields();
    }
}
