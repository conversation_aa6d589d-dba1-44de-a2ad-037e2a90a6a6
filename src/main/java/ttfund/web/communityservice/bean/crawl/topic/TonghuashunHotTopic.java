package ttfund.web.communityservice.bean.crawl.topic;

public class TonghuashunHotTopic {

    private String ios_jump_url;

    private String android_jump_url;

    private String title;

    private String code;

    private String jump_url;

    private String view_key;

    private Integer attach_old_type;

    private String attach_type;

    private TonghuashunAttachInfo attach_info;

    private Integer label_type;

    public String getIos_jump_url() {
        return ios_jump_url;
    }

    public void setIos_jump_url(String ios_jump_url) {
        this.ios_jump_url = ios_jump_url;
    }

    public String getAndroid_jump_url() {
        return android_jump_url;
    }

    public void setAndroid_jump_url(String android_jump_url) {
        this.android_jump_url = android_jump_url;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getJump_url() {
        return jump_url;
    }

    public void setJump_url(String jump_url) {
        this.jump_url = jump_url;
    }

    public String getView_key() {
        return view_key;
    }

    public void setView_key(String view_key) {
        this.view_key = view_key;
    }

    public Integer getAttach_old_type() {
        return attach_old_type;
    }

    public void setAttach_old_type(Integer attach_old_type) {
        this.attach_old_type = attach_old_type;
    }

    public String getAttach_type() {
        return attach_type;
    }

    public void setAttach_type(String attach_type) {
        this.attach_type = attach_type;
    }

    public TonghuashunAttachInfo getAttach_info() {
        return attach_info;
    }

    public void setAttach_info(TonghuashunAttachInfo attach_info) {
        this.attach_info = attach_info;
    }

    public Integer getLabel_type() {
        return label_type;
    }

    public void setLabel_type(Integer label_type) {
        this.label_type = label_type;
    }

    @Override
    public String toString() {
        return "TonghuashunHotTopic{" +
                "ios_jump_url='" + ios_jump_url + '\'' +
                ", android_jump_url='" + android_jump_url + '\'' +
                ", title='" + title + '\'' +
                ", code='" + code + '\'' +
                ", jump_url='" + jump_url + '\'' +
                ", view_key='" + view_key + '\'' +
                ", attach_old_type=" + attach_old_type +
                ", attach_type='" + attach_type + '\'' +
                ", attach_info=" + attach_info +
                ", label_type=" + label_type +
                '}';
    }
}
