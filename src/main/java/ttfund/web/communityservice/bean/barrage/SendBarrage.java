package ttfund.web.communityservice.bean.barrage;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * @Author: liu
 * @Date: 2021/10/19 16:06
 * @Description: 弹幕实体类(未组装)
 */

@Data
@Document("SendBarrage")
public class SendBarrage {
    @Id
    private String id;
    private String passportId;
    private String barCode;
    private Date sendTime;
    private String content;
    private int sendStatus;
    private Date updateTime;
    private String imUserId;
    private int sendNum;
}
