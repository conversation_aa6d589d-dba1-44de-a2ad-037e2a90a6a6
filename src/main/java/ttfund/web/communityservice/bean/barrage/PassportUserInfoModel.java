package ttfund.web.communityservice.bean.barrage;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;


/**
 * @description: 通行证用户信息
 * @author: liulijun
 * @date: 2021/10/20 9:08
 **/

@Document("PassportUserInfo")
public class PassportUserInfoModel {
    public String _id;
    /**
     * 通行证账号ID
     */
    public String PassportID;

    /**
     * 基金openid
     */
    public String OpenID;

    /**
     * 昵称
     */
    public String NickName;

    /**
     * 性别(0:保密,1：男,2：女)
     */
    public String Gender;
    /**
     * 大V类型：301个人-理财师、302个人-普通个人、303官方-企业官方、401-基金经理加V、402-基金公司
     */
    public String Vtype;

    /**
     * 大v状态：空未认证、0审核通过、1审核未通过、2审核中、8用户取消加V、9管理员取消加V
     */
    public String VtypeStatus;

    /**
     * 注册时间
     */
    public String Registertime;

    /**
     * 财富号ID
     */
    public String CaifuhaoID;

    /**
     * 财富号V认证状态(0审核通过,1审核不通过,2审核中,3修改审核中)
     */
    public String CFHVSstatus;
    /**
     * 财富号 认证类型
     */
    public String accreditationType;

    /**
     * 财富号 是否禁用 1：禁用 0：非禁用
     */
    public String abandon;

    /**
     * 简介
     */
    public String Introduce;

    /**
     * 财富号用户认证列表，一个用户会有多种V认证
     */
    public List<CFHVUserInfo> CFHVList;
    /**
     * 是否是天天基金财富号(1 是  0：非)
     */
    public int IsFundCFH;


    /**
     * 基金经理ID
     */
    public String MGRID;


}