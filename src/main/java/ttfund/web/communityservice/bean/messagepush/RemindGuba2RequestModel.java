package ttfund.web.communityservice.bean.messagepush;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;


@Data
@JsonAutoDetect(fieldVisibility=JsonAutoDetect.Visibility.ANY, getterVisibility=JsonAutoDetect.Visibility.NONE)
public class RemindGuba2RequestModel extends   MessagePushBaseModel{

    public RemindGuba2RequestModel()
    {
        super();
    }

    /// <summary>
    /// 业务主键
    /// </summary>
    public String EID ;
    /// <summary>
    /// 通行证ID(我)
    /// </summary>
    public String PassportID ;
    /// <summary>
    /// 通行证昵称(我)
    /// </summary>
    public String PassportName ;
    /// <summary>
    /// 通行证ID(回复我的)
    /// </summary>
    public String ReplyPassportID ;
    /// <summary>
    /// 通行证昵称(回复我的)
    /// </summary>
    public String ReplyPassportName ;
    /// <summary>
    /// 信息类型(1帖子,2回复)
    /// </summary>
    public int InfoType ;
    /// <summary>
    /// 帖子类型(普通帖子0,问题帖子49,回答帖子50)
    /// </summary>
    public int PostType ;
    /// <summary>
    /// 帖子扩展信息json串
    /// 普通帖子 infotype(帖子101/回复102)
    /// 问答帖子 infotype(提问201,回答202,评论203),qid(问题ID),aid(回答ID)
    /// </summary>
    public String PostAbout ;
    /// <summary>
    /// 吧code(子账户)
    /// </summary>
    public String BarCode ;
    /// <summary>
    /// 吧名称(子账户)
    /// </summary>
    public String BarName ;
    /// <summary>
    /// 吧管理者用户名称(子账户)
    /// </summary>
    public String BarAdminUserName ;
    /// <summary>
    /// 原标题
    /// </summary>
    public String Title ;
    /// <summary>
    /// 原内容
    /// </summary>
    public String Content ;
    /// <summary>
    /// 回复内容
    /// </summary>
    public String ReplyContent ;
    /// <summary>
    /// 帖子ID PostAbout.infotype为201时给问题帖子ID，PostAbout.infotype为202,203时给回答帖子ID
    /// </summary>
    public String PostID ;
    /// <summary>
    /// 来源类型(1帖子,2评论)
    /// </summary>
    public String SourceType ;
    /// <summary>
    /// 来源对应的ID(帖子ID/评论ID)
    /// </summary>
    public String SourceID ;
    /// <summary>d
    /// 来源(guba)
    /// </summary>
    public String Source ;
}
