package ttfund.web.communityservice.bean.messagepush;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;


@Data
@JsonAutoDetect(fieldVisibility=JsonAutoDetect.Visibility.ANY, getterVisibility=JsonAutoDetect.Visibility.NONE)
public class MessagePushBaseModel {

    public String Version ;
    public String deviceid ;
    public String plat ;

    public String product;

    public MessagePushBaseModel() {
        Version = "6.4.2";
        deviceid = "jijibaservice";
        plat = "web";
        product = "EFund";
    }
}
