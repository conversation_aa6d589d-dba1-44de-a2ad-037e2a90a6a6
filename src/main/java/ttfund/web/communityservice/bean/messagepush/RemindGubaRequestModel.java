package ttfund.web.communityservice.bean.messagepush;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

@Data
@JsonAutoDetect(fieldVisibility=JsonAutoDetect.Visibility.ANY, getterVisibility=JsonAutoDetect.Visibility.NONE)
public class RemindGubaRequestModel extends MessagePushBaseModel {


    public RemindGubaRequestModel()
    {
        super();
    }
    /// <summary>
    /// 业务主键
    /// </summary>
    public String EID ;
    /// <summary>
    /// 通行证ID(我)
    /// </summary>
    public String PassportID ;
    /// <summary>
    /// 通行证昵称(我)
    /// </summary>
    public String PassportName ;
    /// <summary>
    /// 通行证ID(回复我的)
    /// </summary>
    public String ReplyPassportID ;
    /// <summary>
    /// 通行证昵称(回复我的)
    /// </summary>
    public String ReplyPassportName ;
    /// <summary>
    /// 消息类型(帖子/回复)
    /// </summary>
    public String InfoTypeName ;
    /// <summary>
    /// 原内容
    /// </summary>
    public String Content ;
    /// <summary>
    /// 回复内容
    /// </summary>
    public String ReplyContent ;
    /// <summary>
    /// 帖子ID
    /// </summary>
    public String PostID ;
    /// <summary>
    /// 来源类型(1帖子,2评论)
    /// </summary>
    public String SourceType ;
    /// <summary>
    /// 来源对应的ID(帖子ID/评论ID)
    /// </summary>
    public String SourceID ;
    /// <summary>d
    /// 来源(guba)
    /// </summary>
    public String Source ;
}
