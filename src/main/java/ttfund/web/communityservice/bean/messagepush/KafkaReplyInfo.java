package ttfund.web.communityservice.bean.messagepush;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class KafkaReplyInfo {


    /// <summary>
    ///
    /// </summary>
    public Long ID;

    /// <summary>
    ///
    /// </summary>

    @JsonProperty("topic_id")
    public Long TopicID;

    /// <summary>
    ///
    /// </summary>

    public Integer Louceng;

    /// <summary>
    ///
    /// </summary>

    public Byte Del;

    /// <summary>
    ///
    /// </summary>

    public String Code;

    /// <summary>
    ///
    /// </summary>

    public String UID;

    /// <summary>
    ///
    /// </summary>

    public String Nicheng;

    /// <summary>
    ///
    /// </summary>
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    public Date Time;

    /// <summary>
    ///
    /// </summary>

    public String Text;

    /// <summary>
    ///
    /// </summary>

    public String IP;

    /// <summary>
    ///
    /// </summary>
    @JsonProperty("huifu_id_list")
    public String HuiFuIDList;

    /// <summary>
    ///
    /// </summary>
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    public Date PushTime;

    /// <summary>
    ///
    /// </summary>

    public boolean IsEnabled;

    /// <summary>
    ///
    /// </summary>
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    public Date CreateTime;

    /// <summary>
    ///
    /// </summary>
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    public Date UpdateTime;

    /// <summary>
    ///
    /// </summary>
    public String action_type;

    /// <summary>
    /// 主帖评论数
    /// </summary>
    @JsonProperty("topic_pinglun_num")
    public int TopicPingLunNum;

    /// <summary>
    /// 楼主回复数
    /// </summary>
    @JsonProperty("user_pinglun_num")
    public Long UserPingLunNum;

    /// <summary>
    /// 评论图片地址
    /// </summary>

    public String Pic;
}
