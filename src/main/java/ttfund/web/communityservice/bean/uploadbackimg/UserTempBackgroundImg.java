package ttfund.web.communityservice.bean.uploadbackimg;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * @Author: liu
 * @Date: 2021/9/13 17:21
 * @Description:
 */
@Data
@Document(collection="UserTempBackgroundImg")
public class UserTempBackgroundImg {

    /**
     * 主键, imgUrl
     */
    private String id;

    /**
     * 图片路径
     */
    private String imgUrl;

    /**
     * 通行证id
     */
    private String passportId;

    /**
     * 上传时间
     */
    private Date uploadTime;

}
