package ttfund.web.communityservice.bean.keywordpush;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;
import org.springframework.beans.factory.annotation.Value;

/**
 * @Author: ShaMo
 * @Date: 2022/11/4
 * @ApiNote:
 * post请求发送给CMS域名的数据格式
 */

@Data
@ToString
public class CmsPostModel {
    @JSONField(name = "reqTrace")
    private String reqTrace; // biz + timestamp
    @JSONField(name = "biz")
    private String biz; // 默认值fund_key_word
    @JSONField(name = "topic")
    private String topic; // 默认值fund_key_word
    @JSONField(name = "data")
    private String data; // 封装而成的数据
}
