package ttfund.web.communityservice.bean.infer;

import ttfund.web.communityservice.bean.jijinBar.mongo.FundJBXXModel;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 基金倒排索引
 */
public class ProductIndex {

    /**
     * 代码索引
     */
    private Map<String, FundJBXXModel> codeIndex;

    /**
     * 名称索引
     */
    private Map<String, FundJBXXModel> nameIndex;

    /**
     * 名称倒排索引
     */
    private Map<String, Set<String>> reverseIndex;

    /**
     * 分词索引
     */
    private Map<String, List<String>> segmentIndex;

    /**
     * IDF
     */
    private Map<String, Double> idfMap;

    /**
     * 名称
     */
    private Map<String, String> nameMap;

    /**
     * 长前缀集合
     */
    private Set<String> longPrefixSet;

    /**
     * 短前缀集合
     */
    private Set<String> shortPrefixSet;

    public Map<String, FundJBXXModel> getCodeIndex() {
        return codeIndex;
    }

    public void setCodeIndex(Map<String, FundJBXXModel> codeIndex) {
        this.codeIndex = codeIndex;
    }

    public Map<String, FundJBXXModel> getNameIndex() {
        return nameIndex;
    }

    public void setNameIndex(Map<String, FundJBXXModel> nameIndex) {
        this.nameIndex = nameIndex;
    }

    public Map<String, Set<String>> getReverseIndex() {
        return reverseIndex;
    }

    public void setReverseIndex(Map<String, Set<String>> reverseIndex) {
        this.reverseIndex = reverseIndex;
    }

    public Map<String, List<String>> getSegmentIndex() {
        return segmentIndex;
    }

    public void setSegmentIndex(Map<String, List<String>> segmentIndex) {
        this.segmentIndex = segmentIndex;
    }

    public Map<String, Double> getIdfMap() {
        return idfMap;
    }

    public void setIdfMap(Map<String, Double> idfMap) {
        this.idfMap = idfMap;
    }

    public Map<String, String> getNameMap() {
        return nameMap;
    }

    public void setNameMap(Map<String, String> nameMap) {
        this.nameMap = nameMap;
    }

    public Set<String> getLongPrefixSet() {
        return longPrefixSet;
    }

    public void setLongPrefixSet(Set<String> longPrefixSet) {
        this.longPrefixSet = longPrefixSet;
    }

    public Set<String> getShortPrefixSet() {
        return shortPrefixSet;
    }

    public void setShortPrefixSet(Set<String> shortPrefixSet) {
        this.shortPrefixSet = shortPrefixSet;
    }

    /**
     * 是否存在基金代码
     */
    public boolean containsCode(String code) {
        return codeIndex.containsKey(code);
    }

    /**
     * 是否存在基金名称
     */
    public boolean containsName(String text) {
        return nameIndex.containsKey(text);
    }

    /**
     * 是否存在分词
     */
    public boolean containsSegment(String text) {
        return reverseIndex.containsKey(text);
    }

    /**
     * 根据基金代码获取基金信息
     *
     * @param code
     * @return
     */
    public FundJBXXModel getByCode(String code) {
        return codeIndex.get(code);
    }

    /**
     * 根据基金名称获取基金信息
     *
     * @param name
     * @return
     */
    public FundJBXXModel getByName(String name) {
        return nameIndex.get(name);
    }

    /**
     * 根据分词获取基金信息
     *
     * @param segment
     * @return
     */
    public Set<String> getBySegment(String segment) {
        return reverseIndex.get(segment);
    }

    /**
     * 根据基金代码获取基金名称
     *
     * @param code
     * @return
     */
    public String getNameByCode(String code) {
        return nameMap.get(code);
    }
}
