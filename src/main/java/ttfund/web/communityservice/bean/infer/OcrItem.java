package ttfund.web.communityservice.bean.infer;

import java.util.ArrayList;
import java.util.List;

/**
 * Paddle识别结果
 */
public class OcrItem {

    /**
     * 坐标
     */
    private Coordinate coordinate;

    /**
     * 内容
     */
    private String text;

    /**
     * 置信度
     */
    private double confidence;

    /**
     * 精确匹配
     */
    private List<String> extractList = new ArrayList<>();

    /**
     * 模糊匹配
     */
    private List<String> fuzzyList = new ArrayList<>();

    /**
     * 最小编辑距离
     */
    private int minDistance;

    /**
     * 分割后的文本
     */
    private List<String> textList;

    public Coordinate getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(Coordinate coordinate) {
        this.coordinate = coordinate;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public double getConfidence() {
        return confidence;
    }

    public void setConfidence(double confidence) {
        this.confidence = confidence;
    }

    public List<String> getExtractList() {
        return extractList;
    }

    public void setExtractList(List<String> extractList) {
        this.extractList = extractList;
    }

    public List<String> getFuzzyList() {
        return fuzzyList;
    }

    public void setFuzzyList(List<String> fuzzyList) {
        this.fuzzyList = fuzzyList;
    }

    public int getMinDistance() {
        return minDistance;
    }

    public void setMinDistance(int minDistance) {
        this.minDistance = minDistance;
    }

    public List<String> getTextList() {
        return textList;
    }

    public void setTextList(List<String> textList) {
        this.textList = textList;
    }
}
