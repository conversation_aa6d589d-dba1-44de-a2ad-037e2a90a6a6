package ttfund.web.communityservice.bean.bigshotshow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @file : RoadShowDetail.java
 * @Software: IntelliJ IDEA
 * @date: 2020/9/5 15:14
 * @description: 路演信息
 * @version: 1.0
 */
@ApiModel(value = "路演信息")
public class RoadShowDetail {
    /**
     * 路演状态  1：直播  3：回放 4：预告
     */
    @ApiModelProperty(value = "路演状态  1：直播  3：回放 4：预告")
    private int status;
    /**
     * 路演 - 直播 - rtmp
     */
    @ApiModelProperty(value = "路演 - 直播 - rtmp")
    private String live_rtmp_addr;
    /**
     * 路演 - 直播 - flv
     */
    @ApiModelProperty(value = "路演 - 直播 - flv")
    private String live_flv_addr;
    /**
     * 路演 - 直播 - hls
     */
    @ApiModelProperty(value = "路演 - 直播 - hls")
    private String live_hls_addr;

    /**
     * 是否支持回放
     */
    @ApiModelProperty("是否支持回放")
    private Boolean playback;

    /**
     * 路演回放详情
     */
    @ApiModelProperty("路演回放详情")
    private Object playback_chapter;

    /**
     * 宣传视频链接
     */
    @ApiModelProperty("宣传视频链接")
    public String propaganda_video;

    /**
     * 直播画面高度
     */
    @ApiModelProperty("直播画面高度")
    public int height;

    /**
     * 直播画面宽度
     */
    @ApiModelProperty("直播画面宽度")
    public int width;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getLive_rtmp_addr() {
        return live_rtmp_addr;
    }

    public void setLive_rtmp_addr(String live_rtmp_addr) {
        this.live_rtmp_addr = live_rtmp_addr;
    }

    public String getLive_flv_addr() {
        return live_flv_addr;
    }

    public void setLive_flv_addr(String live_flv_addr) {
        this.live_flv_addr = live_flv_addr;
    }

    public String getLive_hls_addr() {
        return live_hls_addr;
    }

    public void setLive_hls_addr(String live_hls_addr) {
        this.live_hls_addr = live_hls_addr;
    }

    public Boolean getPlayback() {
        return playback;
    }

    public void setPlayback(Boolean playback) {
        this.playback = playback;
    }

    public Object getPlayback_chapter() {
        return playback_chapter;
    }

    public void setPlayback_chapter(Object playback_chapter) {
        this.playback_chapter = playback_chapter;
    }

    public String getPropaganda_video() {
        return propaganda_video;
    }

    public void setPropaganda_video(String propaganda_video) {
        this.propaganda_video = propaganda_video;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }
}
