package ttfund.web.communityservice.bean.jijinBar.data;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 圈子基本信息实体
 *
 * <AUTHOR>
 */
@Data
@ApiModel("圈子基本信息实体")
public class CircleBaseInfo {

    /**
     * 圈子id
     */
    @ApiModelProperty("圈子id")
    private String circleId;

    /**
     * 圈子名称
     */
    @ApiModelProperty("圈子名称")
    private String name;

    /**
     * 圈子头像
     */
    @ApiModelProperty("圈子头像")
    private String avatar;

    /**
     * 圈子理念
     */
    @ApiModelProperty("圈子理念")
    private String concept;

    /**
     * 成员称呼
     */
    @ApiModelProperty("成员称呼")
    private String memberName;

    /**
     * 圈子规则
     */
    @ApiModelProperty("圈子规则")
    private String rule;

    /**
     * 圈子分享图
     */
    @ApiModelProperty("圈子分享图")
    private String sharePic;

    /**
     * 圈子加入条件文案
     */
    @ApiModelProperty("圈子加入条件文案")
    private String conditionDesc;

    /**
     * 实际入圈条件配置
     */
    @ApiModelProperty("实际入圈条件配置")
    private String conditions;

    /**
     * 成员最大数量
     */
    @ApiModelProperty("成员最大数量")
    private Integer maxMembers;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createUser;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateUser;

    /**
     * 是否删除	 0否  1是
     */
    @JsonIgnore
    @ApiModelProperty(name = "是否删除 0否  1是", hidden = true)
    private Integer isDel;

    /**
     * 创建时间
     */
    @JsonIgnore
    @ApiModelProperty(name = "创建时间", hidden = true)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonIgnore
    @ApiModelProperty(name = "更新时间", hidden = true)
    private Date updateTime;

}
