package ttfund.web.communityservice.bean.jijinBar.post.videoArticle;

import lombok.Data;

@Data
public class ChannelInfoDataInfo {
    private Long id ;
    private String name ;

    /// <summary>
    /// 直播的时间获小视频发布时间
    /// </summary>
    private String live_start_time ;

    /// <summary>
    /// 视频地址
    /// </summary>
    private ChannelVideo record_hls ;
    /// <summary>
    /// state	频道状态  0: 无输入流,1: 直播中,2: 异常,3:关闭,4:预告
    /// </summary>
    private Integer state ;

    /// <summary>
    /// list_state	0-正常 2,3-删除 5-审核中 6-移除列表
    /// </summary>
    private Integer list_state ;

    /// <summary>
    /// 类型 0：直播 1：录播  2：小视频，3图片动态
    /// </summary>
    private Integer type ;

    /// <summary>
    /// 录播首页截图地址
    /// </summary>
    private String record_img_url ;
    /// <summary>
    /// 云封面
    /// </summary>
    private String yun_record_img_url ;

    /// <summary>
    /// 直播视频地址 hls格式
    /// </summary>
    private String hls_downstream_address ;

    /// <summary>
    /// 直播视频地址 flv格式
    /// </summary>
    private String flv_downstream_address ;

    /// <summary>
    /// 直播视频地址 rtmp
    /// </summary>
    private String rtmp_downstream_address ;

    private RoomCoverResourceModel room_cover_resource ;
}