package ttfund.web.communityservice.bean.jijinBar.post.finance;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.Date;

/**
 * 稳健理财-理财同路人作者信息
 *
 * <AUTHOR>
 */
@Document(value = BarMongodbConfig.TABLE_WJLC_AUTHOR_INFO)
public class WJLCVUserModel {

    /**
     * 主键，通行证ID
     */
    @Id
    private String id;

    /**
     * 通行证ID
     */
    @Field("UID")
    private String uid;

    /**
     * 更新时间
     */
    @Field("UpdateTime")
    private Date updateTime;

    /**
     * 是否已删除，0-未删除，1-已删除
     */
    @Field("IsDel")
    private int isDel;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public int getIsDel() {
        return isDel;
    }

    public void setIsDel(int isDel) {
        this.isDel = isDel;
    }
}
