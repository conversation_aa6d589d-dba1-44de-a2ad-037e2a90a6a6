package ttfund.web.communityservice.bean.jijinBar.post.caifuhao;

import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.util.Date;
import java.util.List;

/**
 * 直播配置的所有信息的实体类
 * 备注：财富号mongo的TB_CFHLiveBroadcast表 实体(只选取部分字段)
 *
 * <AUTHOR>
 */
public class CFHLiveBroadcastForPostModel {

    @MongoId
    private String _id;

    /**
     * 直播主题
     */
    @Field("Title")
    private String title;

    /**
     * 大咖用户Id
     */
    @Field("UserId")
    private String userId;

    /**
     * 直播开始时间
     */
    @Field("StartTime")
    private Date startTime;

    /**
     * 直播结束时间
     */
    @Field("EndTime")
    private Date endTime;

    /**
     * 帖子内容
     */
    @Field("PostContent")
    private String postContent;

    /**
     * 直播状态：0：草稿，1:提交审核；2:运营审核成功；3：法务审核成功，4：运营审核失败，5：法务审核失败
     */
    @Field("Status")
    private Integer status;

    /**
     * 修改时间
     */
    @Field("UpdateTime")
    private Date updateTime;

    /**
     * 是否删除1:删除；0：未删除
     */
    @Field("IsDelete")
    private Integer isDelete;

    /**
     * 回放房间号
     */
    @Field("RoomNumber")
    private String roomNumber;

    /**
     * 是否隐藏 1：是；0：否
     */
    @Field("IsHide")
    private Integer isHide;

    /**
     * 互动配置基金
     */
    @Field("LiveFundModel")
    private List<LiveInteractFundCacheModel> liveFundModel;

    /**
     * 浪客路演直播详情
     */
    @Field("RoadShowDetail")
    private ApiLangKeRoadshowDetail roadShowDetail;

    /**
     * 是否生成直播回放帖子：0不生成，1生成
     */
    @Field("IsCreatePlaybackPost")
    private Integer isCreatePlaybackPost;

    /**
     * 浪客封面图
     */
    @Field("GubaShareImg")
    private String gubaShareImg;

    public CFHLiveBroadcastForPostModel() {
    }

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getPostContent() {
        return postContent;
    }

    public void setPostContent(String postContent) {
        this.postContent = postContent;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getRoomNumber() {
        return roomNumber;
    }

    public void setRoomNumber(String roomNumber) {
        this.roomNumber = roomNumber;
    }

    public Integer getIsHide() {
        return isHide;
    }

    public void setIsHide(Integer isHide) {
        this.isHide = isHide;
    }

    public List<LiveInteractFundCacheModel> getLiveFundModel() {
        return liveFundModel;
    }

    public void setLiveFundModel(List<LiveInteractFundCacheModel> liveFundModel) {
        this.liveFundModel = liveFundModel;
    }

    public ApiLangKeRoadshowDetail getRoadShowDetail() {
        return roadShowDetail;
    }

    public void setRoadShowDetail(ApiLangKeRoadshowDetail roadShowDetail) {
        this.roadShowDetail = roadShowDetail;
    }

    public Integer getIsCreatePlaybackPost() {
        return isCreatePlaybackPost;
    }

    public void setIsCreatePlaybackPost(Integer isCreatePlaybackPost) {
        this.isCreatePlaybackPost = isCreatePlaybackPost;
    }

    public String getGubaShareImg() {
        return gubaShareImg;
    }

    public void setGubaShareImg(String gubaShareImg) {
        this.gubaShareImg = gubaShareImg;
    }

    @Override
    public String toString() {
        return "CFHLiveBroadcastForPostModel{" +
                "_id='" + _id + '\'' +
                ", title='" + title + '\'' +
                ", userId='" + userId + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", postContent='" + postContent + '\'' +
                ", status=" + status +
                ", updateTime=" + updateTime +
                ", isDelete=" + isDelete +
                ", roomNumber='" + roomNumber + '\'' +
                ", isHide=" + isHide +
                ", liveFundModel=" + liveFundModel +
                ", roadShowDetail=" + roadShowDetail +
                ", isCreatePlaybackPost=" + isCreatePlaybackPost +
                ", gubaShareImg='" + gubaShareImg + '\'' +
                '}';
    }
}
