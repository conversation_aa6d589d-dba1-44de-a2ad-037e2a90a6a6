package ttfund.web.communityservice.bean.jijinBar.post;

import org.springframework.data.mongodb.core.mapping.Document;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.Date;

/**
 * <AUTHOR>
 *
 * @Date 2024-04-23
 **/
@Document(value = BarMongodbConfig.TABLE_HOTPOST)
public class HotPostModel {

    public String _id;
    public long ID;
    public String UID;
    public long YUANID;
    public String CODE;
    public Date TIME;
    /**
     * 按时间去重排序字段 TIME时间戳(毫秒级)+处理时间(秒+毫秒)  1530693076000ssfff(秒毫秒)
     */
    public long TIMEPOINT;
    public int TYPE;
    public int YUANTYPE;
    public int CONTENTLENGTH;
    public int RECOMMENDDEL;
    public double SCORE;
}
