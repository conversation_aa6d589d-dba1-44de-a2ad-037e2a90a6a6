package ttfund.web.communityservice.bean.jijinBar.post.guba;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 帖子拓展信息-该实体为专门kafka->mysql写入数据使用，请勿随意更改
 *
 * <AUTHOR>
 */
public class PostInfoExtendKafka {

    public int Id;

    /**
     * 允许点赞的状态
     * 1-禁止  0-允许
     */
    public int AllowLikesState;

    /**
     * 是否系统设置评论权限：system_comment_authority  1-是  0-否
     */
    public int SystemCommentAuthority;

    /**
     * 是否隐藏帖子：1-隐藏，2-取消隐藏
     */
    @JSONField(name = "noInList")
    public int F1;

    /**
     * 谁删除了用户的帖子
     * 1： 用户自己删除，2 系统删除
     */
    public String F3;

}

