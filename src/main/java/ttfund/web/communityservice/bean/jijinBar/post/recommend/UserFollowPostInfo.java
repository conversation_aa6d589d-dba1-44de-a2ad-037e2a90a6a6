package ttfund.web.communityservice.bean.jijinBar.post.recommend;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * AppHomePostInfo
 * app首页用户关注近一周帖子列表
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserFollowPostInfo {

    /**
     * 对应帖子ID
     */
    private String _id;
    /**
     * 帖子ID
     */
    //public long POSTID;
    public long ID;

    /**
     * 资讯ID，type=20 时 对应财富号文章ID
     */
    public String NEWSID;

    /**
     * 标题
     */
    public String TITLE;
    /**
     * 摘要（新版摘要中不包含关键字）
     */
    public String SUMMARY;

    /**
     * 通行证用户ID
     */
    public String UID;
    /**
     * 用户通行证昵称（不对外使用，通行证昵称会变化）
     */
    public String NICHENG;
    /**
     * 帖子类型
     */
    public int TYPE;

    /**
     * 所在吧
     */
    public String CODE;
    /**
     * 是否可用 1：可用，其它不可用
     */
    public int ISENABLED;

    /**
     * 删除状态 1：删除 0：未删除
     */
    public int DEL;

    /**
     * 天天基金删除状态 1：删除 0：未删除
     */
    public int TTJJDEL;

    /**
     * 发帖时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX", timezone = "GMT+8")
    public Date TIME;

    /**
     * 发帖时间戳
     */
    public long TIMEPOINT;

    /**
     * 帖子最后更新时间
     */
    @JsonFormat(
        shape = JsonFormat.Shape.STRING,
        pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", // 匹配格式：2025-03-21T21:58:23.814+08:00
        timezone = "GMT+8" // 显式指定时区（可选，但推荐）
        )
    public Date UPDATETIME;
    /**
     * 基金吧问答悬赏金额（小于等于0 不显示）
     */
    public BigDecimal AMOUNT;

    /**
     * 问题ID
     */
    public String QID;

    /**
     * 数据更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    public Date DATAUPDATETIME;
    /**
     * 回复列表(当前保留最新的一条)
     * 如果是问题帖获取回复列表数据，如果是普通帖走现有mongdb附加评论数据
     */
    public List<ReplyInfoView> REPLYS;

    /**
     * 财富号ID
     */
    public String CFHID;

    /**
     * 视频封面
     */
    public String VIDEOPIC;
    /**
     * 财富号文章类型
     * 1:普通文章 2：视频内嵌文章 3：纯视频文章
     * 对应枚举 EnumCFHArtType
     */
    public int CFHARTTYPE;

    /**
     * 视频列表
     */
    public List<CFHArticleVideoInfo> VIDEOS;

    /**
     * 封面图
     */
    public String PIC;

    /**
     * 图片数量
     */
    private int PICCOUNT;

    /**
     * 分数
     */
    public BigDecimal SCORE;

    public String get_id() {
        return String.valueOf(ID);
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    @JSONField(name = "PICCOUNT")
    public int getPICCOUNT() {
        try {
            if (PIC == null || PIC.length() == 0) {
                return 0;
            }
            return PIC.split(",").length;
        } catch (Exception ex) {
            return 0;
        }
    }

    public void setPICCOUNT(int PICCOUNT) {
        this.PICCOUNT = PICCOUNT;
    }

    public long getID() {
        return ID;
    }

    public void setID(long ID) {
        this.ID = ID;
    }

    public String getNEWSID() {
        return NEWSID;
    }

    public void setNEWSID(String NEWSID) {
        this.NEWSID = NEWSID;
    }

    public String getTITLE() {
        return TITLE;
    }

    public void setTITLE(String TITLE) {
        this.TITLE = TITLE;
    }

    public String getSUMMARY() {
        return SUMMARY;
    }

    public void setSUMMARY(String SUMMARY) {
        this.SUMMARY = SUMMARY;
    }

    public String getUID() {
        return UID;
    }

    public void setUID(String UID) {
        this.UID = UID;
    }

    public String getNICHENG() {
        return NICHENG;
    }

    public void setNICHENG(String NICHENG) {
        this.NICHENG = NICHENG;
    }

    public int getTYPE() {
        return TYPE;
    }

    public void setTYPE(int TYPE) {
        this.TYPE = TYPE;
    }

    public String getCODE() {
        return CODE;
    }

    public void setCODE(String CODE) {
        this.CODE = CODE;
    }

    public int getISENABLED() {
        return ISENABLED;
    }

    public void setISENABLED(int ISENABLED) {
        this.ISENABLED = ISENABLED;
    }

    public int getDEL() {
        return DEL;
    }

    public void setDEL(int DEL) {
        this.DEL = DEL;
    }

    public int getTTJJDEL() {
        return TTJJDEL;
    }

    public void setTTJJDEL(int TTJJDEL) {
        this.TTJJDEL = TTJJDEL;
    }

    public Date getTIME() {
        return TIME;
    }

    public void setTIME(Date TIME) {
        this.TIME = TIME;
    }

    public long getTIMEPOINT() {
        return TIMEPOINT;
    }

    public void setTIMEPOINT(long TIMEPOINT) {
        this.TIMEPOINT = TIMEPOINT;
    }

    public Date getUPDATETIME() {
        return UPDATETIME;
    }

    public void setUPDATETIME(Date UPDATETIME) {
        this.UPDATETIME = UPDATETIME;
    }

    public BigDecimal getAMOUNT() {
        return AMOUNT;
    }

    public void setAMOUNT(BigDecimal AMOUNT) {
        this.AMOUNT = AMOUNT;
    }

    public String getQID() {
        return QID;
    }

    public void setQID(String QID) {
        this.QID = QID;
    }

    public Date getDATAUPDATETIME() {
        return DATAUPDATETIME;
    }

    public void setDATAUPDATETIME(Date DATAUPDATETIME) {
        this.DATAUPDATETIME = DATAUPDATETIME;
    }

    public List<ReplyInfoView> getREPLYS() {
        return REPLYS;
    }

    public void setREPLYS(List<ReplyInfoView> REPLYS) {
        this.REPLYS = REPLYS;
    }

    public String getCFHID() {
        return CFHID;
    }

    public void setCFHID(String CFHID) {
        this.CFHID = CFHID;
    }

    public String getVIDEOPIC() {
        return VIDEOPIC;
    }

    public void setVIDEOPIC(String VIDEOPIC) {
        this.VIDEOPIC = VIDEOPIC;
    }

    public int getCFHARTTYPE() {
        return CFHARTTYPE;
    }

    public void setCFHARTTYPE(int CFHARTTYPE) {
        this.CFHARTTYPE = CFHARTTYPE;
    }

    public List<CFHArticleVideoInfo> getVIDEOS() {
        return VIDEOS;
    }

    public void setVIDEOS(List<CFHArticleVideoInfo> VIDEOS) {
        this.VIDEOS = VIDEOS;
    }

    public String getPIC() {
        return PIC;
    }

    public void setPIC(String PIC) {
        this.PIC = PIC;
    }

    public BigDecimal getSCORE() {
        return SCORE;
    }

    public void setSCORE(BigDecimal SCORE) {
        this.SCORE = SCORE;
    }
}
