package ttfund.web.communityservice.bean.jijinBar.enums;

/**
 * 基金吧用户类型
 */
public enum EnumPassportUserVType {


    /**
     * 机构
     */
    BlueOrgV(1) ,
    /**
     * 个人 橙V
     */
    OrangeV(2),
    /**
     * 个人 五角星
     */
    FiveStars(3),
    /**
     * 不加V
     */
    Default(0);


    //必须增加一个构造函数),变量),得到该变量的值
    private int  mState=0;
    private EnumPassportUserVType(int value) {
        mState = value;
    }
    /**
     * @return 枚举变量实际返回值
     */
    public int getValue() {
        return mState;
    }
}
