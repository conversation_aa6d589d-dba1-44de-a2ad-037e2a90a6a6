package ttfund.web.communityservice.bean.jijinBar.post.QA;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

/**
 * 回答
 *
 * <AUTHOR>
 */
public class AnswerEntity {

    /// <summary>
    /// 问题id
    /// </summary>
    public String QID;

    /// <summary>
    /// 回答ID
    /// </summary>
    public String AID;

    /// <summary>
    /// 回答帖子id
    /// </summary>
    public String ArticleID;
    /// <summary>
    /// 答主ID
    /// </summary>
    public String CreatorID;

    /// <summary>
    /// 创建时间
    /// </summary>
    @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss")
    public Date Created;
    /// <summary>
    /// 修改时间
    /// </summary>
    @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss")
    public Date Modified;
    /// <summary>
    /// 审核状态
    /// </summary>
    public int AuditStatusType;

    /// <summary>
    /// 是否采纳
    /// </summary>
    public boolean IsAdopted;

    /// <summary>
    /// 采纳类型
    /// </summary>
    public int AdopedType;

    /// <summary>
    /// 采纳时间
    /// </summary>
    @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss")
    public Date Adoped;

    /// <summary>
    /// 所属吧Code
    /// </summary>
    public String StockBarCode;

    /// <summary>
    /// 对接平台类型
    /// </summary>
    public int AppType;

    /// <summary>
    /// 是否是最佳答案
    /// </summary>
    public int IsBestAnswer;

    /**
     * 更新时间
     */
    public Date UpdateTime;

    /**
     * 是否有效
     */
    public int IsEnable;

}
