package ttfund.web.communityservice.bean.jijinBar.post.config;

import lombok.Data;

import java.util.Date;

/**
 * 帖子活动配置实体 -数据库中
 *
 * <AUTHOR>
 */
@Data
public class PostActivityConfigModelInDb {

    /**
     * 写mongo主键用
     */
    private String _id;

    /**
     * 活动绑定的帖子id
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 更新时间，写mongo用
     */
    private Date updateTime;

    /**
     * 来源 1:雨燕配置后台  2:配置后台-顾兴明
     */
    private String source;

    /**
     * 是否删除  0：否  1：是
     * 备注：该字段由本程序逻辑决定，非由配置源头同步过来
     */
    private String isDel;
}
