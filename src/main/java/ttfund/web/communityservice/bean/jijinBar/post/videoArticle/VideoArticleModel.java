package ttfund.web.communityservice.bean.jijinBar.post.videoArticle;

import lombok.Data;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import ttfund.web.communityservice.bean.jijinBar.annotation.MongSetOnInsert;

import java.util.List;

@Data
public class VideoArticleModel extends  VideoArticleBaseModel{
    /// <summary>
    /// 排序 置顶时设为 0,默认 Integer32最大值
    /// </summary>
    @MongSetOnInsert
    public Integer Order ;
    /// <summary>
    /// 后台关闭（0：未关闭,1:已关闭），首次设置，更新时不设置此字段
    /// 逻辑需要调整
    /// </summary>
    @MongSetOnInsert
    public Integer IsClosed ;
    /// <summary>
    /// 业务排序字段 给鲁容淼排序用
    /// {order}_{发布时间转unix}_{my_id}
    /// </summary>
    @MongSetOnInsert
    public Long TimePoint ;

    /// <summary>
    /// 对应FundInfo
    /// </summary>
    @MongSetOnInsert
    public List<FundInfo> FundInfoList ;

    /**
     * 1：天天基金机构用户  0：非机构用户
     */
    public  Integer IsTTOrg;

    /**
     * 单纯的帖子id
     * 【#536851】内部优化 将财富号文章id更换为股吧帖子id
     */
    public String GubaPostID;
}
