package ttfund.web.communityservice.bean.jijinBar.post.guba;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import ttfund.web.communityservice.config.dataconfig.EsConstantConfig;

import java.util.Date;

/**
 * 基金吧基本信息(新)
 *
 * <AUTHOR>
 * @date 2022/12/30
 */
@Data
@Document(indexName = EsConstantConfig.es_bar_index_fundbarinfo, type = EsConstantConfig.java_es_bar_type_barinfo,
        replicas = 1, shards = 5)
public class FundBarInfoNewModel {

    @org.springframework.data.annotation.Id
    @Field(type = FieldType.Keyword)
    public String Code;

    /**
     * 分词名称
     */
    @Field(type = FieldType.Text, name = "name", analyzer = "ik_max_word", searchAnalyzer = "ik_max_word")
    public String IK_Name;

    /**
     * 自定义分词diam
     */
    public String MY_Code;

    /**
     * 自定义分词名称
     */
    public String MY_Name;

    /**
     * 自定义分词名称 拼音
     */
    @Field(type = FieldType.Text, name = "MY_Name_Pinyin", analyzer = "my_ngram_analyzer")
    public String MY_Name_Pinyin;

    /**
     * 自定义分词名称 拼音首字母
     */
    @Field(type = FieldType.Text, name = "MY_Name_Initial", analyzer = "my_ngram_analyzer")
    public String MY_Name_Initial;

    /**
     * 通行证账号ID
     */
    @Field(type = FieldType.Keyword)
    public String PassportId;

    /**
     * 状态
     */
    public Long State;

    /**
     * 类型
     */
    public Integer Type;

    /**
     * 更新时间
     */
    @Field(type = FieldType.Date,format =DateFormat.custom,pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS",timezone="GMT+8")
    public String UpdateTime;

    /**
     * 更新时间
     */
    public Long UpdateTimePoint;

    /**
     * 子帐户VIP类型 2 机构 1 大v 0 普通
     */
    public Integer VIPType;

    /**
     * 关联财富号
     */
    @Field(type = FieldType.Keyword)
    public String CFHID;

    /**
     * 详情地址-投股吧小程序地址
     */
    @Field(type = FieldType.Keyword)
    public String DetailUrl;

    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    public Date RunTime;

    public Long RunTimePoint;
}
