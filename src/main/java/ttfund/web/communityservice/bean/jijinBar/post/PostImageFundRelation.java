package ttfund.web.communityservice.bean.jijinBar.post;

import org.springframework.data.mongodb.core.mapping.Field;
import ttfund.web.communityservice.bean.jijinBar.mongo.FundJBXXModel;

import java.util.List;

public class PostImageFundRelation {

    @Field("Image")
    private String image;

    @Field("Funds")
    private List<FundJBXXModel> funds;

    @Field("InvestmentAdvisers")
    private List<FundJBXXModel> investmentAdvisers;

    @Field("HighFinances")
    private List<FundJBXXModel> highFinances;

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public List<FundJBXXModel> getFunds() {
        return funds;
    }

    public void setFunds(List<FundJBXXModel> funds) {
        this.funds = funds;
    }

    public List<FundJBXXModel> getInvestmentAdvisers() {
        return investmentAdvisers;
    }

    public void setInvestmentAdvisers(List<FundJBXXModel> investmentAdvisers) {
        this.investmentAdvisers = investmentAdvisers;
    }

    public List<FundJBXXModel> getHighFinances() {
        return highFinances;
    }

    public void setHighFinances(List<FundJBXXModel> highFinances) {
        this.highFinances = highFinances;
    }
}
