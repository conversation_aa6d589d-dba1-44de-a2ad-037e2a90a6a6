package ttfund.web.communityservice.bean.jijinBar.post.guba;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

/**
 * 以下代码全是自动生成，请勿随意更改
 *
 * <AUTHOR>
 */
public class BlackList {

    /**
     * 操作ID
     */
    public String UID;

    /**
     * 被操作ID
     */
    @JSONField(name = "target_uid")
    public String TargetUID;

    /**
     * 操作时间
     */
    public Date Time;

    public boolean IsEnabled;

    public Date CreateTime;

    public Date UpdateTime;

    public String action_type;

}
