package ttfund.web.communityservice.bean.jijinBar.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 知识星球
 *
 * <AUTHOR>
 */
@Data
@ApiModel("用户开关")
public class UserSwitch {

    /**
     * id
     */
    private String _id;

    /**
     * 开关类型 1：公开自选基金 2：公开模拟组合 3：展示圈子定制化标签 4：知识星球推送
     */
    @ApiModelProperty("开关类型 1：公开自选基金 2：公开模拟组合 3：展示圈子定制化标签 4：知识星球推送")
    private Integer type;

    /**
     * 状态 0：关闭 1：开启
     */
    @ApiModelProperty("状态 0：关闭 1：开启")
    private Integer value;

    /**
     * 通行证ID
     */
    @ApiModelProperty("通行证ID")
    private String uid;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "更新时间")
    private Date updateTime;
}
