package ttfund.web.communityservice.bean.jijinBar.data;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 帖子置顶
 */
@TableName(value = "tb_set_top")
public class SetTopEntity {

    /**
     * 主键Id
     */
    public int Id;

    /**
     * 帖子id
     */
    public int TId;

    /**
     * 置顶类型
     */
    public String Type;

    /**
     * 置顶范围
     */
    public String Range;

    /**
     * 置顶平台
     */
    public String Plat;

    public int TimeSign;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    public Date StartTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    public Date EndTime;

    /**
     * 所在吧
     */
    public String From;

    /**
     * 状态1-有效 0-无效
     */
    public int State;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    public Date CreateTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    public Date UpdateTime;

    /**
     * 创建人
     */
    public String CreateUser;

    /**
     * 更新人
     */
    public String UpdateUser;

    /**
     * 帖子类型 1置顶 2推荐,3-大V用户帖子置顶
     */
    public int TType;

    /**
     * 展示位置
     */
    public int DisplayLocation;

    /**
     * 基金代码列表
     */
    public String CodeList;

    public long EliteOrderTime;
}
