package ttfund.web.communityservice.bean.jijinBar.post.config;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

/**
 * 帖子活动配置实体
 *
 * <AUTHOR>
 */
public class PostActivityConfigModel {

    /**
     * 写mongo主键用
     */
    private String _id;

    /**
     * 活动绑定的帖子id
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 帖子=post,小程序=miniProgram,其他=other
     */
    private String activityPageType;

    /**
     * 更新时间，写mongo用
     */
    private Date updateTime;

    public PostActivityConfigModel() {
    }

    public String get_id() { return _id; }

    public void set_id(String _id) { this._id = _id; }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public Date getStartTime() {
        return startTime;
    }

    @JSONField(format = "yyyy年MM月dd日 HH:mm:ss")
    public void setStartTime(Date startTime) { this.startTime = startTime; }

    public Date getEndTime() {
        return endTime;
    }

    @JSONField(format = "yyyy年MM月dd日 HH:mm:ss")
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getActivityPageType() {
        return activityPageType;
    }

    public void setActivityPageType(String activityPageType) {
        this.activityPageType = activityPageType;
    }

    public Date getUpdateTime() { return updateTime; }

    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
}
