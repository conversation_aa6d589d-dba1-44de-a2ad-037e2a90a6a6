package ttfund.web.communityservice.bean.jijinBar.data;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 圈子用户关系实体
 *
 * <AUTHOR>
 */
@Data
@ApiModel("圈子用户关系实体")
public class CircleUserRelation {

    /**
     * 圈子id
     */
    @ApiModelProperty("圈子id")
    private String circleId;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private String uid;

    /**
     * 用户类型 1管理员  0普通用户
     */
    @ApiModelProperty("用户类型 1管理员  0普通用户")
    private Integer role;

    /**
     * 状态  0正常  1已退出
     */
    @ApiModelProperty("状态  0正常  1已退出")
    private Integer state;

    /**
     * 入圈方式  0主动加入   1邀请加入
     */
    @ApiModelProperty("入圈方式  0主动加入   1邀请加入")
    private Integer joinType;

    /**
     * 入圈时间
     */
    @ApiModelProperty("入圈时间")
    private Date joinTime;

    /**
     * 退圈方式  0主动退出   1管理员踢出
     */
    @ApiModelProperty("退圈方式  0主动退出   1管理员踢出")
    private Integer leaveType;

    /**
     * 退圈时间
     */
    @ApiModelProperty("退圈时间")
    private Date leaveTime;

    /**
     * 是否删除
     */
    @JsonIgnore
    @ApiModelProperty(name = "是否删除", hidden = true)
    private Integer isDel;

    /**
     * 创建时间
     */
    @JsonIgnore
    @ApiModelProperty(name = "创建时间", hidden = true)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonIgnore
    @ApiModelProperty(name = "更新时间", hidden = true)
    private Date updateTime;

}
