package ttfund.web.communityservice.bean.jijinBar.user;


import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.util.Date;

/**
 * 通行证用户 交易绑定信息
 */
@JsonAutoDetect(fieldVisibility=JsonAutoDetect.Visibility.ANY, getterVisibility=JsonAutoDetect.Visibility.NONE)
public class PassportUserBindInfo {

    public String _id ;
    /// <summary>
    /// 通行证用户ID
    /// </summary>
    public String UID ;
    /// <summary>
    /// 昵称
    /// </summary>
    public String NICHENG ;
    /// <summary>
    ///交易系统 客户编号
    /// </summary>
    public String CUSTOMERNO ;
    /// <summary>
    /// 性别
    /// </summary>

    public String SEX ;
    /// <summary>
    /// 创建时间 
    /// </summary>
//        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
    public Date CREATETIME ;

    /// <summary>
    /// 更新时间
    /// </summary>
//        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
    public Date UPDATETIME ;
    /// <summary>
    /// OPENID
    /// </summary>
    public String OPENID ;
    /// <summary>
    /// 0无效 1有效
    /// </summary>
    public String ISENABLED ;
    
}
