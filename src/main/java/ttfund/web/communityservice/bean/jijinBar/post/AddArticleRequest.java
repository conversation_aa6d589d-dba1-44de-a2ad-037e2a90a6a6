package ttfund.web.communityservice.bean.jijinBar.post;

import java.util.Date;
import java.util.List;

/**
 * 运营后台发文
 *
 * <AUTHOR>
 */
public class AddArticleRequest {
    /**
     * 文章标题
     */
    public String Title;
    /**
     * 封面
     */
    public String ListImage;
    /**
     * 文章内容
     */
    public String Content;

    /**
     * 排序时间 默认值 now
     */
    public Date Ordertime;
    /**
     * 展示时间 默认值 now
     */
    public Date Showtime;

    /**
     * 置顶 - 开始时间
     */
    //  [JsonProperty("topStartTime",DefaultValueHandling =DefaultValueHandling.Ignore, NullValueHandling =NullValueHandling.Ignore)]
    public Date TopStartTime;

    /**
     * 置顶 - 结束时间
     */
    //   [JsonProperty("topEndTime",DefaultValueHandling =DefaultValueHandling.Ignore, NullValueHandling =NullValueHandling.Ignore)]
    public Date TopEndTime;

    /**
     * 文章所属栏目 默认值是2
     */
    public String ColumnIds;
    public String Ip;
    /**
     * 是否纯视频文章 1是纯视频 0不是
     */
    public int IsSimpleVideo;
    /**
     * 视频信息
     */
    public List<ArticleVideo> Videos;

    /**
     * 相关个股
     */
    public List<ArticleStock> Stocks;


    /**
     * 是否声明原创 0 否 1 是
     */
    public int IsOriginal;

    //话题id 默认值209
    public Integer GubaTalkId;

    ///是否临时用户： 0=否（默认，财富号用户），1=是
    public int IsVistor;

    ///临时用户通行证uid
    public String PassportUid;

    /**
     * 0：普通导入 1：抓取导入
     */
    public int ImportType;

    /**
     * 发帖端口号 默认80
     */
    public String Port;

    /**
     * 文章类型 0. 普通文章  1. 普通视频文章  2. 纯视频文章 3：纯音频文章 4：音频文章 5：音视频文章
     */
    public int ArticleType;

    /**
     * 设备号 默认值"8C67557BE560F5454573A4F9CDF472F6"
     */
    public String DeviceId;

    /**
     * 版本 默认值200
     */
    public String Version;

    /**
     * 产品默认值CFH
     */
    public String Product;

    /**
     * 平台 默认值web
     */
    public String Plat;

    /**
     * 业务Id NewGuid
     */
    public String BusinessId;

    /**
     * 发文源头标识 默认值 LKFW
     */
    public String PlatFrom;
    /**
     * 是否显示目录
     */
    public int CatalogPattern;

    /**
     * 是否开启评论
     * 0：关闭
     * 1：开启
     */
    public int IsComment;

    /**
     * 是否推荐阅读
     */
    public int IsReCommendRead;

    /**
     * 发布位置 默认值0
     */
    public int ArticleTag;

    /**
     * 发布模块 默认值“1,2”
     */
    public String Categories;

    /**
     * 内容标签 默认值“1,2”
     */
    public String Tabs;

    /**
     * 选择领域 用于标签显示 默认值 “直播”
     */
    public String DomainLab;

    /**
     * 测试环境和生产环境默认值不同
     */
    public List<ArticleDomainColumnItem> Columns;

    /**
     * 财富号浪客视频id   饶忠彬那里需要
     */
    public String ZMTLKVideoID;

    /**
     * 房间类型
     */
    public int RoomType;

    /**
     * 0不置顶，1置顶
     */
    public int IsSetTop;

    /**
     * 操作人
     */
    public String OperaterName;

}
