package ttfund.web.communityservice.bean.jijinBar.post.recommend;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

@Data
@JsonAutoDetect(fieldVisibility=JsonAutoDetect.Visibility.ANY, getterVisibility=JsonAutoDetect.Visibility.NONE)
public class PostRecommendModelExt extends PostRecommendModel{

    /// <summary>
    /// 关联基金
    /// </summary>
    public String CODELIST;

    /// <summary>
    /// 天天基金删除
    /// </summary>
    public Integer TTJJDEL;
}
