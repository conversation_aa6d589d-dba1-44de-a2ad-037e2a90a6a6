package ttfund.web.communityservice.bean.jijinBar.post.elitepost;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;


@JsonAutoDetect(fieldVisibility=JsonAutoDetect.Visibility.ANY, getterVisibility=JsonAutoDetect.Visibility.NONE)
@Document("ElitePost")
@Data
public class ElitePostModel {

    /// <summary>
    /// 帖子ID
    /// </summary>
    public String _id ;
    /// <summary>
    /// 帖子id
    /// </summary>
    public long ID ;
    /// <summary>
    /// 用户通信证ID
    /// </summary>
    public String UID ;
    /// <summary>
    /// 基金代码
    /// </summary>
    public String CODE ;
    /// <summary>
    /// 发帖时间
    /// </summary>
       // [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
    public Date TIME ;
    /// <summary>
    /// 更新时间
    /// </summary>
       // [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
    public Date UPDATETIME ;
    /// <summary>
    /// 时间戳
    /// </summary>
    public Long TIMEPOINT ;
    /// <summary>
    /// 原帖ID
    /// </summary>
    public Integer YUANID ;
    /// <summary>
    /// 帖子类型
    /// </summary>
    public Integer TYPE ;
    /// <summary>
    /// 原帖类型
    /// </summary>
    public Integer YUANTYPE ;
    /// <summary>
    /// 得分
    /// </summary>
    public Double SCORE ;

    /// <summary>
    /// 是否是新的计算方式
    /// </summary>
    public Integer IsNew ;

    /// <summary>
    /// 1,基金吧配置后台(热点小程序)，2，根据规则计算筛选得分
    /// </summary>
    public Integer Source ;
}
