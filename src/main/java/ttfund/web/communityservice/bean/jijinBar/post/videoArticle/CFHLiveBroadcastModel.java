package ttfund.web.communityservice.bean.jijinBar.post.videoArticle;

import lombok.Data;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.springframework.util.CollectionUtils;
import ttfund.web.communityservice.utils.StringUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class CFHLiveBroadcastModel {


    public String _id ;

    /// <summary>
    /// 评论奖励金额
    /// </summary>
    public Integer CommentRewardCount ;
    /// <summary>
    /// 评论奖励次数限制
    /// </summary>
    public Integer CommentRewardLimit ;

    /// <summary>
    /// 分享奖励次数
    /// </summary>
    public Integer ShareRewardCount ;
    /// <summary>
    /// 分享奖励金额
    /// </summary>
    public Integer ShareRewardLimit ;
    /// <summary>
    /// 翻牌奖励金额
    /// </summary>
    public Integer TurnOverRewardCount ;

    /// <summary>
    /// 大咖秀场次ID
    /// </summary>
    public int LiveId ;
    /// <summary>
    /// 是否删除 0：未删除  1：删除
    /// </summary>
    public int IsDelete ;

    /// <summary>
    /// 帖子ID
    /// </summary>
    public String PostId ;
    /// <summary>
    /// 大咖通行证ID
    /// </summary>

    public String UserUid ;

    /// <summary>
    /// 结束时间
    /// </summary>
    //   [BsonDateOptions(Kind = DateKind.Local)]
    public Date EndTime ;

    /// <summary>
    /// 开始时间
    /// </summary>
    //   [BsonDateOptions(Kind = DateKind.Local)]
    public Date StartTime ;

    /// <summary>
    /// 房间号
    /// </summary>
    public String RoomNumber ;
    /// <summary>
    /// 直播直播-助理信息
    /// </summary>
    public List<LiveUid> LiveUidModel ;

    /// <summary>
    /// 可以做为回复用户信息发奖凭证的通行证ID列表
    /// </summary>
    @BsonIgnore
    public List<String> getReplyPassportIds() {

            List<String> list = new ArrayList<>();
            //大咖通行证ID
            if (!StringUtil.isNull(UserUid)) {
                list.add(UserUid);
            }

            //助手通行证ID
            if (LiveUidModel != null && LiveUidModel.size() > 0) {
                List<LiveUid> tempList = LiveUidModel.stream().filter(a->a.getUnabled()==0).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(tempList)) {
                    List<String> uids = tempList.stream().map(LiveUid::getUID).collect(Collectors.toList());
                    list.addAll(uids);
                }
            }
            return list.stream().distinct().collect(Collectors.toList());
    }

    /// <summary>
    /// 1 : 走积分中心
    /// </summary>
    public Integer DataFromType ;
}
