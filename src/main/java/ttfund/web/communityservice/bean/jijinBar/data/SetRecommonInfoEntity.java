package ttfund.web.communityservice.bean.jijinBar.data;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class SetRecommonInfoEntity {

    public int Id;

    public String MId;

    public String ItemCode;

    public String ItemName;

    public int IsDel;

    public String CreateUser;

    public String UpdateUser;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    public Date CreateTime;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    public Date UpdateTime;

    public String UserProfile;
}
