package ttfund.web.communityservice.bean.jijinBar.post.guba;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

/**
 * 基金吧数据
 *
 * <AUTHOR>
 * @date 2022/12/30
 */
@TableName("tb_fund_bar")
public class FundBarModel {
    /// <summary>
    /// 基金吧内码做ID
    /// </summary>
    public String ID;
    /// <summary>
    ///基金吧外码 不带43-前缀
    /// </summary>
    public String BarCodeOut;
    /// <summary>
    /// 基金吧内码
    /// </summary>
    public String BarCode;
    /// <summary>
    /// 吧类型
    /// (1基金吧，2配置吧，3实盘组合吧，4策略吧（570及以前是2）)
    /// </summary>
    public Integer BarType;
    /// <summary>
    /// 基金吧名称
    /// </summary>
    public String BarName;

    /// <summary>
    /// 基金名称
    /// </summary>
    public String Name;
    /// <summary>
    /// 基金管理人通行证账户ID 只有实盘吧才会有数据
    /// </summary>
    public String AdminPassportId;

    /// <summary>
    /// 0 未删除 1 删除
    /// </summary>
    public Integer Del;
    /// <summary>
    /// 创建时间
    /// </summary>
    public Date CreatTime;
    /// <summary>
    /// 更新时间
    /// </summary>
    public Date UpDateTime;

    /// <summary>
    /// 关联财富号ID 投顾吧用
    /// </summary>
    public String CFHID;

    /// <summary>
    /// 详情地址
    /// </summary>
    public String DetailUrl;
}
