package ttfund.web.communityservice.bean.jijinBar.post.ai;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * AI多机构总结实体类
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@Data
@Document(collection = "ai_multi_summaries")
public class AiMultiSummaryModel {

    /**
     * 主键
     */
    @Id
    private String _id;

    /**
     * 帖子ID列表（多个ID用逗号分隔）
     */
    @Field("postIds")
    private String postIds;

    /**
     * 大V通行证ID列表（多个UID用逗号分隔）
     */
    @Field("uids")
    private String uids;

    /**
     * 大V昵称列表（多个昵称用逗号分隔）
     */
    @Field("nickNames")
    private String nickNames;

    /**
     * 大V用户数量
     */
    @Field("userCount")
    private Integer userCount;

    /**
     * AI总结内容（完整版，包含精简版和详细版）
     */
    @Field("summary")
    private String summary;

    /**
     * AI总结精简版
     */
    @Field("summaryBrief")
    private String summaryBrief;

    /**
     * AI总结详细版
     */
    @Field("summaryDetailed")
    private String summaryDetailed;

    /**
     * 任务ID
     */
    @Field("taskId")
    private String taskId;

    /**
     * 处理状态
     */
    @Field("status")
    private String status;

    /**
     * 时间段（如：08:30-10:30, 10:30-13:30, 10:30-14:30, 14:30-17:30）
     */
    @Field("timeSlot")
    private String timeSlot;

    /**
     * 处理日期
     */
    @Field("processDate")
    private Date processDate;

    /**
     * 创建时间
     */
    @Field("createTime")
    private Date createTime;

    /**
     * 更新时间
     */
    @Field("updateTime")
    private Date updateTime;
}
