package ttfund.web.communityservice.bean.jijinBar.post.postInfo;

import lombok.Data;
import ttfund.web.communityservice.bean.jijinBar.post.FundPostExtrasProP;

import java.util.Date;
import java.util.List;

/**
 * 定时发帖信息实体
 *
 * <AUTHOR>
 */
@Data
public class TimingPostInfoModel {

    /**
     * 主键
     */
    private String _id;

    /**
     * 用户通行证id
     */
    private String uid;

    /**
     * 吧代码
     */
    private String code;

    /**
     * 吧类型。jjcp：普通基金吧 ， tgcp：投顾吧  ， 不传：表示组合吧
     */
    private String jtype;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String text;

    /**
     * 图片
     */
    private String pic;

    /**
     * 是否有话题
     */
    private Integer isTopic;

    /**
     * 扩展信息
     */
    private List<FundPostExtrasProP> extraList;

    /**
     * 客户端ip
     */
    private String ip;

    /**
     * 期望发帖时间
     */
    private Date expectTime;

    /**
     * 状态。 0：待发帖， 1：发帖成功， 2：发帖失败
     */
    private Integer state;

    /**
     * 失败原因
     */
    private String message;

    /**
     * 生成帖子id
     */
    private String postId;

    /**
     * 实际发帖时间
     */
    private Date actualTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Integer isDel;

    public String printString() {
        return "{" +
                "_id='" + _id + '\'' +
                ", expectTime=" + expectTime +
                ", state=" + state +
                ", message='" + message + '\'' +
                ", postId='" + postId + '\'' +
                ", actualTime=" + actualTime +
                '}';
    }
}
