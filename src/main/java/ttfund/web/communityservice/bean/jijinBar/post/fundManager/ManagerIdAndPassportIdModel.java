package ttfund.web.communityservice.bean.jijinBar.post.fundManager;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

/**
 * 基金经理id到通行证id绑定关系实体
 *
 * <AUTHOR>
 */
@Document(BarMongodbConfig.TABLE_PassportFundMrg)
public class ManagerIdAndPassportIdModel {

    /**
     * _id
     */
    @Id
    private String id;

    /**
     * 基金经理id
     */
    @Field("MGRID")
    private String managerId;

    /**
     * 通行证id
     */
    @Field("PassportUID")
    private String passportId;

    public ManagerIdAndPassportIdModel() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getManagerId() {
        return managerId;
    }

    public void setManagerId(String managerId) {
        this.managerId = managerId;
    }

    public String getPassportId() {
        return passportId;
    }

    public void setPassportId(String passportId) {
        this.passportId = passportId;
    }

    @Override
    public String toString() {
        return "ManagerIdAndPassportIdModel{" +
                "id='" + id + '\'' +
                ", managerId='" + managerId + '\'' +
                ", passportId='" + passportId + '\'' +
                '}';
    }
}
