package ttfund.web.communityservice.bean.jijinBar.user;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Date;

@JsonAutoDetect(fieldVisibility=JsonAutoDetect.Visibility.ANY, getterVisibility=JsonAutoDetect.Visibility.NONE)
public class KafkaPassportUserModel {
    /**
     * 通行证账号ID
     */
    public String PassportID;

    /**
     * 基金openid
     */
    public String OpenID ;
    /**
     * 昵称
     */
    public String NickName ;
    /**
     * 性别
     * 0:保密
     * 1：男
     * 2：女
     */
    public String Gender ;
    /**
     * 大V类型
     * 301 个人-理财师,
     * 302 个人-普通个人
     * 303 官方-企业官方
     * 401 基金经理加V
     * 402 基金公司
     * 403
     * 404
     */
    public String Vtype;
    /**
     * 大v状态
     * 空:未认证
     * 0：审核通过
     * 1：审核未通过
     * 2.审核中
     * 8：用户取消加V
     * 9.管理员取消加V
     */
    public String VtypeStatus;
    /**
     * 注册时间
     */
    public String Registertime;
    /**
     * 注册时间，时间格式
     */
    public String RegisterDateTime;
    /**
     * 财富号ID
     */
    public String CaifuhaoID;
    /**
     * 个人简介
     */
    public String Introduce;
    /**
     * kafka创建数据的创建时间
     * redis 序列化时忽略该字段
     *
     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
     */
    @JsonIgnore
    public Date CreatTime;
    /**
     * kafka 更新数据时间
     * redis序列化时忽略该字段
     *
     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    public Date UpdateTime;
}
