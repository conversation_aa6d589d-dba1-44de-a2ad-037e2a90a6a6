package ttfund.web.communityservice.bean.jijinBar.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 知识星球
 *
 * <AUTHOR>
 */
@Data
@ApiModel("知识星球")
public class GroupSceneKnowledgePlanet {

    /**
     * 群组id
     */
    @ApiModelProperty("群组id")
    private Integer groupId;

    /**
     * 群组类型 1：圈子 2：知识星球
     */
    @ApiModelProperty("群组类型 1：圈子 2：知识星球")
    private Integer groupType;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Integer version;

    /**
     * 可见性 0：公开、 1：仅对加入用户
     */
    @ApiModelProperty("可见性 0：公开、 1：仅对加入用户")
    private Integer visibility;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 头像
     */
    @ApiModelProperty("头像")
    private String avatar;

    /**
     * 简介
     */
    @ApiModelProperty("简介")
    private String briefIntroduction;

    /**
     * 介绍
     */
    @ApiModelProperty("介绍")
    private String introduction;

    /**
     * 亮点
     */
    @ApiModelProperty("亮点")
    private String brightIntroduction;

    /**
     * 不匹配描述
     */
    @ApiModelProperty("不匹配描述")
    private String notMatchDescription;

    /**
     * 企微标题
     */
    @ApiModelProperty("企微标题")
    private String weComTitle;

    /**
     * 企微副标题
     */
    @ApiModelProperty("企微副标题")
    private String weComSubtitle;

    /**
     * 企微地址
     */
    @ApiModelProperty("企微地址")
    private String weComUrl;

    /**
     * 须知
     */
    @ApiModelProperty("须知")
    private String notice;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createUser;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateUser;

    /**
     * 删除状态 0否  1是
     */
    @ApiModelProperty("删除状态 0否  1是")
    private String isDel;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "更新时间")
    private Date updateTime;
}
