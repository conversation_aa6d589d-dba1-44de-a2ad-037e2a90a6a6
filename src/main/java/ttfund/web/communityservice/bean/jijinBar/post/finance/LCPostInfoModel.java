package ttfund.web.communityservice.bean.jijinBar.post.finance;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import ttfund.web.communityservice.bean.jijinBar.annotation.MongSetOnInsert;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.Date;

/**
 * 理财页发现-帖子信息
 *
 * <AUTHOR>
 */
@Document(value = BarMongodbConfig.TABLE_LC_POST_INFO)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LCPostInfoModel {

    /**
     * 主键，帖子ID
     */
    @Id
    private String id;

    /**
     * 帖子ID
     */
    @Field("ID")
    private Long tid;

    /**
     * 通行证ID
     */
    @Field("UID")
    private String uid;

    /**
     * 发帖时间
     */
    @Field("TIME")
    private Date time;

    /**
     * 时间戳
     */
    @Field("TIMEPOINT")
    private Long timePoint;

    /**
     * 创建时间
     */
    @Field("CREATETIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @Field("UPDATETIME")
    private Date updateTime;

    /**
     * 是否已删除，0-未删除，1-已删除
     */
    @Field("ISDEL")
    @MongSetOnInsert
    private Integer isDel;

    /**
     * 得分
     */
    @Field("SCORE")
    private Long score;

    /**
     * 排序
     */
    @Field("SORT")
    @MongSetOnInsert
    private Long sort;

    /**
     * 封面图
     */
    @Field("COVER")
    private String cover;

    /**
     * 封面图比例
     */
    @Field("COVERRATIO")
    private String coverRatio;

    /**
     * 推荐标签
     */
    @Field("LABEL")
    @MongSetOnInsert
    private Integer label;

    /**
     * 来源 1-作者底池  2-单独添加的帖子
     */
    @Field("SOURCE")
    @MongSetOnInsert
    private Integer source;

    /**
     * 问题ID
     */
    @Field("QID")
    private String qid;

    /**
     * 基金吧代码
     */
    @Field("CODE")
    private String code;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTid() {
        return tid;
    }

    public void setTid(Long tid) {
        this.tid = tid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Long getTimePoint() {
        return timePoint;
    }

    public void setTimePoint(Long timePoint) {
        this.timePoint = timePoint;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public Long getScore() {
        return score;
    }

    public void setScore(Long score) {
        this.score = score;
    }

    public Long getSort() {
        return sort;
    }

    public void setSort(Long sort) {
        this.sort = sort;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getCoverRatio() {
        return coverRatio;
    }

    public void setCoverRatio(String coverRatio) {
        this.coverRatio = coverRatio;
    }

    public Integer getLabel() {
        return label;
    }

    public void setLabel(Integer label) {
        this.label = label;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getQid() {
        return qid;
    }

    public void setQid(String qid) {
        this.qid = qid;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
