package ttfund.web.communityservice.bean.jijinBar.data;

import lombok.Data;

/**
 * 模拟组合推送消息实体
 *
 * <AUTHOR>
 */
@Data
public class ModelPortfolioKafkaModel {

    /**
     * 模拟组合代码 MP+%10d
     */
    private String subAccountNo;

    /**
     * 模拟组合名称
     */
    private String subAccountName;

    /**
     * 管理人通行证id
     */
    private String passportId;

    /**
     * 组合状态  1-正常  2-解散 3-解散中
     */
    private String state;

    /**
     * 公开状态  1-未公开 ,2-公开,3-停止公开,4-限制公开
     */
    private String openState;

}
