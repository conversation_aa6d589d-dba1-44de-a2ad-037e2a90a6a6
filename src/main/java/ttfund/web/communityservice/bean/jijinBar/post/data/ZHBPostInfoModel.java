package ttfund.web.communityservice.bean.jijinBar.post.data;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

import java.util.Date;
import java.util.List;

@TableName(value = "tb_postinfo_new")
public class ZHBPostInfoModel {

    /**
     * 帖子ID
     */
    public String ID;

    /**
     * 发帖时间
     */
    public Date TIME;

    /**
     * 发帖人ID
     */
    public String UID;

    /**
     * 所在吧
     */
    public String CODE;

    /**
     * 帖子类型
     */
    public int TYPE;

    /**
     * 删除状态
     */
    public int DEL;

    /**
     * 更新时间
     */
    public Date UPDATETIME;

    /**
     * 基金组合管理人通行证ID
     */
    public String AdminPassportId;
}
