package ttfund.web.communityservice.bean.jijinBar.search;

public enum EnumAppFuncType {

    /**
     * 功能
     */
    APP(1) ,
    /**
     * 小程序
     */
    SmallApp(2),
    /**
     * 彩蛋
     */
    EasterEgg (3),
    /**
     * banner 广告位
     */
    Banner(4),

    /**
     * 新版功能
     * APPNew
     */
    APPNew(5);

    //必须增加一个构造函数,变量,得到该变量的值
    private int  mState=0;
    private EnumAppFuncType(int value) {
        mState=value;
    }
    /**
     * @return 枚举变量实际返回值
     */
    public int getValue() {
        return mState;
    }
}
