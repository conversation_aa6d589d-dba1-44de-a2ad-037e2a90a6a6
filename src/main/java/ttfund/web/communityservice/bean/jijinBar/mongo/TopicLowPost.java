package ttfund.web.communityservice.bean.jijinBar.mongo;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import ttfund.web.communityservice.bean.jijinBar.annotation.MongSetOnInsert;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.Date;

/**
 * 话题水帖
 */
@Document(BarMongodbConfig.TABLE_TOPIC_LOW_POST)
public class TopicLowPost {

    @Id
    private String _id;

    @Field("TopicId")
    private String topicId;

    @Field("PostId")
    private String postId;

    @Field("Low")
    private Integer low;

    @Field("IsDel")
    private int isDel;

    @MongSetOnInsert
    @Field("CreateTime")
    private Date createTime;

    @Field("UpdateTime")
    private Date updateTime;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getTopicId() {
        return topicId;
    }

    public void setTopicId(String topicId) {
        this.topicId = topicId;
    }

    public String getPostId() {
        return postId;
    }

    public void setPostId(String postId) {
        this.postId = postId;
    }

    public Integer getLow() {
        return low;
    }

    public void setLow(Integer low) {
        this.low = low;
    }

    public int getIsDel() {
        return isDel;
    }

    public void setIsDel(int isDel) {
        this.isDel = isDel;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
