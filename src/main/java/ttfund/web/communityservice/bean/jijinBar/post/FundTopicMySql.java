package ttfund.web.communityservice.bean.jijinBar.post;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import ttfund.web.communityservice.utils.DateUtil;

import java.lang.String;
import java.sql.Time;
import java.util.Date;

/**
 * @Author: ShaMo
 * @Date: 2023/4/3
 * @ApiNote:
 * @Status:
 */
@Data
public class FundTopicMySql {
    private String htid;
    private String name;
    private String summary;
    private String showtime;
    private String relateStock;
    @JSONField(name = "tsystem")
    private Integer isEnabled;
    @JSONField(name = "ctime")
    private String createTime;
    // 更新时间自己填写
    private String updateTime;

    public void setShowtime(String showtime) {
        if (showtime != null) {
            if (showtime.contains("+")) {
                showtime = DateUtil.offsetDateTimeToStr(showtime, DateUtil.dateTimeDefaultPattern);
            }
        }
        this.showtime = showtime;
    }

    public void setCreateTime(String createTime) {
        if (createTime != null) {
            if (createTime.contains("+")) {
                createTime = DateUtil.offsetDateTimeToStr(createTime, DateUtil.dateTimeDefaultPattern);
            }
        }
        this.createTime = createTime;
    }
}
