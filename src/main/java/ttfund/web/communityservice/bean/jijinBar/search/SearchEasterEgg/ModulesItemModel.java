package ttfund.web.communityservice.bean.jijinBar.search.SearchEasterEgg;

import lombok.Data;

import java.util.List;

@Data
public class ModulesItemModel {

    /// <summary>
    ///
    /// </summary>
    public int ModuleType;
    /// <summary>
    ///
    /// </summary>
    public CustomObjectModel CustomObject ;
    /// <summary>
    ///
    /// </summary>
    public List<CustomArrayItemModel> CustomArray;
    /// <summary>
    ///
    /// </summary>
    public int SubModuleType ;
    /// <summary>
    ///
    /// </summary>
    public String ModuleCode;
    /// <summary>
    ///
    /// </summary>
    public String Title;
    /// <summary>
    ///
    /// </summary>
    public String SubTitle;
    /// <summary>
    ///
    /// </summary>
    public String UpdateReminder;
    /// <summary>
    ///
    /// </summary>
    public String UpdateNode;
    /// <summary>
    ///
    /// </summary>
    public String LockSort;
    /// <summary>
    ///
    /// </summary>
    public String MText;
}
