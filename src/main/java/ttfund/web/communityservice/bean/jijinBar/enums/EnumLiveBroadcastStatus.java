package ttfund.web.communityservice.bean.jijinBar.enums;

public enum EnumLiveBroadcastStatus {


//        草稿 = 0,
    /**
     * 草稿
     */
    Draf(0),
    /**
     * 提交审核
     */
    SubmitAduit(1),
    /**
     * 运营审核成功
     */
    YYAduitSucess(2),
    /**
     * 法务审核成功
     * 法务审核成功 = 3,
     */
    FWAduit(3),

    // 运营审核失败 = 4,
    /**
     * 运营审核失败
     */
    YYAduitFail(4),
    /**
     * 法务审核失败
     */
    FWAduitFail(5);


    private int mState = 0;

    private EnumLiveBroadcastStatus(int value) {
        mState = value;
    }

    /**
     * @return 枚举变量实际返回值
     */
    public int getValue() {
        return mState;
    }
}
