package ttfund.web.communityservice.bean.jijinBar.post.config;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 帖子正文banner广告位
 *
 * <AUTHOR>
 */
public class FundArticleAdConfig {

    /**
     * 主键
     */
    public String _id;

    /**
     * 背景图片
     */
    public String BackUrl;

    /**
     * 跳转地址
     */
    public String LinkUrl;

    /**
     * 跳转类型
     */
    public int LinkType;

    /**
     * 开始时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss.SSS")
    public Date StartTime;

    /**
     * 结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss.SSS")
    public Date EndTime;

    /**
     * 状态 1-正常 0-删除
     */
    public int State;

    /**
     * 广告展示位置类型   0帖子正文   1资讯正文
     */
    public int Type;

    /**
     * 广告类型 0图片 1文字
     */
    public int ShowType;

    /**
     * 文字
     */
    public String Text;

    /**
     * 具体位置 0评论区（原有数据）1.详情页评论上方  2内容顶部  3内容底部  4正文顶部轮播/文字按钮弹窗  5正文底部随机展示
     */
    public int ShowPosition;

    /**
     * 帖子ID   空：全部帖子   非空：具体帖子
     */
    public String Pid;

    /**
     * 按钮文案
     */
    public String Button;

    /**
     * 排序
     */
    public int Sort;

    /**
     * 用户组
     */
    public String UserGroup;

    /**
     * 用户组类型。  1交易账号  2通行证账号
     */
    public Integer UserGroupType;

    /**
     * 更新时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss.SSS")
    public Date UpdateTime;

}
