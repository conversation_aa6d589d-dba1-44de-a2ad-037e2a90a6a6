package ttfund.web.communityservice.bean.jijinBar.post.config;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

/**
 * 大数据配置实体
 *
 * <AUTHOR>
 */
public class BigDataConfigEntity {

    /**
    * 链接Id
    */
    public String id;
    /**
    * 榜单分类
    */
    public String ClCategory;
    /**
    * 榜单编号
    */
    public String ClType;
    /**
    * 数据策略编号
    */
    public String DataType;
    /**
    * 榜单标题
    */
    public String Title;
    /**
    * 榜单副标题
    */
    public String SubTitle;
    /**
    * 副标题颜色
    */
    public String SubColor;
    /**
    * Tip 榜单文本
    */
    public String Text;
    /**
    * 是否显示文本
    */
    public String IsTextShow;
    /**
    * 榜单列表阶段中文描述
    */
    public String ShowMark;
    /**
    * 榜单跳转Code
    */
    public String Link;
    /**
    * 列表及详情低收益率过滤
    */
    public String FilterSyl;
    /**
    * 详情页排序字段
    */
    public String SortField;
    /**
    * 详情页排序类型
    */
    public String SortType;
    /**
    * 列表页排序字段
    */
    public String ShowSort;
    /**
    * 列表页排序类型
    */
    public String ShowSortType;
    /**
    * 列表关键字字体颜色
    */
    public String FontColor;
    /**
    * 详情页基金类型, 1是股票型，2是混合型，3是债券型，4是指数型，5是QDII
    */
    public String ShowType;
    /**
    * 详情页基金类型, 1是股票型，2是混合型，3是债券型，4是指数型，5是QDII
    */
    public String ShowTypeName;
    /**
    * 详情页对应基金类型收益阶段
    */
    public String Period;
    /**
    * 收益描述中文描述
    */
    public String PeriodText;
    /**
    * 列表及详情榜单模板类型
    */
    public String SType;
    /**
    * 列表单位换算 1000
    */
    public String Showunit;
    /**
    * 列表单位文案 万
    */
    public String ShowunitMark;
    /**
    * 列表单位 人
    */
    public String ShowunitMark2;
    /**
    * 详情 1000
    */
    public String DShowunit;
    /**
    * 详情 万
    */
    public String DShowunitMark;
    /**
    * 详情 人
    */
    public String DShowunitMark2;

    /**
    * 详情页榜单描述
    */
    public String Description;
    /**
    * 详情页头部图片
    */
    public String Pic;
    /**
    * 详情页文字介绍的跳转
    */
    public String Url;

    /**
    * 详情页列表中基金数量
    */
    public String PS;
    /**
    * 详情页图文介绍
    */
    public String SPic;
    /**
    * 详情页表头文字
    */
    public String Display2Mark;
    /**
    * 详情页表头文字
    */
    public String Display3Mark;
    /**
    * 开始版本
    */
    public String StartVersion;
    /**
    * 结束版本
    */
    public String EndVersion;

    /**
    * 排序
    */
    public int Sort;

    /**
    * 最后更新时间 处理后
    */
    @JSONField(name = "UpdateDate")
    public String UpdateDateTemp;
    /**
     * 最后更新时间
     */
    @JSONField(deserialize = false)
    public Date UpdateDate;
    /**
    * 最后更新用户
    */
    public String UpdateUser;
    /**
    * 逻辑删除
    */
    public int IsDel;

}
