package ttfund.web.communityservice.bean.jijinBar.post.guba;

import org.apache.commons.lang3.StringUtils;

/**
 * 吧基本信息
 *
 * <AUTHOR>
 */
public class BarInfoModel {
    /**
     * 吧code(对外业务用此字段)
     */
    private String barCodeOut;

    public String getBarCodeOut() {
        return !StringUtils.isEmpty(barCodeOut) ? barCodeOut.toLowerCase() : "";
    }

    public void setBarCodeOut(String barCodeOut) {
        this.barCodeOut = barCodeOut;
    }

    /**
     * 吧code(基金吧内部使用)
     */

    private String barCode;

    public String getBarCode() {
        return !StringUtils.isEmpty(barCode) ? barCode.toLowerCase() : "";
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    /**
     * 吧类型(0未知,1基金吧，2配置吧，3实盘组合吧，4策略吧（570及以前是2）)，5投顾吧
     */
    private int barType;

    /**
     * 吧名
     */
    private String barName;

    /**
     * 管理人通行证ID
     */
    private String adminPassportId;


    /**
     * 吧跳转地址（部分类型吧可以使用）如：5投顾吧；【该数据源——秦业的配置后台】
     */
    private String barUrl;


    /**
     * 吧对应的小程序id（部分类型吧可以使用）如：5投顾吧；【该数据源——秦业的配置后台】
     */
    private String detailUrl;

    public int getBarType() {
        return barType;
    }

    public void setBarType(int barType) {
        this.barType = barType;
    }

    public String getBarName() {
        return barName;
    }

    public void setBarName(String barName) {
        this.barName = barName;
    }

    public String getAdminPassportId() {
        return adminPassportId;
    }

    public void setAdminPassportId(String adminPassportId) {
        this.adminPassportId = adminPassportId;
    }

    public String getBarUrl() {
        return barUrl;
    }

    public void setBarUrl(String barUrl) {
        this.barUrl = barUrl;
    }

    public String getDetailUrl() {
        return detailUrl;
    }

    public void setDetailUrl(String detailUrl) {
        this.detailUrl = detailUrl;
    }
}
