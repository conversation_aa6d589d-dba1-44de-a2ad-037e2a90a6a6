package ttfund.web.communityservice.bean.jijinBar.post.videoArticle;

import lombok.Data;

import java.util.Date;

@Data
public class CFHArticleModel {


        public String _id;
        public String ArtCode;
        public String Title;
        public String SubTitle;
        /// <summary>
        /// 财富号ID
        /// </summary>
        public String AuthorId;
        public String ColumnIds;
        public String ListImage;
        public String Digest;
        public String MediaName;
        public String MediaType;
        public Integer IsLink;
        public Integer IsComment;
        public String MisdeedReason;
        public Integer MisdeedState;
        public Integer ApprovalState;
        public String ApprovalReason;
        public Integer IsPush;
        public Integer IsDeleted;
//        [BsonDateOptions(Kind = DateKind.Local)]
        public Date Ordertime;
//        [BsonDateOptions(Kind = DateKind.Local)]
        public Date Showtime;
        public String DigestAuto;

        /// <summary>
        /// 是否是基金财富号 true：基金公司财富号
        /// </summary>
        public Boolean IsCfhArticle;

//        [BsonDateOptions(Kind = DateKind.Local)]
        public Date CreatTime;
//        [BsonDateOptions(Kind = DateKind.Local)]
        public Date UpdateTime;
//        [BsonDateOptions(Kind = DateKind.Local)]
        public Date InsertTime;
        /// <summary>
        /// 视频封面地址
        /// </summary>
        public String VideoPic;
        /// <summary>
        /// 视频地址
        /// </summary>
        public String VideoSrc;
        /// <summary>
        /// 视频时长
        /// </summary>
        public Integer VideoTime;
        /// <summary>
        /// 视频大小
        /// </summary>
        public Integer VideoSize;

        public String VideoInfo;
        public Integer ISCFH;
        public Integer IsSimpleVideo;
        public Integer VideoType;

        /// <summary>
        /// 视频列表
        /// </summary>VideoType
        public String Videos;

        /**
         * 关联通行证id
         */
        public  String RelatedUid;

        /**
         * 帖子id
         */
        public String PostId;

}
