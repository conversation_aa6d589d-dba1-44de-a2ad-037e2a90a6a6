package ttfund.web.communityservice.bean.jijinBar.mongo;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import ttfund.web.communityservice.config.dataconfig.BarMongodbConfig;

import java.util.Date;

/**
 * 优质帖-话题关联关系表
 */
@Document(BarMongodbConfig.TABLE_QUALITY_POST_TOPIC_RELATION)
public class QualityPostTopicRelation {

    @Id
    private String _id;

    @Field("TopicId")
    private String topicId;

    @Field("PostId")
    private String postId;

    @Field("SIMI")
    private Double simi;

    @Field("IsDel")
    private int isDel;

    @Field("State")
    private int state;

    @Field("Time")
    private Date time;

    @Field("Length")
    private Integer length;

    @Field("CreateTime")
    private Date createTime;

    @Field("UpdateTime")
    private Date updateTime;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getTopicId() {
        return topicId;
    }

    public void setTopicId(String topicId) {
        this.topicId = topicId;
    }

    public String getPostId() {
        return postId;
    }

    public void setPostId(String postId) {
        this.postId = postId;
    }

    public Double getSimi() {
        return simi;
    }

    public void setSimi(Double simi) {
        this.simi = simi;
    }

    public int getIsDel() {
        return isDel;
    }

    public void setIsDel(int isDel) {
        this.isDel = isDel;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
