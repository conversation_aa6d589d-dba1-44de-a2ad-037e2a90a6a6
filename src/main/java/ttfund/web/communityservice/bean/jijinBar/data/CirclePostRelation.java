package ttfund.web.communityservice.bean.jijinBar.data;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 圈子帖子关系实体
 *
 * <AUTHOR>
 */
@Data
@ApiModel("圈子帖子关系实体")
public class CirclePostRelation {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String _id;

    /**
     * 圈子id
     */
    @ApiModelProperty("圈子id")
    private String circleId;

    /**
     * 帖子id
     */
    @ApiModelProperty("帖子id")
    private String postId;

    /**
     * 帖子标题
     */
    @ApiModelProperty("帖子标题")
    private String postTitle;

    /**
     * 帖子作者通行证id
     */
    @ApiModelProperty("帖子作者通行证id")
    private String postUid;

    /**
     * 帖子股吧删除标识
     */
    @ApiModelProperty("帖子股吧删除标识")
    private Integer postDel;

    /**
     * 帖子天天基金删除标识
     */
    @ApiModelProperty("帖子天天基金删除标识")
    private Integer postTtjjdel;

    /**
     * 帖子时间戳
     */
    @ApiModelProperty("帖子时间戳")
    private Long postTimepoint;

    /**
     * 发帖时间
     */
    @ApiModelProperty("发帖时间")
    private Date postTime;

    /**
     * 帖子圈子内标签  1：精华
     */
    @ApiModelProperty("帖子圈子内标签  1：精华")
    private Integer tag;

    /**
     * 状态  0正常   1已退出
     */
    @ApiModelProperty("状态  0正常   1已退出")
    private Integer state;

    /**
     * 入圈方式    0发帖添加   1管理员添加
     */
    @ApiModelProperty("入圈方式    0发帖添加   1管理员添加")
    private Integer joinType;

    /**
     * 入圈时间
     */
    @ApiModelProperty("入圈时间")
    private Date joinTime;

    /**
     * 入圈邀请人
     */
    @JsonIgnore
    @ApiModelProperty("入圈邀请人")
    private String joinUser;

    /**
     * 退圈方式   1管理员踢出
     */
    @ApiModelProperty("退圈方式   1管理员踢出")
    private Integer leaveType;

    /**
     * 退圈时间
     */
    @ApiModelProperty("退圈时间")
    private Date leaveTime;

    /**
     * 踢出人
     */
    @JsonIgnore
    @ApiModelProperty("踢出人")
    private String leaveUser;

    /**
     * 是否删除
     */
    @JsonIgnore
    @ApiModelProperty(name = "是否删除", hidden = true)
    private Integer isDel;

    /**
     * 创建时间
     */
    @JsonIgnore
    @ApiModelProperty(name = "创建时间", hidden = true)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonIgnore
    @ApiModelProperty(name = "更新时间", hidden = true)
    private Date updateTime;


}
