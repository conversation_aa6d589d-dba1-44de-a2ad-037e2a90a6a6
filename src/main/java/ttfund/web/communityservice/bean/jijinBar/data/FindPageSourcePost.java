package ttfund.web.communityservice.bean.jijinBar.data;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 社区发现页来源帖子实体
 *
 * <AUTHOR>
 */
@Data
public class FindPageSourcePost {

    /**
     * 主键
     */
    private String _id;

    /**
     * 帖子id
     */
    private String postId;

    /**
     * 帖子标题
     */
    private String title;

    /**
     * 帖子发帖时间
     */
    private Date time;

    /**
     * 帖子发帖时间时间戳
     */
    private Long timePoint;

    /**
     * 帖子类型
     */
    private Integer type;

    /**
     * 帖子发帖人通行证id
     */
    private String uid;

    /**
     * 帖子吧代码
     */
    private String code;

    /**
     * 帖子股吧状态
     */
    private Integer del;

    /**
     * 帖子天天基金状态
     */
    private Integer ttjjDel;

    /**
     * 点击量
     */
    private Integer clickCount;

    /**
     * 点赞量
     */
    private Integer likeCount;

    /**
     * 评论量
     */
    private Integer replyCount;

    /**
     * 字数
     */
    private Integer contentCount;

    /**
     * 来源
     * 1 ：人工优质贴
     * 2 ：AI优质贴
     * 3 ：单吧热帖
     * 4：自选基金单吧贴
     * 5：话题贴
     * 6：圈子贴
     * 7：模板贴
     * 8：用户贴
     * 9：资讯贴
     * 10：运营贴
     */
    private List<Integer> source;

    /**
     * 分数
     */
    private Double score;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
