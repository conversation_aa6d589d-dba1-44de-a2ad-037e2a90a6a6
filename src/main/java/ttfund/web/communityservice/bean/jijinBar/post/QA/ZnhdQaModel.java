package ttfund.web.communityservice.bean.jijinBar.post.QA;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class ZnhdQaModel {

    /**
     * ID  用GUID
     */
    public String ID;

    /**
     * 问题ID
     */
    public String QID;
    /**
     * 问题内容
     */
    public String Question;

    /**
     * 提问人ID
     */
    public String QPassportId;

    /**
     * 提问人昵称
     */
    public String QNickName;

    /**
     * 驳回原因
     */
    public String RejectReason;

    /**
     * 回答ID
     */
    public String AID;

    /**
     * 回答内容
     */
    public String Answer;

    /**
     * 基金代码名称
     */
    public String Code;

    /**
     * 股吧code
     */
    public String GubaCode;
    /**
     * 基金吧名称
     */
    public String BarName;

    /**
     * 分配所属人的通行证用户ID
     */
    public String DistributionPassportId;

    /**
     * 回答者通行证ID
     */
    public String AnPassportId;

    /**
     * 回答类别 1：智能回答 2：财富号用户 3：基金吧回答
     */
    public int AnswerType;


    /**
     * 回答这个用户昵称
     */
    public String AnNickName;
    /**
     * 回答是否被修改过 0:未修改,1：已修改
     */
    public int IsModifyAnswer;
    /**
     * 回答修改时间
     */
    public Date AnModifyDateTime;

    /**
     * 用户类型:1 财富号用户,2 基金提供用户
     */
    public int UserType;

    /**
     * 匹配得分
     */
    public BigDecimal Score;

    /// <summary>
    /// 0：机构待处理
    /// 1:基金待处理
    /// 2:机构待申核
    /// 3:基金待审核
    /// 4:审状态通过
    /// 5:无效关闭
    /// 6:已回复
    /// 7:转天天客服
    /// </summary>
    public int State;

    /**
     * 删除状态 0:正常 1:删除
     */
    public int Del;

    /**
     * 提问时间
     */
    public Date AskTime;

    /**
     * 创建时间
     */
    public Date CreatTime;

    /**
     * 更新时间
     */
    public Date UpdateTime;

    /**
     * 对应的智能客服回答的ID
     */
    public String TopicId;


    /**
     * 帖子标题
     */
    public String PostTitle;
    /**
     * 图片列表
     */
    public String PostPic;

    /**
     * 图片数量
     */
    public int PostImg;
    /// <summary>
    /// 回复帖子状态
    /// 调用股吧接口完成帖子回复
    /// 0:待回复 1:已回复
    /// </summary>
    public int ReplyPostState;

    /**
     * 帖子ID
     */
    public int PostId;


    /**
     * 回答附加图片，多个用逗号分隔
     */
    public String AnswerPic;

    /**
     * 0:正常数据 1:分发至基金吧后台数据
     */
    public int DataType;

}
