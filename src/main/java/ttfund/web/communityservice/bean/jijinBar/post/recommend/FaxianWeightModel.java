package ttfund.web.communityservice.bean.jijinBar.post.recommend;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

/**
 * 发现页 计算逻辑配置
 */
@Data
@JsonAutoDetect(fieldVisibility=JsonAutoDetect.Visibility.ANY, getterVisibility=JsonAutoDetect.Visibility.NONE)
@TableName(value = "tb_faxian_weight")//指定表名
public class FaxianWeightModel {




    public Integer ID;

    /// <summary>
    /// 组名
    /// </summary>
    @TableField(value="GroupName")
    public String GroupName;

    /**================== #region 帖子统计相关参数 6=================*/
    /// <summary>
    /// 近3h点击量/50--
    /// </summary>
    @TableField(value="ClickNum_Last3h")
    public Double ClickNum_Last3h;

    /// <summary>
    /// 近3h评论量--
    /// </summary>
    public Double ReplyNum_Last3h;

    /// <summary>
    /// 近3h主贴点赞量--
    /// </summary>
    public Double LikeNum_Last3h ;

    /// <summary>
    /// 累计点击量/50--
    /// </summary>
    public Double ClickNum_Total;

    /// <summary>
    /// 累计评论量--
    /// </summary>
    public Double ReplyNum_Total ;

    /// <summary>
    /// 累计主贴点赞量--
    /// </summary>
    public Double LikeNum_Total ;



     /**===============   #region 作者相关参数 6***=====================*/
    /// <summary>
    /// 标记过的优质作者--
    /// </summary>
    public Double FineAuthor ;
    /// <summary>
    /// 刷屏等需降权作者--
    /// </summary>
    public Double BlackAuthor ;

    /// <summary>
    /// 新帖--
    /// </summary>
    public Double NewPost ;

    /// <summary>
    /// 老帖--
    /// </summary>
    public Double OldPost ;

    /// <summary>
    /// 作者存量调整参数  刷屏等需降权作者--
    /// </summary>
    public Double HisBlackAuthor ;

    /// <summary>
    /// 存量分量级调整系数(由计算所得 ，现在走配置？？) 增量分：存量分比例
    /// </summary>
    //public double HisScore ;



     /**==   #region  时间衰减系数相关参数配置 7==================*/
    /// <summary>
    /// 解盘作者前期放大参数--
    /// </summary>
    public double TimeDecayJiePanAuthorIncr ;

    /// <summary>
    /// 解盘作者固定衰减系数--
    /// </summary>
    public double TimeDecayJiePanAuthorDec ;
    /// <summary>
    /// 其他作者衰减节点1经历过的访问高峰次数--
    /// </summary>

    public double TimeDecayOtherAuthorFFN ;

    /// <summary>
    /// 其他作者衰减节点1的衰减目标--
    /// </summary>
    public double TimeDecayOtherAuthorFFNVal ;


    /// <summary>
    /// 其他作者衰减节点2经历过的访问高峰次数--
    /// </summary>
    public double TimeDecayOtherAuthorFFN2 ;

    /// <summary>
    /// 其他作者衰减节点2的衰减目标
    /// </summary>
    public double TimeDecayOtherAuthorFFN2Val ;

    /// <summary>
    /// 其他作者衰减节点2后的每次衰减的乘数
    /// </summary>--
    public double TimeDecayOtherAuthorChenShu ;


    /**==   #region  人为选择精华帖的评分调整参数 ==================*/
    /// <summary>
    /// 人为选择精华帖的评分调整参数
    /// </summary>
    public double ElitePostScore ;
}
