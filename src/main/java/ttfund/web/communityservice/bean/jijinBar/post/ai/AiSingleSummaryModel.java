package ttfund.web.communityservice.bean.jijinBar.post.ai;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * AI单篇总结实体类
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@Data
@Document(collection = "ai_single_summaries")
public class AiSingleSummaryModel {

    /**
     * 主键
     */
    @Id
    private String _id;

    /**
     * 帖子ID
     */
    @Field("postId")
    private String postId;

    /**
     * 大V通行证ID
     */
    @Field("uid")
    private String uid;

    /**
     * 大V昵称
     */
    @Field("nickName")
    private String nickName;

    /**
     * AI总结内容
     */
    @Field("summary")
    private String summary;

    /**
     * 原始标题
     */
    @Field("title")
    private String title;

    /**
     * 原始内容
     */
    @Field("content")
    private String content;

    /**
     * 任务ID
     */
    @Field("taskId")
    private String taskId;

    /**
     * 处理状态
     */
    @Field("status")
    private String status;

    /**
     * 创建时间
     */
    @Field("createTime")
    private Date createTime;

    /**
     * 更新时间
     */
    @Field("updateTime")
    private Date updateTime;

    /**
     * 创建时间（大写字段名）
     */
    @Field("CREATETIME")
    private Date CREATETIME;
}
