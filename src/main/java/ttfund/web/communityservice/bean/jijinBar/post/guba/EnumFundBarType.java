package ttfund.web.communityservice.bean.jijinBar.post.guba;


/**
 * 枚举
 *
 * <AUTHOR>
 * @date 2022/12/30
 */
public enum EnumFundBarType {

    /**
     * 基金吧
     */
    JJB (1),
    /**
     * 配置吧
     */
    PZB (2),
    /**
     * 实盘吧
     */
    SPB (3),
    /**
     * 策略吧
     */
    CLB (4),
    /**
     * 投顾吧
     */
    TGB(5),

    /**
     * 模拟组合吧
     */
    MNZHB(6);

    //必须增加一个构造函数,变量,得到该变量的值
    private int mState=0;
    private EnumFundBarType(int value) {
        mState=value;
    }
    /**
     * @return 枚举变量实际返回值
     */
    public int getValue() {
        return mState;
    }
}
