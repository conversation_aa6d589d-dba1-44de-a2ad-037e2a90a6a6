package ttfund.web.communityservice.bean.jijinBar.post.recommend;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.math.BigDecimal;

/**
 * 基金吧的热帖
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class HostPostModel {

     /*
     *  {"ID":999136592,"UID":"7140112566493934","YUANID":0,"CODE":"cfhpl","TIME":"2021-01-25T06:35:01",
     "TIMEPOINT":2021012506350126743,"TYPE":20,"YUANTYPE":0,"CONTENTLENGTH":3694,"RECOMMENDDEL":0,"SCORE":1674.6785857986113,"_ID":1}
    */
    public String _id;

    /**
     * 帖子ID
     */
    public long ID;

    /**
     * 用户ID
     */
    public String UID;

    /**
     * 原帖ID
     */
    public long YUANID;

    /**
     * 基金代码
     */
    public String CODE;

    /**
     * 发帖时间
     */
    public String TIME;

    /**
     * 发帖时间，时间戳
     */
    public long TIMEPOINT;

    /**
     * 帖子类型
     */
    public int TYPE;

    /**
     * 原帖类型
     */
    public int YUANTYPE;

    /**
     * 长度
     */
    public int CONTENTLENGTH;

    /**
     * 删除状态
     */
    public int RECOMMENDDEL;

    /**
     * 得分
     */
    public BigDecimal SCORE;

    @Override
    public String toString() {
        return "HostPostModel{" +
            "_id='" + _id + '\'' +
            ", ID=" + ID +
            ", UID='" + UID + '\'' +
            ", YUANID=" + YUANID +
            ", CODE='" + CODE + '\'' +
            ", TIME='" + TIME + '\'' +
            ", TIMEPOINT=" + TIMEPOINT +
            ", TYPE=" + TYPE +
            ", YUANTYPE=" + YUANTYPE +
            ", CONTENTLENGTH=" + CONTENTLENGTH +
            ", RECOMMENDDEL=" + RECOMMENDDEL +
            ", SCORE=" + SCORE +
            '}';
    }
}
