package ttfund.web.communityservice.bean.jijinBar.post.userpost;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * 万家理财V用户实体类
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@Data
@Document(collection = "WJLCVUser")
public class WjlcVuserDo {

    /**
     * 通行证ID
     */
    @Id
    @JsonProperty("_id")
    @Field("_id")
    private String id;

    /**
     * 用户UID
     */
    @JsonProperty("UID")
    @Field("UID")
    private String uid;

    /**
     * 备注（作为昵称使用）
     */
    @JsonProperty("Remark")
    @Field("Remark")
    private String remark;

    /**
     * 是否删除（0-未删除，1-已删除）
     */
    @JsonProperty("IsDel")
    @Field("IsDel")
    private Integer isDel;

    /**
     * 创建时间
     */
    @JsonProperty("CreateTime")
    @Field("CreateTime")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonProperty("UpdateTime")
    @Field("UpdateTime")
    private Date updateTime;
}
