package ttfund.web.communityservice.timedtask;

import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfo;
import ttfund.web.communityservice.bean.jijinBar.post.ai.AiMultiSummaryModel;
import ttfund.web.communityservice.bean.jijinBar.post.ai.BigVAiTaskResponse;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.CalBigVDataUserModel;
import ttfund.web.communityservice.dao.mongo.AiMultiSummariesDao;
import ttfund.web.communityservice.dao.mongo.PostInfoMongoDao;
import ttfund.web.communityservice.dao.msyql.CalBigVDataUserDao;
import ttfund.web.communityservice.service.BigVAiSummaryService;
import ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post.BigVMultiPostAiSummary1030Job;
import ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post.BigVMultiPostAiSummary1330Job;
import ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post.BigVMultiPostAiSummary1430Job;
import ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post.BigVMultiPostAiSummary1730Job;

import java.util.*;

/**
 * 大V多机构帖子AI总结测试类
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class BigVMultiPostAiSummaryJobTest {

    @Autowired
    private BigVMultiPostAiSummary1030Job job1030;

    @Autowired
    private BigVMultiPostAiSummary1330Job job1330;

    @Autowired
    private BigVMultiPostAiSummary1430Job job1430;

    @Autowired
    private BigVMultiPostAiSummary1730Job job1730;

    @Autowired
    private CalBigVDataUserDao calBigVDataUserDao;

    @Autowired
    private PostInfoMongoDao postInfoMongoDao;

    @Autowired
    private AiMultiSummariesDao aiMultiSummariesDao;

    @Autowired
    private BigVAiSummaryService bigVAiSummaryService;

    @Test
    public void testGetBigVUsers() {
        System.out.println("=== 测试获取大V用户 ===");
        List<CalBigVDataUserModel> users = calBigVDataUserDao.getAllActiveBigVUsers();
        System.out.println("找到大V用户数量：" + users.size());
        for (CalBigVDataUserModel user : users) {
            System.out.println("UID: " + user.getUID() + ", NickName: " + user.getNickName());
        }
    }

    @Test
    public void testGetPostsByTimeRange() {
        System.out.println("=== 测试获取时间段帖子 ===");
        
        // 获取第一个大V用户进行测试
        List<CalBigVDataUserModel> users = calBigVDataUserDao.getAllActiveBigVUsers();
        if (users.isEmpty()) {
            System.out.println("没有找到大V用户");
            return;
        }
        
        CalBigVDataUserModel testUser = users.get(0);
        System.out.println("测试用户：" + testUser.getNickName() + " (" + testUser.getUID() + ")");
        
        // 测试今天8:30-10:30的帖子
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 8);
        cal.set(Calendar.MINUTE, 30);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date startTime = cal.getTime();
        
        cal.set(Calendar.HOUR_OF_DAY, 10);
        cal.set(Calendar.MINUTE, 30);
        Date endTime = cal.getTime();
        
        System.out.println("查询时间范围：" + startTime + " 到 " + endTime);
        
        List<PostInfo> posts = postInfoMongoDao.getPostsByUidAndTimeRange(
                testUser.getUID(), startTime, endTime);
        
        System.out.println("找到帖子数量：" + posts.size());
        for (PostInfo post : posts) {
            System.out.println("PostId: " + post._id + ", Title: " + post.TITLE + ", Time: " + post.TIME);
        }
    }

    @Test
    public void testFormatMultiMessage() {
        System.out.println("=== 测试多机构消息格式化 ===");
        
        List<BigVAiSummaryService.PostData> postDataList = new ArrayList<>();
        postDataList.add(new BigVAiSummaryService.PostData(
                "张三", "今日市场分析", "市场整体表现良好，建议关注科技股", "post1", "uid1"));
        postDataList.add(new BigVAiSummaryService.PostData(
                "李四", "投资建议", "债市表现稳定，股市有所波动", "post2", "uid2"));
        postDataList.add(new BigVAiSummaryService.PostData(
                "王五", "收盘总结", "今日收盘价格平稳，明日关注政策面", "post3", "uid3"));
        
        String formattedMessage = bigVAiSummaryService.formatMultiMessage(postDataList);
        System.out.println("格式化后的消息：");
        System.out.println(formattedMessage);
    }

    @Test
    public void testSubmitMultiSummaryTask() {
        System.out.println("=== 测试提交多机构总结任务 ===");
        
        String testMessage = "大V：张三\n今日市场分析\n市场整体表现良好\n大V：李四\n投资建议\n建议关注科技股";
        
        try {
            BigVAiTaskResponse response = bigVAiSummaryService.submitMultiSummaryTask(testMessage);
            if (response != null) {
                System.out.println("任务提交成功：");
                System.out.println("TaskId: " + response.getTaskId());
                System.out.println("Status: " + response.getStatus());
                System.out.println("Message: " + response.getMessage());
            } else {
                System.out.println("任务提交失败");
            }
        } catch (Exception e) {
            System.out.println("任务提交异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testPollMultiTaskResult() {
        System.out.println("=== 测试轮询多机构任务结果 ===");
        
        String testTaskId = "test-task-id-12345";
        
        try {
            BigVAiTaskResponse response = bigVAiSummaryService.pollMultiTaskResult(testTaskId);
            if (response != null) {
                System.out.println("轮询结果：");
                System.out.println("TaskId: " + response.getTaskId());
                System.out.println("Status: " + response.getStatus());
                System.out.println("Data: " + response.getData());
            } else {
                System.out.println("轮询结果为空");
            }
        } catch (Exception e) {
            System.out.println("轮询异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testSaveMultiSummary() {
        System.out.println("=== 测试保存多机构总结 ===");
        
        AiMultiSummaryModel summary = new AiMultiSummaryModel();
        summary.setPostIds("post1,post2,post3");
        summary.setUids("uid1,uid2,uid3");
        summary.setNickNames("张三,李四,王五");
        summary.setUserCount(3); // 设置用户数量
        summary.setPostCount(3); // 设置帖子数量

        String fullSummary = "{\n" +
                "    \"simplifiedVersion\": {\n" +
                "        \"marketAnalysis\": {\n" +
                "            \"title\": \"行情分析\",\n" +
                "            \"overallJudgment\": {\n" +
                "                \"title\": \"整体预判\",\n" +
                "                \"content\": \"债市偏暖（资金面宽松+股弱债强），利率债小幅上涨，信用债平稳\"\n" +
                "            }\n" +
                "        },\n" +
                "        \"todayOperations\": {\n" +
                "            \"title\": \"今日操作\",\n" +
                "            \"operations\": [\n" +
                "                {\n" +
                "                    \"type\": \"无操作\",\n" +
                "                    \"strategy\": \"市场波动较小，等待尾盘信号\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    },\n" +
                "    \"detailedVersion\": {\n" +
                "        \"marketAnalysis\": {\n" +
                "            \"title\": \"行情分析\",\n" +
                "            \"details\": [\n" +
                "                {\n" +
                "                    \"category\": \"资金面\",\n" +
                "                    \"content\": \"央行连续净回笼但资金仍宽松（DR007仅1.42%），利好债市\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"category\": \"市场表现\",\n" +
                "                    \"content\": \"利率债小幅上涨，信用债平稳，A股弱势凸显债市优势\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        \"practicalOperations\": {\n" +
                "            \"title\": \"实盘操作\",\n" +
                "            \"coreLogic\": \"观望为主（涨幅有限+机构谨慎）\",\n" +
                "            \"bigVOperations\": []\n" +
                "        }\n" +
                "    }\n" +
                "}";
        summary.setSummary(fullSummary);

        // 拆分总结
        BigVAiSummaryService.SummaryParts parts = bigVAiSummaryService.splitSummary(fullSummary);
        summary.setSummaryBrief(parts.getBriefSummary());
        summary.setSummaryDetailed(parts.getDetailedSummary());
        summary.setTaskId("test-task-id");
        summary.setStatus("COMPLETED");
        summary.setTimeSlot("08:30-10:30");
        summary.setProcessDate(new Date());
        summary.setCreateTime(new Date());
        summary.setUpdateTime(new Date());
        
        try {
            AiMultiSummaryModel saved = aiMultiSummariesDao.save(summary);
            System.out.println("保存成功，ID: " + saved.get_id());
        } catch (Exception e) {
            System.out.println("保存失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testQueryMultiSummary() {
        System.out.println("=== 测试查询多机构总结 ===");
        
        Date today = new Date();
        List<AiMultiSummaryModel> summaries = aiMultiSummariesDao.findByDate(today);
        
        System.out.println("今日总结数量：" + summaries.size());
        for (AiMultiSummaryModel summary : summaries) {
            System.out.println("时间段: " + summary.getTimeSlot() +
                             ", 状态: " + summary.getStatus() +
                             ", 帖子数: " + summary.getPostCount() +
                             ", 大V用户数: " + summary.getUserCount());
        }
    }

    @Test
    public void testExecute1030Job() throws Exception {
        System.out.println("=== 测试10:30任务执行 ===");
        
        // 测试当天数据
        job1030.execute("");
    }

    @Test
    public void testExecute1030JobWithDate() throws Exception {
        System.out.println("=== 测试10:30任务执行（指定日期） ===");
        
        // 测试指定日期数据
        Map<String, String> param = new HashMap<>();
        param.put("date", "2025-07-25");
        
        String paramJson = JSON.toJSONString(param);
        System.out.println("执行参数：" + paramJson);
        
        job1030.execute(paramJson);
    }

    @Test
    public void testExecuteAllJobs() throws Exception {
        System.out.println("=== 测试所有时间段任务执行 ===");
        
        Map<String, String> param = new HashMap<>();
        param.put("date", "2025-07-29");
        String paramJson = JSON.toJSONString(param);
        
        System.out.println("执行10:30任务...");
        job1030.execute(paramJson);
        
        System.out.println("执行13:30任务...");
        job1330.execute(paramJson);
        
        System.out.println("执行14:30任务...");
        job1430.execute(paramJson);
        
        System.out.println("执行17:30任务...");
        job1730.execute(paramJson);
        
        System.out.println("所有任务执行完成");
    }

    @Test
    public void testCheckDuplicateProcessing() {
        System.out.println("=== 测试重复处理检查 ===");

        Date today = new Date();

        // 检查各个时间段是否已处理
        String[] timeSlots = {"08:30-10:30", "10:30-13:30", "10:30-14:30", "14:30-17:30"};
        for (String timeSlot : timeSlots) {
            AiMultiSummaryModel existing = aiMultiSummariesDao.findByDateAndTimeSlot(today, timeSlot);
            if (existing != null) {
                System.out.println("时间段" + timeSlot + "已处理，TaskId: " + existing.getTaskId());
            } else {
                System.out.println("时间段" + timeSlot + "未处理");
            }
        }
    }

    @Test
    public void testTimeZoneDebug() {
        System.out.println("=== 时区调试测试 ===");

        // 获取当前时间信息
        Date now = new Date();
        Calendar cal = Calendar.getInstance();

        System.out.println("当前时间: " + now);
        System.out.println("当前时间戳: " + now.getTime());
        System.out.println("当前时区: " + cal.getTimeZone().getDisplayName());
        System.out.println("时区偏移: " + cal.getTimeZone().getRawOffset() / (1000 * 60 * 60) + "小时");

        // 测试各个时间段的时间范围
        Date today = new Date();

        // 时间段1: 8:30-10:30
        Date start1 = getTimeInDay(today, 8, 30);
        Date end1 = getTimeInDay(today, 10, 30);
        System.out.println("时间段1: " + start1 + " 到 " + end1);
        System.out.println("时间段1时间戳: " + start1.getTime() + " 到 " + end1.getTime());

        // 时间段4: 14:30-17:30
        Date start4 = getTimeInDay(today, 14, 30);
        Date end4 = getTimeInDay(today, 17, 30);
        System.out.println("时间段4: " + start4 + " 到 " + end4);
        System.out.println("时间段4时间戳: " + start4.getTime() + " 到 " + end4.getTime());

        // 测试问题时间
        Calendar problemTime = Calendar.getInstance();
        problemTime.set(2025, Calendar.JULY, 25, 8, 27, 13);
        problemTime.set(Calendar.MILLISECOND, 0);
        Date testTime = problemTime.getTime();

        System.out.println("问题时间: " + testTime);
        System.out.println("问题时间戳: " + testTime.getTime());

        // 检查时间是否在各个范围内
        System.out.println("是否在时间段1内: " + (testTime.getTime() >= start1.getTime() && testTime.getTime() < end1.getTime()));
        System.out.println("是否在时间段4内: " + (testTime.getTime() >= start4.getTime() && testTime.getTime() < end4.getTime()));
    }

    private Date getTimeInDay(Date date, int hour, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    @Test
    public void testMongoQueryDebug() {
        System.out.println("=== MongoDB查询调试测试 ===");

        // 获取第一个大V用户进行测试
        List<CalBigVDataUserModel> users = calBigVDataUserDao.getAllActiveBigVUsers();
        if (users.isEmpty()) {
            System.out.println("没有找到大V用户");
            return;
        }

        CalBigVDataUserModel testUser = users.get(0);
        System.out.println("测试用户：" + testUser.getNickName() + " (" + testUser.getUID() + ")");

        Date today = new Date();

        // 测试时间段4的查询（14:30-17:30）
        Date start4 = getTimeInDay(today, 14, 30);
        Date end4 = getTimeInDay(today, 17, 30);

        System.out.println("查询时间段4：" + start4 + " 到 " + end4);
        System.out.println("查询时间戳：" + start4.getTime() + " 到 " + end4.getTime());

        List<PostInfo> posts = postInfoMongoDao.getPostsByUidAndTimeRange(
                testUser.getUID(), start4, end4);

        System.out.println("查询结果：找到" + posts.size() + "个帖子");

        // 检查是否包含问题时间的帖子
        Calendar problemTime = Calendar.getInstance();
        problemTime.set(2025, Calendar.JULY, 25, 8, 27, 13);
        problemTime.set(Calendar.MILLISECOND, 0);
        Date testTime = problemTime.getTime();

        for (PostInfo post : posts) {
            if (post.TIME != null && Math.abs(post.TIME.getTime() - testTime.getTime()) < 1000) {
                System.out.println("发现问题帖子！PostId: " + post._id + ", TIME: " + post.TIME);
                System.out.println("帖子时间戳: " + post.TIME.getTime());
                System.out.println("预期时间戳: " + testTime.getTime());
                System.out.println("时间差: " + (post.TIME.getTime() - testTime.getTime()) + "毫秒");
            }
        }
    }



    @Test
    public void testSplitJsonSummary() {
        System.out.println("=== 测试JSON格式AI总结拆分功能 ===");

        String jsonSummary = "{\n" +
                "    \"simplifiedVersion\": {\n" +
                "        \"marketAnalysis\": {\n" +
                "            \"title\": \"行情分析\",\n" +
                "            \"overallJudgment\": {\n" +
                "                \"title\": \"整体预判\",\n" +
                "                \"content\": \"债市震荡分化，利率债回升转阴，信用债延续阴天，同业存单偏弱。A股跳水带动债市回升，机构做多利率债。\"\n" +
                "            }\n" +
                "        },\n" +
                "        \"todayOperations\": {\n" +
                "            \"title\": \"操作分享\",\n" +
                "            \"operations\": [\n" +
                "                {\n" +
                "                    \"type\": \"利率债\",\n" +
                "                    \"strategy\": \"机构做多，长端需求修复\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"type\": \"信用债\",\n" +
                "                    \"strategy\": \"短端受资金面支撑，AA+级以下表现较好\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    },\n" +
                "    \"detailedVersion\": {\n" +
                "        \"marketAnalysis\": {\n" +
                "            \"title\": \"行情分析\",\n" +
                "            \"details\": [\n" +
                "                {\n" +
                "                    \"category\": \"资金面\",\n" +
                "                    \"content\": \"央行净回笼230亿元，资金面整体维持宽松\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"category\": \"市场表现\",\n" +
                "                    \"content\": \"利率债震荡分化，信用债涨跌互现，同业存单收益率走弱\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        \"practicalOperations\": {\n" +
                "            \"title\": \"操作分享\",\n" +
                "            \"coreLogic\": \"分散配置，灵活应对波动\",\n" +
                "            \"bigVOperations\": [\n" +
                "                {\n" +
                "                    \"bigVName\": \"@大表哥\",\n" +
                "                    \"operations\": [\n" +
                "                        {\n" +
                "                            \"type\": \"持有不动\",\n" +
                "                            \"content\": \"长端利率债\",\n" +
                "                            \"product\": \"南方聪元债券发起A\",\n" +
                "                            \"productCode\": \"009005\",\n" +
                "                            \"reason\": \"维持区间震荡观点\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    }\n" +
                "}";

        System.out.println("原始JSON总结：");
        System.out.println(jsonSummary);
        System.out.println("\n==================================================\n");

        BigVAiSummaryService.SummaryParts parts = bigVAiSummaryService.splitSummary(jsonSummary);

        System.out.println("精简版（JSON格式）：");
        System.out.println(parts.getBriefSummary());
        System.out.println("\n------------------------------\n");

        System.out.println("详细版（JSON格式）：");
        System.out.println(parts.getDetailedSummary());

        // 验证拆分结果
        System.out.println("\n=== JSON拆分结果验证 ===");
        System.out.println("精简版长度: " + parts.getBriefSummary().length());
        System.out.println("详细版长度: " + parts.getDetailedSummary().length());
        System.out.println("精简版是否为JSON格式: " + parts.getBriefSummary().trim().startsWith("{"));
        System.out.println("详细版是否为JSON格式: " + parts.getDetailedSummary().trim().startsWith("{"));
        System.out.println("精简版是否包含'marketAnalysis': " + parts.getBriefSummary().contains("marketAnalysis"));
        System.out.println("详细版是否包含'marketAnalysis': " + parts.getDetailedSummary().contains("marketAnalysis"));
        System.out.println("详细版是否包含'practicalOperations': " + parts.getDetailedSummary().contains("practicalOperations"));

        // 验证精简版JSON可以正确解析
        try {
            com.alibaba.fastjson.JSONObject briefJson = com.alibaba.fastjson.JSON.parseObject(parts.getBriefSummary());
            System.out.println("精简版JSON解析成功");
            System.out.println("包含marketAnalysis: " + briefJson.containsKey("marketAnalysis"));
            System.out.println("包含todayOperations: " + briefJson.containsKey("todayOperations"));
        } catch (Exception e) {
            System.out.println("精简版JSON解析失败: " + e.getMessage());
        }

        // 验证详细版JSON可以正确解析
        try {
            com.alibaba.fastjson.JSONObject detailedJson = com.alibaba.fastjson.JSON.parseObject(parts.getDetailedSummary());
            System.out.println("详细版JSON解析成功");
            System.out.println("包含marketAnalysis: " + detailedJson.containsKey("marketAnalysis"));
            System.out.println("包含practicalOperations: " + detailedJson.containsKey("practicalOperations"));
        } catch (Exception e) {
            System.out.println("详细版JSON解析失败: " + e.getMessage());
        }
    }

    @Test
    public void testDetailedVersionJsonStructure() {
        System.out.println("=== 测试详细版JSON结构 ===");

        // 模拟保存的详细版JSON数据
        String detailedJson = "{\n" +
                "    \"practicalOperations\": {\n" +
                "        \"coreLogic\": \"资金面宽松推动债市偏晴，A股市场较弱形成股债跷跷板效应\",\n" +
                "        \"bigVOperations\": [],\n" +
                "        \"title\": \"操作分享\",\n" +
                "        \"strategy\": \"关注利率债小幅下行机会，信用债及同业存单涨幅不高\"\n" +
                "    },\n" +
                "    \"marketAnalysis\": {\n" +
                "        \"details\": [\n" +
                "            {\n" +
                "                \"category\": \"资金面\",\n" +
                "                \"content\": \"资金面延续宽松，DR007加权利率为1.42%，资金情绪明显宽松\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"category\": \"市场表现\",\n" +
                "                \"content\": \"10年期国债活跃券收益率小幅下行至1.64%下方，利率债晴天，信用债及同业存单晴到多云\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"category\": \"机构行为\",\n" +
                "                \"content\": \"证券盘中买方力量较强，银行、基金及保险卖方力量较强，机构做多意愿较低\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"title\": \"行情分析\"\n" +
                "    }\n" +
                "}";

        try {
            com.alibaba.fastjson.JSONObject detailedObj = com.alibaba.fastjson.JSON.parseObject(detailedJson);

            System.out.println("=== 市场分析部分 ===");
            if (detailedObj.containsKey("marketAnalysis")) {
                com.alibaba.fastjson.JSONObject marketAnalysis = detailedObj.getJSONObject("marketAnalysis");
                System.out.println("标题: " + marketAnalysis.getString("title"));

                if (marketAnalysis.containsKey("details")) {
                    com.alibaba.fastjson.JSONArray details = marketAnalysis.getJSONArray("details");
                    for (int i = 0; i < details.size(); i++) {
                        com.alibaba.fastjson.JSONObject detail = details.getJSONObject(i);
                        System.out.println((i + 1) + ". " + detail.getString("category") + ": " + detail.getString("content"));
                    }
                }
            }

            System.out.println("\n=== 实盘操作部分 ===");
            if (detailedObj.containsKey("practicalOperations")) {
                com.alibaba.fastjson.JSONObject practicalOps = detailedObj.getJSONObject("practicalOperations");
                System.out.println("标题: " + practicalOps.getString("title"));
                System.out.println("核心逻辑: " + practicalOps.getString("coreLogic"));
                System.out.println("策略: " + practicalOps.getString("strategy"));
                System.out.println("大V操作数量: " + practicalOps.getJSONArray("bigVOperations").size());
            }

        } catch (Exception e) {
            System.out.println("JSON解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testMultiPostScenario() {
        System.out.println("=== 测试多大V多帖子场景 ===");

        // 模拟场景：3个大V，总共5篇帖子
        // 大V1：张三，发了2篇帖子
        // 大V2：李四，发了1篇帖子
        // 大V3：王五，发了2篇帖子

        AiMultiSummaryModel summary = new AiMultiSummaryModel();
        summary.setPostIds("post1,post2,post3,post4,post5"); // 5篇帖子
        summary.setUids("uid1,uid2,uid3"); // 3个大V
        summary.setNickNames("张三,李四,王五");
        summary.setUserCount(3); // 3个大V用户
        summary.setPostCount(5); // 5篇帖子
        summary.setTimeSlot("08:30-10:30");
        summary.setProcessDate(new Date());
        summary.setCreateTime(new Date());
        summary.setUpdateTime(new Date());

        String fullSummary = "**精简版：**\n市场分析：债市偏暖\n**详细版：**\n详细的市场分析内容";
        summary.setSummary(fullSummary);

        BigVAiSummaryService.SummaryParts parts = bigVAiSummaryService.splitSummary(fullSummary);
        summary.setSummaryBrief(parts.getBriefSummary());
        summary.setSummaryDetailed(parts.getDetailedSummary());

        try {
            AiMultiSummaryModel saved = aiMultiSummariesDao.save(summary);
            System.out.println("保存成功，ID: " + saved.get_id());
            System.out.println("大V用户数: " + saved.getUserCount());
            System.out.println("帖子总数: " + saved.getPostCount());
            System.out.println("平均每个大V发帖数: " + (saved.getPostCount() / (double) saved.getUserCount()));

            // 验证数据
            System.out.println("\n=== 数据验证 ===");
            System.out.println("帖子ID列表: " + saved.getPostIds());
            System.out.println("UID列表: " + saved.getUids());
            System.out.println("昵称列表: " + saved.getNickNames());

        } catch (Exception e) {
            System.out.println("保存失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
