package ttfund.web.communityservice.timedtask;

import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfo;
import ttfund.web.communityservice.bean.jijinBar.post.ai.AiMultiSummaryModel;
import ttfund.web.communityservice.bean.jijinBar.post.ai.BigVAiTaskResponse;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.CalBigVDataUserModel;
import ttfund.web.communityservice.dao.mongo.AiMultiSummariesDao;
import ttfund.web.communityservice.dao.mongo.PostInfoMongoDao;
import ttfund.web.communityservice.dao.msyql.CalBigVDataUserDao;
import ttfund.web.communityservice.service.BigVAiSummaryService;
import ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post.BigVMultiPostAiSummary1030Job;
import ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post.BigVMultiPostAiSummary1330Job;
import ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post.BigVMultiPostAiSummary1430Job;
import ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post.BigVMultiPostAiSummary1730Job;

import java.util.*;

/**
 * 大V多机构帖子AI总结测试类
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class BigVMultiPostAiSummaryJobTest {

    @Autowired
    private BigVMultiPostAiSummary1030Job job1030;

    @Autowired
    private BigVMultiPostAiSummary1330Job job1330;

    @Autowired
    private BigVMultiPostAiSummary1430Job job1430;

    @Autowired
    private BigVMultiPostAiSummary1730Job job1730;

    @Autowired
    private CalBigVDataUserDao calBigVDataUserDao;

    @Autowired
    private PostInfoMongoDao postInfoMongoDao;

    @Autowired
    private AiMultiSummariesDao aiMultiSummariesDao;

    @Autowired
    private BigVAiSummaryService bigVAiSummaryService;

    @Test
    public void testGetBigVUsers() {
        System.out.println("=== 测试获取大V用户 ===");
        List<CalBigVDataUserModel> users = calBigVDataUserDao.getAllActiveBigVUsers();
        System.out.println("找到大V用户数量：" + users.size());
        for (CalBigVDataUserModel user : users) {
            System.out.println("UID: " + user.getUID() + ", NickName: " + user.getNickName());
        }
    }

    @Test
    public void testGetPostsByTimeRange() {
        System.out.println("=== 测试获取时间段帖子 ===");
        
        // 获取第一个大V用户进行测试
        List<CalBigVDataUserModel> users = calBigVDataUserDao.getAllActiveBigVUsers();
        if (users.isEmpty()) {
            System.out.println("没有找到大V用户");
            return;
        }
        
        CalBigVDataUserModel testUser = users.get(0);
        System.out.println("测试用户：" + testUser.getNickName() + " (" + testUser.getUID() + ")");
        
        // 测试今天8:30-10:30的帖子
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 8);
        cal.set(Calendar.MINUTE, 30);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date startTime = cal.getTime();
        
        cal.set(Calendar.HOUR_OF_DAY, 10);
        cal.set(Calendar.MINUTE, 30);
        Date endTime = cal.getTime();
        
        System.out.println("查询时间范围：" + startTime + " 到 " + endTime);
        
        List<PostInfo> posts = postInfoMongoDao.getPostsByUidAndTimeRange(
                testUser.getUID(), startTime, endTime);
        
        System.out.println("找到帖子数量：" + posts.size());
        for (PostInfo post : posts) {
            System.out.println("PostId: " + post._id + ", Title: " + post.TITLE + ", Time: " + post.TIME);
        }
    }

    @Test
    public void testFormatMultiMessage() {
        System.out.println("=== 测试多机构消息格式化 ===");
        
        List<BigVAiSummaryService.PostData> postDataList = new ArrayList<>();
        postDataList.add(new BigVAiSummaryService.PostData(
                "张三", "今日市场分析", "市场整体表现良好，建议关注科技股", "post1", "uid1"));
        postDataList.add(new BigVAiSummaryService.PostData(
                "李四", "投资建议", "债市表现稳定，股市有所波动", "post2", "uid2"));
        postDataList.add(new BigVAiSummaryService.PostData(
                "王五", "收盘总结", "今日收盘价格平稳，明日关注政策面", "post3", "uid3"));
        
        String formattedMessage = bigVAiSummaryService.formatMultiMessage(postDataList);
        System.out.println("格式化后的消息：");
        System.out.println(formattedMessage);
    }

    @Test
    public void testSubmitMultiSummaryTask() {
        System.out.println("=== 测试提交多机构总结任务 ===");
        
        String testMessage = "大V：张三\n今日市场分析\n市场整体表现良好\n大V：李四\n投资建议\n建议关注科技股";
        
        try {
            BigVAiTaskResponse response = bigVAiSummaryService.submitMultiSummaryTask(testMessage);
            if (response != null) {
                System.out.println("任务提交成功：");
                System.out.println("TaskId: " + response.getTaskId());
                System.out.println("Status: " + response.getStatus());
                System.out.println("Message: " + response.getMessage());
            } else {
                System.out.println("任务提交失败");
            }
        } catch (Exception e) {
            System.out.println("任务提交异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testPollMultiTaskResult() {
        System.out.println("=== 测试轮询多机构任务结果 ===");
        
        String testTaskId = "test-task-id-12345";
        
        try {
            BigVAiTaskResponse response = bigVAiSummaryService.pollMultiTaskResult(testTaskId);
            if (response != null) {
                System.out.println("轮询结果：");
                System.out.println("TaskId: " + response.getTaskId());
                System.out.println("Status: " + response.getStatus());
                System.out.println("Data: " + response.getData());
            } else {
                System.out.println("轮询结果为空");
            }
        } catch (Exception e) {
            System.out.println("轮询异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testSaveMultiSummary() {
        System.out.println("=== 测试保存多机构总结 ===");
        
        AiMultiSummaryModel summary = new AiMultiSummaryModel();
        summary.setPostIds("post1,post2,post3");
        summary.setUids("uid1,uid2,uid3");
        summary.setNickNames("张三,李四,王五");
        summary.setSummary("市场信号：股弱债强，资金面宽松支撑债市\n操作逻辑：机构谨慎参与，证券买方力量较强\n收蛋预期：债市平稳，收蛋希望较大");
        summary.setTaskId("test-task-id");
        summary.setStatus("COMPLETED");
        summary.setTimeSlot(1);
        summary.setProcessDate(new Date());
        summary.setCreateTime(new Date());
        summary.setUpdateTime(new Date());
        
        try {
            AiMultiSummaryModel saved = aiMultiSummariesDao.save(summary);
            System.out.println("保存成功，ID: " + saved.get_id());
        } catch (Exception e) {
            System.out.println("保存失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testQueryMultiSummary() {
        System.out.println("=== 测试查询多机构总结 ===");
        
        Date today = new Date();
        List<AiMultiSummaryModel> summaries = aiMultiSummariesDao.findByDate(today);
        
        System.out.println("今日总结数量：" + summaries.size());
        for (AiMultiSummaryModel summary : summaries) {
            System.out.println("时间段: " + summary.getTimeSlot() + 
                             ", 状态: " + summary.getStatus() + 
                             ", 帖子数: " + (summary.getPostIds() != null ? summary.getPostIds().split(",").length : 0));
        }
    }

    @Test
    public void testExecute1030Job() throws Exception {
        System.out.println("=== 测试10:30任务执行 ===");
        
        // 测试当天数据
        job1030.execute("");
    }

    @Test
    public void testExecute1030JobWithDate() throws Exception {
        System.out.println("=== 测试10:30任务执行（指定日期） ===");
        
        // 测试指定日期数据
        Map<String, String> param = new HashMap<>();
        param.put("date", "2025-07-25");
        
        String paramJson = JSON.toJSONString(param);
        System.out.println("执行参数：" + paramJson);
        
        job1030.execute(paramJson);
    }

    @Test
    public void testExecuteAllJobs() throws Exception {
        System.out.println("=== 测试所有时间段任务执行 ===");
        
        Map<String, String> param = new HashMap<>();
        param.put("date", "2025-07-25");
        String paramJson = JSON.toJSONString(param);
        
        System.out.println("执行10:30任务...");
        job1030.execute(paramJson);
        
        System.out.println("执行13:30任务...");
        job1330.execute(paramJson);
        
        System.out.println("执行14:30任务...");
        job1430.execute(paramJson);
        
        System.out.println("执行17:30任务...");
        job1730.execute(paramJson);
        
        System.out.println("所有任务执行完成");
    }

    @Test
    public void testCheckDuplicateProcessing() {
        System.out.println("=== 测试重复处理检查 ===");

        Date today = new Date();

        // 检查各个时间段是否已处理
        for (int timeSlot = 1; timeSlot <= 4; timeSlot++) {
            AiMultiSummaryModel existing = aiMultiSummariesDao.findByDateAndTimeSlot(today, timeSlot);
            if (existing != null) {
                System.out.println("时间段" + timeSlot + "已处理，TaskId: " + existing.getTaskId());
            } else {
                System.out.println("时间段" + timeSlot + "未处理");
            }
        }
    }

    @Test
    public void testTimeZoneDebug() {
        System.out.println("=== 时区调试测试 ===");

        // 获取当前时间信息
        Date now = new Date();
        Calendar cal = Calendar.getInstance();

        System.out.println("当前时间: " + now);
        System.out.println("当前时间戳: " + now.getTime());
        System.out.println("当前时区: " + cal.getTimeZone().getDisplayName());
        System.out.println("时区偏移: " + cal.getTimeZone().getRawOffset() / (1000 * 60 * 60) + "小时");

        // 测试各个时间段的时间范围
        Date today = new Date();

        // 时间段1: 8:30-10:30
        Date start1 = getTimeInDay(today, 8, 30);
        Date end1 = getTimeInDay(today, 10, 30);
        System.out.println("时间段1: " + start1 + " 到 " + end1);
        System.out.println("时间段1时间戳: " + start1.getTime() + " 到 " + end1.getTime());

        // 时间段4: 14:30-17:30
        Date start4 = getTimeInDay(today, 14, 30);
        Date end4 = getTimeInDay(today, 17, 30);
        System.out.println("时间段4: " + start4 + " 到 " + end4);
        System.out.println("时间段4时间戳: " + start4.getTime() + " 到 " + end4.getTime());

        // 测试问题时间
        Calendar problemTime = Calendar.getInstance();
        problemTime.set(2025, Calendar.JULY, 25, 8, 27, 13);
        problemTime.set(Calendar.MILLISECOND, 0);
        Date testTime = problemTime.getTime();

        System.out.println("问题时间: " + testTime);
        System.out.println("问题时间戳: " + testTime.getTime());

        // 检查时间是否在各个范围内
        System.out.println("是否在时间段1内: " + (testTime.getTime() >= start1.getTime() && testTime.getTime() < end1.getTime()));
        System.out.println("是否在时间段4内: " + (testTime.getTime() >= start4.getTime() && testTime.getTime() < end4.getTime()));
    }

    private Date getTimeInDay(Date date, int hour, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    @Test
    public void testMongoQueryDebug() {
        System.out.println("=== MongoDB查询调试测试 ===");

        // 获取第一个大V用户进行测试
        List<CalBigVDataUserModel> users = calBigVDataUserDao.getAllActiveBigVUsers();
        if (users.isEmpty()) {
            System.out.println("没有找到大V用户");
            return;
        }

        CalBigVDataUserModel testUser = users.get(0);
        System.out.println("测试用户：" + testUser.getNickName() + " (" + testUser.getUID() + ")");

        Date today = new Date();

        // 测试时间段4的查询（14:30-17:30）
        Date start4 = getTimeInDay(today, 14, 30);
        Date end4 = getTimeInDay(today, 17, 30);

        System.out.println("查询时间段4：" + start4 + " 到 " + end4);
        System.out.println("查询时间戳：" + start4.getTime() + " 到 " + end4.getTime());

        List<PostInfo> posts = postInfoMongoDao.getPostsByUidAndTimeRange(
                testUser.getUID(), start4, end4);

        System.out.println("查询结果：找到" + posts.size() + "个帖子");

        // 检查是否包含问题时间的帖子
        Calendar problemTime = Calendar.getInstance();
        problemTime.set(2025, Calendar.JULY, 25, 8, 27, 13);
        problemTime.set(Calendar.MILLISECOND, 0);
        Date testTime = problemTime.getTime();

        for (PostInfo post : posts) {
            if (post.TIME != null && Math.abs(post.TIME.getTime() - testTime.getTime()) < 1000) {
                System.out.println("发现问题帖子！PostId: " + post._id + ", TIME: " + post.TIME);
                System.out.println("帖子时间戳: " + post.TIME.getTime());
                System.out.println("预期时间戳: " + testTime.getTime());
                System.out.println("时间差: " + (post.TIME.getTime() - testTime.getTime()) + "毫秒");
            }
        }
    }
}
