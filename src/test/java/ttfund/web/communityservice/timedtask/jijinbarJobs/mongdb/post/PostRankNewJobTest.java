package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.communityservice.dao.mongo.PostRankNewDao;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024-11-13 10:33
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class PostRankNewJobTest {

    @Autowired
    private PostRankNewJob postRankNewJob;

    @Test
    void execute() throws Exception {
        postRankNewJob.execute("2020-08-08 00:00:00.000");
    }
}