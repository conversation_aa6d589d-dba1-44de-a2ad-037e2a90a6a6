package ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2025-01-22 15:48
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class CommunityVideoConfigSyncJobTest {

    @Autowired
    private CommunityVideoConfigSyncJob communityVideoConfigSyncJob;

    @Test
    void execute() throws Exception {
        communityVideoConfigSyncJob.execute("2020-10-10 00:00:00.000");
    }
}