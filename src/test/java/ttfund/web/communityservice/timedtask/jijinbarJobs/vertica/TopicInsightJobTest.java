package ttfund.web.communityservice.timedtask.jijinbarJobs.vertica;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024-08-21 10:33
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class TopicInsightJobTest {

    @Autowired
    private TopicInsightJob topicInsightJob;

    @Test
    void execute() throws Exception {
        topicInsightJob.execute("2020-08-11 00:00:00.000");
    }
}