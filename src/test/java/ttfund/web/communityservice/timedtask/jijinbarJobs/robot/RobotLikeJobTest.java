package ttfund.web.communityservice.timedtask.jijinbarJobs.robot;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024-11-22 17:03
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class RobotLikeJobTest {

    @Autowired
    private RobotLikeJob robotLikeJob;

    @Test
    void execute() throws Exception {
        robotLikeJob.execute("");
    }
}