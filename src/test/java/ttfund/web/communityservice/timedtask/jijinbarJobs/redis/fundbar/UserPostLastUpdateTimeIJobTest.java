package ttfund.web.communityservice.timedtask.jijinbarJobs.redis.fundbar;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;
/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024-08-22 14:23
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class UserPostLastUpdateTimeIJobTest {

    @Autowired
    private UserPostLastUpdateTimeIJob postLastUpdateTimeIJob;

    @Test
    void logicDeal() {
        postLastUpdateTimeIJob.logicDeal("");
    }
}