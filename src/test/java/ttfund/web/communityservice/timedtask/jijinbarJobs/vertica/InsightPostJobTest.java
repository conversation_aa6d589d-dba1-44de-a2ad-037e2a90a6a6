package ttfund.web.communityservice.timedtask.jijinbarJobs.vertica;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024-11-18 18:57
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class InsightPostJobTest {

    @Autowired
    private InsightPostJob insightPostJob;

    @Test
    void execute() throws Exception {
        insightPostJob.execute("2020-01-11 00:00:00.000");
    }
}