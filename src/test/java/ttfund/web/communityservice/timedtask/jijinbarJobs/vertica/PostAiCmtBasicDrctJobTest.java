package ttfund.web.communityservice.timedtask.jijinbarJobs.vertica;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;
/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024-08-22 10:44
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class PostAiCmtBasicDrctJobTest {

    @Autowired
    private PostAiCmtBasicDrctJob postAiCmtBasicDrctJob;

    @Test
    void execute() throws Exception {
        postAiCmtBasicDrctJob.execute("2023-08-14 00:00:00.000");
    }
}