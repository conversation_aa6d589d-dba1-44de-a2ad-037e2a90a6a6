package ttfund.web.communityservice.timedtask;

import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.communityservice.bean.jijinBar.post.userpost.CalBigVDataUserModel;
import ttfund.web.communityservice.dao.msyql.CalBigVDataUserDao;
import ttfund.web.communityservice.dao.mongo.PostInfoMongoDao;
import ttfund.web.communityservice.service.BigVAiSummaryService;
import ttfund.web.communityservice.timedtask.jijinbarJobs.mongdb.post.BigVPostAiSummaryJob;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfo;
import java.util.Date;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 大V帖子AI总结任务测试
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class BigVPostAiSummaryJobTest {

    @Autowired
    private BigVPostAiSummaryJob bigVPostAiSummaryJob;

    @Autowired
    private CalBigVDataUserDao calBigVDataUserDao;

    @Autowired
    private BigVAiSummaryService bigVAiSummaryService;

    @Autowired
    private PostInfoMongoDao postInfoMongoDao;

    @Test
    public void testGetBigVUsers() {
        List<CalBigVDataUserModel> users = calBigVDataUserDao.getAllActiveBigVUsers();
        System.out.println("找到大V用户数量：" + users.size());
        for (CalBigVDataUserModel user : users) {
            System.out.println("UID: " + user.getUID() + ", NickName: " + user.getNickName());
        }
    }

    @Test
    public void testFormatMessage() {
        String message = bigVAiSummaryService.formatMessage("测试大V", "测试标题", "测试内容");
        System.out.println("格式化消息：");
        System.out.println(message);
    }

    @Test
    public void testExecuteJob() throws Exception {
        // 测试执行任务（使用昨天的日期）
        Map<String, String> param = new HashMap<>();
        param.put("date", "2025-07-24");
        
        String paramJson = JSON.toJSONString(param);
        System.out.println("执行参数：" + paramJson);
        
        bigVPostAiSummaryJob.execute(paramJson);
    }

    @Test
    public void testExecuteJobWithoutParam() throws Exception {
        // 测试执行任务（使用当天日期）
        bigVPostAiSummaryJob.execute("");
    }

    @Test
    public void testPostInfoFields() {
        // 测试PostInfo字段访问
        PostInfo post = new PostInfo();
        post._id = "test_id";
        post.TITLE = "测试标题";
        post.CONTENTEND = "测试内容";
        post.UID = "test_uid";

        System.out.println("PostId: " + post._id);
        System.out.println("Title: " + post.TITLE);
        System.out.println("Content: " + post.CONTENTEND);
        System.out.println("UID: " + post.UID);
    }
}
