package ttfund.web.communityservice.dao.msyql;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.communityservice.bean.jijinBar.post.ReplyInfoModel;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> Zhuyuang
 * @date : 2024-08-23 13:16
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class ReplyInfoDaoTest {

    @Autowired
    private ReplyInfoDao replyInfoDao;

    @Test
    void getLatestReply() {
        List<ReplyInfoModel> latestReply = replyInfoDao.getLatestReply(Arrays.asList(1376688769));
        System.out.println(latestReply);
    }
}