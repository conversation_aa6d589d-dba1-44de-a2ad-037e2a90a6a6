package ttfund.web.communityservice.dao.mongo;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.communityservice.bean.jijinBar.data.TopicWordDo;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Zhu<PERSON><PERSON>
 * @date : 2024-08-20 17:55
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class TopicWordMongoDaoTest {

    @Autowired
    private TopicWordMongoDao topicWordMongoDao;

    @Test
    void upsertMany() {
        TopicWordDo wordDo = new TopicWordDo();
        wordDo.setTopicId("test");
        wordDo.setWord("testWord");
        wordDo.setCount(100);
        wordDo.setUpdateTime(new Date());
        List<TopicWordDo> testCase = Arrays.asList(wordDo);
        topicWordMongoDao.upsertMany(testCase, "_id");
    }
}