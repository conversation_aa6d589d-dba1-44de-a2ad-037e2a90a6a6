package ttfund.web.communityservice.dao.mongo;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.communityservice.bean.jijinBar.post.TopicInsightModel;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Zhuyuang
 * @version : 1.0
 * @date : 2024-08-19 16:46
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class TopicInsightMongoDaoTest {

    @Autowired
    private TopicInsightMongoDao topicInsightMongoDao;

    @Test
    void upsertMany() {
        TopicInsightModel topicInsight = new TopicInsightModel();
        topicInsight.setTopicId("testTopic");
        TopicInsightModel.Insight insight = new TopicInsightModel.Insight();
        insight.setOpinionId("test");
        insight.setContent("testContent");
        insight.setDetail(Arrays.asList("test1", "test2", "test3"));
        topicInsight.setInsights(Arrays.asList(insight));
        List<TopicInsightModel> testCase = Arrays.asList(topicInsight);
        topicInsightMongoDao.upsertMany(testCase, "_id");
    }
}