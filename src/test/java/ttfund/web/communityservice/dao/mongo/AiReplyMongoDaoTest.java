package ttfund.web.communityservice.dao.mongo;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.communityservice.bean.jijinBar.data.AiCommentDo;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024-08-22 11:02
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class AiReplyMongoDaoTest {

    @Autowired
    private AiReplyMongoDao aiReplyMongoDao;

    @Test
    void getListByTime() {
        List<AiCommentDo> list = aiReplyMongoDao.getList(100, null);
        System.out.println(list);
    }
}