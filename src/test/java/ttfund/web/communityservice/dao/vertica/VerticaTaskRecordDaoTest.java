package ttfund.web.communityservice.dao.vertica;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.communityservice.constant.DateConstant;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;

/**
 * <AUTHOR> Zhuyuang
 * @version : 1.0
 * @date : 2024-08-19 15:53
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class VerticaTaskRecordDaoTest {

    @Autowired
    private VerticaTaskRecordDao verticaTaskRecordDao;

    @Test
    void selectLatestRecordExist() {
        String dateStr = "2023-04-28 10:37:16";
        Date endTime = DateUtil.strToDate(dateStr, DateConstant.YYYY_MM_DD_HH_MM_SS_FORMAT);
        Date result = verticaTaskRecordDao.selectLatestRecordExist("CONFIG_HOTSPOT_SORT_STAT_ALL_SYN", endTime);
        System.out.println(result);
    }
}