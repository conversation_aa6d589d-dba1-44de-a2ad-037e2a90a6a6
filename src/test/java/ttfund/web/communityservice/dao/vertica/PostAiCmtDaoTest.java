package ttfund.web.communityservice.dao.vertica;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> Zhu<PERSON><PERSON>
 * @date : 2024-08-22 10:53
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class PostAiCmtDaoTest {

    @Autowired
    private PostAiCmtDao postAiCmtDao;

    @Test
    void select() {
        postAiCmtDao.select(DateUtil.dateToStr(new Date()));
    }
}