package ttfund.web.communityservice.dao.vertica;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.communityservice.bean.jijinBar.data.TopicInsightDo;
import ttfund.web.communityservice.constant.DateConstant;
import ttfund.web.communityservice.utils.DateUtil;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2024-08-20 16:40
 * @description :
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class FundTopicViewVerticaDaoTest {

    @Autowired
    private FundTopicViewVerticaDao fundTopicViewVerticaDao;

    @Test
    void select() {
        String dateStr = "2023-04-26 10:37:16";
        Date endTime = DateUtil.strToDate(dateStr, DateConstant.YYYY_MM_DD_HH_MM_SS_FORMAT);
        List<TopicInsightDo> result = fundTopicViewVerticaDao.select(endTime);
        System.out.println(result);
    }
}