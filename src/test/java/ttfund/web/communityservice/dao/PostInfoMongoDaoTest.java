package ttfund.web.communityservice.dao;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.communityservice.bean.jijinBar.post.PostInfo;
import ttfund.web.communityservice.dao.mongo.PostInfoMongoDao;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * PostInfoMongoDao测试
 *
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PostInfoMongoDaoTest {

    @Autowired
    private PostInfoMongoDao postInfoMongoDao;

    @Test
    public void testGetPostsByUidAndTimeRange() {
        // 测试根据UID和时间范围获取帖子
        String testUid = "test_uid";
        
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date startTime = cal.getTime();
        
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        Date endTime = cal.getTime();
        
        System.out.println("查询参数：");
        System.out.println("UID: " + testUid);
        System.out.println("开始时间: " + startTime);
        System.out.println("结束时间: " + endTime);
        
        List<PostInfo> posts = postInfoMongoDao.getPostsByUidAndTimeRange(testUid, startTime, endTime);
        System.out.println("查询结果数量: " + posts.size());
        
        for (PostInfo post : posts) {
            System.out.println("PostId: " + post._id + ", Title: " + post.TITLE);
        }
    }

    @Test
    public void testGetPostsByIds() {
        // 测试根据ID列表获取帖子
        List<String> postIds = Arrays.asList("test_id_1", "test_id_2", "test_id_3");
        
        System.out.println("查询帖子ID列表: " + postIds);
        
        List<PostInfo> posts = postInfoMongoDao.getPostsByIds(postIds);
        System.out.println("查询结果数量: " + posts.size());
        
        for (PostInfo post : posts) {
            System.out.println("PostId: " + post._id + ", Title: " + post.TITLE);
        }
    }

    @Test
    public void testGetPostById() {
        // 测试根据单个ID获取帖子
        String postId = "test_post_id";
        
        System.out.println("查询帖子ID: " + postId);
        
        PostInfo post = postInfoMongoDao.getPostById(postId);
        if (post != null) {
            System.out.println("找到帖子: " + post._id + ", Title: " + post.TITLE);
        } else {
            System.out.println("未找到帖子");
        }
    }
}
